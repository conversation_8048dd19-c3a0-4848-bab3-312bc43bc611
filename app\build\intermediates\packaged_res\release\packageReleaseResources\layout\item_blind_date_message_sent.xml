<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingTop="4dp"
    android:paddingEnd="16dp"
    android:paddingBottom="4dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="80dp"
        app:cardBackgroundColor="#B3E5FC"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp">

            <TextView
                android:id="@+id/textViewMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="240dp"
                android:textColor="#000000"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Este es un mensaje enviado por mí" />

            <ImageView
                android:id="@+id/imageViewMessageStatus"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="4dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/textViewMessage"
                app:layout_constraintStart_toEndOf="@+id/textViewMessage"
                tools:src="@android:drawable/ic_popup_sync"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
