<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:paddingStart="48dp"
    android:paddingEnd="8dp">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/imageViewUserProfile"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_profile_placeholder"
        app:civ_border_width="1dp"
        app:civ_border_color="#CCCCCC"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/contentLayout"
        android:contentDescription="User profile picture" />

    <LinearLayout
        android:id="@+id/contentLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="end"
        android:layout_marginEnd="8dp"
        app:layout_constraintEnd_toStartOf="@+id/imageViewUserProfile"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Reply layout -->
        <LinearLayout
            android:id="@+id/layoutReply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginBottom="4dp"
            android:background="@drawable/background_reply_sent"
            android:orientation="vertical"
            android:padding="8dp"
            android:visibility="gone">

            <TextView
                android:id="@+id/textViewReplyLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Respondiendo a:"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

            <androidx.emoji2.widget.EmojiTextView
                android:id="@+id/textViewReplyText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="Original message text"
                android:textColor="@android:color/white"
                android:textSize="14sp" />
        </LinearLayout>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            app:cardBackgroundColor="#E3F2FD"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:cardUseCompatPadding="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="vertical"
                android:padding="4dp">

                <androidx.emoji2.widget.EmojiTextView
                    android:id="@+id/textViewMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:breakStrategy="simple"
                    android:hyphenationFrequency="none"
                    android:text="GIF"
                    android:textColor="#000000"
                    android:textSize="16sp" />

                <!-- GIF container -->
                <ImageView
                    android:id="@+id/imageViewGif"
                    android:layout_width="match_parent"
                    android:layout_height="350dp"
                    android:layout_marginTop="4dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:contentDescription="GIF" />

                <TextView
                    android:id="@+id/textViewTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="4dp"
                    android:text="12:34"
                    android:textColor="#757575"
                    android:textSize="12sp" />

                <!-- Check mark for read status -->
                <ImageView
                    android:id="@+id/imageViewReadStatus"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="end"
                    android:layout_marginTop="2dp"
                    android:src="@drawable/ic_check"
                    android:visibility="visible" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Reactions container -->
        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/reactionsContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            app:flexWrap="wrap"
            app:justifyContent="flex_end" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>