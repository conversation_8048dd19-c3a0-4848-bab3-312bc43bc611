package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010%\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0002\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u00018B\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\"\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u0005J\b\u0010$\u001a\u00020\u000bH\u0016J\u0018\u0010%\u001a\n \'*\u0004\u0018\u00010&0&2\u0006\u0010(\u001a\u00020)H\u0002J\u0010\u0010*\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020!H\u0016J\u001c\u0010+\u001a\u00020\u000f2\n\u0010,\u001a\u00060\u0002R\u00020\u00002\u0006\u0010-\u001a\u00020\u000bH\u0016J\u001c\u0010.\u001a\u00060\u0002R\u00020\u00002\u0006\u0010/\u001a\u0002002\u0006\u00101\u001a\u00020\u000bH\u0016J\u0010\u00102\u001a\u00020\u000f2\u0006\u0010 \u001a\u00020!H\u0016J\u0014\u00103\u001a\u00020\u000f2\n\u0010,\u001a\u00060\u0002R\u00020\u0000H\u0016J\u0006\u00104\u001a\u00020\u000fJ\u0014\u00105\u001a\u00020\u000f2\f\u00106\u001a\b\u0012\u0004\u0012\u00020\u000507R\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R(\u0010\r\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013R(\u0010\u0014\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0011\"\u0004\b\u0016\u0010\u0013R(\u0010\u0017\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0018\u0010\u0011\"\u0004\b\u0019\u0010\u0013R(\u0010\u001a\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u0011\"\u0004\b\u001c\u0010\u0013R(\u0010\u001d\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000f\u0018\u00010\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u0011\"\u0004\b\u001f\u0010\u0013R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00069"}, d2 = {"Lcom/spyro/vmeet/ui/community/PostAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/ui/community/PostAdapter$PostViewHolder;", "posts", "", "Lcom/spyro/vmeet/data/community/Post;", "(Ljava/util/List;)V", "API_URL", "", "activePlayers", "", "", "Landroid/media/MediaPlayer;", "onCommentClickListener", "Lkotlin/Function1;", "", "getOnCommentClickListener", "()Lkotlin/jvm/functions/Function1;", "setOnCommentClickListener", "(Lkotlin/jvm/functions/Function1;)V", "onDeleteClickListener", "getOnDeleteClickListener", "setOnDeleteClickListener", "onImageClickListener", "getOnImageClickListener", "setOnImageClickListener", "onLikeClickListener", "getOnLikeClickListener", "setOnLikeClickListener", "onUserClickListener", "getOnUserClickListener", "setOnUserClickListener", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "addPost", "post", "getItemCount", "getSharedPreferences", "Landroid/content/SharedPreferences;", "kotlin.jvm.PlatformType", "context", "Landroid/content/Context;", "onAttachedToRecyclerView", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "onDetachedFromRecyclerView", "onViewRecycled", "releaseAllPlayers", "updatePosts", "newPosts", "", "PostViewHolder", "app_release"})
public final class PostAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.ui.community.PostAdapter.PostViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.data.community.Post> posts;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> onLikeClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> onCommentClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> onUserClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> onImageClickListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> onDeleteClickListener;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, android.media.MediaPlayer> activePlayers = null;
    @org.jetbrains.annotations.Nullable()
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    
    public PostAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.community.Post> posts) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.community.Post, kotlin.Unit> getOnLikeClickListener() {
        return null;
    }
    
    public final void setOnLikeClickListener(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.community.Post, kotlin.Unit> getOnCommentClickListener() {
        return null;
    }
    
    public final void setOnCommentClickListener(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.community.Post, kotlin.Unit> getOnUserClickListener() {
        return null;
    }
    
    public final void setOnUserClickListener(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.community.Post, kotlin.Unit> getOnImageClickListener() {
        return null;
    }
    
    public final void setOnImageClickListener(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.community.Post, kotlin.Unit> getOnDeleteClickListener() {
        return null;
    }
    
    public final void setOnDeleteClickListener(@org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Post, kotlin.Unit> p0) {
    }
    
    @java.lang.Override()
    public void onAttachedToRecyclerView(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView recyclerView) {
    }
    
    @java.lang.Override()
    public void onDetachedFromRecyclerView(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView recyclerView) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.ui.community.PostAdapter.PostViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.ui.community.PostAdapter.PostViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @java.lang.Override()
    public void onViewRecycled(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.ui.community.PostAdapter.PostViewHolder holder) {
    }
    
    public final void releaseAllPlayers() {
    }
    
    public final void updatePosts(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.community.Post> newPosts) {
    }
    
    public final void addPost(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.data.community.Post post) {
    }
    
    private final android.content.SharedPreferences getSharedPreferences(android.content.Context context) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008c\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\r\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u00103\u001a\u0002042\u0006\u00105\u001a\u00020\nJ\u0012\u00106\u001a\u0004\u0018\u0001072\u0006\u00108\u001a\u000207H\u0002J\u0010\u00109\u001a\u0002072\u0006\u0010:\u001a\u00020;H\u0002J\u0010\u0010<\u001a\u0002072\u0006\u0010=\u001a\u00020>H\u0002J\u0010\u0010?\u001a\u0002042\u0006\u0010@\u001a\u000207H\u0002J\u0010\u0010A\u001a\u0002042\u0006\u0010@\u001a\u000207H\u0002J\u0018\u0010B\u001a\u0002042\u0006\u0010C\u001a\u0002072\u0006\u0010D\u001a\u00020\nH\u0002J\u0006\u0010E\u001a\u000204J\u0010\u0010F\u001a\u0002042\u0006\u00105\u001a\u00020\nH\u0002J\u0018\u0010G\u001a\u0002042\u0006\u0010H\u001a\u00020\u00032\u0006\u00105\u001a\u00020\nH\u0002J\b\u0010I\u001a\u000204H\u0002J\b\u0010J\u001a\u000204H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0012\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010*\u001a\u0004\u0018\u00010+X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010,\u001a\u00020-\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u000e\u00100\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006K"}, d2 = {"Lcom/spyro/vmeet/ui/community/PostAdapter$PostViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/ui/community/PostAdapter;Landroid/view/View;)V", "commentButton", "Landroid/widget/ImageButton;", "commentCount", "Landroid/widget/TextView;", "currentPlayingPost", "Lcom/spyro/vmeet/data/community/Post;", "handler", "Landroid/os/Handler;", "isYoutubePlayerInitialized", "", "()Z", "setYoutubePlayerInitialized", "(Z)V", "likeButton", "mediaPlayer", "Landroid/media/MediaPlayer;", "playPauseVoiceNoteButton", "postImage", "Landroid/widget/ImageView;", "postImageContainer", "Landroidx/cardview/widget/CardView;", "reactionCount", "textContent", "timestamp", "updateSeekBarRunnable", "Ljava/lang/Runnable;", "userAvatar", "username", "voiceNoteCurrentTimeTextView", "voiceNoteDurationTextView", "voiceNoteIcon", "voiceNotePlayerLayout", "Landroid/widget/LinearLayout;", "voiceNoteSeekBar", "Landroid/widget/SeekBar;", "youtubeContainer", "youtubePlayButton", "youtubePlayer", "Lcom/pierfrancescosoffritti/androidyoutubeplayer/core/player/YouTubePlayer;", "youtubePlayerView", "Lcom/pierfrancescosoffritti/androidyoutubeplayer/core/player/views/YouTubePlayerView;", "getYoutubePlayerView", "()Lcom/pierfrancescosoffritti/androidyoutubeplayer/core/player/views/YouTubePlayerView;", "youtubeThumbnail", "youtubeThumbnailLayout", "youtubeTitle", "bind", "", "post", "extractYoutubeId", "", "text", "formatDuration", "durationMillis", "", "formatTimestamp", "epochMillis", "", "openYoutubeVideoExternally", "videoId", "playYoutubeVideo", "prepareAndPlay", "url", "postData", "releaseMediaPlayer", "showDeleteConfirmationDialog", "showPostOptionsMenu", "view", "stopSeekBarUpdate", "updateSeekBar", "app_release"})
    public final class PostViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView userAvatar = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView username = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView timestamp = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textContent = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.cardview.widget.CardView postImageContainer = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView postImage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageButton likeButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView reactionCount = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageButton commentButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView commentCount = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.cardview.widget.CardView youtubeContainer = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView youtubeThumbnail = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView youtubePlayButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView youtubeTitle = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout youtubeThumbnailLayout = null;
        @org.jetbrains.annotations.NotNull()
        private final com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView youtubePlayerView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout voiceNotePlayerLayout = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageButton playPauseVoiceNoteButton = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView voiceNoteCurrentTimeTextView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.SeekBar voiceNoteSeekBar = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView voiceNoteDurationTextView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView voiceNoteIcon = null;
        @org.jetbrains.annotations.Nullable()
        private android.media.MediaPlayer mediaPlayer;
        @org.jetbrains.annotations.NotNull()
        private final android.os.Handler handler = null;
        @org.jetbrains.annotations.Nullable()
        private com.spyro.vmeet.data.community.Post currentPlayingPost;
        @org.jetbrains.annotations.Nullable()
        private com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer youtubePlayer;
        private boolean isYoutubePlayerInitialized = false;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.Runnable updateSeekBarRunnable = null;
        
        public PostViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView getYoutubePlayerView() {
            return null;
        }
        
        public final boolean isYoutubePlayerInitialized() {
            return false;
        }
        
        public final void setYoutubePlayerInitialized(boolean p0) {
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.community.Post post) {
        }
        
        private final java.lang.String formatTimestamp(long epochMillis) {
            return null;
        }
        
        private final java.lang.String formatDuration(int durationMillis) {
            return null;
        }
        
        private final void updateSeekBar() {
        }
        
        private final void stopSeekBarUpdate() {
        }
        
        private final void prepareAndPlay(java.lang.String url, com.spyro.vmeet.data.community.Post postData) {
        }
        
        public final void releaseMediaPlayer() {
        }
        
        private final java.lang.String extractYoutubeId(java.lang.String text) {
            return null;
        }
        
        private final void playYoutubeVideo(java.lang.String videoId) {
        }
        
        private final void openYoutubeVideoExternally(java.lang.String videoId) {
        }
        
        private final void showPostOptionsMenu(android.view.View view, com.spyro.vmeet.data.community.Post post) {
        }
        
        private final void showDeleteConfirmationDialog(com.spyro.vmeet.data.community.Post post) {
        }
    }
}