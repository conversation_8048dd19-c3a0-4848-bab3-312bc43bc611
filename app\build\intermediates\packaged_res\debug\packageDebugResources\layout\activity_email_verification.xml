<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient"
    tools:context=".activity.EmailVerificationActivity">

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="64dp"
        android:layout_marginEnd="24dp"
        android:text="Verificación de correo"
        android:textColor="@color/comfy_blue"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewSubtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:text="Hemos enviado un código de verificación a tu correo electrónico:"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewTitle" />

    <TextView
        android:id="@+id/textViewEmail"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:text="e***<EMAIL>"
        android:textColor="@color/neon_blue"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewSubtitle" />

    <TextView
        android:id="@+id/textViewInstructions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:text="Ingresa el código de 6 dígitos para verificar tu cuenta:"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewEmail" />



    <LinearLayout
        android:id="@+id/linearLayoutCode"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewInstructions">

        <EditText
            android:id="@+id/editTextDigit1"
            android:layout_width="48dp"
            android:layout_height="56dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/rounded_edittext_background"
            android:gravity="center"
            android:inputType="number"
            android:maxLength="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editTextDigit2"
            android:layout_width="48dp"
            android:layout_height="56dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/rounded_edittext_background"
            android:gravity="center"
            android:inputType="number"
            android:maxLength="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editTextDigit3"
            android:layout_width="48dp"
            android:layout_height="56dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/rounded_edittext_background"
            android:gravity="center"
            android:inputType="number"
            android:maxLength="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editTextDigit4"
            android:layout_width="48dp"
            android:layout_height="56dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/rounded_edittext_background"
            android:gravity="center"
            android:inputType="number"
            android:maxLength="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editTextDigit5"
            android:layout_width="48dp"
            android:layout_height="56dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/rounded_edittext_background"
            android:gravity="center"
            android:inputType="number"
            android:maxLength="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editTextDigit6"
            android:layout_width="48dp"
            android:layout_height="56dp"
            android:background="@drawable/rounded_edittext_background"
            android:gravity="center"
            android:inputType="number"
            android:maxLength="1"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />
    </LinearLayout>

    <Button
        android:id="@+id/buttonVerify"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/rounded_button_background"
        android:text="Verificar"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayoutCode" />

    <TextView
        android:id="@+id/textViewTimer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:text="Puedes solicitar un nuevo código en 60 segundos"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/buttonVerify" />

    <Button
        android:id="@+id/buttonResend"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/rounded_button_outline_background"
        android:text="Reenviar código"
        android:textColor="@color/comfy_blue"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewTimer" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
