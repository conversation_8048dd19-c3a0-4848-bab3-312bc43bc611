<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.RECORD_AUDIO"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
                     android:maxSdkVersion="32"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>

    <!-- Location permissions for Radar feature - ONLY foreground access -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

    <application
        android:name=".VMeetApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.VMeet"
        android:networkSecurityConfig="@xml/network_security_config"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.VMeet">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".LoginActivity" />
        <activity android:name=".TutorialActivity" />
        <activity
            android:name=".ProfileActivity"
            android:exported="false" />
        <activity
            android:name=".BlockedUsersActivity"
            android:exported="false" />
        <activity android:name=".SwipeActivity" />
        <activity android:name=".MatchesActivity" />
        <activity android:name=".CommunityHostActivity" />
        <activity android:name=".ui.community.CommentsActivity" />
        <activity
            android:name=".ui.community.StoryEditorActivity" />
        <activity
            android:name=".activity.BlindDateActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name=".activity.ChatActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity android:name=".activity.ChatListActivity" />
        <activity android:name=".activity.SettingsActivity" />

        <!-- Email verification and password reset activities -->
        <activity android:name=".activity.EmailVerificationActivity" />
        <activity android:name=".activity.PasswordResetActivity" />
        <activity android:name=".activity.ForgotPasswordActivity" />

        <!-- Chat Room Loader - handles safe transition to chat room -->
        <activity
            android:name=".activity.RoomLoaderActivity"
            android:theme="@style/Theme.VMeet.NoActionBar"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustResize" />

        <!-- Chat Room Activity for group chat functionality -->
        <activity
            android:name=".activity.ChatRoomActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:excludeFromRecents="false" />

        <!-- Video feature activities -->
        <activity
            android:name=".VideoFeedActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.VMeet.NoActionBar"/>
        <activity
            android:name=".UploadVideoActivity"
            android:theme="@style/Theme.VMeet.NoActionBar"/>
        <activity
            android:name=".MyVideosActivity"
            android:theme="@style/Theme.VMeet.NoActionBar"/>

        <!-- Admin panel activities -->
        <activity
            android:name=".AdminPanelActivity"
            android:label="Panel de Administración" />
        <activity
            android:name=".UserManagementActivity"
            android:label="Gestión de Usuarios" />
        <activity
            android:name=".ReportsActivity"
            android:label="Reportes de Usuarios" />

        <!-- Full-screen image viewer activity with a special theme for immersive viewing -->
        <activity
            android:name=".FullScreenImageActivity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />

        <!-- Story Viewer Activity -->
        <activity
            android:name=".ui.community.StoryViewerActivity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />

        <!-- User Profile Activity -->
        <activity
            android:name=".activity.UserProfileActivity"
            android:label="Perfil de Usuario" />

        <!-- Edit Personality Traits Activity -->
        <activity
            android:name=".activity.EditPersonalityTraitsActivity"
            android:label="Editar Rasgos de Personalidad" />

        <!-- Community Guidelines Activity -->
        <activity
            android:name=".activity.CommunityGuidelinesActivity"
            android:label="Normas de la comunidad" />

        <!-- User Verification Activity -->
        <activity
            android:name=".VerificationActivity"
            android:label="Verificación de Usuario"
            android:theme="@style/Theme.VMeet.NoActionBar" />

        <!-- Firebase Cloud Messaging Service -->
        <service
            android:name=".notifications.VMeetFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- BroadcastReceiver for notification actions -->
        <receiver
            android:name=".notifications.MessageActionReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.spyro.vmeet.ACTION_MARK_AS_READ" />
                <action android:name="com.spyro.vmeet.ACTION_REPLY" />
            </intent-filter>
        </receiver>

        <!-- Sample AdMob App ID. Replace with your real App ID before production. -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~**********"/>

        <!-- FileProvider for sharing images from camera -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>