package com.spyro.vmeet.fragment;

/**
 * Fragment for the Radar feature - location-based user discovery
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ee\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u0000 \u008e\u00012\u00020\u0001:\u0002\u008e\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0016\u00108\u001a\u0002092\f\u0010:\u001a\b\u0012\u0004\u0012\u00020&0;H\u0002J\u0010\u0010<\u001a\u0002092\u0006\u0010=\u001a\u00020\u0016H\u0002J\b\u0010>\u001a\u000209H\u0002J\b\u0010?\u001a\u000209H\u0002J\b\u0010@\u001a\u000209H\u0002J\b\u0010A\u001a\u000209H\u0002J\b\u0010B\u001a\u000209H\u0002J\u0010\u0010C\u001a\u00020\u001f2\u0006\u0010D\u001a\u00020EH\u0002J\u0010\u0010F\u001a\u0002092\u0006\u0010G\u001a\u00020\u0012H\u0002J\u0010\u0010H\u001a\u0002092\u0006\u0010D\u001a\u00020EH\u0002J\b\u0010I\u001a\u00020\u0012H\u0002J\b\u0010J\u001a\u000209H\u0002J\b\u0010K\u001a\u000209H\u0002J\b\u0010L\u001a\u000209H\u0002J\u0010\u0010M\u001a\u0002092\u0006\u0010N\u001a\u00020\u0016H\u0002J\b\u0010O\u001a\u00020\u0012H\u0002J\b\u0010P\u001a\u000209H\u0002J\b\u0010Q\u001a\u000209H\u0002J\b\u0010R\u001a\u000209H\u0002J\b\u0010S\u001a\u000209H\u0002J\u0012\u0010T\u001a\u0002092\b\u0010U\u001a\u0004\u0018\u00010VH\u0016J&\u0010W\u001a\u0004\u0018\u00010\u00162\u0006\u0010X\u001a\u00020Y2\b\u0010Z\u001a\u0004\u0018\u00010[2\b\u0010U\u001a\u0004\u0018\u00010VH\u0016J\b\u0010\\\u001a\u000209H\u0016J\b\u0010]\u001a\u000209H\u0016J\b\u0010^\u001a\u000209H\u0016J\u001a\u0010_\u001a\u0002092\u0006\u0010N\u001a\u00020\u00162\b\u0010U\u001a\u0004\u0018\u00010VH\u0016J\u0016\u0010`\u001a\b\u0012\u0004\u0012\u00020&0;2\u0006\u0010a\u001a\u00020bH\u0002J\u001c\u0010c\u001a\u0002092\u0012\u0010d\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u0002090eH\u0002J\b\u0010f\u001a\u000209H\u0002J\b\u0010g\u001a\u000209H\u0002J\u0012\u0010h\u001a\u0002092\b\b\u0002\u0010i\u001a\u00020\u0012H\u0002J\b\u0010j\u001a\u000209H\u0002J\b\u0010k\u001a\u000209H\u0002J$\u0010l\u001a\u0002092\u0006\u00107\u001a\u00020#2\b\u0010m\u001a\u0004\u0018\u00010\u001f2\b\u0010n\u001a\u0004\u0018\u00010\u001fH\u0002J\u0010\u0010o\u001a\u0002092\u0006\u0010=\u001a\u00020\u0016H\u0002J\b\u0010p\u001a\u000209H\u0002J\u0010\u0010q\u001a\u0002092\u0006\u0010r\u001a\u00020sH\u0002J\u0010\u0010t\u001a\u0002092\u0006\u0010r\u001a\u00020sH\u0002J\b\u0010u\u001a\u000209H\u0002J\b\u0010v\u001a\u000209H\u0002J\b\u0010w\u001a\u000209H\u0002J\u0010\u0010x\u001a\u0002092\u0006\u0010y\u001a\u00020\u001fH\u0002J\b\u0010z\u001a\u000209H\u0002J\u0010\u0010{\u001a\u0002092\u0006\u0010|\u001a\u00020\u0012H\u0002J\b\u0010}\u001a\u000209H\u0002J\u0010\u0010~\u001a\u0002092\u0006\u0010y\u001a\u00020\u001fH\u0002J\b\u0010\u007f\u001a\u000209H\u0002J\u0011\u0010\u0080\u0001\u001a\u0002092\u0006\u0010y\u001a\u00020\u001fH\u0002J\t\u0010\u0081\u0001\u001a\u000209H\u0002J\t\u0010\u0082\u0001\u001a\u000209H\u0002J\t\u0010\u0083\u0001\u001a\u000209H\u0002J\u0012\u0010\u0084\u0001\u001a\u0002092\u0007\u0010\u0085\u0001\u001a\u00020\u000eH\u0002J\u001e\u0010\u0086\u0001\u001a\u0002092\u0007\u0010\u0085\u0001\u001a\u00020\u000e2\n\u0010\u0087\u0001\u001a\u0005\u0018\u00010\u0088\u0001H\u0002J\u0017\u0010\u0089\u0001\u001a\u0002092\f\u0010:\u001a\b\u0012\u0004\u0012\u00020&0;H\u0002J\u0017\u0010\u008a\u0001\u001a\u0002092\f\u0010:\u001a\b\u0012\u0004\u0012\u00020&0;H\u0002J\u0017\u0010\u008b\u0001\u001a\u0002092\f\u0010:\u001a\b\u0012\u0004\u0012\u00020&0;H\u0002J \u0010\u008c\u0001\u001a\u0002092\u0006\u0010a\u001a\u00020b2\r\u0010\u008d\u0001\u001a\b\u0012\u0004\u0012\u00020&0;H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010$\u001a\b\u0012\u0004\u0012\u00020&0%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020+X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020-X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010.\u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010/\u001a\u0004\u0018\u000100X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00102\u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u000204X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u000206X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020#X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u008f\u0001"}, d2 = {"Lcom/spyro/vmeet/fragment/RadarFragment;", "Lcom/spyro/vmeet/ui/base/BaseFragment;", "()V", "buttonExpandSearch", "Landroid/widget/Button;", "buttonFilter", "Landroid/widget/ImageButton;", "buttonRefreshLocation", "buttonRequestPermission", "client", "Lokhttp3/OkHttpClient;", "currentFilters", "Lcom/spyro/vmeet/data/RadarFilters;", "currentLocation", "Landroid/location/Location;", "fusedLocationClient", "Lcom/google/android/gms/location/FusedLocationProviderClient;", "isUpdatingUsersList", "", "lastUpdateTimestamp", "", "layoutEmptyState", "Landroid/view/View;", "layoutLocationStatus", "Landroid/widget/LinearLayout;", "layoutPermissionRequest", "locationCallback", "Lcom/google/android/gms/location/LocationCallback;", "locationPermissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "", "mainHandler", "Landroid/os/Handler;", "maxRetries", "", "nearbyUsers", "", "Lcom/spyro/vmeet/data/RadarUser;", "pendingUpdate", "progressBar", "Landroid/widget/ProgressBar;", "radarAdapter", "Lcom/spyro/vmeet/adapter/RadarUserAdapter;", "recyclerViewUsers", "Landroidx/recyclerview/widget/RecyclerView;", "refreshHandler", "refreshRunnable", "Ljava/lang/Runnable;", "retryCount", "retryHandler", "swipeRefreshLayout", "Landroidx/swiperefreshlayout/widget/SwipeRefreshLayout;", "textViewLocationStatus", "Landroid/widget/TextView;", "userId", "addNearbyUsers", "", "users", "", "applyFiltersFromDialog", "dialogView", "checkLocationPermissions", "expandSearchRadius", "forceIPGeolocation", "forceLocationUpdate", "getCurrentLocation", "getErrorMessage", "exception", "Ljava/io/IOException;", "handleEmptyState", "isEmpty", "handleNetworkError", "hasLocationPermissions", "hideEmptyState", "hideLocationStatusAfterDelay", "hidePermissionRequest", "initializeViews", "view", "isNetworkAvailable", "loadNearbyUsers", "loadNearbyUsersInternal", "loadNearbyUsersWithMinimalUI", "loadNearbyUsersWithRetry", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onPause", "onResume", "onViewCreated", "parseUsersFromJson", "usersArray", "Lorg/json/JSONArray;", "performServerHealthCheck", "onResult", "Lkotlin/Function1;", "refreshNearbyUsers", "refreshUI", "requestFreshLocation", "withIpFallback", "requestLocationPermissions", "requestLocationUpdates", "saveUserLocation", "city", "country", "setCurrentFilterValues", "setupClickListeners", "setupDistanceSpinner", "spinner", "Landroid/widget/Spinner;", "setupGenderSpinner", "setupRecyclerView", "setupSwipeRefresh", "showEmptyState", "showError", "message", "showFiltersDialog", "showLoading", "show", "showLocationOptionsMenu", "showLocationStatus", "showPermissionRequest", "showToast", "startAutoRefresh", "stopAutoRefresh", "stopLocationUpdates", "updateLocationOnServer", "location", "updateLocationOnServerWithGeocoding", "locationInfo", "Lcom/spyro/vmeet/utils/LocationUtils$LocationInfo;", "updateNearbyUsers", "updateUsersList", "updateUsersListSafely", "verifyDataConsistency", "parsedUsers", "Companion", "app_release"})
public final class RadarFragment extends com.spyro.vmeet.ui.base.BaseFragment {
    private androidx.recyclerview.widget.RecyclerView recyclerViewUsers;
    private androidx.swiperefreshlayout.widget.SwipeRefreshLayout swipeRefreshLayout;
    private android.widget.ProgressBar progressBar;
    private android.view.View layoutEmptyState;
    private android.view.View layoutPermissionRequest;
    private android.widget.LinearLayout layoutLocationStatus;
    private android.widget.TextView textViewLocationStatus;
    private android.widget.ImageButton buttonFilter;
    private android.widget.ImageButton buttonRefreshLocation;
    private android.widget.Button buttonRequestPermission;
    private android.widget.Button buttonExpandSearch;
    private com.spyro.vmeet.adapter.RadarUserAdapter radarAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.RadarUser> nearbyUsers = null;
    @org.jetbrains.annotations.NotNull()
    private com.spyro.vmeet.data.RadarFilters currentFilters;
    private com.google.android.gms.location.FusedLocationProviderClient fusedLocationClient;
    @org.jetbrains.annotations.Nullable()
    private android.location.Location currentLocation;
    @org.jetbrains.annotations.Nullable()
    private com.google.android.gms.location.LocationCallback locationCallback;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    private int retryCount = 0;
    private final int maxRetries = 3;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler retryHandler;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler mainHandler = null;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler refreshHandler;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable refreshRunnable;
    private boolean isUpdatingUsersList = false;
    private boolean pendingUpdate = false;
    private long lastUpdateTimestamp = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "RadarFragment";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    private static final long AUTO_REFRESH_INTERVAL = 30000L;
    private static final long MIN_UPDATE_INTERVAL = 5000L;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String[]> locationPermissionLauncher = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.fragment.RadarFragment.Companion Companion = null;
    
    public RadarFragment() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeViews(android.view.View view) {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupSwipeRefresh() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void showLocationOptionsMenu() {
    }
    
    private final void showToast(java.lang.String message) {
    }
    
    private final void checkLocationPermissions() {
    }
    
    private final boolean hasLocationPermissions() {
        return false;
    }
    
    private final void requestLocationPermissions() {
    }
    
    private final void showPermissionRequest() {
    }
    
    private final void hidePermissionRequest() {
    }
    
    private final void forceLocationUpdate() {
    }
    
    private final void requestFreshLocation(boolean withIpFallback) {
    }
    
    private final void getCurrentLocation() {
    }
    
    private final void requestLocationUpdates() {
    }
    
    private final void showLocationStatus(java.lang.String message) {
    }
    
    private final void hideLocationStatusAfterDelay() {
    }
    
    private final void updateLocationOnServer(android.location.Location location) {
    }
    
    private final void updateLocationOnServerWithGeocoding(android.location.Location location, com.spyro.vmeet.utils.LocationUtils.LocationInfo locationInfo) {
    }
    
    private final void loadNearbyUsers() {
    }
    
    private final void refreshNearbyUsers() {
    }
    
    private final void showFiltersDialog() {
    }
    
    private final void expandSearchRadius() {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    private final void stopLocationUpdates() {
    }
    
    private final void startAutoRefresh() {
    }
    
    private final void stopAutoRefresh() {
    }
    
    private final void showLoading(boolean show) {
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void showEmptyState() {
    }
    
    private final void hideEmptyState() {
    }
    
    private final boolean isNetworkAvailable() {
        return false;
    }
    
    private final void loadNearbyUsersWithRetry() {
    }
    
    private final void loadNearbyUsersInternal() {
    }
    
    private final java.lang.String getErrorMessage(java.io.IOException exception) {
        return null;
    }
    
    private final void performServerHealthCheck(kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onResult) {
    }
    
    private final void handleNetworkError(java.io.IOException exception) {
    }
    
    private final void setupDistanceSpinner(android.widget.Spinner spinner) {
    }
    
    private final void setupGenderSpinner(android.widget.Spinner spinner) {
    }
    
    private final void setCurrentFilterValues(android.view.View dialogView) {
    }
    
    private final void applyFiltersFromDialog(android.view.View dialogView) {
    }
    
    private final void verifyDataConsistency(org.json.JSONArray usersArray, java.util.List<com.spyro.vmeet.data.RadarUser> parsedUsers) {
    }
    
    private final java.util.List<com.spyro.vmeet.data.RadarUser> parseUsersFromJson(org.json.JSONArray usersArray) {
        return null;
    }
    
    /**
     * Versión con mínima UI de loadNearbyUsers para refresh automático
     * No muestra indicadores de carga ni mensajes de error
     */
    private final void loadNearbyUsersWithMinimalUI() {
    }
    
    /**
     * Actualiza la lista de usuarios de forma segura, evitando que se mezclen datos
     */
    private final void updateUsersListSafely(java.util.List<com.spyro.vmeet.data.RadarUser> users) {
    }
    
    /**
     * Versión original de updateUsersList para uso interactivo
     */
    private final void updateUsersList(java.util.List<com.spyro.vmeet.data.RadarUser> users) {
    }
    
    /**
     * Fuerza la actualización de ubicación usando el servidor, con fallback a IP
     * Esta función es útil cuando el GPS no está disponible o no es preciso
     */
    private final void forceIPGeolocation() {
    }
    
    /**
     * Método para forzar la actualización de UI después de una actualización automática
     */
    private final void refreshUI() {
    }
    
    /**
     * Guarda la información de ubicación de un usuario en SharedPreferences
     * para que esté disponible en otras partes de la aplicación
     */
    private final void saveUserLocation(int userId, java.lang.String city, java.lang.String country) {
    }
    
    /**
     * Actualiza la lista de usuarios cercanos
     */
    private final void updateNearbyUsers(java.util.List<com.spyro.vmeet.data.RadarUser> users) {
    }
    
    /**
     * Añade usuarios a la lista existente
     */
    private final void addNearbyUsers(java.util.List<com.spyro.vmeet.data.RadarUser> users) {
    }
    
    /**
     * Gestiona la visualización del estado vacío cuando no hay usuarios
     */
    private final void handleEmptyState(boolean isEmpty) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/fragment/RadarFragment$Companion;", "", "()V", "API_URL", "", "AUTO_REFRESH_INTERVAL", "", "LOCATION_PERMISSION_REQUEST_CODE", "", "MIN_UPDATE_INTERVAL", "TAG", "newInstance", "Lcom/spyro/vmeet/fragment/RadarFragment;", "userId", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.fragment.RadarFragment newInstance(int userId) {
            return null;
        }
    }
}