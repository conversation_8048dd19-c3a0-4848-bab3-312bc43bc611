package com.spyro.vmeet.network

import android.content.Context
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import java.util.concurrent.TimeUnit

/**
 * Centralized HTTP client configuration with authentication
 */
object HttpClient {
    
    private var client: OkHttpClient? = null
    
    /**
     * Get configured OkHttpClient instance
     */
    fun getInstance(context: Context): OkHttpClient {
        if (client == null) {
            synchronized(this) {
                if (client == null) {
                    client = createClient(context)
                }
            }
        }
        return client!!
    }
    
    /**
     * Create and configure OkHttpClient
     */
    private fun createClient(context: Context): OkHttpClient {
        val builder = OkHttpClient.Builder()
        
        // Add authentication interceptor
        builder.addInterceptor(AuthInterceptor(context))
        
        // Add logging interceptor for debugging (only in debug builds)
        if (BuildConfig.DEBUG) {
            val loggingInterceptor = HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            }
            builder.addInterceptor(loggingInterceptor)
        }
        
        // Configure timeouts
        builder.apply {
            connectTimeout(30, TimeUnit.SECONDS)
            readTimeout(30, TimeUnit.SECONDS)
            writeTimeout(30, TimeUnit.SECONDS)
        }
        
        return builder.build()
    }
    
    /**
     * Reset client instance (useful for testing or configuration changes)
     */
    fun reset() {
        synchronized(this) {
            client = null
        }
    }
}
