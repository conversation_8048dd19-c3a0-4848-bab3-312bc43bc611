package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0014J\b\u0010\u0010\u001a\u00020\rH\u0002J\u0010\u0010\u0011\u001a\u00020\r2\u0006\u0010\u0012\u001a\u00020\u0004H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/spyro/vmeet/activity/ForgotPasswordActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "TAG", "emailEditText", "Landroid/widget/EditText;", "progressBar", "Landroid/widget/ProgressBar;", "resetButton", "Landroid/widget/Button;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "requestPasswordReset", "showSpamWarningDialog", "message", "app_debug"})
public final class ForgotPasswordActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "ForgotPasswordActivity";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    private android.widget.EditText emailEditText;
    private android.widget.Button resetButton;
    private android.widget.ProgressBar progressBar;
    
    public ForgotPasswordActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void requestPasswordReset() {
    }
    
    private final void showSpamWarningDialog(java.lang.String message) {
    }
}