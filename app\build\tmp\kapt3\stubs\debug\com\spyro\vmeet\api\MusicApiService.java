package com.spyro.vmeet.api;

/**
 * Servicio para interactuar con la API de Deezer para búsqueda de música
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0007\u0018\u0000 \u000f2\u00020\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0002J*\u0010\u0003\u001a\u00020\u00042\"\u0010\u0005\u001a\u001e\u0012\f\u0012\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\u0006\u0012\u0004\u0018\u00010\t\u0012\u0004\u0012\u00020\u00040\u0006J\u0016\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\u000b\u001a\u00020\tH\u0002J\u0016\u0010\f\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\u000b\u001a\u00020\tH\u0002J2\u0010\r\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\t2\"\u0010\u0005\u001a\u001e\u0012\f\u0012\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\u0006\u0012\u0004\u0018\u00010\t\u0012\u0004\u0012\u00020\u00040\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/api/MusicApiService;", "", "()V", "getPopularTracks", "", "callback", "Lkotlin/Function2;", "", "Lcom/spyro/vmeet/data/community/MusicData;", "", "parseChartData", "jsonResponse", "parseMusicData", "searchSongs", "query", "Companion", "app_debug"})
public final class MusicApiService {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "MusicApiService";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEEZER_API_URL = "https://api.deezer.com";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String SEARCH_ENDPOINT = "/search";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CHART_ENDPOINT = "/chart/0/tracks";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.api.MusicApiService.Companion Companion = null;
    
    public MusicApiService() {
        super();
    }
    
    /**
     * Obtiene canciones populares del momento
     * @param callback Callback para recibir resultados
     */
    public final void getPopularTracks(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.util.List<com.spyro.vmeet.data.community.MusicData>, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * Buscar canciones por consulta
     * @param query La consulta de búsqueda (artista, título, etc.)
     * @param callback Callback para recibir resultados
     */
    public final void searchSongs(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.util.List<com.spyro.vmeet.data.community.MusicData>, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * Parsear los datos de música de la respuesta JSON
     */
    private final java.util.List<com.spyro.vmeet.data.community.MusicData> parseMusicData(java.lang.String jsonResponse) {
        return null;
    }
    
    /**
     * Parsear los datos de música del chart de la respuesta JSON
     */
    private final java.util.List<com.spyro.vmeet.data.community.MusicData> parseChartData(java.lang.String jsonResponse) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/spyro/vmeet/api/MusicApiService$Companion;", "", "()V", "CHART_ENDPOINT", "", "DEEZER_API_URL", "SEARCH_ENDPOINT", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}