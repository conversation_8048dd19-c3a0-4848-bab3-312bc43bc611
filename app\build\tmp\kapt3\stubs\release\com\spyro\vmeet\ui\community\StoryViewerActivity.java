package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ba\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010%\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u0007\n\u0002\b\u0003\b\u0007\u0018\u0000 f2\u00020\u0001:\u0001fB\u0005\u00a2\u0006\u0002\u0010\u0002J \u0010:\u001a\u00020;2\u0006\u0010<\u001a\u00020=2\u0006\u0010>\u001a\u00020\f2\u0006\u0010?\u001a\u00020\fH\u0002J\u0018\u0010@\u001a\u00020;2\u000e\u0010A\u001a\n\u0012\u0004\u0012\u00020=\u0018\u000103H\u0002J\u0010\u0010B\u001a\u00020;2\u0006\u0010C\u001a\u00020\u0006H\u0002J\b\u0010D\u001a\u00020;H\u0002J\b\u0010E\u001a\u00020;H\u0002J\b\u0010F\u001a\u00020;H\u0002J\u0010\u0010G\u001a\u00020;2\u0006\u0010H\u001a\u000204H\u0002J\u0010\u0010I\u001a\u00020;2\u0006\u0010H\u001a\u000204H\u0002J\u0010\u0010J\u001a\u00020\f2\u0006\u0010K\u001a\u00020\fH\u0002J\u0010\u0010L\u001a\u00020\u00042\u0006\u0010M\u001a\u00020\u0006H\u0002J\u0012\u0010N\u001a\u00020O2\b\u0010P\u001a\u0004\u0018\u00010\u0004H\u0002J\u0010\u0010Q\u001a\u00020;2\u0006\u0010R\u001a\u000204H\u0002J\u0012\u0010S\u001a\u00020;2\b\u0010T\u001a\u0004\u0018\u00010UH\u0014J\b\u0010V\u001a\u00020;H\u0014J\b\u0010W\u001a\u00020;H\u0014J\b\u0010X\u001a\u00020;H\u0014J\u0010\u0010Y\u001a\u00020;2\u0006\u0010H\u001a\u000204H\u0002J\b\u0010Z\u001a\u00020;H\u0002J\u0010\u0010[\u001a\u00020;2\u0006\u0010H\u001a\u000204H\u0002J\b\u0010\\\u001a\u00020;H\u0002J\b\u0010]\u001a\u00020;H\u0002J\b\u0010^\u001a\u00020;H\u0002J\b\u0010_\u001a\u00020;H\u0002J\b\u0010`\u001a\u00020;H\u0002J\u0018\u0010a\u001a\u00020;2\u0006\u0010b\u001a\u00020\u00192\u0006\u0010c\u001a\u00020dH\u0002J\u0010\u0010e\u001a\u00020;2\u0006\u0010H\u001a\u000204H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00190\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020*X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010/\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u001b0#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u00102\u001a\b\u0012\u0004\u0012\u00020403X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u000207X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006g"}, d2 = {"Lcom/spyro/vmeet/ui/community/StoryViewerActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "()V", "API_URL", "", "AUTO_CLOSE_DELAY_MS", "", "activeAnimations", "", "Landroid/view/animation/Animation;", "animationNameMap", "", "", "closeButton", "Landroid/widget/ImageView;", "closeRunnable", "Ljava/lang/Runnable;", "currentProgressAnimation", "Landroid/animation/ObjectAnimator;", "currentStoryIndex", "deleteStoryButton", "fontNameMap", "handler", "Landroid/os/Handler;", "leftTapArea", "Landroid/view/View;", "musicArtistText", "Landroid/widget/TextView;", "musicCoverArt", "musicInfoContainer", "Landroid/widget/LinearLayout;", "musicPlayer", "Landroid/media/MediaPlayer;", "musicTitleText", "musicUrlCache", "", "progressBar", "Landroid/widget/ProgressBar;", "progressIndicatorContainer", "progressIndicatorSegments", "rightTapArea", "storyContainer", "Landroidx/constraintlayout/widget/ConstraintLayout;", "storyImage", "storyTimestampText", "textOverlayContainer", "Landroid/widget/FrameLayout;", "textViewCache", "userAvatar", "userId", "userStories", "", "Lcom/spyro/vmeet/data/community/Story;", "usernameText", "videoView", "Landroid/widget/VideoView;", "viewCountText", "viewsContainer", "addSingleTextOverlay", "", "overlay", "Lcom/spyro/vmeet/ui/community/TextOverlayData;", "containerWidth", "containerHeight", "addTextOverlays", "overlays", "animateCurrentSegmentProgress", "duration", "cleanupTextOverlays", "createProgressIndicatorSegments", "displayCurrentStory", "displayImageStory", "story", "displayVideoStory", "dpToPx", "dp", "getTimeAgo", "timestamp", "isValidUrl", "", "url", "loadUserStories", "initialStory", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onPause", "onStop", "playMusic", "releaseMusic", "showDeleteConfirmationDialog", "showNextStory", "showPreviousStory", "showStoryViewers", "stopMusic", "updateProgressIndicatorSegments", "updateSegmentProgress", "segmentView", "progress", "", "updateViewsCount", "Companion", "app_release"})
public final class StoryViewerActivity extends com.spyro.vmeet.activity.BaseActivity {
    private android.widget.ImageView storyImage;
    private android.widget.VideoView videoView;
    private android.widget.ImageView closeButton;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView usernameText;
    private android.widget.LinearLayout progressIndicatorContainer;
    private androidx.constraintlayout.widget.ConstraintLayout storyContainer;
    private android.widget.ImageView userAvatar;
    private android.view.View leftTapArea;
    private android.view.View rightTapArea;
    private android.widget.TextView viewCountText;
    private android.view.View viewsContainer;
    private android.widget.ImageView deleteStoryButton;
    private android.widget.TextView storyTimestampText;
    private android.widget.FrameLayout textOverlayContainer;
    private android.widget.LinearLayout musicInfoContainer;
    private android.widget.ImageView musicCoverArt;
    private android.widget.TextView musicTitleText;
    private android.widget.TextView musicArtistText;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer musicPlayer;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.String> musicUrlCache = null;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.data.community.Story> userStories;
    private int currentStoryIndex = 0;
    private int userId = -1;
    private final long AUTO_CLOSE_DELAY_MS = 7000L;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler handler = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Runnable closeRunnable = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private java.util.List<android.view.View> progressIndicatorSegments;
    @org.jetbrains.annotations.Nullable()
    private android.animation.ObjectAnimator currentProgressAnimation;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.Integer> fontNameMap = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.Integer> animationNameMap = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<android.view.animation.Animation> activeAnimations = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, android.widget.TextView> textViewCache = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_STORY = "extra_story";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_USER_ID = "extra_user_id";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "StoryViewerActivity";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.community.StoryViewerActivity.Companion Companion = null;
    
    public StoryViewerActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    @java.lang.Override()
    protected void onStop() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    private final void playMusic(com.spyro.vmeet.data.community.Story story) {
    }
    
    private final boolean isValidUrl(java.lang.String url) {
        return false;
    }
    
    private final void stopMusic() {
    }
    
    private final void releaseMusic() {
    }
    
    private final void loadUserStories(com.spyro.vmeet.data.community.Story initialStory) {
    }
    
    private final void createProgressIndicatorSegments() {
    }
    
    private final int dpToPx(int dp) {
        return 0;
    }
    
    private final void cleanupTextOverlays() {
    }
    
    private final void displayCurrentStory() {
    }
    
    private final void addTextOverlays(java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> overlays) {
    }
    
    private final void addSingleTextOverlay(com.spyro.vmeet.ui.community.TextOverlayData overlay, int containerWidth, int containerHeight) {
    }
    
    private final void updateProgressIndicatorSegments() {
    }
    
    private final void updateSegmentProgress(android.view.View segmentView, float progress) {
    }
    
    private final void animateCurrentSegmentProgress(long duration) {
    }
    
    private final void displayImageStory(com.spyro.vmeet.data.community.Story story) {
    }
    
    private final void displayVideoStory(com.spyro.vmeet.data.community.Story story) {
    }
    
    private final void showNextStory() {
    }
    
    private final void showPreviousStory() {
    }
    
    private final void updateViewsCount(com.spyro.vmeet.data.community.Story story) {
    }
    
    private final void showStoryViewers() {
    }
    
    private final void showDeleteConfirmationDialog(com.spyro.vmeet.data.community.Story story) {
    }
    
    private final java.lang.String getTimeAgo(long timestamp) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/spyro/vmeet/ui/community/StoryViewerActivity$Companion;", "", "()V", "EXTRA_STORY", "", "EXTRA_USER_ID", "TAG", "newIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "story", "Lcom/spyro/vmeet/data/community/Story;", "userId", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent newIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.community.Story story, int userId) {
            return null;
        }
    }
}