  
targetContext android.app.Instrumentation  packageName android.content.Context  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  
AndroidJUnit4 com.spyro.vmeet  ExampleInstrumentedTest com.spyro.vmeet  InstrumentationRegistry com.spyro.vmeet  RunWith com.spyro.vmeet  Test com.spyro.vmeet  assertEquals com.spyro.vmeet  InstrumentationRegistry 'com.spyro.vmeet.ExampleInstrumentedTest  assertEquals 'com.spyro.vmeet.ExampleInstrumentedTest  
AndroidJUnit4 	org.junit  InstrumentationRegistry 	org.junit  RunWith 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         