package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\b\b\u0007\u0018\u0000 22\u00020\u0001:\u00012B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u001a\u0010\u001c\u001a\u00020\u00192\u0006\u0010\u001d\u001a\u00020\u001b2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0004H\u0002J\u0010\u0010\u001f\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0010\u0010 \u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0010\u0010!\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0012\u0010\"\u001a\u00020\u00192\b\u0010#\u001a\u0004\u0018\u00010$H\u0014J\b\u0010%\u001a\u00020\u0019H\u0014J\u0010\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020)H\u0016J\u0010\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020\u0004H\u0002J\b\u0010-\u001a\u00020\u0019H\u0002J\u001a\u0010.\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\b\u0010/\u001a\u0004\u0018\u00010\u0004H\u0002J\u0010\u00100\u001a\u00020\u00192\u0006\u00101\u001a\u00020\u0004H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/spyro/vmeet/activity/UserProfileActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "TAG", "buttonSendMessage", "Lcom/google/android/material/button/MaterialButton;", "imageViewUserAvatar", "Landroid/widget/ImageView;", "postAdapter", "Lcom/spyro/vmeet/ui/community/PostAdapter;", "progressBar", "Landroid/widget/ProgressBar;", "progressBarUserLevel", "recyclerViewUserPosts", "Landroidx/recyclerview/widget/RecyclerView;", "textViewAdminBadge", "Landroid/widget/TextView;", "textViewUserInfo", "textViewUserLevel", "textViewUserXP", "textViewUsername", "textViewVerifiedBadge", "checkVerificationStatus", "", "userId", "", "createDirectChat", "otherUserId", "otherUsername", "loadUserLevelAndXP", "loadUserPosts", "loadUserProfile", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onOptionsItemSelected", "", "item", "Landroid/view/MenuItem;", "parseTimestamp", "", "dateString", "setupRecyclerView", "setupSendMessageButton", "username", "showError", "message", "Companion", "app_release"})
public final class UserProfileActivity extends androidx.appcompat.app.AppCompatActivity {
    private android.widget.ImageView imageViewUserAvatar;
    private android.widget.TextView textViewUsername;
    private android.widget.TextView textViewUserInfo;
    private android.widget.TextView textViewAdminBadge;
    private android.widget.TextView textViewVerifiedBadge;
    private androidx.recyclerview.widget.RecyclerView recyclerViewUserPosts;
    private android.widget.ProgressBar progressBar;
    private com.spyro.vmeet.ui.community.PostAdapter postAdapter;
    private com.google.android.material.button.MaterialButton buttonSendMessage;
    private android.widget.TextView textViewUserLevel;
    private android.widget.ProgressBar progressBarUserLevel;
    private android.widget.TextView textViewUserXP;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "UserProfileActivity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_USER_ID = "extra_user_id";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_USERNAME = "extra_username";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.UserProfileActivity.Companion Companion = null;
    
    public UserProfileActivity() {
        super();
    }
    
    private final void loadUserLevelAndXP(int userId) {
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void loadUserProfile(int userId) {
    }
    
    private final void loadUserPosts(int userId) {
    }
    
    private final long parseTimestamp(java.lang.String dateString) {
        return 0L;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void createDirectChat(int otherUserId, java.lang.String otherUsername) {
    }
    
    @java.lang.Override()
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull()
    android.view.MenuItem item) {
        return false;
    }
    
    private final void setupSendMessageButton(int userId, java.lang.String username) {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    private final void checkVerificationStatus(int userId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/spyro/vmeet/activity/UserProfileActivity$Companion;", "", "()V", "EXTRA_USERNAME", "", "EXTRA_USER_ID", "newIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "userId", "", "username", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent newIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, int userId, @org.jetbrains.annotations.Nullable()
        java.lang.String username) {
            return null;
        }
    }
}