[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_radar_header_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\radar_header_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_tab_unselected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\tab_unselected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_chats.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_chats.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_online_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\online_status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_chat_bubble.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_chat_bubble.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_stop_mic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_stop_mic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_menu_community_dropdown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\menu_community_dropdown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_profile_image_slider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_profile_image_slider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_audiotrack.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_audiotrack.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_emoji_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_emoji_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_comments.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_comments.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_gif_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_gif_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_story_ring_unseen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\story_ring_unseen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_color_palette_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_color_palette_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_gradient_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\gradient_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_video_feed.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_video_feed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_search_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\search_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_emoji.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_emoji.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_send_comment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_send_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_user_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_user_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_mark_read.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_mark_read.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_trait_selected_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\trait_selected_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_swipe_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\swipe_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_default_avatar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_default_avatar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_knightwarriorw16n8.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\knightwarriorw16n8.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_default_room_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\default_room_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_message_buzz_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_message_buzz_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_text_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\text_fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_admin_panel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_admin_panel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_tab_dot_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\tab_dot_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_like_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_like_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_circle_online_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\circle_online_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_tutorial_page.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\tutorial_page.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_blind_date_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_blind_date_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_fragment_blind_date.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\fragment_blind_date.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_my_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_my_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_gradient_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\gradient_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_document_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_document_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_greatsracingfreeforpersonalitalicbll.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\greatsracingfreeforpersonalitalicbll.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_edittext_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_edittext_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_fragment_chat_rooms.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\fragment_chat_rooms.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_modern_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\modern_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_room_chat_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\room_chat_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_menu_room_admin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\menu_room_admin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_admin_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_admin_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_tab_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\tab_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_change_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_change_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_badge_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\badge_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_circular_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\circular_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_reaction_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\reaction_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_story_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_story_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_reaction_background_own.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\reaction_background_own.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_distance_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\distance_badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_chat_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\chat_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_message_options.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_message_options.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_voice_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_voice_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_join_room_prompt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_join_room_prompt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_fragment_profiles.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\fragment_profiles.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_emoji.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_emoji.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_circle_dot.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\circle_dot.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_play.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_play.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_unread_counter_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\unread_counter_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_story_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_story_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_app_font.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\app_font.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_chat_room.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_chat_room.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_admin_crown.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_admin_crown.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\raw_welcome.mp3.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\raw\\welcome.mp3"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_match.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_match.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_wowdinog33vp.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\wowdinog33vp.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_cyberpunk_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\cyberpunk_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_reply_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\reply_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_message_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\message_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_story_ring_unseen_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\story_ring_unseen_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_edit_text.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_edit_text.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_blind_date_reveal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_blind_date_reveal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_dark_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_dark_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_interitalic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\interitalic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_add_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_add_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_location_permission.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_location_permission.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_fragment_radar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\fragment_radar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_forgot_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_forgot_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_chat_room.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_chat_room.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_glowing_neon_ring.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\glowing_neon_ring.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_person_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_person_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_button_rounded_accent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\button_rounded_accent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_unread_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\unread_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_button_cyberpunk.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\button_cyberpunk.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_comment_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\comment_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_indicator_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\indicator_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_sent_gif_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_sent_gif_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_ban_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_ban_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_music_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_music_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_check_outlined.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_check_outlined.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_color_square_shape.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\color_square_shape.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_vibration.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_vibration.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_blind_date_chat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_blind_date_chat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_edittext_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_edittext_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_profile_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\profile_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_delete_room_confirmation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_delete_room_confirmation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_radar_filters.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_radar_filters.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_placeholder_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_placeholder_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_blind_date_result.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_blind_date_result.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_veniteadoremusstraightyzo6v.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\veniteadoremusstraightyzo6v.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_community.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_community.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_upload_video.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_upload_video.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_reject_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_reject_verification.xml"}, {"merged": "com.spyro.vmeet.app-debug-79:/layout_activity_verification_management.xml.flat", "source": "com.spyro.vmeet.app-main-81:/layout/activity_verification_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_button_outline_cyberpunk.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\button_outline_cyberpunk.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_notification_vmeet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_notification_vmeet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_email_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_email_verification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_preview_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\preview_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_buzz_earthquake.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\buzz_earthquake.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_user_muted.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_user_muted.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_default_avatar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\default_avatar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_comment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_comment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_neon_button_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\neon_button_red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_mute_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_mute_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_buzz_flash.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\buzz_flash.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_reply.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_reply.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_matches.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_matches.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_add_post.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_add_post.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_recording_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\recording_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_main_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_main_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_badge_admin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\badge_admin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_steelarj9vnj.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\steelarj9vnj.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_modern_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\modern_edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_edit_personality_traits.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_edit_personality_traits.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_story_editor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_story_editor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_gif_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_gif_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_status_pending.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\status_pending.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_comments_bottom_sheet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_comments_bottom_sheet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_buzz_pulse_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\buzz_pulse_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_change_email.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_change_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_buzz_shake.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\buzz_shake.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_supremespikekvo8d.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\supremespikekvo8d.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_music_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_music_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_blocked_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_blocked_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_buzz_intense_shake.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\buzz_intense_shake.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_eye.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_eye.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_verification_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_verification_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_my_videos.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_my_videos.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_received_gif_message.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_received_gif_message.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_verification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_recording_cancel_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\recording_cancel_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_arrow_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_arrow_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_status_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\status_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_post.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_post.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dropdown_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dropdown_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_pause.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_pause.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_progress_bar_cyberpunk.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\progress_bar_cyberpunk.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_rules_acceptance.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_rules_acceptance.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_blocked_users.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_blocked_users.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_gradient_1.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_gradient_1.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_improved_emoji_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_improved_emoji_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_menu_swipe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\menu_swipe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_verification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_verification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_name_gradient_3.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_name_gradient_3.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_report_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_report_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_music_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_music_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_neon_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\neon_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_user_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_user_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_play_arrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_play_arrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_status_read.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\status_read.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_swipe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_swipe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_rotate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\rotate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_create_post.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_create_post.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_youtube_play_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\youtube_play_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_story.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_story.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_image_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_image_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_reaction_selector_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\reaction_selector_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_image_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\image_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_online_status_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\online_status_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_voice_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_voice_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_name_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_name_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_circle_button_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\circle_button_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_neon_button_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\neon_button_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bottom_sheet_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bottom_sheet_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_reports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_reports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_gradient_3.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_gradient_3.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_room_loader.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_room_loader.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_add_gif.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_add_gif.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_button_outline_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_button_outline_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_image_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\image_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_heart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_heart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_tab_dot_default.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\tab_dot_default.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_radar_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_radar_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_profile_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_profile_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_chat_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_chat_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_gradient_bottom_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\gradient_bottom_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_comment_outline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_comment_outline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_community_host.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_community_host.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_avatar_circle_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\avatar_circle_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_gif.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_gif.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_community_guidelines.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_community_guidelines.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_voice_note_player_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\voice_note_player_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_profile_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_profile_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_message_sent_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\message_sent_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_cyberpunk_circle_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\cyberpunk_circle_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_user_management.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_user_management.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_gif.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_gif.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_inter.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\inter.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_add_mic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_add_mic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_gif_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_gif_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_chat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_chat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_modern_button_alt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\modern_button_alt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_story_ring_seen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\story_ring_seen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_buzz_message_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_buzz_message_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_blind_date_topic.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_blind_date_topic.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_mute.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_mute.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_blind_date_message_sent_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_blind_date_message_sent_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_menu_location_options.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\menu_location_options.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_password_reset.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_password_reset.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_post_viewers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_post_viewers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_change_username.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_change_username.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_blind_date_chat_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_blind_date_chat_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_reaction_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_reaction_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_section_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\section_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_create_room.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_create_room.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_document_type_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_document_type_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_global_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_global_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_post_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\post_edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_image_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_image_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_personality_trait.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_personality_trait.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_radar_user.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_radar_user.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_text_rotate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\text_rotate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_message_received_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\message_received_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_blind_date.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_blind_date.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_custom_color_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_custom_color_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_text_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\text_slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_trait_unselected_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\trait_unselected_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_mic_button_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\mic_button_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_topic_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_topic_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_background_reply_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\background_reply_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_profile_dropdown_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\profile_dropdown_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_reaction_container_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\reaction_container_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_veniteadoremusrgrba.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\veniteadoremusrgrba.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_visibility.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_visibility.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_grid_emoji_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\grid_emoji_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_glowing_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\glowing_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_tutorial.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_tutorial.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_gradient_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_gradient_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_blind_date_message_received_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_blind_date_message_received_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_neon_button_green.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\neon_button_green.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_buzz_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_buzz_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_message_buzz_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_message_buzz_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_name_gradient_1.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_name_gradient_1.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_login_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\login_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_more_vert.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_more_vert.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_orbitron.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\orbitron.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_neon_button_alt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\neon_button_alt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_mention_suggestion.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_mention_suggestion.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable-v26_ic_notification_vmeet.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable-v26\\ic_notification_vmeet.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_circular_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\circular_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_neon_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\neon_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_post_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_post_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_chat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_chat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_message_reaction.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_message_reaction.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\font_combackhomeregularjemd9.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\font\\combackhomeregularjemd9.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_matches.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_matches.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_menu_match_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\menu_match_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_image_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_image_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_chat_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_chat_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_color_preview_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\color_preview_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_selected_color_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\selected_color_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_close_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_close_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_add_text_with_style.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_add_text_with_style.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_fragment_community.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\fragment_community.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_color_swatch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\color_swatch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_tab_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\tab_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_pulse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\pulse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_edit_room.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_edit_room.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_send.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_send.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_blind_date_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_blind_date_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\menu_menu_post_options.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\menu\\menu_post_options.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_dialog_story_viewers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\dialog_story_viewers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_report.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_report.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_gradient_2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_gradient_2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_bg_message_received.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\bg_message_received.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_status_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\status_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_fragment_private_chats.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\fragment_private_chats.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_name_gradient_2.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_name_gradient_2.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_settings_compat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_settings_compat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_activity_full_screen_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\activity_full_screen_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_background_reply_sent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\background_reply_sent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_item_personality_trait_category.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\item_personality_trait_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_admin_name_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\admin_name_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_rounded_corner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\rounded_corner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\layout_layout_blind_date_waiting.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\layout\\layout_blind_date_waiting.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-debug-79:\\drawable_ic_swipe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.spyro.vmeet.app-main-81:\\drawable\\ic_swipe.xml"}]