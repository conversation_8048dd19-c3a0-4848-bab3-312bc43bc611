<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimaryDark">

    <!-- Modern header with gradient background -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#1A1A3A"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/textViewChatTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:text="Chat Anónimo"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textViewChatTopic"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="16dp"
                android:textColor="@color/comfy_blue"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@id/buttonLeaveChat"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewChatTitle"
                tools:text="Tema: Videojuegos" />

            <TextView
                android:id="@+id/textViewChatTimer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/rounded_corner_background"
                android:paddingStart="12dp"
                android:paddingTop="4dp"
                android:paddingEnd="12dp"
                android:paddingBottom="4dp"
                android:textColor="@color/white"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewChatTopic"
                tools:text="Tiempo restante: 2:30" />

            <Button
                android:id="@+id/buttonLeaveChat"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:background="@drawable/neon_button_red"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:text="Abandonar"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <!-- Chat messages with modern styling -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewChat"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        app:layout_constraintBottom_toTopOf="@id/cardViewChatInput"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardViewHeader"
        tools:listitem="@layout/item_blind_date_message_received_modern" />

    <!-- Modern input area with card view -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewChatInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#1A1A3A"
        app:cardElevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewEditText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                app:cardBackgroundColor="#2A2A4A"
                app:cardCornerRadius="24dp"
                app:cardElevation="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/buttonSendMessage"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <EditText
                    android:id="@+id/editTextChatMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:hint="Escribe un mensaje..."
                    android:inputType="textMultiLine"
                    android:maxLines="4"
                    android:padding="12dp"
                    android:textColor="@color/white"
                    android:textColorHint="#AAAAAA"
                    android:textSize="16sp" />
            </androidx.cardview.widget.CardView>

            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/buttonSendMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:contentDescription="Enviar mensaje"
                android:src="@drawable/ic_send"
                app:backgroundTint="@color/comfy_blue"
                app:fabSize="mini"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <!-- Typing indicator -->
    <TextView
        android:id="@+id/textViewTypingIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="4dp"
        android:text="Tu pareja está escribiendo..."
        android:textColor="#AAAAAA"
        android:textSize="12sp"
        android:textStyle="italic"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/cardViewChatInput"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
