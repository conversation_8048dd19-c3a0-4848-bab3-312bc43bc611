<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingTop="4dp"
    android:paddingEnd="16dp"
    android:paddingBottom="4dp">

    <!-- Sender name with modern styling -->
    <TextView
        android:id="@+id/textViewSender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="2dp"
        android:textColor="#E0E0E0"
        android:textSize="12sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/cardViewMessage"
        app:layout_constraintStart_toStartOf="@id/cardViewMessage"
        tools:text="Jugador A" />

    <!-- Time stamp for message -->
    <TextView
        android:id="@+id/textViewTimestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginBottom="2dp"
        android:textColor="#AAAAAA"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/cardViewMessage"
        app:layout_constraintStart_toEndOf="@id/textViewSender"
        tools:text="12:34"
        tools:visibility="visible" />

    <!-- Modern message bubble with gradient background -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="80dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="18dp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewSender">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_message_received"
            android:padding="12dp">

            <TextView
                android:id="@+id/textViewMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="240dp"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Este es un mensaje recibido de otra persona con un diseño más moderno y atractivo" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
