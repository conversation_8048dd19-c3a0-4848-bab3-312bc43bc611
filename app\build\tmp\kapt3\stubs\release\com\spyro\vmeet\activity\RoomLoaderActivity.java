package com.spyro.vmeet.activity;

/**
 * Lightweight loader activity that safely prepares data before launching the ChatRoomActivity.
 * This helps avoid activity transition issues that can lead to DeadObjectException.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u0000 \u00132\u00020\u0001:\u0001\u0013B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0002J\b\u0010\u000b\u001a\u00020\bH\u0002J\b\u0010\f\u001a\u00020\bH\u0002J\u0012\u0010\r\u001a\u00020\b2\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0014J\u0010\u0010\u0010\u001a\u00020\b2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/spyro/vmeet/activity/RoomLoaderActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "progressBar", "Landroid/widget/ProgressBar;", "textViewLoading", "Landroid/widget/TextView;", "delayedFinish", "", "delayMs", "", "ensureEmojiCompatInitialized", "launchChatRoomActivity", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "showError", "message", "", "Companion", "app_release"})
public final class RoomLoaderActivity extends androidx.appcompat.app.AppCompatActivity {
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewLoading;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "RoomLoaderActivity";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.RoomLoaderActivity.Companion Companion = null;
    
    public RoomLoaderActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void ensureEmojiCompatInitialized() {
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void delayedFinish(long delayMs) {
    }
    
    private final void launchChatRoomActivity() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/activity/RoomLoaderActivity$Companion;", "", "()V", "TAG", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}