<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cyberpunk_background"
    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_marginTop="@dimen/status_bar_height"
        android:background="#1A1A3A"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Benefits Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cyberpunk_card_background"
                app:cardCornerRadius="12dp"
                app:strokeColor="@color/neon_blue"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🏆 Beneficios de la Verificación"
                        android:textColor="@color/neon_blue"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/textBenefits"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Aumenta tus posibilidades de match\n• Genera mayor confianza en otros usuarios\n• Badge de usuario verificado en tu perfil\n• Perfil destacado en búsquedas\n• Mayor credibilidad en la comunidad\n\n📋 Documentos aceptados:\n• DNI/Cédula de identidad\n• Pasaporte\n• Carnet de conducir\n• Tarjeta de residencia\n• Credencial para votar"
                        android:textColor="@color/cyberpunk_text_primary"
                        android:textSize="14sp"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Instructions Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@color/cyberpunk_card_background"
                app:cardCornerRadius="12dp"
                app:strokeColor="@color/neon_pink"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📋 Instrucciones"
                        android:textColor="@color/neon_pink"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/textInstructions"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="1. Toma una foto clara de tu documento de identidad (lado frontal):\n   • DNI/Cédula de identidad\n   • Pasaporte\n   • Carnet de conducir\n   • Tarjeta de residencia\n   • Credencial para votar\n\n2. Toma un selfie sosteniendo tu documento junto a tu cara\n3. Asegúrate de que ambas fotos sean nítidas y legibles\n4. El proceso de verificación puede tomar 24-48 horas"
                        android:textColor="@color/cyberpunk_text_primary"
                        android:textSize="14sp"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Document Type Selection -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cyberpunk_card_background"
                app:cardCornerRadius="12dp"
                app:strokeColor="@color/neon_green"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📋 Tipo de Documento"
                        android:textColor="@color/neon_green"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <Spinner
                        android:id="@+id/spinnerDocumentType"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/spinner_background"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Selecciona el tipo de documento que vas a fotografiar"
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- DNI Photo Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardDniPhoto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/cyberpunk_card_background"
                app:cardCornerRadius="12dp"
                app:strokeColor="@color/cyberpunk_accent_secondary"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📄 Foto del Documento"
                        android:textColor="@color/cyberpunk_accent_secondary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <ImageView
                        android:id="@+id/imageDni"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/cyberpunk_background"
                        android:scaleType="centerCrop"
                        android:visibility="gone"
                        tools:visibility="visible"
                        tools:src="@tools:sample/backgrounds/scenic" />

                    <TextView
                        android:id="@+id/textDniStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📷 Toca el botón para tomar la foto"
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:textSize="14sp"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonTakeDni"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Tomar Foto del Documento"
                        android:textColor="@color/white"
                        app:backgroundTint="@color/cyberpunk_accent_secondary"
                        app:icon="@drawable/ic_add_image"
                        app:iconGravity="textStart" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Selfie Photo Section -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardSelfiePhoto"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardBackgroundColor="@color/cyberpunk_card_background"
                app:cardCornerRadius="12dp"
                app:strokeColor="@color/neon_blue"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🤳 Selfie con Documento"
                        android:textColor="@color/neon_blue"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <ImageView
                        android:id="@+id/imageSelfie"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="8dp"
                        android:background="@color/cyberpunk_background"
                        android:scaleType="centerCrop"
                        android:visibility="gone"
                        tools:visibility="visible"
                        tools:src="@tools:sample/backgrounds/scenic" />

                    <TextView
                        android:id="@+id/textSelfieStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📷 Toca el botón para tomar el selfie"
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:textSize="14sp"
                        android:layout_marginBottom="12dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonTakeSelfie"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Tomar Selfie con Documento"
                        android:textColor="@color/white"
                        app:backgroundTint="@color/neon_blue"
                        app:icon="@drawable/ic_add_image"
                        app:iconGravity="textStart" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Submit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSubmit"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Enviar Solicitud de Verificación"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@color/neon_pink"
                app:cornerRadius="12dp"
                app:icon="@drawable/ic_check"
                app:iconGravity="textStart" />

        </LinearLayout>

    </ScrollView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
