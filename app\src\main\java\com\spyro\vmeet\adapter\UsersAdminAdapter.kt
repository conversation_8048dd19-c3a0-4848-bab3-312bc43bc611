package com.spyro.vmeet.adapter

import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.spyro.vmeet.R
import de.hdodenhof.circleimageview.CircleImageView
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

class UsersAdminAdapter(
    private val users: List<JSONObject>,
    private val listener: UserAdminListener
) : RecyclerView.Adapter<UsersAdminAdapter.UserViewHolder>() {

    interface UserAdminListener {
        fun onBanUser(userObject: JSONObject)
        fun onUnbanUser(userObject: JSONObject)
        fun onViewHistory(userObject: JSONObject)
        fun onMuteUser(userObject: J<PERSON>NObject)
        fun onUnmuteUser(userObject: JSONObject)
        fun onVerifyUser(userObject: JSONObject)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_admin_user, parent, false)
        return UserViewHolder(view)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        val userObject = users[position]
        holder.bind(userObject, listener)
    }

    override fun getItemCount(): Int = users.size

    class UserViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imgAvatar: CircleImageView = itemView.findViewById(R.id.imageViewAvatar)
        private val tvUsername: TextView = itemView.findViewById(R.id.tvUsername)
        private val tvEmail: TextView = itemView.findViewById(R.id.tvEmail)
        private val tvRole: TextView = itemView.findViewById(R.id.tvRole)
        private val tvStatus: TextView = itemView.findViewById(R.id.tvStatus)
        private val tvLastIP: TextView = itemView.findViewById(R.id.tvLastIP)
        private val tvDeviceInfo: TextView = itemView.findViewById(R.id.tvDeviceInfo)
        private val tvIpHistory: TextView = itemView.findViewById(R.id.tvIpHistory)
        private val banInfoLayout: LinearLayout = itemView.findViewById(R.id.banInfoLayout)
        private val tvBanReason: TextView = itemView.findViewById(R.id.tvBanReason)
        private val tvBanExpiration: TextView = itemView.findViewById(R.id.tvBanExpiration)
        private val btnBan: Button = itemView.findViewById(R.id.btnBan)
        private val btnUnban: Button = itemView.findViewById(R.id.btnUnban)
        private val btnHistory: Button = itemView.findViewById(R.id.btnHistory)
        private val btnMute: Button = itemView.findViewById(R.id.btnMute)
        private val btnUnmute: Button = itemView.findViewById(R.id.btnUnmute)
        private val btnVerify: Button = itemView.findViewById(R.id.btnVerify)
        private val muteInfoLayout: LinearLayout = itemView.findViewById(R.id.muteInfoLayout)
        private val tvMuteExpiration: TextView = itemView.findViewById(R.id.tvMuteExpiration)
        private val tvDeviceHistoryTitle: TextView = itemView.findViewById(R.id.tvDeviceHistoryTitle)
        private val deviceHistoryContainer: LinearLayout = itemView.findViewById(R.id.deviceHistoryContainer)
        private val API_URL = "http://77.110.116.89:3000"

        fun bind(userObject: JSONObject, listener: UserAdminListener) {
            try {
                // Basic user info
                tvUsername.text = userObject.getString("username")
                tvEmail.text = if (userObject.has("email")) userObject.getString("email") else "Sin email"

                // User role and styling
                val role = if (userObject.has("role")) userObject.getString("role") else "usuario"
                val isAdmin = role == "admin"

                tvRole.text = when (role) {
                    "admin" -> "Administrador"
                    "moderator" -> "Moderador"
                    else -> "Usuario"
                }

                if (isAdmin) {
                    tvRole.setBackgroundResource(R.drawable.badge_admin)
                    tvRole.setTextColor(itemView.context.getColor(android.R.color.white))
                    tvUsername.setTextColor(itemView.context.getColor(android.R.color.holo_red_light))
                } else {
                    tvRole.setBackgroundResource(R.drawable.badge_user)
                    tvRole.setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                    tvUsername.setTextColor(itemView.context.getColor(android.R.color.black))
                }

                // Connection status and last seen
                if (userObject.has("last_seen") && !userObject.isNull("last_seen")) {
                    val lastSeen = userObject.getString("last_seen")
                    try {
                        val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                        val date = inputFormat.parse(lastSeen)
                        val outputFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                        val formattedDate = outputFormat.format(date!!)
                        tvStatus.text = "Último acceso: $formattedDate"
                        tvStatus.setTextColor(itemView.context.getColor(android.R.color.holo_blue_dark))                } catch (e: Exception) {
                        tvStatus.text = "Último acceso: $lastSeen"
                        tvStatus.setTextColor(itemView.context.getColor(android.R.color.holo_blue_dark))
                    }
                } else {
                    tvStatus.text = "Sin acceso registrado"
                    tvStatus.setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                }

                // Connection Info
                val lastIp = userObject.optString("last_ip", "Desconocida")
                val deviceInfo = userObject.optString("device_info", "No disponible")
                val deviceSerial = userObject.optString("device_serial", "No disponible")

                tvLastIP.text = "IP actual: $lastIp"
                tvDeviceInfo.text = "Dispositivo: " +
                    if (deviceInfo.isNullOrEmpty() || deviceInfo == "null") {
                        if (deviceSerial.isNullOrEmpty() || deviceSerial == "null") {
                            "No disponible"
                        } else {
                            "ID: $deviceSerial"
                        }
                    } else {
                        deviceInfo
                    }

                // IP History handling
                try {
                    val ipHistory = userObject.optJSONArray("ip_history")
                    if (ipHistory != null && ipHistory.length() > 0) {
                        val ipList = StringBuilder()
                        for (i in 0 until ipHistory.length()) {
                            val ip = ipHistory.getString(i)
                            if (!ip.isNullOrEmpty() && ip != "null" && ip != "[]") {
                                ipList.append("• $ip\n")
                            }
                        }

                        if (ipList.isNotEmpty()) {
                            tvIpHistory.text = "IPs registradas:\n$ipList"
                            tvIpHistory.visibility = View.VISIBLE
                        } else {
                            // Si solo tenemos la IP actual, mostrarla como único historial
                            if (lastIp != "Desconocida" && lastIp != "null") {
                                tvIpHistory.text = "IPs registradas:\n• $lastIp"
                                tvIpHistory.visibility = View.VISIBLE
                            } else {
                                tvIpHistory.text = "Sin historial de IPs"
                                tvIpHistory.visibility = View.GONE
                            }
                        }
                    } else {
                        // If no array found, check for string format
                        val ipHistoryStr = userObject.optString("ip_history", "")
                        if (!ipHistoryStr.isNullOrEmpty() && ipHistoryStr != "null" && ipHistoryStr != "[]") {
                            val ips = ipHistoryStr.split(",")
                            val ipList = StringBuilder("IPs registradas:\n")

                            for (ip in ips) {
                                val trimmedIp = ip.trim()
                                if (trimmedIp.isNotEmpty() && trimmedIp != "null" && trimmedIp != "[]") {
                                    ipList.append("• $trimmedIp\n")
                                }
                            }

                            if (ipList.toString() != "IPs registradas:\n") {
                                tvIpHistory.text = ipList.toString()
                                tvIpHistory.visibility = View.VISIBLE
                            } else if (lastIp != "Desconocida" && lastIp != "null") {
                                tvIpHistory.text = "IPs registradas:\n• $lastIp"
                                tvIpHistory.visibility = View.VISIBLE
                            } else {
                                tvIpHistory.text = "Sin historial de IPs"
                                tvIpHistory.visibility = View.GONE
                            }
                        } else if (lastIp != "Desconocida" && lastIp != "null") {
                            tvIpHistory.text = "IPs registradas:\n• $lastIp"
                            tvIpHistory.visibility = View.VISIBLE
                        } else {
                            tvIpHistory.visibility = View.GONE
                        }
                    }
                } catch (e: Exception) {
                    Log.e("UsersAdminAdapter", "Error processing IP history", e)
                    if (lastIp != "Desconocida" && lastIp != "null") {
                        tvIpHistory.text = "IPs registradas:\n• $lastIp"
                        tvIpHistory.visibility = View.VISIBLE
                    } else {
                        tvIpHistory.visibility = View.GONE
                    }
                }

                // Device History handling
                try {
                    deviceHistoryContainer.removeAllViews()
                    val deviceHistory = userObject.optJSONArray("device_history")
                    var hasValidDevices = false

                    if (deviceHistory != null && deviceHistory.length() > 0) {
                        tvDeviceHistoryTitle.visibility = View.VISIBLE
                        deviceHistoryContainer.visibility = View.VISIBLE

                        for (i in 0 until deviceHistory.length()) {
                            val device = deviceHistory.getString(i)
                            if (!device.isNullOrEmpty() && device != "null" && device != "[]") {
                                addDeviceToHistory(device)
                                hasValidDevices = true
                            }
                        }
                    }

                    // Si no hay dispositivos en el historial, mostrar al menos el dispositivo actual
                    if (!hasValidDevices && deviceSerial != "No disponible" && deviceSerial != "null") {
                        tvDeviceHistoryTitle.visibility = View.VISIBLE
                        deviceHistoryContainer.visibility = View.VISIBLE
                        addDeviceToHistory(deviceSerial)
                        hasValidDevices = true
                    }

                    if (!hasValidDevices) {
                        tvDeviceHistoryTitle.visibility = View.GONE
                        deviceHistoryContainer.visibility = View.GONE
                    }
                } catch (e: Exception) {
                    Log.e("UsersAdminAdapter", "Error processing device history", e)
                    if (deviceSerial != "No disponible" && deviceSerial != "null") {
                        tvDeviceHistoryTitle.visibility = View.VISIBLE
                        deviceHistoryContainer.visibility = View.VISIBLE
                        addDeviceToHistory(deviceSerial)
                    } else {
                        tvDeviceHistoryTitle.visibility = View.GONE
                        deviceHistoryContainer.visibility = View.GONE
                    }
                }

                // Ban status
                val isBanned = userObject.optBoolean("is_banned", false)
                if (isBanned) {
                    banInfoLayout.visibility = View.VISIBLE
                    btnBan.visibility = View.GONE
                    btnUnban.visibility = View.VISIBLE

                    val banReason = userObject.optString("last_ban_reason", "Sin especificar")
                    tvBanReason.text = "Razón: $banReason"

                    if (userObject.has("ban_end_date") && !userObject.isNull("ban_end_date")) {
                        val banEndDate = userObject.getString("ban_end_date")
                        try {
                            val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                            val date = inputFormat.parse(banEndDate)
                            val outputFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                            val formattedDate = outputFormat.format(date!!)
                            tvBanExpiration.text = "Hasta: $formattedDate"
                        } catch (e: Exception) {
                            tvBanExpiration.text = "Hasta: $banEndDate"
                        }
                    } else {
                        tvBanExpiration.text = "Baneo permanente"
                    }
                } else {
                    banInfoLayout.visibility = View.GONE
                    btnBan.visibility = View.VISIBLE
                    btnUnban.visibility = View.GONE
                }

                // Mute status
                val isMuted = userObject.optBoolean("is_muted", false)
                if (isMuted) {
                    muteInfoLayout.visibility = View.VISIBLE
                    btnMute.visibility = View.GONE
                    btnUnmute.visibility = View.VISIBLE

                    if (userObject.has("mute_end_time") && !userObject.isNull("mute_end_time")) {
                        val muteEndTime = userObject.getString("mute_end_time")
                        try {
                            val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                            val date = inputFormat.parse(muteEndTime)
                            val outputFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                            val formattedDate = outputFormat.format(date!!)
                            tvMuteExpiration.text = "Silenciado hasta: $formattedDate"
                        } catch (e: Exception) {
                            tvMuteExpiration.text = "Silenciado hasta: $muteEndTime"
                        }
                    } else {
                        tvMuteExpiration.text = "Silenciado (sin fecha de finalización)"
                    }
                } else {
                    muteInfoLayout.visibility = View.GONE
                    btnMute.visibility = View.VISIBLE
                    btnUnmute.visibility = View.GONE
                }

                // Set up button click listeners
                btnBan.setOnClickListener {
                    listener.onBanUser(userObject)
                }

                btnUnban.setOnClickListener {
                    listener.onUnbanUser(userObject)
                }

                btnHistory.setOnClickListener {
                    listener.onViewHistory(userObject)
                }

                btnMute.setOnClickListener {
                    listener.onMuteUser(userObject)
                }

                btnUnmute.setOnClickListener {
                    listener.onUnmuteUser(userObject)
                }

                btnVerify.setOnClickListener {
                    listener.onVerifyUser(userObject)
                }

                // Load user avatar
                loadUserAvatar(userObject)

            } catch (e: Exception) {
                Log.e("UsersAdminAdapter", "Error binding user data", e)
                tvUsername.text = "Error al cargar usuario"
                tvEmail.text = "Error de datos"
                tvStatus.text = ""
                tvLastIP.text = ""
                tvDeviceInfo.text = ""
            }
        }        private fun addDeviceToHistory(deviceId: String) {
            if (deviceId.isEmpty() || deviceId == "null" || deviceId == "[]") return

            // Limpiar el formato si viene con corchetes o comillas
            val cleanDeviceId = deviceId.replace("[\\[\\]\"]".toRegex(), "").trim()
            if (cleanDeviceId.isEmpty()) return

            val deviceText = TextView(itemView.context).apply {
                text = "• $cleanDeviceId"
                textSize = 14f
                setTextColor(itemView.context.getColor(android.R.color.darker_gray))
                setPadding(0, 4, 0, 4)
            }
            deviceHistoryContainer.addView(deviceText)
        }

        private fun loadUserAvatar(userObject: JSONObject) {
            var imageUrl: String? = null

            if (userObject.has("profile_images") && !userObject.isNull("profile_images")) {
                try {
                    val profileImages = userObject.getJSONArray("profile_images")
                    for (i in 0 until profileImages.length()) {
                        val image = profileImages.getJSONObject(i)
                        if (image.optBoolean("isMain", false)) {
                            imageUrl = image.getString("url")
                            break
                        }
                    }

                    if (imageUrl == null && profileImages.length() > 0) {
                        imageUrl = profileImages.getJSONObject(0).getString("url")
                    }
                } catch (e: Exception) {
                }
            }

            if (imageUrl == null && userObject.has("avatar_url") && !userObject.isNull("avatar_url")) {
                val avatarUrl = userObject.getString("avatar_url")
                if (avatarUrl.isNotEmpty() && avatarUrl != "null") {
                    imageUrl = avatarUrl
                }
            }

            if (!imageUrl.isNullOrEmpty()) {
                val fullUrl = if (imageUrl.startsWith("http")) {
                    imageUrl
                } else {
                    "$API_URL$imageUrl"
                }

                Glide.with(itemView.context)
                    .load(fullUrl)
                    .placeholder(R.drawable.default_avatar)
                    .error(R.drawable.default_avatar)
                    .into(imgAvatar)
            } else {
                Glide.with(itemView.context)
                    .load(R.drawable.default_avatar)
                    .into(imgAvatar)
            }
        }
    }
}