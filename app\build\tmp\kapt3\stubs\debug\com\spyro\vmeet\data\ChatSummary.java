package com.spyro.vmeet.data;

/**
 * Data class representing the information needed to display
 * a single chat conversation in a list.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0017\b\u0087\b\u0018\u00002\u00020\u0001BE\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0006\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0006\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u000bH\u00c6\u0003JU\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00062\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u000b2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001J\t\u0010!\u001a\u00020\u0006H\u00d6\u0001R\u0016\u0010\n\u001a\u00020\u000b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\rR\u0018\u0010\b\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0018\u0010\t\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0018\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u000fR\u0016\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0016\u0010\u0005\u001a\u00020\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000f\u00a8\u0006\""}, d2 = {"Lcom/spyro/vmeet/data/ChatSummary;", "", "matchId", "", "otherUserId", "otherUserName", "", "otherUserAvatar", "lastMessageText", "lastMessageTimestamp", "isLastMessageFromCurrentUser", "", "(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V", "()Z", "getLastMessageText", "()Ljava/lang/String;", "getLastMessageTimestamp", "getMatchId", "()I", "getOtherUserAvatar", "getOtherUserId", "getOtherUserName", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
public final class ChatSummary {
    @com.google.gson.annotations.SerializedName(value = "match_id")
    private final int matchId = 0;
    @com.google.gson.annotations.SerializedName(value = "other_user_id")
    private final int otherUserId = 0;
    @com.google.gson.annotations.SerializedName(value = "other_user_name")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String otherUserName = null;
    @com.google.gson.annotations.SerializedName(value = "other_user_avatar")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String otherUserAvatar = null;
    @com.google.gson.annotations.SerializedName(value = "last_message_text")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String lastMessageText = null;
    @com.google.gson.annotations.SerializedName(value = "last_message_timestamp")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String lastMessageTimestamp = null;
    @com.google.gson.annotations.SerializedName(value = "is_last_message_from_current_user")
    private final boolean isLastMessageFromCurrentUser = false;
    
    public ChatSummary(int matchId, int otherUserId, @org.jetbrains.annotations.NotNull()
    java.lang.String otherUserName, @org.jetbrains.annotations.Nullable()
    java.lang.String otherUserAvatar, @org.jetbrains.annotations.Nullable()
    java.lang.String lastMessageText, @org.jetbrains.annotations.Nullable()
    java.lang.String lastMessageTimestamp, boolean isLastMessageFromCurrentUser) {
        super();
    }
    
    public final int getMatchId() {
        return 0;
    }
    
    public final int getOtherUserId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOtherUserName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOtherUserAvatar() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLastMessageText() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLastMessageTimestamp() {
        return null;
    }
    
    public final boolean isLastMessageFromCurrentUser() {
        return false;
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.spyro.vmeet.data.ChatSummary copy(int matchId, int otherUserId, @org.jetbrains.annotations.NotNull()
    java.lang.String otherUserName, @org.jetbrains.annotations.Nullable()
    java.lang.String otherUserAvatar, @org.jetbrains.annotations.Nullable()
    java.lang.String lastMessageText, @org.jetbrains.annotations.Nullable()
    java.lang.String lastMessageTimestamp, boolean isLastMessageFromCurrentUser) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}