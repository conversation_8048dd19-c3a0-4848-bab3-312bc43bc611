<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/containerTrait"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardBackgroundColor="@color/cyberpunk_card_background"
    app:cardCornerRadius="20dp"
    app:cardElevation="2dp"
    app:strokeColor="@color/cyberpunk_card_stroke"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="12dp">

        <ImageView
            android:id="@+id/imageViewTraitIcon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginEnd="8dp"
            android:contentDescription="@string/trait_icon_description"
            android:src="@android:drawable/ic_menu_info_details"
            android:tint="@color/cyberpunk_text_secondary" />

        <TextView
            android:id="@+id/textViewTraitName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter"
            android:text="Rasgo"
            android:textColor="@color/cyberpunk_text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
