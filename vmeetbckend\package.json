{"name": "vmeetbckend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "test-chat-rooms": "node test-chat-rooms.js"}, "keywords": [], "author": "", "license": "ISC", "description": "Backend API for VMeet - a dating app for gamers", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "firebase-admin": "^11.11.0", "fs-extra": "^11.3.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.1", "node-fetch": "^2.6.12", "node-geocoder": "^4.4.1", "nodemailer": "^7.0.3", "ws": "^8.18.2"}, "devDependencies": {"nodemon": "^3.0.1"}}