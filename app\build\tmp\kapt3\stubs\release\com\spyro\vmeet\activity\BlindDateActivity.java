package com.spyro.vmeet.activity;

/**
 * Activity for the blind date feature
 * Handles topic selection, waiting for match, chat, and reveal process
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b6\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u001e\b\u0007\u0018\u0000 o2\u00020\u0001:\u0003nopB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010B\u001a\u00020CH\u0002J\u0018\u0010D\u001a\u00020C2\u0006\u0010E\u001a\u00020#2\u0006\u0010F\u001a\u00020\u001bH\u0002J\b\u0010G\u001a\u00020CH\u0002J&\u0010H\u001a\u00020C2\u0006\u0010=\u001a\u00020\u001b2\u0014\u0010I\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010#\u0012\u0004\u0012\u00020C0JH\u0002J\b\u0010K\u001a\u00020CH\u0002J\u0010\u0010L\u001a\u00020C2\u0006\u0010M\u001a\u00020<H\u0002J\b\u0010N\u001a\u00020CH\u0002J\b\u0010O\u001a\u00020CH\u0002J\u0010\u0010P\u001a\u00020C2\u0006\u0010E\u001a\u00020#H\u0002J\u0012\u0010Q\u001a\u00020C2\b\u0010R\u001a\u0004\u0018\u00010SH\u0014J\b\u0010T\u001a\u00020CH\u0014J\b\u0010U\u001a\u00020CH\u0002J\b\u0010V\u001a\u00020CH\u0002J\b\u0010W\u001a\u00020CH\u0002J\u0010\u0010X\u001a\u00020C2\u0006\u0010Y\u001a\u00020#H\u0002J\b\u0010Z\u001a\u00020CH\u0002J\b\u0010[\u001a\u00020CH\u0002J\"\u0010\\\u001a\u00020C2\u0006\u0010]\u001a\u00020#2\u0006\u0010E\u001a\u00020#2\b\b\u0002\u0010^\u001a\u00020\u001bH\u0002J\u0010\u0010_\u001a\u00020C2\u0006\u0010`\u001a\u00020\u0017H\u0002J\b\u0010a\u001a\u00020CH\u0002J\b\u0010b\u001a\u00020CH\u0002J\b\u0010c\u001a\u00020CH\u0002J\b\u0010d\u001a\u00020CH\u0002J\b\u0010e\u001a\u00020CH\u0002J\b\u0010f\u001a\u00020CH\u0002J\b\u0010g\u001a\u00020CH\u0002J\b\u0010h\u001a\u00020CH\u0002J\b\u0010i\u001a\u00020CH\u0002J\b\u0010j\u001a\u00020CH\u0002J\b\u0010k\u001a\u00020CH\u0002J\b\u0010l\u001a\u00020CH\u0002J\b\u0010m\u001a\u00020CH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020 0\u001fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010!\u001a\u000e\u0012\u0004\u0012\u00020#\u0012\u0004\u0012\u00020 0\"X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u00101\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u00103\u001a\u0004\u0018\u000104X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00105\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u000207X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010:\u001a\b\u0012\u0004\u0012\u00020<0;X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010@\u001a\u0004\u0018\u00010AX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006q"}, d2 = {"Lcom/spyro/vmeet/activity/BlindDateActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "cancelWaitingButton", "Landroid/widget/Button;", "chatLayout", "Landroid/view/View;", "chatLeaveChatButton", "chatMessageAdapter", "Lcom/spyro/vmeet/adapter/BlindDateMessageAdapter;", "chatMessageInput", "Landroid/widget/EditText;", "chatRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "chatSendButton", "Landroid/widget/ImageButton;", "chatTimerText", "Landroid/widget/TextView;", "countDownTimer", "Landroid/os/CountDownTimer;", "currentState", "Lcom/spyro/vmeet/activity/BlindDateActivity$BlindDateState;", "isPollingMessages", "", "isPollingRevealResult", "isPollingSessionStatus", "lastMessageId", "", "messagePollingHandler", "Landroid/os/Handler;", "messages", "", "Lcom/spyro/vmeet/data/BlindDateMessage;", "pendingMessages", "", "", "reconnectionSnackbar", "Lcom/google/android/material/snackbar/Snackbar;", "remainingSeconds", "resultContinueButton", "resultLayout", "resultNewMatchButton", "resultText", "resultUserCard", "Lcom/google/android/material/card/MaterialCardView;", "resultUserName", "revealLayout", "revealNoButton", "revealQuestion", "revealResultPollingHandler", "revealYesButton", "session", "Lcom/spyro/vmeet/data/BlindDateSession;", "sessionStatusPollingHandler", "topicProgressBar", "Landroid/widget/ProgressBar;", "topicRecyclerView", "topicSelectionLayout", "topics", "", "Lcom/spyro/vmeet/data/BlindDateTopic;", "userId", "waitingLayout", "waitingTopicText", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "cancelWaiting", "", "confirmMessageDelivery", "clientMessageId", "serverId", "continueToChat", "fetchOtherUserAvatar", "callback", "Lkotlin/Function1;", "initializeViews", "joinQueue", "topic", "leaveChat", "loadTopics", "markMessageAsError", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "pollMessages", "pollRevealResult", "pollSessionStatus", "processWebSocketMessage", "message", "resetToTopicSelection", "sendChatMessage", "sendMessageWithRetry", "messageText", "retryCount", "sendRevealDecision", "wantsToContinue", "setupTopicAdapter", "setupWebSocket", "showLeaveChatConfirmation", "showReconnectionMessage", "startChatTimer", "startMessagePolling", "startRevealResultPolling", "startSessionStatusPolling", "stopMessagePolling", "stopRevealResultPolling", "stopSessionStatusPolling", "updateTimerDisplay", "updateUIForCurrentState", "BlindDateState", "Companion", "TopicViewHolder", "app_release"})
public final class BlindDateActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "BlindDateActivity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_URL = "77.110.116.89";
    private static final int PORT = 3000;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String WS_URL = "ws://77.110.116.89:3000";
    private static final int NORMAL_CLOSURE_STATUS = 1000;
    private static final int CHAT_DURATION_SECONDS = 150;
    private int userId = -1;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.WebSocketClient webSocketClient;
    @org.jetbrains.annotations.NotNull()
    private com.spyro.vmeet.activity.BlindDateActivity.BlindDateState currentState = com.spyro.vmeet.activity.BlindDateActivity.BlindDateState.TOPIC_SELECTION;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.data.BlindDateTopic> topics;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.BlindDateSession session;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.BlindDateMessage> messages = null;
    private int remainingSeconds = 150;
    @org.jetbrains.annotations.Nullable()
    private android.os.CountDownTimer countDownTimer;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler messagePollingHandler;
    private boolean isPollingMessages = false;
    private int lastMessageId = 0;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler sessionStatusPollingHandler;
    private boolean isPollingSessionStatus = false;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler revealResultPollingHandler;
    private boolean isPollingRevealResult = false;
    private android.view.View topicSelectionLayout;
    private androidx.recyclerview.widget.RecyclerView topicRecyclerView;
    private android.widget.ProgressBar topicProgressBar;
    private android.view.View waitingLayout;
    private android.widget.TextView waitingTopicText;
    private android.widget.Button cancelWaitingButton;
    private android.view.View chatLayout;
    private android.widget.TextView chatTimerText;
    private androidx.recyclerview.widget.RecyclerView chatRecyclerView;
    private android.widget.EditText chatMessageInput;
    private android.widget.ImageButton chatSendButton;
    private android.widget.Button chatLeaveChatButton;
    private com.spyro.vmeet.adapter.BlindDateMessageAdapter chatMessageAdapter;
    private android.view.View revealLayout;
    private android.widget.TextView revealQuestion;
    private android.widget.Button revealYesButton;
    private android.widget.Button revealNoButton;
    private android.view.View resultLayout;
    private android.widget.TextView resultText;
    private com.google.android.material.card.MaterialCardView resultUserCard;
    private android.widget.TextView resultUserName;
    private android.widget.Button resultContinueButton;
    private android.widget.Button resultNewMatchButton;
    @org.jetbrains.annotations.Nullable()
    private com.google.android.material.snackbar.Snackbar reconnectionSnackbar;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.spyro.vmeet.data.BlindDateMessage> pendingMessages = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.BlindDateActivity.Companion Companion = null;
    
    public BlindDateActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeViews() {
    }
    
    private final void setupWebSocket() {
    }
    
    private final void showReconnectionMessage() {
    }
    
    private final void loadTopics() {
    }
    
    private final void setupTopicAdapter() {
    }
    
    private final void joinQueue(com.spyro.vmeet.data.BlindDateTopic topic) {
    }
    
    private final void cancelWaiting() {
    }
    
    private final void sendChatMessage() {
    }
    
    private final void sendMessageWithRetry(java.lang.String messageText, java.lang.String clientMessageId, int retryCount) {
    }
    
    private final void markMessageAsError(java.lang.String clientMessageId) {
    }
    
    private final void confirmMessageDelivery(java.lang.String clientMessageId, int serverId) {
    }
    
    private final void showLeaveChatConfirmation() {
    }
    
    private final void leaveChat() {
    }
    
    private final void sendRevealDecision(boolean wantsToContinue) {
    }
    
    private final void startRevealResultPolling() {
    }
    
    private final void stopRevealResultPolling() {
    }
    
    private final void pollRevealResult() {
    }
    
    private final void continueToChat() {
    }
    
    private final void resetToTopicSelection() {
    }
    
    private final void updateUIForCurrentState() {
    }
    
    private final void startSessionStatusPolling() {
    }
    
    private final void stopSessionStatusPolling() {
    }
    
    private final void pollSessionStatus() {
    }
    
    private final void startMessagePolling() {
    }
    
    private final void stopMessagePolling() {
    }
    
    private final void pollMessages() {
    }
    
    private final void startChatTimer() {
    }
    
    private final void updateTimerDisplay() {
    }
    
    private final void processWebSocketMessage(java.lang.String message) {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    private final void fetchOtherUserAvatar(int userId, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/spyro/vmeet/activity/BlindDateActivity$BlindDateState;", "", "(Ljava/lang/String;I)V", "TOPIC_SELECTION", "WAITING", "CHAT", "REVEAL", "RESULT", "app_release"})
    static enum BlindDateState {
        /*public static final*/ TOPIC_SELECTION /* = new TOPIC_SELECTION() */,
        /*public static final*/ WAITING /* = new WAITING() */,
        /*public static final*/ CHAT /* = new CHAT() */,
        /*public static final*/ REVEAL /* = new REVEAL() */,
        /*public static final*/ RESULT /* = new RESULT() */;
        
        BlindDateState() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.spyro.vmeet.activity.BlindDateActivity.BlindDateState> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/spyro/vmeet/activity/BlindDateActivity$Companion;", "", "()V", "API_URL", "", "BASE_URL", "CHAT_DURATION_SECONDS", "", "NORMAL_CLOSURE_STATUS", "PORT", "TAG", "WS_URL", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/spyro/vmeet/activity/BlindDateActivity$TopicViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/activity/BlindDateActivity;Landroid/view/View;)V", "descriptionTextView", "Landroid/widget/TextView;", "titleTextView", "bind", "", "topic", "Lcom/spyro/vmeet/data/BlindDateTopic;", "app_release"})
    public final class TopicViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView titleTextView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView descriptionTextView = null;
        
        public TopicViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.BlindDateTopic topic) {
        }
    }
}