package com.spyro.vmeet.dialog;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\b0\u001f2\u0006\u0010 \u001a\u00020\u0005H\u0082@\u00a2\u0006\u0002\u0010!J\u0014\u0010\"\u001a\b\u0012\u0004\u0012\u00020\b0\u001fH\u0082@\u00a2\u0006\u0002\u0010#J\u0012\u0010$\u001a\u0004\u0018\u00010%2\u0006\u0010&\u001a\u00020\u0005H\u0002J\u000e\u0010\'\u001a\u00020\tH\u0082@\u00a2\u0006\u0002\u0010#J\u0012\u0010(\u001a\u00020\t2\b\u0010)\u001a\u0004\u0018\u00010*H\u0014J\b\u0010+\u001a\u00020\tH\u0016J\u0018\u0010,\u001a\b\u0012\u0004\u0012\u00020\b0\u001f2\b\u0010-\u001a\u0004\u0018\u00010%H\u0002J\u0012\u0010.\u001a\u0004\u0018\u00010%2\u0006\u0010/\u001a\u000200H\u0002J \u00101\u001a\u00020\t2\u0006\u0010 \u001a\u00020\u00052\b\b\u0002\u00102\u001a\u000203H\u0082@\u00a2\u0006\u0002\u00104R\u000e\u0010\u000b\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00065"}, d2 = {"Lcom/spyro/vmeet/dialog/GifPickerDialog;", "Landroid/app/Dialog;", "context", "Landroid/content/Context;", "apiKey", "", "onGifSelected", "Lkotlin/Function1;", "Lcom/spyro/vmeet/data/TenorGif;", "", "(Landroid/content/Context;Ljava/lang/String;Lkotlin/jvm/functions/Function1;)V", "CLIENT_KEY", "TAG", "categoriesLabel", "Landroid/widget/TextView;", "closeButton", "Landroid/widget/ImageButton;", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "gifAdapter", "Lcom/spyro/vmeet/adapter/GifAdapter;", "noResultsText", "progressBar", "Landroid/widget/ProgressBar;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "searchEditText", "Landroid/widget/EditText;", "searchJob", "Lkotlinx/coroutines/Job;", "fetchGifs", "", "query", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "fetchTrendingGifs", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getApiResponse", "Lorg/json/JSONObject;", "urlString", "loadInitialContent", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDetachedFromWindow", "parseGifsFromApiResponse", "jsonObject", "parseJsonResponse", "connection", "Ljava/net/HttpURLConnection;", "searchGifs", "isTrending", "", "(Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public final class GifPickerDialog extends android.app.Dialog {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String apiKey = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.TenorGif, kotlin.Unit> onGifSelected = null;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private android.widget.EditText searchEditText;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView noResultsText;
    private android.widget.ImageButton closeButton;
    private android.widget.TextView categoriesLabel;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "GifPickerDialog";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String CLIENT_KEY = "vmeet_app";
    @org.jetbrains.annotations.NotNull()
    private final com.spyro.vmeet.adapter.GifAdapter gifAdapter = null;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job searchJob;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    
    public GifPickerDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String apiKey, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.TenorGif, kotlin.Unit> onGifSelected) {
        super(null);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final java.lang.Object loadInitialContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object searchGifs(java.lang.String query, boolean isTrending, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object fetchGifs(java.lang.String query, kotlin.coroutines.Continuation<? super java.util.List<com.spyro.vmeet.data.TenorGif>> $completion) {
        return null;
    }
    
    private final java.lang.Object fetchTrendingGifs(kotlin.coroutines.Continuation<? super java.util.List<com.spyro.vmeet.data.TenorGif>> $completion) {
        return null;
    }
    
    private final org.json.JSONObject getApiResponse(java.lang.String urlString) {
        return null;
    }
    
    private final org.json.JSONObject parseJsonResponse(java.net.HttpURLConnection connection) {
        return null;
    }
    
    private final java.util.List<com.spyro.vmeet.data.TenorGif> parseGifsFromApiResponse(org.json.JSONObject jsonObject) {
        return null;
    }
    
    @java.lang.Override()
    public void onDetachedFromWindow() {
    }
}