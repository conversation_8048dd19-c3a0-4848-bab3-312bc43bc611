package com.spyro.vmeet.util;

/**
 * Utility class for handling message reactions in the chat.
 * This handles both HTTP and WebSocket reaction operations.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B]\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012 \u0010\t\u001a\u001c\u0012\f\u0012\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u000b\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e0\n\u0012\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u000e0\u0010\u00a2\u0006\u0002\u0010\u0011J\u000e\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u0005J\u0010\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u0005H\u0002J&\u0010\u0018\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u00032\u000e\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u000bJ\u0018\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00052\u0006\u0010\u0019\u001a\u00020\u0003H\u0002R\u000e\u0010\u0012\u001a\u00020\u0003X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u000e0\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R(\u0010\t\u001a\u001c\u0012\f\u0012\n\u0012\u0004\u0012\u00020\f\u0018\u00010\u000b\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\u000e0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/spyro/vmeet/util/ReactionHandler;", "", "baseUrl", "", "userId", "", "matchId", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "onReactionSuccess", "Lkotlin/Function2;", "", "Lcom/spyro/vmeet/data/MessageReaction;", "", "", "onReactionError", "Lkotlin/Function1;", "(Ljava/lang/String;IILcom/spyro/vmeet/data/WebSocketClient;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;)V", "TAG", "httpClient", "Lokhttp3/OkHttpClient;", "removeReaction", "messageId", "removeReactionViaHttp", "sendReaction", "reaction", "existingReactions", "sendReactionViaHttp", "app_debug"})
public final class ReactionHandler {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String baseUrl = null;
    private final int userId = 0;
    private final int matchId = 0;
    @org.jetbrains.annotations.Nullable()
    private final com.spyro.vmeet.data.WebSocketClient webSocketClient = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<java.util.List<com.spyro.vmeet.data.MessageReaction>, java.lang.Boolean, kotlin.Unit> onReactionSuccess = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onReactionError = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "ReactionHandler";
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient httpClient = null;
    
    public ReactionHandler(@org.jetbrains.annotations.NotNull()
    java.lang.String baseUrl, int userId, int matchId, @org.jetbrains.annotations.Nullable()
    com.spyro.vmeet.data.WebSocketClient webSocketClient, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.util.List<com.spyro.vmeet.data.MessageReaction>, ? super java.lang.Boolean, kotlin.Unit> onReactionSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onReactionError) {
        super();
    }
    
    /**
     * Sends a reaction to a message.
     * If the user has already reacted with the same reaction, this will remove it (toggle behavior).
     * An empty string reaction is treated as an explicit removal request.
     *
     * @param messageId The ID of the message to react to
     * @param reaction The emoji reaction to send, or empty string to remove reaction
     * @param existingReactions Current reactions on the message (to check for existing reaction)
     */
    public final void sendReaction(int messageId, @org.jetbrains.annotations.NotNull()
    java.lang.String reaction, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.data.MessageReaction> existingReactions) {
    }
    
    /**
     * Explicitly removes a reaction via HTTP DELETE method.
     *
     * @param messageId The ID of the message to remove reaction from
     */
    public final void removeReaction(int messageId) {
    }
    
    /**
     * Send a reaction via HTTP POST
     */
    private final void sendReactionViaHttp(int messageId, java.lang.String reaction) {
    }
    
    /**
     * Remove a reaction via HTTP DELETE
     */
    private final void removeReactionViaHttp(int messageId) {
    }
}