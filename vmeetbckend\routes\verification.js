const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');

// Database connection - using the same config as main app
const dbConfig = {
    host: '*************',
    user: 'vmeet',
    password: 'Dickies30@@@',
    database: 'vmeet',
    port: 3306,
    connectTimeout: 60000
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

async function getConnection() {
    const connection = await pool.getConnection();
    const origRelease = connection.release.bind(connection);
    connection.end = () => Promise.resolve(origRelease());
    return connection;
}

// Function to send verification notification
async function sendVerificationNotification(userId, status, username) {
    try {
        let title, body;

        if (status === 'approved') {
            title = '¡Verificación Aprobada! ✅';
            body = `¡Felicidades ${username}! Tu cuenta ha sido verificada exitosamente. Ahora tienes acceso a todas las funciones premium.`;
        } else if (status === 'rejected') {
            title = 'Verificación Rechazada ❌';
            body = `Hola ${username}, tu solicitud de verificación ha sido rechazada. Revisa los comentarios del administrador y envía una nueva solicitud.`;
        }

        // Get user's FCM token
        const connection = await getConnection();
        const [tokenRows] = await connection.execute(
            'SELECT fcm_token FROM users WHERE id = ? AND fcm_token IS NOT NULL',
            [userId]
        );
        await connection.end();

        if (tokenRows.length > 0 && tokenRows[0].fcm_token) {
            const fcmToken = tokenRows[0].fcm_token;

            // Send FCM notification
            const admin = require('firebase-admin');

            const message = {
                notification: {
                    title: title,
                    body: body
                },
                data: {
                    type: 'verification_update',
                    status: status,
                    userId: userId.toString()
                },
                token: fcmToken
            };

            await admin.messaging().send(message);
            console.log(`Verification notification sent to user ${userId}: ${status}`);
        }
    } catch (error) {
        console.error('Error sending verification notification:', error);
        // Don't throw error to avoid breaking the verification process
    }
}

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../uploads/verification');
        try {
            await fs.mkdir(uploadDir, { recursive: true });
            cb(null, uploadDir);
        } catch (error) {
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileExtension = path.extname(file.originalname);
        cb(null, `${req.body.userId || 'user'}-${file.fieldname}-${uniqueSuffix}${fileExtension}`);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Solo se permiten archivos de imagen (JPEG, JPG, PNG)'));
        }
    }
});

// Validate image resolution and quality
async function validateImage(filePath, minWidth = 800, minHeight = 600) {
    try {
        const metadata = await sharp(filePath).metadata();
        return metadata.width >= minWidth && metadata.height >= minHeight;
    } catch (error) {
        console.error('Error validating image:', error);
        return false;
    }
}

// Submit verification request
router.post('/submit', upload.fields([
    { name: 'dni_photo', maxCount: 1 },
    { name: 'selfie_photo', maxCount: 1 }
]), async (req, res) => {
    let connection;

    try {
        const { userId, documentType } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'ID de usuario requerido' });
        }

        if (!req.files || !req.files.dni_photo || !req.files.selfie_photo) {
            return res.status(400).json({ error: 'Se requieren ambas fotos: documento y selfie' });
        }

        // Validate document type
        const validDocumentTypes = [
            'dni', 'cedula', 'passport', 'driving_license',
            'residence_card', 'voter_id'
        ];
        const docType = documentType || 'dni'; // Default to DNI if not specified

        if (!validDocumentTypes.includes(docType)) {
            return res.status(400).json({ error: 'Tipo de documento no válido' });
        }

        const dniPhoto = req.files.dni_photo[0];
        const selfiePhoto = req.files.selfie_photo[0];

        // Validate image quality
        const isDniValid = await validateImage(dniPhoto.path);
        const isSelfieValid = await validateImage(selfiePhoto.path);

        if (!isDniValid || !isSelfieValid) {
            // Clean up uploaded files
            await fs.unlink(dniPhoto.path).catch(() => {});
            await fs.unlink(selfiePhoto.path).catch(() => {});

            return res.status(400).json({
                error: 'Las imágenes deben tener una resolución mínima de 800x600 píxeles'
            });
        }

        connection = await getConnection();

        // Check if user already has a pending verification
        const [existingVerifications] = await connection.execute(
            'SELECT id, status FROM user_verifications WHERE user_id = ? AND status = "pending"',
            [userId]
        );

        if (existingVerifications.length > 0) {
            return res.status(400).json({
                error: 'Ya tienes una solicitud de verificación pendiente'
            });
        }

        // Create verification record
        const dniPhotoUrl = `/uploads/verification/${path.basename(dniPhoto.path)}`;
        const selfiePhotoUrl = `/uploads/verification/${path.basename(selfiePhoto.path)}`;

        const [result] = await connection.execute(
            `INSERT INTO user_verifications (user_id, document_type, dni_photo_url, selfie_photo_url, status)
             VALUES (?, ?, ?, ?, 'pending')`,
            [userId, docType, dniPhotoUrl, selfiePhotoUrl]
        );

        // Update user status to pending
        await connection.execute(
            'UPDATE users SET verified_status = "pending" WHERE id = ?',
            [userId]
        );

        res.json({
            success: true,
            message: 'Solicitud de verificación enviada correctamente',
            verificationId: result.insertId
        });

    } catch (error) {
        console.error('Error submitting verification:', error);

        // Clean up uploaded files on error
        if (req.files) {
            if (req.files.dni_photo) {
                await fs.unlink(req.files.dni_photo[0].path).catch(() => {});
            }
            if (req.files.selfie_photo) {
                await fs.unlink(req.files.selfie_photo[0].path).catch(() => {});
            }
        }

        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Get user verification status
router.get('/status/:userId', async (req, res) => {
    let connection;

    try {
        const { userId } = req.params;

        connection = await getConnection();

        const [verifications] = await connection.execute(
            `SELECT v.*, u.verified_status
             FROM user_verifications v
             JOIN users u ON v.user_id = u.id
             WHERE v.user_id = ?
             ORDER BY v.submitted_at DESC
             LIMIT 1`,
            [userId]
        );

        if (verifications.length === 0) {
            return res.json({
                status: 'not_verified',
                message: 'Usuario no verificado'
            });
        }

        const verification = verifications[0];

        res.json({
            status: verification.status,
            verified_status: verification.verified_status,
            document_type: verification.document_type,
            submitted_at: verification.submitted_at,
            reviewed_at: verification.reviewed_at,
            admin_notes: verification.admin_notes
        });

    } catch (error) {
        console.error('Error getting verification status:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Admin routes for verification management
router.get('/admin/list', async (req, res) => {
    let connection;

    try {
        const { status = 'all', page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * limit;

        connection = await getConnection();

        let whereClause = '';
        let queryParams = [];

        if (status !== 'all') {
            whereClause = 'WHERE v.status = ?';
            queryParams.push(status);
        }

        // Get total count
        const [countResult] = await connection.execute(
            `SELECT COUNT(*) as total FROM user_verifications v ${whereClause}`,
            queryParams
        );

        // Get verification requests
        const [verifications] = await connection.execute(
            `SELECT v.*, u.username, u.avatar_url, u.email,
                    admin.username as admin_username
             FROM user_verifications v
             JOIN users u ON v.user_id = u.id
             LEFT JOIN users admin ON v.admin_id = admin.id
             ${whereClause}
             ORDER BY v.submitted_at DESC
             LIMIT ? OFFSET ?`,
            [...queryParams, parseInt(limit), parseInt(offset)]
        );

        res.json({
            verifications,
            pagination: {
                total: countResult[0].total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(countResult[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Error getting verification list:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Approve verification
router.put('/admin/approve/:verificationId', async (req, res) => {
    let connection;

    try {
        const { verificationId } = req.params;
        const { adminId, notes = '' } = req.body;

        connection = await getConnection();

        // Start transaction
        await connection.beginTransaction();

        // Update verification status
        await connection.execute(
            `UPDATE user_verifications
             SET status = 'approved', reviewed_at = NOW(), admin_id = ?, admin_notes = ?
             WHERE id = ?`,
            [adminId, notes, verificationId]
        );

        // Get user_id and username for updating user table and notification
        const [verification] = await connection.execute(
            'SELECT uv.user_id, u.username FROM user_verifications uv JOIN users u ON uv.user_id = u.id WHERE uv.id = ?',
            [verificationId]
        );

        if (verification.length > 0) {
            const userId = verification[0].user_id;
            const username = verification[0].username;

            // Update user verified status
            await connection.execute(
                'UPDATE users SET verified_status = "approved", verified_at = NOW() WHERE id = ?',
                [userId]
            );

            // Add audit trail
            await connection.execute(
                'INSERT INTO verification_audit (verification_id, admin_id, action, notes) VALUES (?, ?, "approved", ?)',
                [verificationId, adminId, notes]
            );

            // Send push notification (async, don't wait)
            sendVerificationNotification(userId, 'approved', username).catch(console.error);
        }

        await connection.commit();

        res.json({
            success: true,
            message: 'Verificación aprobada correctamente'
        });

    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error('Error approving verification:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Reject verification
router.put('/admin/reject/:verificationId', async (req, res) => {
    let connection;

    try {
        const { verificationId } = req.params;
        const { adminId, notes = '' } = req.body;

        connection = await getConnection();

        // Start transaction
        await connection.beginTransaction();

        // Update verification status
        await connection.execute(
            `UPDATE user_verifications
             SET status = 'rejected', reviewed_at = NOW(), admin_id = ?, admin_notes = ?
             WHERE id = ?`,
            [adminId, notes, verificationId]
        );

        // Get user_id and username for updating user table and notification
        const [verification] = await connection.execute(
            'SELECT uv.user_id, u.username FROM user_verifications uv JOIN users u ON uv.user_id = u.id WHERE uv.id = ?',
            [verificationId]
        );

        if (verification.length > 0) {
            const userId = verification[0].user_id;
            const username = verification[0].username;

            // Update user verified status
            await connection.execute(
                'UPDATE users SET verified_status = "rejected" WHERE id = ?',
                [userId]
            );

            // Add audit trail
            await connection.execute(
                'INSERT INTO verification_audit (verification_id, admin_id, action, notes) VALUES (?, ?, "rejected", ?)',
                [verificationId, adminId, notes]
            );

            // Send push notification (async, don't wait)
            sendVerificationNotification(userId, 'rejected', username).catch(console.error);
        }

        await connection.commit();

        res.json({
            success: true,
            message: 'Verificación rechazada correctamente'
        });

    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error('Error rejecting verification:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Admin direct verification endpoint
router.post('/admin-verify', async (req, res) => {
    const connection = await getConnection();

    try {
        const { userId, adminId, notes } = req.body;

        if (!userId || !adminId) {
            return res.status(400).json({
                success: false,
                message: 'userId y adminId son requeridos'
            });
        }

        await connection.beginTransaction();

        // Check if admin exists and has admin privileges
        const [adminRows] = await connection.execute(
            'SELECT id, is_admin FROM users WHERE id = ? AND is_admin = 1',
            [adminId]
        );

        if (adminRows.length === 0) {
            await connection.rollback();
            return res.status(403).json({
                success: false,
                message: 'No tienes permisos de administrador'
            });
        }

        // Check if user exists
        const [userRows] = await connection.execute(
            'SELECT id, username, verified_status FROM users WHERE id = ?',
            [userId]
        );

        if (userRows.length === 0) {
            await connection.rollback();
            return res.status(404).json({
                success: false,
                message: 'Usuario no encontrado'
            });
        }

        const user = userRows[0];
        const username = user.username;

        // Check if user is already verified
        if (user.verified_status === 'approved') {
            await connection.rollback();
            return res.status(400).json({
                success: false,
                message: 'El usuario ya está verificado'
            });
        }

        // Create verification record if it doesn't exist
        const [existingVerification] = await connection.execute(
            'SELECT id FROM user_verifications WHERE user_id = ? AND status = "approved"',
            [userId]
        );

        let verificationId;

        if (existingVerification.length === 0) {
            // Create new verification record
            const [insertResult] = await connection.execute(
                'INSERT INTO user_verifications (user_id, document_type, document_number, document_image, selfie_image, status, submitted_at, reviewed_at, admin_id, admin_notes) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)',
                [userId, 'admin_direct', 'N/A', 'admin_verified', 'admin_verified', 'approved', adminId, notes || 'Verificado directamente por administrador']
            );
            verificationId = insertResult.insertId;
        } else {
            verificationId = existingVerification[0].id;
        }

        // Update user verified status
        await connection.execute(
            'UPDATE users SET verified_status = "approved", verified_at = NOW() WHERE id = ?',
            [userId]
        );

        // Add audit trail
        await connection.execute(
            'INSERT INTO verification_audit (verification_id, admin_id, action, notes) VALUES (?, ?, "admin_direct_verify", ?)',
            [verificationId, adminId, notes || 'Verificación directa por administrador']
        );

        await connection.commit();

        // Send notification after commit
        sendVerificationNotification(userId, 'approved', username).catch(console.error);

        res.json({
            success: true,
            message: `Usuario ${username} verificado exitosamente por administrador`
        });

    } catch (error) {
        await connection.rollback();
        console.error('Error in admin verification:', error);
        res.status(500).json({
            success: false,
            message: 'Error interno del servidor'
        });
    } finally {
        await connection.end();
    }
});

module.exports = router;
