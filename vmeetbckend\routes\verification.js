const express = require('express');
const router = express.Router();
const mysql = require('mysql2/promise');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');

// Database connection
const dbConfig = {
    host: 'localhost',
    user: 'root',
    password: '',
    database: 'vmeet'
};

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: async (req, file, cb) => {
        const uploadDir = path.join(__dirname, '../uploads/verification');
        try {
            await fs.mkdir(uploadDir, { recursive: true });
            cb(null, uploadDir);
        } catch (error) {
            cb(error);
        }
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const fileExtension = path.extname(file.originalname);
        cb(null, `${req.body.userId || 'user'}-${file.fieldname}-${uniqueSuffix}${fileExtension}`);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png/;
        const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);

        if (mimetype && extname) {
            return cb(null, true);
        } else {
            cb(new Error('Solo se permiten archivos de imagen (JPEG, JPG, PNG)'));
        }
    }
});

// Validate image resolution and quality
async function validateImage(filePath, minWidth = 800, minHeight = 600) {
    try {
        const metadata = await sharp(filePath).metadata();
        return metadata.width >= minWidth && metadata.height >= minHeight;
    } catch (error) {
        console.error('Error validating image:', error);
        return false;
    }
}

// Submit verification request
router.post('/submit', upload.fields([
    { name: 'dni_photo', maxCount: 1 },
    { name: 'selfie_photo', maxCount: 1 }
]), async (req, res) => {
    let connection;

    try {
        const { userId, documentType } = req.body;

        if (!userId) {
            return res.status(400).json({ error: 'ID de usuario requerido' });
        }

        if (!req.files || !req.files.dni_photo || !req.files.selfie_photo) {
            return res.status(400).json({ error: 'Se requieren ambas fotos: documento y selfie' });
        }

        // Validate document type
        const validDocumentTypes = [
            'dni', 'cedula', 'passport', 'driving_license',
            'residence_card', 'voter_id'
        ];
        const docType = documentType || 'dni'; // Default to DNI if not specified

        if (!validDocumentTypes.includes(docType)) {
            return res.status(400).json({ error: 'Tipo de documento no válido' });
        }

        const dniPhoto = req.files.dni_photo[0];
        const selfiePhoto = req.files.selfie_photo[0];

        // Validate image quality
        const isDniValid = await validateImage(dniPhoto.path);
        const isSelfieValid = await validateImage(selfiePhoto.path);

        if (!isDniValid || !isSelfieValid) {
            // Clean up uploaded files
            await fs.unlink(dniPhoto.path).catch(() => {});
            await fs.unlink(selfiePhoto.path).catch(() => {});

            return res.status(400).json({
                error: 'Las imágenes deben tener una resolución mínima de 800x600 píxeles'
            });
        }

        connection = await mysql.createConnection(dbConfig);

        // Check if user already has a pending verification
        const [existingVerifications] = await connection.execute(
            'SELECT id, status FROM user_verifications WHERE user_id = ? AND status = "pending"',
            [userId]
        );

        if (existingVerifications.length > 0) {
            return res.status(400).json({
                error: 'Ya tienes una solicitud de verificación pendiente'
            });
        }

        // Create verification record
        const dniPhotoUrl = `/uploads/verification/${path.basename(dniPhoto.path)}`;
        const selfiePhotoUrl = `/uploads/verification/${path.basename(selfiePhoto.path)}`;

        const [result] = await connection.execute(
            `INSERT INTO user_verifications (user_id, document_type, dni_photo_url, selfie_photo_url, status)
             VALUES (?, ?, ?, ?, 'pending')`,
            [userId, docType, dniPhotoUrl, selfiePhotoUrl]
        );

        // Update user status to pending
        await connection.execute(
            'UPDATE users SET verified_status = "pending" WHERE id = ?',
            [userId]
        );

        res.json({
            success: true,
            message: 'Solicitud de verificación enviada correctamente',
            verificationId: result.insertId
        });

    } catch (error) {
        console.error('Error submitting verification:', error);

        // Clean up uploaded files on error
        if (req.files) {
            if (req.files.dni_photo) {
                await fs.unlink(req.files.dni_photo[0].path).catch(() => {});
            }
            if (req.files.selfie_photo) {
                await fs.unlink(req.files.selfie_photo[0].path).catch(() => {});
            }
        }

        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Get user verification status
router.get('/status/:userId', async (req, res) => {
    let connection;

    try {
        const { userId } = req.params;

        connection = await mysql.createConnection(dbConfig);

        const [verifications] = await connection.execute(
            `SELECT v.*, u.verified_status
             FROM user_verifications v
             JOIN users u ON v.user_id = u.id
             WHERE v.user_id = ?
             ORDER BY v.submitted_at DESC
             LIMIT 1`,
            [userId]
        );

        if (verifications.length === 0) {
            return res.json({
                status: 'not_verified',
                message: 'Usuario no verificado'
            });
        }

        const verification = verifications[0];

        res.json({
            status: verification.status,
            verified_status: verification.verified_status,
            document_type: verification.document_type,
            submitted_at: verification.submitted_at,
            reviewed_at: verification.reviewed_at,
            admin_notes: verification.admin_notes
        });

    } catch (error) {
        console.error('Error getting verification status:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Admin routes for verification management
router.get('/admin/list', async (req, res) => {
    let connection;

    try {
        const { status = 'all', page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * limit;

        connection = await mysql.createConnection(dbConfig);

        let whereClause = '';
        let queryParams = [];

        if (status !== 'all') {
            whereClause = 'WHERE v.status = ?';
            queryParams.push(status);
        }

        // Get total count
        const [countResult] = await connection.execute(
            `SELECT COUNT(*) as total FROM user_verifications v ${whereClause}`,
            queryParams
        );

        // Get verification requests
        const [verifications] = await connection.execute(
            `SELECT v.*, u.username, u.avatar_url, u.email,
                    admin.username as admin_username
             FROM user_verifications v
             JOIN users u ON v.user_id = u.id
             LEFT JOIN users admin ON v.admin_id = admin.id
             ${whereClause}
             ORDER BY v.submitted_at DESC
             LIMIT ? OFFSET ?`,
            [...queryParams, parseInt(limit), parseInt(offset)]
        );

        res.json({
            verifications,
            pagination: {
                total: countResult[0].total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(countResult[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Error getting verification list:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Approve verification
router.put('/admin/approve/:verificationId', async (req, res) => {
    let connection;

    try {
        const { verificationId } = req.params;
        const { adminId, notes = '' } = req.body;

        connection = await mysql.createConnection(dbConfig);

        // Start transaction
        await connection.beginTransaction();

        // Update verification status
        await connection.execute(
            `UPDATE user_verifications
             SET status = 'approved', reviewed_at = NOW(), admin_id = ?, admin_notes = ?
             WHERE id = ?`,
            [adminId, notes, verificationId]
        );

        // Get user_id for updating user table
        const [verification] = await connection.execute(
            'SELECT user_id FROM user_verifications WHERE id = ?',
            [verificationId]
        );

        if (verification.length > 0) {
            // Update user verified status
            await connection.execute(
                'UPDATE users SET verified_status = "approved", verified_at = NOW() WHERE id = ?',
                [verification[0].user_id]
            );

            // Add audit trail
            await connection.execute(
                'INSERT INTO verification_audit (verification_id, admin_id, action, notes) VALUES (?, ?, "approved", ?)',
                [verificationId, adminId, notes]
            );
        }

        await connection.commit();

        res.json({
            success: true,
            message: 'Verificación aprobada correctamente'
        });

    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error('Error approving verification:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

// Reject verification
router.put('/admin/reject/:verificationId', async (req, res) => {
    let connection;

    try {
        const { verificationId } = req.params;
        const { adminId, notes = '' } = req.body;

        connection = await mysql.createConnection(dbConfig);

        // Start transaction
        await connection.beginTransaction();

        // Update verification status
        await connection.execute(
            `UPDATE user_verifications
             SET status = 'rejected', reviewed_at = NOW(), admin_id = ?, admin_notes = ?
             WHERE id = ?`,
            [adminId, notes, verificationId]
        );

        // Get user_id for updating user table
        const [verification] = await connection.execute(
            'SELECT user_id FROM user_verifications WHERE id = ?',
            [verificationId]
        );

        if (verification.length > 0) {
            // Update user verified status
            await connection.execute(
                'UPDATE users SET verified_status = "rejected" WHERE id = ?',
                [verification[0].user_id]
            );

            // Add audit trail
            await connection.execute(
                'INSERT INTO verification_audit (verification_id, admin_id, action, notes) VALUES (?, ?, "rejected", ?)',
                [verificationId, adminId, notes]
            );
        }

        await connection.commit();

        res.json({
            success: true,
            message: 'Verificación rechazada correctamente'
        });

    } catch (error) {
        if (connection) {
            await connection.rollback();
        }
        console.error('Error rejecting verification:', error);
        res.status(500).json({ error: 'Error interno del servidor' });
    } finally {
        if (connection) {
            await connection.end();
        }
    }
});

module.exports = router;
