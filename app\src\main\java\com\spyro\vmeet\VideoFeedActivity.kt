package com.spyro.vmeet

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.spyro.vmeet.adapter.VideoFeedAdapter
import com.spyro.vmeet.model.VideoPost
import com.spyro.vmeet.ui.CommunityDropdownMenu
import com.google.android.material.bottomnavigation.BottomNavigationView
import android.util.Log
import okhttp3.*
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import kotlin.concurrent.thread

class VideoFeedActivity : AppCompatActivity() {
    private lateinit var viewPager: ViewPager2
    private lateinit var fabUpload: FloatingActionButton
    private lateinit var fabMyVideos: FloatingActionButton
    private lateinit var bottomNavView: BottomNavigationView
    private lateinit var adapter: VideoFeedAdapter
    private var userId: Int = 0
    private val TAG = "VideoFeedActivity"
    private val API_URL = "http://77.110.116.89:3000"
    private var isFirstResume = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_video_feed)

        // Get userId from intent or preferences
        userId = intent.getIntExtra("USER_ID", 0)
        if (userId == 0) {
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", 0)
            if (userId == 0) {
                // If still no userId, redirect to login
                startActivity(Intent(this, LoginActivity::class.java))
                finish()
                return
            }
        }

        // Initialize views
        initializeViews()

        // Setup bottom navigation
        setupBottomNavigation()

        // Setup FABs
        setupFABs()

        // Load videos
        loadVideos()
    }

    private fun initializeViews() {
        viewPager = findViewById(R.id.viewPagerVideos)
        fabUpload = findViewById(R.id.fabUpload)
        fabMyVideos = findViewById(R.id.fabMyVideos)
        bottomNavView = findViewById(R.id.bottom_navigation)

        // Setup ViewPager2
        adapter = VideoFeedAdapter(this, userId)
        viewPager.adapter = adapter
        viewPager.orientation = ViewPager2.ORIENTATION_VERTICAL

        // Register page change callback
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                adapter.pauseAllVideosExcept(position)
                if (position == adapter.itemCount - 2) {
                    // Load more videos when near the end
                    loadMoreVideos()
                }
            }
        })
    }

    private fun setupBottomNavigation() {
        bottomNavView.selectedItemId = R.id.navigation_community
        bottomNavView.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_swipe -> {
                    startActivity(Intent(this, SwipeActivity::class.java).apply {
                        putExtra("USER_ID", userId)
                        flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                    })
                    true
                }
                R.id.navigation_matches -> {
                    startActivity(Intent(this, MatchesActivity::class.java).apply {
                        putExtra("USER_ID", userId)
                        flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                    })
                    true
                }
                R.id.navigation_community -> {
                    // Show community menu
                    val communityMenu = CommunityDropdownMenu(this, userId)
                    communityMenu.show(bottomNavView)
                    false
                }
                R.id.navigation_chats -> {
                    startActivity(Intent(this, com.spyro.vmeet.activity.ChatListActivity::class.java).apply {
                        putExtra("USER_ID", userId)
                        flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                    })
                    true
                }
                R.id.navigation_profile -> {
                    startActivity(Intent(this, ProfileActivity::class.java).apply {
                        putExtra("USER_ID", userId)
                        putExtra("VIEW_ONLY_MODE", false)
                        flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                    })
                    true
                }
                else -> false
            }
        }
    }

    private fun setupFABs() {
        fabUpload.setOnClickListener {
            startActivity(Intent(this, UploadVideoActivity::class.java).apply {
                putExtra("USER_ID", userId)
            })
        }

        fabMyVideos.setOnClickListener {
            startActivity(Intent(this, MyVideosActivity::class.java).apply {
                putExtra("USER_ID", userId)
            })
        }
    }

    private fun loadVideos() {
        Log.d(TAG, "Loading videos from API...")

        val client = OkHttpClient()
        val request = Request.Builder()
            .url("$API_URL/videos/feed")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error loading videos", e)
                runOnUiThread {
                    Toast.makeText(this@VideoFeedActivity, "Error al cargar videos", Toast.LENGTH_SHORT).show()
                    // Show dummy data as fallback
                    showDummyData()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    Log.d(TAG, "API Response Code: ${response.code}")
                    Log.d(TAG, "API Response Body: $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonObject = JSONObject(responseBody)
                        val videosArray = jsonObject.getJSONArray("videos")
                        val videos = mutableListOf<VideoPost>()

                        for (i in 0 until videosArray.length()) {
                            val videoJson = videosArray.getJSONObject(i)
                            Log.d(TAG, "Processing video: ${videoJson.toString()}")

                            val video = VideoPost(
                                id = videoJson.getInt("id"),
                                userId = videoJson.getInt("userId"),
                                videoUrl = videoJson.getString("videoUrl"),
                                description = videoJson.getString("description"),
                                likes = videoJson.getInt("likes"),
                                comments = videoJson.getInt("comments"),
                                views = videoJson.getInt("views")
                            )
                            videos.add(video)
                            Log.d(TAG, "Added video: ${video.id} - ${video.description}")
                        }

                        runOnUiThread {
                            Log.d(TAG, "Loaded ${videos.size} videos")
                            adapter.updateVideos(videos)
                            if (videos.isEmpty()) {
                                Toast.makeText(this@VideoFeedActivity, "No hay videos disponibles", Toast.LENGTH_SHORT).show()
                            } else {
                                // Start the first video after a short delay to ensure UI is ready
                                viewPager.post {
                                    adapter.startFirstVideo()
                                    // Also refresh the first video's user info after a small delay
                                    viewPager.postDelayed({
                                        adapter.refreshFirstVideoUserInfo()
                                    }, 500)
                                }
                            }
                        }
                    } else {
                        Log.e(TAG, "API Error: ${response.code} - $responseBody")
                        runOnUiThread {
                            Toast.makeText(this@VideoFeedActivity, "Error del servidor", Toast.LENGTH_SHORT).show()
                            showDummyData()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing response", e)
                    runOnUiThread {
                        Toast.makeText(this@VideoFeedActivity, "Error al procesar videos", Toast.LENGTH_SHORT).show()
                        showDummyData()
                    }
                }
            }
        })
    }

    private fun showDummyData() {
        // Fallback dummy data for testing
        val dummyVideos = listOf(
            VideoPost(1, userId, "http://77.110.116.89:3000/videos/sample1.mp4", "Video de prueba 1", 0, 0, 0),
            VideoPost(2, userId, "http://77.110.116.89:3000/videos/sample2.mp4", "Video de prueba 2", 0, 0, 0)
        )
        adapter.updateVideos(dummyVideos)
        // Start the first video after a short delay to ensure UI is ready
        viewPager.post {
            adapter.startFirstVideo()
            // Also refresh the first video's user info after a small delay
            viewPager.postDelayed({
                adapter.refreshFirstVideoUserInfo()
            }, 500)
        }
    }

    private fun loadMoreVideos() {
        // TODO: Implement pagination
    }

    override fun onPause() {
        super.onPause()
        adapter.stopAllVideos()
    }

    override fun onResume() {
        super.onResume()

        if (isFirstResume) {
            Log.d(TAG, "onResume called - first time, skipping player recreation")
            isFirstResume = false
        } else {
            Log.d(TAG, "onResume called - recreating players and resuming video")

            // Recreate players if they were released and resume current video
            adapter.recreatePlayersIfNeeded()

            // Add a small delay to ensure the UI is ready before resuming
            viewPager.postDelayed({
                adapter.resumeCurrentVideo()
                // Force reconnection if needed
                adapter.reconnectCurrentPlayerView()
            }, 100)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        adapter.releaseAllPlayers()
    }
}
