<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cyberpunk_background"
    tools:context=".ui.community.CommunityFragment">    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbarCommunity"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:elevation="4dp"
        android:paddingTop="0dp"
        android:minHeight="?attr/actionBarSize"
        android:layout_marginTop="@dimen/status_bar_height"
        android:background="@color/darker_blue"
        app:title="Comunidad"
        app:titleTextColor="@color/comfy_blue"
        app:titleTextAppearance="@style/TextAppearance.VMeet.Headline2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <Button
        android:id="@+id/buttonAddStory"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="+ Historia"
        android:textColor="@android:color/white"
        android:backgroundTint="@color/neon_blue"
        android:drawableStart="@drawable/ic_add_image"
        android:drawablePadding="8dp"
        android:drawableTint="@android:color/white"
        android:layout_marginTop="8dp"
        android:layout_marginStart="16dp"
        app:layout_constraintTop_toBottomOf="@id/toolbarCommunity"
        app:layout_constraintStart_toStartOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewStories"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:clipToPadding="false"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/buttonAddStory"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_story" />

    <View
        android:id="@+id/stories_divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/cyberpunk_divider"
        app:layout_constraintTop_toBottomOf="@id/recyclerViewStories" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewPosts"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingTop="8dp"
        android:paddingBottom="0dp"
        app:layout_constraintTop_toBottomOf="@id/stories_divider"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_post" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabCreatePost"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_add_post"
        app:tint="@color/white"
        app:backgroundTint="@color/neon_pink"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:contentDescription="@string/create_new_post" />

    <ProgressBar
        android:id="@+id/progressBarCommunity"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/toolbarCommunity"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>