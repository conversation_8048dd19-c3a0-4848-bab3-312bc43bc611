package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010 \u001a\u00020\u0004H\u0002J#\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020\u00042\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\t0%H\u0002\u00a2\u0006\u0002\u0010&J\b\u0010\'\u001a\u00020\"H\u0002J\u0010\u0010(\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u0004H\u0002J\u0012\u0010)\u001a\u00020\"2\b\u0010*\u001a\u0004\u0018\u00010+H\u0014J\b\u0010,\u001a\u00020\"H\u0014J\b\u0010-\u001a\u00020\"H\u0002J\b\u0010.\u001a\u00020\"H\u0002J\b\u0010/\u001a\u00020\"H\u0002J\u001b\u00100\u001a\u00020\"2\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\t0%H\u0002\u00a2\u0006\u0002\u00101J\u0010\u00102\u001a\u00020\"2\u0006\u00103\u001a\u00020\u0004H\u0002J\b\u00104\u001a\u00020\"H\u0002J\u0010\u00105\u001a\u00020\"2\u0006\u00106\u001a\u00020\u0004H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00067"}, d2 = {"Lcom/spyro/vmeet/activity/EmailVerificationActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "COUNTDOWN_TIME", "", "TAG", "codeDigit1", "Landroid/widget/EditText;", "codeDigit2", "codeDigit3", "codeDigit4", "codeDigit5", "codeDigit6", "countDownTimer", "Landroid/os/CountDownTimer;", "email", "emailTextView", "Landroid/widget/TextView;", "isPasswordReset", "", "progressBar", "Landroid/widget/ProgressBar;", "resendButton", "Landroid/widget/Button;", "timerTextView", "userId", "", "username", "verificationCode", "verifyButton", "getEnteredCode", "handlePastedText", "", "text", "editTexts", "", "(Ljava/lang/String;[Landroid/widget/EditText;)V", "initViews", "maskEmail", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "resendVerificationCode", "setupCodeDigitListeners", "setupListeners", "showPasteDialog", "([Landroid/widget/EditText;)V", "showSpamWarningDialog", "message", "startCountdownTimer", "verifyCode", "enteredCode", "app_debug"})
public final class EmailVerificationActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "EmailVerificationActivity";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    private android.widget.EditText codeDigit1;
    private android.widget.EditText codeDigit2;
    private android.widget.EditText codeDigit3;
    private android.widget.EditText codeDigit4;
    private android.widget.EditText codeDigit5;
    private android.widget.EditText codeDigit6;
    private android.widget.Button verifyButton;
    private android.widget.Button resendButton;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView timerTextView;
    private android.widget.TextView emailTextView;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String username = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String email = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String verificationCode = "";
    private boolean isPasswordReset = false;
    @org.jetbrains.annotations.Nullable()
    private android.os.CountDownTimer countDownTimer;
    private final long COUNTDOWN_TIME = 60000L;
    
    public EmailVerificationActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void setupListeners() {
    }
    
    private final void setupCodeDigitListeners() {
    }
    
    private final void showPasteDialog(android.widget.EditText[] editTexts) {
    }
    
    private final void handlePastedText(java.lang.String text, android.widget.EditText[] editTexts) {
    }
    
    private final java.lang.String getEnteredCode() {
        return null;
    }
    
    private final void verifyCode(java.lang.String enteredCode) {
    }
    
    private final void resendVerificationCode() {
    }
    
    private final void startCountdownTimer() {
    }
    
    private final java.lang.String maskEmail(java.lang.String email) {
        return null;
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    private final void showSpamWarningDialog(java.lang.String message) {
    }
}