<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/rounded_dialog_background"
    android:padding="24dp">

    <!-- Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Filtros de radar"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <!-- Distance Filter -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Distancia máxima"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinnerDistance"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/spinner_background"
            android:layout_marginBottom="8dp" />

    </LinearLayout>

    <!-- Age Range Filter -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Rango de edad"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/editTextMinAge"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@drawable/rounded_edittext_background"
                android:hint="Min"
                android:textColorHint="@color/text_secondary"
                android:textColor="@color/text_primary"
                android:inputType="number"
                android:gravity="center"
                android:fontFamily="sans-serif"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="-"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:layout_marginHorizontal="8dp" />

            <EditText
                android:id="@+id/editTextMaxAge"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@drawable/rounded_edittext_background"
                android:hint="Max"
                android:textColorHint="@color/text_secondary"
                android:textColor="@color/text_primary"
                android:inputType="number"
                android:gravity="center"
                android:fontFamily="sans-serif"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Gender Filter -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="20dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Identidad de género"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinnerGender"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/spinner_background" />

    </LinearLayout>

    <!-- Online Only Switch -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="20dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Solo personas en línea"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:fontFamily="sans-serif-medium" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switchOnlineOnly"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:theme="@style/CustomSwitchStyle" />

    </LinearLayout>

    <!-- Global Search Switch -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Búsqueda mundial"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:fontFamily="sans-serif-medium" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Muestra personas de todo el mundo"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="sans-serif"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switchGlobalSearch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:theme="@style/CustomSwitchStyle" />

    </LinearLayout>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/buttonClearFilters"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Limpiar"
            android:textColor="@color/text_secondary"
            android:background="@drawable/button_outline_cyberpunk"
            android:fontFamily="sans-serif-medium"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/buttonApplyFilters"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Aplicar"
            android:textColor="@color/dark_background"
            android:background="@drawable/button_cyberpunk"
            android:fontFamily="sans-serif-medium"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
