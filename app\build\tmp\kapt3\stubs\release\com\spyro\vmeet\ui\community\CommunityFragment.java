package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0014\b\u0007\u0018\u0000 @2\u00020\u00012\u00020\u0002:\u0003@ABB\u0005\u00a2\u0006\u0002\u0010\u0003J\u0012\u0010\u0017\u001a\u00020\u00182\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0002J\u000e\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001d0\u001cH\u0002J\b\u0010\u001e\u001a\u00020\u001fH\u0002J\"\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u00122\u0006\u0010\"\u001a\u00020\u00122\b\u0010#\u001a\u0004\u0018\u00010$H\u0016J&\u0010%\u001a\u0004\u0018\u00010\u00182\u0006\u0010&\u001a\u00020\'2\b\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\b\u0010(\u001a\u0004\u0018\u00010)H\u0016J\b\u0010*\u001a\u00020\u001fH\u0016J\b\u0010+\u001a\u00020\u001fH\u0016J\u000e\u0010,\u001a\u00020\u001f2\u0006\u0010-\u001a\u00020\u0014JG\u0010,\u001a\u00020\u001f2\u0006\u0010-\u001a\u00020\u00142\b\u0010.\u001a\u0004\u0018\u00010/2\b\u00100\u001a\u0004\u0018\u00010/2\b\u00101\u001a\u0004\u0018\u00010\u00122\b\u00102\u001a\u0004\u0018\u00010\u00142\b\u00103\u001a\u0004\u0018\u00010\u0014H\u0016\u00a2\u0006\u0002\u00104J\b\u00105\u001a\u00020\u001fH\u0016J\b\u00106\u001a\u00020\u001fH\u0002J\b\u00107\u001a\u00020\u001fH\u0002J\b\u00108\u001a\u00020\u001fH\u0002J\b\u00109\u001a\u00020\u001fH\u0002J\b\u0010:\u001a\u00020\u001fH\u0002J\b\u0010;\u001a\u00020\u001fH\u0002J\b\u0010<\u001a\u00020\u001fH\u0002J\u0018\u0010=\u001a\u00020\u001f2\u0006\u0010>\u001a\u00020/2\u0006\u0010?\u001a\u00020\u0014H\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006C"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommunityFragment;", "Landroidx/fragment/app/Fragment;", "Lcom/spyro/vmeet/ui/community/CreatePostDialog$CreatePostListener;", "()V", "buttonAddStory", "Landroid/widget/Button;", "fabCreatePost", "Lcom/google/android/material/floatingactionbutton/FloatingActionButton;", "postAdapter", "Lcom/spyro/vmeet/ui/community/PostAdapter;", "progressBar", "Landroid/widget/ProgressBar;", "recyclerViewPosts", "Landroidx/recyclerview/widget/RecyclerView;", "recyclerViewStories", "storiesAdapter", "Lcom/spyro/vmeet/adapter/StoriesAdapter;", "userId", "", "username", "", "viewModel", "Lcom/spyro/vmeet/ui/community/CommunityViewModel;", "createFallbackLayout", "Landroid/view/View;", "container", "Landroid/view/ViewGroup;", "getDummyPosts", "", "Lcom/spyro/vmeet/data/community/Post;", "observeViewModel", "", "onActivityResult", "requestCode", "resultCode", "data", "Landroid/content/Intent;", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "onPause", "onPostCreated", "postContent", "imageUri", "Landroid/net/Uri;", "voiceNoteUri", "voiceNoteDuration", "gifUrl", "gifPreviewUrl", "(Ljava/lang/String;Landroid/net/Uri;Landroid/net/Uri;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)V", "onResume", "pickImageForStory", "pickVideoForStory", "setupListeners", "setupRecyclerView", "setupStoriesRecyclerView", "showCreatePostDialog", "showStoryMediaPicker", "uploadStory", "mediaUri", "mediaType", "Companion", "CreatePostListener", "HorizontalSpaceItemDecoration", "app_release"})
public final class CommunityFragment extends androidx.fragment.app.Fragment implements com.spyro.vmeet.ui.community.CreatePostDialog.CreatePostListener {
    private androidx.recyclerview.widget.RecyclerView recyclerViewPosts;
    private com.spyro.vmeet.ui.community.PostAdapter postAdapter;
    private androidx.recyclerview.widget.RecyclerView recyclerViewStories;
    private com.spyro.vmeet.adapter.StoriesAdapter storiesAdapter;
    private android.widget.ProgressBar progressBar;
    private com.google.android.material.floatingactionbutton.FloatingActionButton fabCreatePost;
    private android.widget.Button buttonAddStory;
    private com.spyro.vmeet.ui.community.CommunityViewModel viewModel;
    private int userId = -1;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String username = "Usuario";
    private static final int REQUEST_IMAGE_PICK = 1001;
    private static final int REQUEST_VIDEO_PICK = 1002;
    private static final int REQUEST_STORY_EDITED = 1003;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.community.CommunityFragment.Companion Companion = null;
    
    public CommunityFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final android.view.View createFallbackLayout(android.view.ViewGroup container) {
        return null;
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupStoriesRecyclerView() {
    }
    
    private final void observeViewModel() {
    }
    
    private final void setupListeners() {
    }
    
    private final void showCreatePostDialog() {
    }
    
    private final void showStoryMediaPicker() {
    }
    
    private final void pickImageForStory() {
    }
    
    private final void pickVideoForStory() {
    }
    
    @java.lang.Override()
    public void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
    }
    
    private final void uploadStory(android.net.Uri mediaUri, java.lang.String mediaType) {
    }
    
    public final void onPostCreated(@org.jetbrains.annotations.NotNull()
    java.lang.String postContent) {
    }
    
    @java.lang.Override()
    public void onPostCreated(@org.jetbrains.annotations.NotNull()
    java.lang.String postContent, @org.jetbrains.annotations.Nullable()
    android.net.Uri imageUri, @org.jetbrains.annotations.Nullable()
    android.net.Uri voiceNoteUri, @org.jetbrains.annotations.Nullable()
    java.lang.Integer voiceNoteDuration, @org.jetbrains.annotations.Nullable()
    java.lang.String gifUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String gifPreviewUrl) {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
    
    private final java.util.List<com.spyro.vmeet.data.community.Post> getDummyPosts() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommunityFragment$Companion;", "", "()V", "REQUEST_IMAGE_PICK", "", "REQUEST_STORY_EDITED", "REQUEST_VIDEO_PICK", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001JQ\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005H&\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommunityFragment$CreatePostListener;", "", "onPostCreated", "", "postContent", "", "imageUri", "Landroid/net/Uri;", "voiceNoteUri", "voiceNoteDuration", "", "gifUrl", "gifPreviewUrl", "(Ljava/lang/String;Landroid/net/Uri;Landroid/net/Uri;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)V", "app_release"})
    public static abstract interface CreatePostListener {
        
        public abstract void onPostCreated(@org.jetbrains.annotations.NotNull()
        java.lang.String postContent, @org.jetbrains.annotations.Nullable()
        android.net.Uri imageUri, @org.jetbrains.annotations.Nullable()
        android.net.Uri voiceNoteUri, @org.jetbrains.annotations.Nullable()
        java.lang.Integer voiceNoteDuration, @org.jetbrains.annotations.Nullable()
        java.lang.String gifUrl, @org.jetbrains.annotations.Nullable()
        java.lang.String gifPreviewUrl);
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
        public static final class DefaultImpls {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J(\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommunityFragment$HorizontalSpaceItemDecoration;", "Landroidx/recyclerview/widget/RecyclerView$ItemDecoration;", "spacing", "", "(I)V", "getItemOffsets", "", "outRect", "Landroid/graphics/Rect;", "view", "Landroid/view/View;", "parent", "Landroidx/recyclerview/widget/RecyclerView;", "state", "Landroidx/recyclerview/widget/RecyclerView$State;", "app_release"})
    static final class HorizontalSpaceItemDecoration extends androidx.recyclerview.widget.RecyclerView.ItemDecoration {
        private final int spacing = 0;
        
        public HorizontalSpaceItemDecoration(int spacing) {
            super();
        }
        
        @java.lang.Override()
        public void getItemOffsets(@org.jetbrains.annotations.NotNull()
        android.graphics.Rect outRect, @org.jetbrains.annotations.NotNull()
        android.view.View view, @org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView parent, @org.jetbrains.annotations.NotNull()
        androidx.recyclerview.widget.RecyclerView.State state) {
        }
    }
}