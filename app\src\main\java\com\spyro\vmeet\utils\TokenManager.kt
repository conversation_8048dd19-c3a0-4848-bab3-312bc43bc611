package com.spyro.vmeet.utils

import android.content.Context
import android.util.Log
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * Secure token management using EncryptedSharedPreferences
 */
class TokenManager(private val context: Context) {
    
    companion object {
        private const val TAG = "TokenManager"
        private const val PREFS_NAME = "vmeet_secure_tokens"
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USERNAME = "username"
        private const val KEY_EMAIL = "email"
        private const val KEY_ROLE = "role"
        private const val KEY_IS_ADMIN = "is_admin"
        private const val API_URL = "http://77.110.116.89:3000"
    }
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedPrefs = EncryptedSharedPreferences.create(
        context,
        PREFS_NAME,
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    /**
     * Save authentication tokens and user info
     */
    fun saveTokens(
        accessToken: String,
        refreshToken: String,
        userId: Int,
        username: String,
        email: String,
        role: String,
        isAdmin: Boolean
    ) {
        try {
            encryptedPrefs.edit().apply {
                putString(KEY_ACCESS_TOKEN, accessToken)
                putString(KEY_REFRESH_TOKEN, refreshToken)
                putInt(KEY_USER_ID, userId)
                putString(KEY_USERNAME, username)
                putString(KEY_EMAIL, email)
                putString(KEY_ROLE, role)
                putBoolean(KEY_IS_ADMIN, isAdmin)
                apply()
            }
            Log.d(TAG, "Tokens saved successfully for user: $username")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving tokens", e)
        }
    }
    
    /**
     * Get access token
     */
    fun getAccessToken(): String? {
        return try {
            encryptedPrefs.getString(KEY_ACCESS_TOKEN, null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting access token", e)
            null
        }
    }
    
    /**
     * Get refresh token
     */
    fun getRefreshToken(): String? {
        return try {
            encryptedPrefs.getString(KEY_REFRESH_TOKEN, null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting refresh token", e)
            null
        }
    }
    
    /**
     * Get current user ID
     */
    fun getUserId(): Int {
        return try {
            encryptedPrefs.getInt(KEY_USER_ID, -1)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user ID", e)
            -1
        }
    }
    
    /**
     * Get current username
     */
    fun getUsername(): String? {
        return try {
            encryptedPrefs.getString(KEY_USERNAME, null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting username", e)
            null
        }
    }
    
    /**
     * Get current email
     */
    fun getEmail(): String? {
        return try {
            encryptedPrefs.getString(KEY_EMAIL, null)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting email", e)
            null
        }
    }
    
    /**
     * Get current role
     */
    fun getRole(): String? {
        return try {
            encryptedPrefs.getString(KEY_ROLE, "user")
        } catch (e: Exception) {
            Log.e(TAG, "Error getting role", e)
            "user"
        }
    }
    
    /**
     * Check if current user is admin
     */
    fun isAdmin(): Boolean {
        return try {
            encryptedPrefs.getBoolean(KEY_IS_ADMIN, false)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking admin status", e)
            false
        }
    }
    
    /**
     * Check if user is logged in
     */
    fun isLoggedIn(): Boolean {
        val accessToken = getAccessToken()
        val userId = getUserId()
        return !accessToken.isNullOrEmpty() && userId > 0
    }
    
    /**
     * Clear all stored tokens and user data
     */
    fun clearTokens() {
        try {
            encryptedPrefs.edit().clear().apply()
            Log.d(TAG, "All tokens cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing tokens", e)
        }
    }
    
    /**
     * Refresh access token using refresh token (synchronous)
     */
    fun refreshTokenSync(): Boolean {
        val refreshToken = getRefreshToken()
        if (refreshToken.isNullOrEmpty()) {
            Log.w(TAG, "No refresh token available")
            return false
        }
        
        return try {
            val url = URL("$API_URL/auth/refresh")
            val connection = url.openConnection() as HttpURLConnection
            
            connection.apply {
                requestMethod = "POST"
                setRequestProperty("Content-Type", "application/json")
                doOutput = true
                connectTimeout = 10000
                readTimeout = 10000
            }
            
            // Send refresh token
            val requestBody = JSONObject().apply {
                put("refreshToken", refreshToken)
            }
            
            connection.outputStream.use { os ->
                os.write(requestBody.toString().toByteArray())
            }
            
            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().readText()
                val jsonResponse = JSONObject(response)
                
                if (jsonResponse.getBoolean("success")) {
                    val newAccessToken = jsonResponse.getString("accessToken")
                    val newRefreshToken = jsonResponse.getString("refreshToken")
                    
                    // Update tokens
                    encryptedPrefs.edit().apply {
                        putString(KEY_ACCESS_TOKEN, newAccessToken)
                        putString(KEY_REFRESH_TOKEN, newRefreshToken)
                        apply()
                    }
                    
                    Log.d(TAG, "Tokens refreshed successfully")
                    return true
                } else {
                    Log.w(TAG, "Token refresh failed: ${jsonResponse.optString("error")}")
                }
            } else {
                Log.w(TAG, "Token refresh failed with code: $responseCode")
            }
            
            connection.disconnect()
            false
            
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing token", e)
            false
        }
    }
    
    /**
     * Refresh access token using refresh token (asynchronous)
     */
    fun refreshTokenAsync(callback: (Boolean) -> Unit) {
        Thread {
            val success = refreshTokenSync()
            callback(success)
        }.start()
    }
    
    /**
     * Verify if current token is still valid
     */
    fun verifyTokenAsync(callback: (Boolean) -> Unit) {
        Thread {
            val accessToken = getAccessToken()
            if (accessToken.isNullOrEmpty()) {
                callback(false)
                return@Thread
            }
            
            try {
                val url = URL("$API_URL/auth/verify")
                val connection = url.openConnection() as HttpURLConnection
                
                connection.apply {
                    requestMethod = "GET"
                    setRequestProperty("Authorization", "Bearer $accessToken")
                    connectTimeout = 10000
                    readTimeout = 10000
                }
                
                val responseCode = connection.responseCode
                val isValid = responseCode == HttpURLConnection.HTTP_OK
                
                connection.disconnect()
                callback(isValid)
                
            } catch (e: Exception) {
                Log.e(TAG, "Error verifying token", e)
                callback(false)
            }
        }.start()
    }
}
