<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:padding="8dp">

    <FrameLayout
        android:layout_width="70dp"
        android:layout_height="70dp">

        <!-- Gradient ring for unseen stories with improved visibility -->
        <View
            android:id="@+id/story_avatar_ring_unseen"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:background="@drawable/story_ring_unseen_gradient" />
        
        <!-- Gray ring for seen stories -->
        <View
            android:id="@+id/story_avatar_ring_seen"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:background="@drawable/story_ring_seen"
            android:visibility="gone"/>

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/story_avatar"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_default_avatar" />

    </FrameLayout>

    <TextView
        android:id="@+id/story_username"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="Username"
        android:textSize="12sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textColor="?android:attr/textColorPrimary" />

    <TextView
        android:id="@+id/story_time_ago"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="10sp"
        android:maxLines="1"
        android:textColor="@android:color/darker_gray"
        tools:text="14 h" />

</LinearLayout> 