{"logs": [{"outputFile": "com.spyro.vmeet.app-mergeDebugResources-78:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09b775847530a523f5664d9561d9e519\\transformed\\appcompat-1.7.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1311,1418,1519,1630,1716,1824,1942,2021,2098,2189,2282,2380,2474,2574,2667,2762,2860,2951,3042,3126,3231,3339,3438,3544,3656,3759,3925,23861", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1413,1514,1625,1711,1819,1937,2016,2093,2184,2277,2375,2469,2569,2662,2757,2855,2946,3037,3121,3226,3334,3433,3539,3651,3754,3920,4018,23939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2811904dfbb970a24e446f20309c4400\\transformed\\material3-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4606,4693,4794,4874,4960,5057,5160,5253,5350,5438,5543,5640,5739,5859,5939,6041", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4601,4688,4789,4869,4955,5052,5155,5248,5345,5433,5538,5635,5734,5854,5934,6036,6129"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12612,12727,12842,12952,13067,13165,13260,13372,13507,13623,13775,13860,13961,14053,14150,14266,14388,14494,14627,14760,14894,15058,15186,15310,15440,15560,15653,15750,15871,15994,16092,16195,16304,16445,16594,16703,16803,16887,16981,17076,17163,17250,17351,17431,17517,17614,17717,17810,17907,17995,18100,18197,18296,18416,18496,18598", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "12722,12837,12947,13062,13160,13255,13367,13502,13618,13770,13855,13956,14048,14145,14261,14383,14489,14622,14755,14889,15053,15181,15305,15435,15555,15648,15745,15866,15989,16087,16190,16299,16440,16589,16698,16798,16882,16976,17071,17158,17245,17346,17426,17512,17609,17712,17805,17902,17990,18095,18192,18291,18411,18491,18593,18686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\802769e58155747426a628dc0cc15a1d\\transformed\\play-services-basement-18.1.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "81", "startColumns": "4", "startOffsets": "6642", "endColumns": "138", "endOffsets": "6776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d54c6bc7ec49a885f32785c74a047713\\transformed\\media3-exoplayer-1.3.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "120,121,122,123,124,125,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10380,10457,10519,10583,10654,10731,10805,10889,10971", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "10452,10514,10578,10649,10726,10800,10884,10966,11046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40bc0ded6ebc3196905947594b68c381\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "61,62,63,64,65,66,67,278", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4426,4522,4624,4725,4823,4933,5041,24250", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "4517,4619,4720,4818,4928,5036,5158,24346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\330d2aa732692cb12a9344e8c098a4ae\\transformed\\play-services-base-18.1.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5643,5749,5909,6033,6143,6299,6427,6540,6781,6950,7061,7231,7361,7524,7688,7756,7823", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "5744,5904,6028,6138,6294,6422,6535,6637,6945,7056,7226,7356,7519,7683,7751,7818,7905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\86ff0429b92cf0cf9a2921fd8c52f631\\transformed\\media3-ui-1.3.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3748,3814,3866,3930,4008,4086", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3743,3809,3861,3925,4003,4081,4135"}, "to": {"startLines": "2,11,17,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,719,8326,8407,8487,8569,8672,8771,8850,8915,9006,9100,9170,9236,9301,9378,9500,9617,9738,9812,9894,9967,10049,10149,10248,10315,11051,11104,11162,11210,11271,11343,11417,11480,11553,11618,11678,11743,11807,11873,11925,11989,12067,12145", "endLines": "10,16,22,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "383,714,1028,8402,8482,8564,8667,8766,8845,8910,9001,9095,9165,9231,9296,9373,9495,9612,9733,9807,9889,9962,10044,10144,10243,10310,10375,11099,11157,11205,11266,11338,11412,11475,11548,11613,11673,11738,11802,11868,11920,11984,12062,12140,12194"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f80b51d56d568461b2fe773f95a970d9\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "71,72,91,92,93,149,150,266,267,268,269,271,272,276,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5464,5559,7910,8005,8108,12374,12453,23303,23393,23474,23543,23691,23774,24102,24351,24429,24497", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "5554,5638,8000,8103,8195,12448,12542,23388,23469,23538,23607,23769,23856,24169,24424,24492,24606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0825f58c9c9dfc18f502349536013ef2\\transformed\\material-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,378,453,528,606,698,781,873,1001,1082,1143,1208,1307,1383,1448,1538,1602,1668,1722,1791,1851,1905,2022,2082,2144,2198,2270,2400,2487,2567,2663,2747,2839,2978,3047,3125,3256,3344,3424,3478,3529,3595,3667,3744,3815,3897,3969,4046,4119,4190,4295,4383,4455,4547,4643,4717,4791,4887,4939,5021,5088,5175,5262,5324,5388,5451,5519,5625,5732,5830,5947,6005,6060,6139,6222,6297", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "373,448,523,601,693,776,868,996,1077,1138,1203,1302,1378,1443,1533,1597,1663,1717,1786,1846,1900,2017,2077,2139,2193,2265,2395,2482,2562,2658,2742,2834,2973,3042,3120,3251,3339,3419,3473,3524,3590,3662,3739,3810,3892,3964,4041,4114,4185,4290,4378,4450,4542,4638,4712,4786,4882,4934,5016,5083,5170,5257,5319,5383,5446,5514,5620,5727,5825,5942,6000,6055,6134,6217,6292,6368"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,94,95,147,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1033,4023,4098,4173,4251,4343,5163,5255,5383,8200,8261,12199,12298,12547,18691,18781,18845,18911,18965,19034,19094,19148,19265,19325,19387,19441,19513,19643,19730,19810,19906,19990,20082,20221,20290,20368,20499,20587,20667,20721,20772,20838,20910,20987,21058,21140,21212,21289,21362,21433,21538,21626,21698,21790,21886,21960,22034,22130,22182,22264,22331,22418,22505,22567,22631,22694,22762,22868,22975,23073,23190,23248,23612,23944,24027,24174", "endLines": "28,56,57,58,59,60,68,69,70,94,95,147,148,151,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,270,274,275,277", "endColumns": "12,74,74,77,91,82,91,127,80,60,64,98,75,64,89,63,65,53,68,59,53,116,59,61,53,71,129,86,79,95,83,91,138,68,77,130,87,79,53,50,65,71,76,70,81,71,76,72,70,104,87,71,91,95,73,73,95,51,81,66,86,86,61,63,62,67,105,106,97,116,57,54,78,82,74,75", "endOffsets": "1306,4093,4168,4246,4338,4421,5250,5378,5459,8256,8321,12293,12369,12607,18776,18840,18906,18960,19029,19089,19143,19260,19320,19382,19436,19508,19638,19725,19805,19901,19985,20077,20216,20285,20363,20494,20582,20662,20716,20767,20833,20905,20982,21053,21135,21207,21284,21357,21428,21533,21621,21693,21785,21881,21955,22029,22125,22177,22259,22326,22413,22500,22562,22626,22689,22757,22863,22970,23068,23185,23243,23298,23686,24022,24097,24245"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\90510453f8412948a35d8ec71808d347\\transformed\\foundation-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "282,283", "startColumns": "4,4", "startOffsets": "24611,24695", "endColumns": "83,86", "endOffsets": "24690,24777"}}]}]}