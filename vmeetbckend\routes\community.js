const express = require('express');
const router = express.Router();
const db = require('../db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Set up multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        // Create the uploads/community directory if it doesn't exist
        const dir = path.join(__dirname, '../uploads/community');
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        cb(null, dir);
    },
    filename: (req, file, cb) => {
        // Generate a unique name for the uploaded file
        const uniqueFileName = `${uuidv4()}${path.extname(file.originalname)}`;
        cb(null, uniqueFileName);
    }
});

// Filter file types (only allow images)
const fileFilter = (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'), false);
    }
};

const upload = multer({
    storage,
    fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB size limit
    }
});

// Get all posts (paginated)
router.get('/posts', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
// Get posts by user ID
router.get('/posts/user/:userId', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        const { userId } = req.params;
        const page = parseInt(req.query.page) || 0;
        const limit = parseInt(req.query.limit) || 10;
        const offset = page * limit;
        
        console.log(`[GET /community/posts/user/${userId}] Request for user posts`);
        
        // Get posts for a specific user with user info
        const [posts] = await connection.query(`
            SELECT p.*, u.username, u.avatar_url as user_avatar, 
                  (SELECT COUNT(*) FROM post_comments WHERE post_id = p.id) as comment_count
            FROM posts p
            JOIN users u ON p.user_id = u.id
            WHERE p.user_id = ?
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        `, [userId, limit, offset]);
        
        // For each post, get the reactions and normalize avatar
        for (let post of posts) {
            if (post.user_avatar) {
                post.user_avatar = `/uploads/${path.basename(post.user_avatar)}`;
            }
            const [reactions] = await connection.query(`
                SELECT pr.*, u.username
                FROM post_reactions pr
                JOIN users u ON pr.user_id = u.id
                WHERE pr.post_id = ?
            `, [post.id]);
            
            post.reactions = reactions;
        }
        
        res.json({ success: true, posts });
    } catch (error) {
        console.error('Error fetching user posts:', error);
        res.status(500).json({ success: false, message: 'Error fetching user posts', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});
        
        const page = parseInt(req.query.page) || 0;
        const limit = parseInt(req.query.limit) || 10;
        const offset = page * limit;
        
        // Get posts with user info, ordered by most recent first
        const [posts] = await connection.query(`
            SELECT p.*, u.username, u.avatar_url as user_avatar, 
                  (SELECT COUNT(*) FROM post_comments WHERE post_id = p.id) as comment_count
            FROM posts p
            JOIN users u ON p.user_id = u.id
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        `, [limit, offset]);
        
        // For each post, get the reactions and normalize avatar
        for (let post of posts) {
            if (post.user_avatar) {
                post.user_avatar = `/uploads/${path.basename(post.user_avatar)}`;
            }
            const [reactions] = await connection.query(`
                SELECT pr.*, u.username
                FROM post_reactions pr
                JOIN users u ON pr.user_id = u.id
                WHERE pr.post_id = ?
            `, [post.id]);
            
            post.reactions = reactions;
        }
        
        res.json({ success: true, posts });
    } catch (error) {
        console.error('Error fetching posts:', error);
        res.status(500).json({ success: false, message: 'Error fetching posts', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// Get single post by ID with comments
router.get('/posts/:postId', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        const { postId } = req.params;
        
        // Get post with user info
        const [posts] = await connection.query(`
            SELECT p.*, u.username, u.avatar_url as user_avatar
            FROM posts p
            JOIN users u ON p.user_id = u.id
            WHERE p.id = ?
        `, [postId]);
        
        if (posts.length === 0) {
            return res.status(404).json({ success: false, message: 'Post not found' });
        }
        
        const post = posts[0];
        
        // Normalize post author avatar
        if (post.user_avatar) {
            post.user_avatar = `/uploads/${path.basename(post.user_avatar)}`;
        }
        
        // Get reactions for the post
        const [reactions] = await connection.query(`
            SELECT pr.*, u.username
            FROM post_reactions pr
            JOIN users u ON pr.user_id = u.id
            WHERE pr.post_id = ?
        `, [postId]);
        
        post.reactions = reactions;
        
        // Get comments for the post with user info
        const [comments] = await connection.query(`
            SELECT c.*, u.username, u.avatar_url as user_avatar
            FROM post_comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.post_id = ?
            ORDER BY c.created_at ASC
        `, [postId]);
        
        // For each comment, get reactions and normalize avatar
        for (let comment of comments) {
            if (comment.user_avatar) {
                comment.user_avatar = `/uploads/${path.basename(comment.user_avatar)}`;
            }
            const [commentReactions] = await connection.query(`
                SELECT cr.*, u.username
                FROM comment_reactions cr
                JOIN users u ON cr.user_id = u.id
                WHERE cr.comment_id = ?
            `, [comment.id]);
            
            comment.reactions = commentReactions;
        }
        
        post.comments = comments;
        
        res.json({ success: true, post });
    } catch (error) {
        console.error('Error fetching post:', error);
        res.status(500).json({ success: false, message: 'Error fetching post', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// Create a new post
router.post('/posts', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        console.log('[COMMUNITY POSTS] Received request for /posts');
        console.log('[COMMUNITY POSTS] req.file:', req.file); 
        console.log("[POST /community/posts] Received body for post creation:", JSON.stringify(req.body, null, 2));
        
        const { userId, textContent, image_url, voice_note_url, voice_note_duration, gif_url, gif_preview_url } = req.body;
        console.log('[COMMUNITY POSTS] Destructured image_url from req.body:', image_url);
        console.log('[COMMUNITY POSTS] Destructured voice_note_url from req.body:', voice_note_url);
        console.log('[COMMUNITY POSTS] Destructured voice_note_duration from req.body:', voice_note_duration);
        console.log('[COMMUNITY POSTS] Destructured gif_url from req.body:', gif_url);
        console.log('[COMMUNITY POSTS] Destructured gif_preview_url from req.body:', gif_preview_url);
        
        const trimmedTextContent = textContent ? textContent.trim() : "";

        if (!userId) {
            return res.status(400).json({ success: false, message: 'User ID is required' });
        }
        
        // Stricter validation: post must contain actual text, an image, a voice note, or a GIF
        if (!trimmedTextContent && !req.file && !image_url && !voice_note_url && !gif_url) {
            return res.status(400).json({ success: false, message: 'Post must contain actual text, an image, a voice note, or a GIF' });
        }
        
        let finalImageUrl = null;
        if (req.file) {
            finalImageUrl = `/uploads/community/${req.file.filename}`;
            console.log('[COMMUNITY POSTS] Using image from req.file:', finalImageUrl);
        } else if (image_url) {
            finalImageUrl = image_url;
            console.log('[COMMUNITY POSTS] Using image_url from req.body:', finalImageUrl);
        } else {
            console.log('[COMMUNITY POSTS] No image provided (no req.file and no image_url in body).');
        }
        
        const parsedDuration = voice_note_duration ? parseInt(voice_note_duration, 10) : null;
        console.log(`[POST /community/posts] Parsed duration: ${parsedDuration}, voice_note_url_from_body: ${voice_note_url}`);

        // Insert the post
        const query = `
            INSERT INTO posts (user_id, text_content, image_url, voice_note_url, voice_note_duration, gif_url, gif_preview_url, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
        `;
        const params = [userId, trimmedTextContent, finalImageUrl, voice_note_url || null, parsedDuration, gif_url || null, gif_preview_url || null];
        
        console.log("[POST /community/posts] Executing SQL:", query);
        console.log("[POST /community/posts] With params:", JSON.stringify(params, null, 2));

        const [result] = await connection.query(query, params);
        
        const newPostId = result.insertId;

        // Get the created post
        const [posts] = await connection.query(`
            SELECT p.*, u.username, u.avatar_url as user_avatar
            FROM posts p
            JOIN users u ON p.user_id = u.id
            WHERE p.id = ?
        `, [newPostId]);
        
        if (posts.length > 0) {
            const createdPost = posts[0];
            // Normalize created post author avatar
            if (createdPost.user_avatar) {
                createdPost.user_avatar = `/uploads/${path.basename(createdPost.user_avatar)}`;
            }
            console.log("[POST /community/posts] Fetched new post from DB:", JSON.stringify(createdPost, null, 2));
            res.status(201).json({ success: true, post: createdPost });
        } else {
            console.error(`[POST /community/posts] Failed to retrieve created post with ID: ${newPostId}`);
            res.status(404).json({ success: false, message: 'Failed to retrieve created post' });
        }

    } catch (error) {
        console.error('Error creating post:', error);
        res.status(500).json({ success: false, message: 'Error creating post', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// Add a reaction to a post
router.post('/posts/:postId/reactions', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        const { postId } = req.params;
        const { userId, reaction } = req.body;
        
        if (!userId || !reaction) {
            return res.status(400).json({ success: false, message: 'User ID and reaction are required' });
        }
        
        // Get user info for WebSocket broadcasting
        const [userInfo] = await connection.query(`
            SELECT username FROM users WHERE id = ?
        `, [userId]);
        
        const username = userInfo.length > 0 ? userInfo[0].username : 'Unknown User';
        
        // Check if reaction already exists
        const [existingReactions] = await connection.query(`
            SELECT * FROM post_reactions 
            WHERE post_id = ? AND user_id = ?
        `, [postId, userId]);
        
        let responseData = {};
        let broadcastData = {
            type: 'post_reaction_update',
            postId: parseInt(postId),
            userId: parseInt(userId),
            username: username,
            reaction: reaction
        };
        
        if (existingReactions.length > 0) {
            // If same reaction, remove it (toggle)
            if (existingReactions[0].reaction === reaction) {
                await connection.query(`
                    DELETE FROM post_reactions 
                    WHERE post_id = ? AND user_id = ?
                `, [postId, userId]);
                
                responseData = { 
                    success: true, 
                    message: 'Reaction removed',
                    removed: true
                };
                
                broadcastData.action = 'removed';
            } else {
                // If different reaction, update it
                await connection.query(`
                    UPDATE post_reactions
                    SET reaction = ?, created_at = NOW() 
                    WHERE post_id = ? AND user_id = ?
                `, [reaction, postId, userId]);
                
                responseData = { 
                    success: true, 
                    message: 'Reaction updated',
                    updated: true
                };
                
                broadcastData.action = 'updated';
                broadcastData.previousReaction = existingReactions[0].reaction;
            }
        } else {
            // Add new reaction
            await connection.query(`
                INSERT INTO post_reactions (post_id, user_id, reaction, created_at)
                VALUES (?, ?, ?, NOW())
            `, [postId, userId, reaction]);
            
            responseData = { 
                success: true, 
                message: 'Reaction added',
                added: true
            };
            
            broadcastData.action = 'added';
        }
        
        // Broadcast reaction update via WebSocket to all connected clients
        try {
            const clients = global.websocketServer?.clients || global.wsClients;
            if (clients) {
                // If clients is a Map (from WebSocket server)
                if (typeof clients.forEach === 'function') {
                    clients.forEach((ws, clientUserId) => {
                        if (ws && ws.readyState === 1) { // WebSocket.OPEN = 1
                            try {
                                ws.send(JSON.stringify(broadcastData));
                            } catch (wsError) {
                                console.error(`Error sending reaction update to user ${clientUserId}:`, wsError);
                            }
                        }
                    });
                } 
                // If clients is WebSocket Server clients set
                else if (clients.forEach) {
                    clients.forEach((ws) => {
                        if (ws && ws.readyState === 1) { // WebSocket.OPEN = 1
                            try {
                                ws.send(JSON.stringify(broadcastData));
                            } catch (wsError) {
                                console.error(`Error sending reaction update via WebSocket:`, wsError);
                            }
                        }
                    });
                }
                console.log(`Broadcasted reaction ${broadcastData.action} for post ${postId} by user ${username}`);
            } else {
                console.warn('WebSocket clients not available for reaction broadcasting');
            }
        } catch (broadcastError) {
            console.error('Error broadcasting reaction update:', broadcastError);
            // Don't fail the response if WebSocket broadcast fails
        }
        
        return res.json(responseData);
    } catch (error) {
        console.error('Error handling post reaction:', error);
        res.status(500).json({ success: false, message: 'Error handling post reaction', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// Add a comment to a post
router.post('/posts/:postId/comments', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        const { postId } = req.params;
        const { userId, textContent } = req.body;
        
        if (!userId || !textContent) {
            return res.status(400).json({ success: false, message: 'User ID and comment text are required' });
        }
        
        // Insert the comment
        const [result] = await connection.query(`
            INSERT INTO post_comments (post_id, user_id, text_content, created_at)
            VALUES (?, ?, ?, NOW())
        `, [postId, userId, textContent]);
        
        // Get the created comment with user info
        const [comments] = await connection.query(`
            SELECT c.*, u.username, u.avatar_url as user_avatar
            FROM post_comments c
            JOIN users u ON c.user_id = u.id
            WHERE c.id = ?
        `, [result.insertId]);
        
        const createdComment = comments[0];
        // Normalize created comment author avatar
        if (createdComment.user_avatar) {
            createdComment.user_avatar = `/uploads/${path.basename(createdComment.user_avatar)}`;
        }
        
        // Initialize empty reactions array
        createdComment.reactions = [];
        
        res.status(201).json({ success: true, comment: createdComment });
    } catch (error) {
        console.error('Error adding comment:', error);
        res.status(500).json({ success: false, message: 'Error adding comment', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// Delete a post (only by the post owner)
router.delete('/posts/:postId', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        const { postId } = req.params;
        const { userId } = req.query; // Passing userId as a query parameter for authorization
        
        console.log(`[DELETE /community/posts/${postId}] Request from userId: ${userId}`);
        
        if (!userId) {
            return res.status(400).json({ success: false, message: 'User ID is required' });
        }
        
        // First check if the post exists and belongs to the user
        const [posts] = await connection.query(`
            SELECT * FROM posts 
            WHERE id = ? AND user_id = ?
        `, [postId, userId]);
        
        if (posts.length === 0) {
            return res.status(403).json({ 
                success: false, 
                message: 'Post not found or you do not have permission to delete it'
            });
        }
        
        // Delete related data first (comments, reactions, etc.) to maintain referential integrity
        await connection.query('DELETE FROM post_comments WHERE post_id = ?', [postId]);
        await connection.query('DELETE FROM post_reactions WHERE post_id = ?', [postId]);
        
        // Delete the post itself
        await connection.query('DELETE FROM posts WHERE id = ?', [postId]);
        
        res.json({ success: true, message: 'Post deleted successfully' });
    } catch (error) {
        console.error('Error deleting post:', error);
        res.status(500).json({ success: false, message: 'Error deleting post', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

// Add a reaction to a comment
router.post('/comments/:commentId/reactions', async (req, res) => {
    let connection;
    try {
        connection = await db.getConnection();
        
        const { commentId } = req.params;
        const { userId, reaction } = req.body;
        
        if (!userId || !reaction) {
            return res.status(400).json({ success: false, message: 'User ID and reaction are required' });
        }
        
        // Check if reaction already exists
        const [existingReactions] = await connection.query(`
            SELECT * FROM comment_reactions 
            WHERE comment_id = ? AND user_id = ?
        `, [commentId, userId]);
        
        if (existingReactions.length > 0) {
            // If same reaction, remove it (toggle)
            if (existingReactions[0].reaction === reaction) {
                await connection.query(`
                    DELETE FROM comment_reactions 
                    WHERE comment_id = ? AND user_id = ?
                `, [commentId, userId]);
                
                return res.json({ 
                    success: true, 
                    message: 'Reaction removed',
                    removed: true
                });
            } else {
                // If different reaction, update it
                await connection.query(`
                    UPDATE comment_reactions
                    SET reaction = ?, created_at = NOW() 
                    WHERE comment_id = ? AND user_id = ?
                `, [reaction, commentId, userId]);
                
                return res.json({ 
                    success: true, 
                    message: 'Reaction updated',
                    updated: true
                });
            }
        } else {
            // Add new reaction
            await connection.query(`
                INSERT INTO comment_reactions (comment_id, user_id, reaction, created_at)
                VALUES (?, ?, ?, NOW())
            `, [commentId, userId, reaction]);
            
            return res.json({ 
                success: true, 
                message: 'Reaction added',
                added: true
            });
        }
    } catch (error) {
        console.error('Error handling comment reaction:', error);
        res.status(500).json({ success: false, message: 'Error handling comment reaction', error: error.message });
    } finally {
        if (connection) connection.release();
    }
});

module.exports = router; 