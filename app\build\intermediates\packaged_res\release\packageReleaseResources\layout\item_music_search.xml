<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp">

        <ImageView
            android:id="@+id/imageViewCoverArt"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@tools:sample/avatars" />

        <TextView
            android:id="@+id/textViewSongTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/buttonPlayPreview"
            app:layout_constraintStart_toEndOf="@+id/imageViewCoverArt"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Título de la canción" />

        <TextView
            android:id="@+id/textViewArtistName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/buttonPlayPreview"
            app:layout_constraintStart_toEndOf="@+id/imageViewCoverArt"
            app:layout_constraintTop_toBottomOf="@+id/textViewSongTitle"
            tools:text="Nombre del artista" />

        <ImageButton
            android:id="@+id/buttonPlayPreview"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Reproducir previsualización"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/buttonSelectSong"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_play" />

        <TextView
            android:id="@+id/buttonSelectSong"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/button_rounded_accent"
            android:padding="8dp"
            android:text="Añadir"
            android:textColor="@android:color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressBarPreview"
            style="?android:attr/progressBarStyleSmall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/buttonPlayPreview"
            app:layout_constraintEnd_toEndOf="@+id/buttonPlayPreview"
            app:layout_constraintStart_toStartOf="@+id/buttonPlayPreview"
            app:layout_constraintTop_toTopOf="@+id/buttonPlayPreview" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 