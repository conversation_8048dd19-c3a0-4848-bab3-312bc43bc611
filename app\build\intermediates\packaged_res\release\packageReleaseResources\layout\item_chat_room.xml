<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="#1A1A3A">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/imageViewRoomIcon"
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:src="@drawable/default_avatar"
            app:civ_border_width="2dp"
            app:civ_border_color="@color/neon_blue"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <TextView
            android:id="@+id/textViewRoomName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/white"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/imageViewRoomIcon"
            app:layout_constraintEnd_toStartOf="@id/textViewUnreadCount"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Game Night Room" />

        <TextView
            android:id="@+id/textViewRoomDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="2dp"
            android:textSize="14sp"
            android:textColor="#B0B0B0"
            android:maxLines="1"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:singleLine="true"
            app:layout_constraintStart_toEndOf="@id/imageViewRoomIcon"
            app:layout_constraintEnd_toStartOf="@id/textViewLastMessageTime"
            app:layout_constraintTop_toBottomOf="@id/textViewRoomName"
            tools:text="A place to chat about gaming" />

        <TextView
            android:id="@+id/textViewLastMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="4dp"
            android:textSize="14sp"
            android:textColor="#D0D0D0"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/imageViewRoomIcon"
            app:layout_constraintEnd_toStartOf="@id/textViewLastMessageTime"
            app:layout_constraintTop_toBottomOf="@id/textViewRoomDescription"
            tools:text="John: Hey everyone! Excited for tonight's event?" />

        <TextView
            android:id="@+id/textViewLastMessageTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="#808080"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="10:30" />

        <TextView
            android:id="@+id/textViewMemberCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="#808080"
            android:drawableStart="@android:drawable/ic_menu_myplaces"
            android:drawablePadding="4dp"
            android:gravity="center_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textViewLastMessageTime"
            tools:text="24" />

        <!-- Unread Message Counter -->
        <TextView
            android:id="@+id/textViewUnreadCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/unread_counter_background"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:paddingHorizontal="6dp"
            android:paddingVertical="2dp"
            android:minWidth="28dp"
            android:gravity="center"
            android:visibility="gone"
            android:layout_marginEnd="4dp"
            app:layout_constraintEnd_toStartOf="@id/textViewLastMessageTime"
            app:layout_constraintTop_toTopOf="@id/textViewRoomName"
            app:layout_constraintBottom_toBottomOf="@id/textViewRoomName"
            tools:text="+100"
            tools:visibility="visible" />

        <View
            android:id="@+id/viewUnreadIndicator"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:background="@drawable/unread_indicator"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/textViewUnreadCount"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="8dp"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 