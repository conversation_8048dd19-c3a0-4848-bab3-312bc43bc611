<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Outer glow -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#3000F3FF" />
            <size android:width="140dp" android:height="140dp" />
        </shape>
    </item>
    <!-- Main ring -->
    <item android:left="5dp" android:top="5dp" android:right="5dp" android:bottom="5dp">
        <shape android:shape="oval">
            <stroke android:width="3dp" android:color="@color/neon_blue" />
            <size android:width="130dp" android:height="130dp" />
        </shape>
    </item>
    <!-- Inner glow -->
    <item android:left="15dp" android:top="15dp" android:right="15dp" android:bottom="15dp">
        <shape android:shape="oval">
            <stroke android:width="1dp" android:color="#80FF00E4" />
            <size android:width="110dp" android:height="110dp" />
        </shape>
    </item>
    <!-- Center -->
    <item android:left="20dp" android:top="20dp" android:right="20dp" android:bottom="20dp">
        <shape android:shape="oval">
            <solid android:color="#101033" />
            <size android:width="100dp" android:height="100dp" />
        </shape>
    </item>
</layer-list> 