<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <ImageView
            android:id="@+id/imageViewAvatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:contentDescription="Avatar del usuario"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/default_avatar" />

        <TextView
            android:id="@+id/textViewUsername"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/imageViewAvatar"
            app:layout_constraintEnd_toStartOf="@id/buttonUnblock"
            app:layout_constraintStart_toEndOf="@id/imageViewAvatar"
            app:layout_constraintTop_toTopOf="@id/imageViewAvatar"
            tools:text="Nombre de usuario" />

        <Button
            android:id="@+id/buttonUnblock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Desbloquear"
            android:textSize="12sp"
            android:backgroundTint="#E91E63"
            android:textColor="@android:color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 