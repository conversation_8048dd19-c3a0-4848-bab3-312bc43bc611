<dependencies>
  <compile
      roots="com.vanniktech:emoji-material:0.8.0@aar,com.google.android.material:material:1.12.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,com.vanniktech:emoji:0.8.0@aar,androidx.appcompat:appcompat:1.7.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,com.google.android.gms:play-services-maps:18.2.0@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,com.google.android.gms:play-services-location:21.0.1@aar,com.google.firebase:firebase-messaging-ktx:23.4.1@aar,com.google.firebase:firebase-messaging:23.4.1@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.firebase:firebase-analytics-ktx:21.5.1@aar,com.google.firebase:firebase-analytics:21.5.1@aar,com.google.android.gms:play-services-measurement-api:21.5.1@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-common:20.4.2@aar,com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.emoji2:emoji2-bundled:1.4.0@aar,androidx.emoji2:emoji2-views-helper:1.4.0@aar,androidx.emoji2:emoji2-views:1.4.0@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,com.google.android.gms:play-services-measurement:21.5.1@aar,com.google.android.gms:play-services-measurement-sdk:21.5.1@aar,com.google.android.gms:play-services-measurement-impl:21.5.1@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.compose.material3:material3-android:1.2.1@aar,androidx.compose.ui:ui-util-android:1.6.8@aar,androidx.compose.ui:ui-unit-android:1.6.8@aar,androidx.compose.ui:ui-text-android:1.6.8@aar,androidx.compose.foundation:foundation-layout-android:1.6.8@aar,androidx.compose.material:material-icons-core-android:1.6.8@aar,androidx.compose.material:material-ripple-android:1.6.8@aar,androidx.compose.foundation:foundation-android:1.6.8@aar,androidx.compose.animation:animation-core-android:1.6.8@aar,androidx.compose.animation:animation-android:1.6.8@aar,androidx.compose.ui:ui-geometry-android:1.6.8@aar,androidx.compose.ui:ui-graphics-android:1.6.8@aar,androidx.compose.ui:ui-android:1.6.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar,androidx.compose.runtime:runtime-saveable-android:1.6.8@aar,androidx.compose.runtime:runtime-android:1.6.8@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.1.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:21.5.1@aar,com.google.android.gms:play-services-measurement-base:21.5.1@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity:1.9.0@aar,androidx.activity:activity-compose:1.9.0@aar,androidx.activity:activity-ktx:1.9.0@aar,androidx.core:core-ktx:1.13.1@aar,com.google.android.flexbox:flexbox:3.0.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.media3:media3-exoplayer:1.3.0@aar,androidx.media3:media3-common:1.3.0@aar,androidx.annotation:annotation-experimental:1.4.0@aar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp-urlconnection:4.12.0@jar,com.squareup.okhttp3:mockwebserver:4.12.0@jar,com.squareup.okhttp3:okhttp-sse:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okhttp3:okhttp-tls:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,com.google.firebase:firebase-components:17.1.5@aar,androidx.interpolator:interpolator:1.0.0@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.firebase:firebase-datatransport:18.1.7@aar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,androidx.collection:collection-jvm:1.4.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.annotation:annotation-jvm:1.8.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.google.code.gson:gson:2.10.1@jar,com.sun.mail:android-mail:1.6.7@jar,com.sun.mail:android-activation:1.6.7@jar,com.github.chrisbanes:PhotoView:2.3.0@aar,androidx.media3:media3-exoplayer-dash:1.3.0@aar,androidx.media3:media3-ui:1.3.0@aar,de.hdodenhof:circleimageview:3.1.0@aar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:guava:32.1.3-android@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.errorprone:error_prone_annotations:2.21.1@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-core:1.3@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.37.0@jar,androidx.media3:media3-container:1.3.0@aar,androidx.media3:media3-database:1.3.0@aar,androidx.media3:media3-datasource:1.3.0@aar,androidx.media3:media3-decoder:1.3.0@aar,androidx.media3:media3-extractor:1.3.0@aar,com.google.firebase:firebase-annotations:16.2.0@jar,javax.inject:javax.inject:1@jar,com.google.j2objc:j2objc-annotations:2.8@jar">
    <dependency
        name="com.vanniktech:emoji-material:0.8.0@aar"
        simpleName="com.vanniktech:emoji-material"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="com.vanniktech:emoji:0.8.0@aar"
        simpleName="com.vanniktech:emoji"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.android.gms:play-services-maps:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.0.1@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.firebase:firebase-messaging-ktx:23.4.1@aar"
        simpleName="com.google.firebase:firebase-messaging-ktx"/>
    <dependency
        name="com.google.firebase:firebase-messaging:23.4.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-analytics-ktx:21.5.1@aar"
        simpleName="com.google.firebase:firebase-analytics-ktx"/>
    <dependency
        name="com.google.firebase:firebase-analytics:21.5.1@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0@aar"
        simpleName="com.pierfrancescosoffritti.androidyoutubeplayer:core"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.emoji2:emoji2-bundled:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-bundled"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2-views:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.2.1@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.6.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.6.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.6.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.6.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.6.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.6.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-compose:1.9.0@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="com.google.android.flexbox:flexbox:3.0.0@aar"
        simpleName="com.google.android.flexbox:flexbox"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.3.0@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.media3:media3-common:1.3.0@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:mockwebserver:4.12.0@jar"
        simpleName="com.squareup.okhttp3:mockwebserver"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-sse:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-sse"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-tls:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-tls"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.1.7@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.sun.mail:android-mail:1.6.7@jar"
        simpleName="com.sun.mail:android-mail"/>
    <dependency
        name="com.sun.mail:android-activation:1.6.7@jar"
        simpleName="com.sun.mail:android-activation"/>
    <dependency
        name="com.github.chrisbanes:PhotoView:2.3.0@aar"
        simpleName="com.github.chrisbanes:PhotoView"/>
    <dependency
        name="androidx.media3:media3-exoplayer-dash:1.3.0@aar"
        simpleName="androidx.media3:media3-exoplayer-dash"/>
    <dependency
        name="androidx.media3:media3-ui:1.3.0@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="de.hdodenhof:circleimageview:3.1.0@aar"
        simpleName="de.hdodenhof:circleimageview"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.21.1@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="androidx.media3:media3-container:1.3.0@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-database:1.3.0@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-datasource:1.3.0@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.3.0@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-extractor:1.3.0@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="com.vanniktech:emoji-material:0.8.0@aar,com.google.android.material:material:1.12.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.github.chrisbanes:PhotoView:2.3.0@aar,androidx.appcompat:appcompat-resources:1.7.0@aar,com.vanniktech:emoji:0.8.0@aar,androidx.appcompat:appcompat:1.7.0@aar,androidx.media3:media3-exoplayer-dash:1.3.0@aar,androidx.media3:media3-exoplayer:1.3.0@aar,androidx.media3:media3-extractor:1.3.0@aar,androidx.media3:media3-container:1.3.0@aar,androidx.media3:media3-datasource:1.3.0@aar,androidx.media3:media3-decoder:1.3.0@aar,androidx.media3:media3-database:1.3.0@aar,androidx.media3:media3-common:1.3.0@aar,androidx.media3:media3-ui:1.3.0@aar,androidx.viewpager2:viewpager2:1.1.0-beta02@aar,androidx.recyclerview:recyclerview:1.3.2@aar,androidx.emoji2:emoji2-bundled:1.4.0@aar,androidx.emoji2:emoji2-views-helper:1.4.0@aar,androidx.emoji2:emoji2-views:1.4.0@aar,com.github.bumptech.glide:glide:4.16.0@aar,com.google.android.gms:play-services-maps:18.2.0@aar,com.google.android.gms:play-services-location:21.0.1@aar,com.google.firebase:firebase-messaging-ktx:23.4.1@aar,com.google.firebase:firebase-messaging:23.4.1@aar,com.google.android.gms:play-services-base:18.1.0@aar,com.google.firebase:firebase-analytics-ktx:21.5.1@aar,com.google.firebase:firebase-analytics:21.5.1@aar,com.google.android.gms:play-services-measurement-api:21.5.1@aar,com.google.firebase:firebase-installations:17.2.0@aar,com.google.firebase:firebase-common-ktx:20.4.2@aar,com.google.firebase:firebase-common:20.4.2@aar,com.google.firebase:firebase-iid-interop:17.1.0@aar,com.google.android.gms:play-services-cloud-messaging:17.1.0@aar,com.google.firebase:firebase-installations-interop:17.1.1@aar,com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0@aar,androidx.compose.material3:material3-android:1.2.1@aar,androidx.transition:transition:1.5.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,com.google.android.gms:play-services-measurement:21.5.1@aar,com.google.android.gms:play-services-measurement-sdk:21.5.1@aar,com.google.android.gms:play-services-measurement-impl:21.5.1@aar,com.google.android.gms:play-services-stats:17.0.2@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar,androidx.lifecycle:lifecycle-livedata:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar,androidx.lifecycle:lifecycle-common-java8:2.8.3@jar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar,androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar,androidx.lifecycle:lifecycle-process:2.8.3@aar,androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar,androidx.compose.animation:animation-core-android:1.6.8@aar,androidx.compose.material:material-icons-core-android:1.6.8@aar,androidx.compose.material:material-ripple-android:1.6.8@aar,androidx.compose.animation:animation-android:1.6.8@aar,androidx.compose.foundation:foundation-layout-android:1.6.8@aar,androidx.compose.foundation:foundation-android:1.6.8@aar,androidx.compose.ui:ui-unit-android:1.6.8@aar,androidx.compose.ui:ui-geometry-android:1.6.8@aar,androidx.compose.ui:ui-util-android:1.6.8@aar,androidx.compose.ui:ui-text-android:1.6.8@aar,androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar,androidx.compose.ui:ui-graphics-android:1.6.8@aar,androidx.compose.runtime:runtime-saveable-android:1.6.8@aar,androidx.compose.runtime:runtime-android:1.6.8@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar,com.google.android.gms:play-services-tasks:18.0.2@aar,com.google.firebase:firebase-measurement-connector:19.0.0@aar,com.google.android.gms:play-services-ads-identifier:18.0.0@aar,com.google.android.gms:play-services-measurement-sdk-api:21.5.1@aar,com.google.android.gms:play-services-measurement-base:21.5.1@aar,com.google.android.gms:play-services-basement:18.1.0@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity:1.9.0@aar,androidx.activity:activity-ktx:1.9.0@aar,androidx.activity:activity-compose:1.9.0@aar,androidx.compose.ui:ui-android:1.6.8@aar,androidx.emoji2:emoji2:1.4.0@aar,androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.media:media:1.7.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.autofill:autofill:1.0.0@aar,androidx.core:core:1.13.1@aar,androidx.core:core:1.13.1@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.13.1@aar,com.google.android.flexbox:flexbox:3.0.0@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.profileinstaller:profileinstaller:1.3.1@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.interpolator:interpolator:1.0.0@aar,com.google.firebase:firebase-components:17.1.5@aar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,com.google.firebase:firebase-datatransport:18.1.7@aar,com.google.android.datatransport:transport-backend-cct:3.1.8@aar,com.google.android.datatransport:transport-runtime:3.1.8@aar,com.google.firebase:firebase-encoders-proto:16.0.0@jar,com.google.android.datatransport:transport-api:3.1.0@aar,com.google.firebase:firebase-encoders-json:18.0.0@aar,com.google.firebase:firebase-encoders:17.0.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.collection:collection-ktx:1.4.0@jar,androidx.collection:collection-jvm:1.4.0@jar,androidx.annotation:annotation-jvm:1.8.0@jar,com.squareup.okhttp3:okhttp-tls:4.12.0@jar,com.squareup.okhttp3:mockwebserver:4.12.0@jar,com.squareup.okhttp3:okhttp-sse:4.12.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp-urlconnection:4.12.0@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,com.google.code.gson:gson:2.10.1@jar,com.sun.mail:android-mail:1.6.7@jar,com.sun.mail:android-activation:1.6.7@jar,de.hdodenhof:circleimageview:3.1.0@aar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:guava:32.1.3-android@jar,com.google.errorprone:error_prone_annotations:2.21.1@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,junit:junit:4.13.2@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,org.hamcrest:hamcrest-core:1.3@jar,com.google.firebase:firebase-annotations:16.2.0@jar,com.google.guava:failureaccess:1.0.1@jar,javax.inject:javax.inject:1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.37.0@jar">
    <dependency
        name="com.vanniktech:emoji-material:0.8.0@aar"
        simpleName="com.vanniktech:emoji-material"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.github.chrisbanes:PhotoView:2.3.0@aar"
        simpleName="com.github.chrisbanes:PhotoView"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="com.vanniktech:emoji:0.8.0@aar"
        simpleName="com.vanniktech:emoji"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.0@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.media3:media3-exoplayer-dash:1.3.0@aar"
        simpleName="androidx.media3:media3-exoplayer-dash"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.3.0@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.media3:media3-extractor:1.3.0@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.3.0@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-datasource:1.3.0@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.3.0@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.3.0@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.3.0@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-ui:1.3.0@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.1.0-beta02@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.2@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.emoji2:emoji2-bundled:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-bundled"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2-views:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2-views"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="com.google.android.gms:play-services-maps:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-maps"/>
    <dependency
        name="com.google.android.gms:play-services-location:21.0.1@aar"
        simpleName="com.google.android.gms:play-services-location"/>
    <dependency
        name="com.google.firebase:firebase-messaging-ktx:23.4.1@aar"
        simpleName="com.google.firebase:firebase-messaging-ktx"/>
    <dependency
        name="com.google.firebase:firebase-messaging:23.4.1@aar"
        simpleName="com.google.firebase:firebase-messaging"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.firebase:firebase-analytics-ktx:21.5.1@aar"
        simpleName="com.google.firebase:firebase-analytics-ktx"/>
    <dependency
        name="com.google.firebase:firebase-analytics:21.5.1@aar"
        simpleName="com.google.firebase:firebase-analytics"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-api:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-api"/>
    <dependency
        name="com.google.firebase:firebase-installations:17.2.0@aar"
        simpleName="com.google.firebase:firebase-installations"/>
    <dependency
        name="com.google.firebase:firebase-common-ktx:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common-ktx"/>
    <dependency
        name="com.google.firebase:firebase-common:20.4.2@aar"
        simpleName="com.google.firebase:firebase-common"/>
    <dependency
        name="com.google.firebase:firebase-iid-interop:17.1.0@aar"
        simpleName="com.google.firebase:firebase-iid-interop"/>
    <dependency
        name="com.google.android.gms:play-services-cloud-messaging:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-cloud-messaging"/>
    <dependency
        name="com.google.firebase:firebase-installations-interop:17.1.1@aar"
        simpleName="com.google.firebase:firebase-installations-interop"/>
    <dependency
        name="com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0@aar"
        simpleName="com.pierfrancescosoffritti.androidyoutubeplayer:core"/>
    <dependency
        name="androidx.compose.material3:material3-android:1.2.1@aar"
        simpleName="androidx.compose.material3:material3-android"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="com.google.android.gms:play-services-measurement:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-impl:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-impl"/>
    <dependency
        name="com.google.android.gms:play-services-stats:17.0.2@aar"
        simpleName="com.google.android.gms:play-services-stats"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-jvm:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-jvm"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-android"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common-java8:2.8.3@jar"
        simpleName="androidx.lifecycle:lifecycle-common-java8"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx-android"/>
    <dependency
        name="androidx.compose.animation:animation-core-android:1.6.8@aar"
        simpleName="androidx.compose.animation:animation-core-android"/>
    <dependency
        name="androidx.compose.material:material-icons-core-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-icons-core-android"/>
    <dependency
        name="androidx.compose.material:material-ripple-android:1.6.8@aar"
        simpleName="androidx.compose.material:material-ripple-android"/>
    <dependency
        name="androidx.compose.animation:animation-android:1.6.8@aar"
        simpleName="androidx.compose.animation:animation-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-layout-android:1.6.8@aar"
        simpleName="androidx.compose.foundation:foundation-layout-android"/>
    <dependency
        name="androidx.compose.foundation:foundation-android:1.6.8@aar"
        simpleName="androidx.compose.foundation:foundation-android"/>
    <dependency
        name="androidx.compose.ui:ui-unit-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-unit-android"/>
    <dependency
        name="androidx.compose.ui:ui-geometry-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-geometry-android"/>
    <dependency
        name="androidx.compose.ui:ui-util-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-util-android"/>
    <dependency
        name="androidx.compose.ui:ui-text-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-text-android"/>
    <dependency
        name="androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-tooling-preview-android"/>
    <dependency
        name="androidx.compose.ui:ui-graphics-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-graphics-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-saveable-android:1.6.8@aar"
        simpleName="androidx.compose.runtime:runtime-saveable-android"/>
    <dependency
        name="androidx.compose.runtime:runtime-android:1.6.8@aar"
        simpleName="androidx.compose.runtime:runtime-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices-java"/>
    <dependency
        name="androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05@aar"
        simpleName="androidx.privacysandbox.ads:ads-adservices"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-play-services"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.0.2@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.firebase:firebase-measurement-connector:19.0.0@aar"
        simpleName="com.google.firebase:firebase-measurement-connector"/>
    <dependency
        name="com.google.android.gms:play-services-ads-identifier:18.0.0@aar"
        simpleName="com.google.android.gms:play-services-ads-identifier"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-sdk-api:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-sdk-api"/>
    <dependency
        name="com.google.android.gms:play-services-measurement-base:21.5.1@aar"
        simpleName="com.google.android.gms:play-services-measurement-base"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.1.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.9.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.9.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity-compose:1.9.0@aar"
        simpleName="androidx.activity:activity-compose"/>
    <dependency
        name="androidx.compose.ui:ui-android:1.6.8@aar"
        simpleName="androidx.compose.ui:ui-android"/>
    <dependency
        name="androidx.emoji2:emoji2:1.4.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
        simpleName="androidx.swiperefreshlayout:swiperefreshlayout"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.autofill:autofill:1.0.0@aar"
        simpleName="androidx.autofill:autofill"/>
    <dependency
        name="androidx.core:core:1.13.1@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.13.1@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="com.google.android.flexbox:flexbox:3.0.0@aar"
        simpleName="com.google.android.flexbox:flexbox"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="com.google.firebase:firebase-components:17.1.5@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="com.google.firebase:firebase-datatransport:18.1.7@aar"
        simpleName="com.google.firebase:firebase-datatransport"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:3.1.8@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.firebase:firebase-encoders-proto:16.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders-proto"/>
    <dependency
        name="com.google.android.datatransport:transport-api:3.1.0@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:18.0.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:17.0.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.collection:collection-ktx:1.4.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="androidx.collection:collection-jvm:1.4.0@jar"
        simpleName="androidx.collection:collection-jvm"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-tls:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-tls"/>
    <dependency
        name="com.squareup.okhttp3:mockwebserver:4.12.0@jar"
        simpleName="com.squareup.okhttp3:mockwebserver"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-sse:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-sse"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-urlconnection:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-urlconnection"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.sun.mail:android-mail:1.6.7@jar"
        simpleName="com.sun.mail:android-mail"/>
    <dependency
        name="com.sun.mail:android-activation:1.6.7@jar"
        simpleName="com.sun.mail:android-activation"/>
    <dependency
        name="de.hdodenhof:circleimageview:3.1.0@aar"
        simpleName="de.hdodenhof:circleimageview"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.21.1@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.2.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
  </package>
</dependencies>
