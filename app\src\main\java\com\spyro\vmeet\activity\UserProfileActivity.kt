package com.spyro.vmeet.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.spyro.vmeet.R
import com.spyro.vmeet.data.community.Post
import com.spyro.vmeet.ui.community.PostAdapter
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone
import kotlin.concurrent.thread

class UserProfileActivity : AppCompatActivity() {
    // ...
    private fun loadUserLevelAndXP(userId: Int) {
        progressBarUserLevel.visibility = View.VISIBLE
        textViewUserLevel.text = "Nivel --"
        textViewUserXP.text = "XP: --"
        progressBarUserLevel.progress = 0

        thread {
            try {
                // Fetch profile data including level_info
                val url = URL("$API_URL/profile/profile/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                // No auth needed for profile endpoint

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Level JSON response: $response")
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val userData = jsonResponse.optJSONObject("user")
                        val levelInfo = userData?.optJSONObject("level_info")
                        if (levelInfo != null) {
                            val level = levelInfo.optInt("level", 1)
                            val totalXP = levelInfo.optInt("total_xp", 0)
                            val currentLevelXP = levelInfo.optInt("current_level_xp", 0)
                            val xpForNextLevel = levelInfo.optInt("xp_for_next_level", 100)
                            val progressPercent = levelInfo.optInt("progress_percentage", 0)
                            Log.d(TAG, "Parsed levelInfo: level=$level currentLevelXP=$currentLevelXP xpForNextLevel=$xpForNextLevel totalXP=$totalXP")

                            runOnUiThread {
                                textViewUserLevel.text = "Nivel $level"
                                textViewUserXP.text = "XP: $currentLevelXP / $xpForNextLevel (Total: $totalXP)"
                                progressBarUserLevel.max = xpForNextLevel
                                progressBarUserLevel.progress = currentLevelXP
                                progressBarUserLevel.visibility = View.VISIBLE
                            }
                        } else {
                            Log.e(TAG, "level_info is null in JSON: $response")
                        }
                    } else {
                        val message = jsonResponse.optString("message", "No se pudo cargar el nivel del usuario")
                        runOnUiThread {
                            textViewUserLevel.text = "Nivel --"
                            textViewUserXP.text = "XP: --"
                            progressBarUserLevel.progress = 0
                            progressBarUserLevel.visibility = View.VISIBLE
                            Toast.makeText(this@UserProfileActivity, message, Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    runOnUiThread {
                        textViewUserLevel.text = "Nivel --"
                        textViewUserXP.text = "XP: --"
                        progressBarUserLevel.progress = 0
                        progressBarUserLevel.visibility = View.VISIBLE
                        Toast.makeText(this@UserProfileActivity, "Error del servidor: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }
                connection.disconnect()
            } catch (e: Exception) {
                runOnUiThread {
                    textViewUserLevel.text = "Nivel --"
                    textViewUserXP.text = "XP: --"
                    progressBarUserLevel.progress = 0
                    progressBarUserLevel.visibility = View.VISIBLE
                    Toast.makeText(this@UserProfileActivity, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }


    private lateinit var imageViewUserAvatar: ImageView
    private lateinit var textViewUsername: TextView
    private lateinit var textViewUserInfo: TextView
    private lateinit var textViewAdminBadge: TextView
    private lateinit var textViewVerifiedBadge: TextView
    private lateinit var recyclerViewUserPosts: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var postAdapter: PostAdapter
    private lateinit var buttonSendMessage: com.google.android.material.button.MaterialButton

    // XP/Nivel UI
    private lateinit var textViewUserLevel: TextView
    private lateinit var progressBarUserLevel: ProgressBar
    private lateinit var textViewUserXP: TextView

    private val API_URL = "http://77.110.116.89:3000"
    private val TAG = "UserProfileActivity"

    companion object {
        private const val EXTRA_USER_ID = "extra_user_id"
        private const val EXTRA_USERNAME = "extra_username"

        fun newIntent(context: Context, userId: Int, username: String?): Intent {
            return Intent(context, UserProfileActivity::class.java).apply {
                putExtra(EXTRA_USER_ID, userId)
                putExtra(EXTRA_USERNAME, username)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_profile)

        // Setup toolbar with back button
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
        }

        // Initialize views
        imageViewUserAvatar = findViewById(R.id.imageViewProfileAvatar)
        textViewUsername = findViewById(R.id.textViewProfileUsername)
        textViewUserInfo = findViewById(R.id.textViewProfileInfo)
        textViewAdminBadge = findViewById(R.id.textViewAdminBadge)
        textViewVerifiedBadge = findViewById(R.id.textViewVerifiedBadge)
        recyclerViewUserPosts = findViewById(R.id.recyclerViewUserPosts)
        progressBar = findViewById(R.id.progressBarProfile)
        buttonSendMessage = findViewById(R.id.buttonSendMessage)
        // XP/Nivel UI
        textViewUserLevel = findViewById(R.id.textViewUserLevel)
        progressBarUserLevel = findViewById(R.id.progressBarUserLevel)
        textViewUserXP = findViewById(R.id.textViewUserXP)

        // Get user ID from intent
        val userId = intent.getIntExtra(EXTRA_USER_ID, -1)
        val username = intent.getStringExtra(EXTRA_USERNAME)

        if (userId == -1) {
            Toast.makeText(this, "Error: User ID not provided", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        // Set initial username if provided
        username?.let {
            textViewUsername.text = it
            supportActionBar?.title = it
        }

        // Setup RecyclerView for user posts
        setupRecyclerView()

        // Setup send message button
        setupSendMessageButton(userId, username)

        // Load user data
        loadUserProfile(userId)

        // Load user XP y nivel
        loadUserLevelAndXP(userId)

        // Load user posts
        loadUserPosts(userId)
    }

    private fun setupRecyclerView() {
        postAdapter = PostAdapter(mutableListOf())
        recyclerViewUserPosts.apply {
            layoutManager = LinearLayoutManager(this@UserProfileActivity)
            adapter = postAdapter
        }

        // Setup click listeners
        postAdapter.onLikeClickListener = { post ->
            // Implement like functionality
            Toast.makeText(this, "Feature coming soon", Toast.LENGTH_SHORT).show()
        }

        postAdapter.onCommentClickListener = { post ->
            // Navigate to comments
            Toast.makeText(this, "Feature coming soon", Toast.LENGTH_SHORT).show()
        }

        postAdapter.onImageClickListener = { post ->
            // Handle image click
            Toast.makeText(this, "Feature coming soon", Toast.LENGTH_SHORT).show()
        }

        // The user profile itself doesn't need avatar click navigation
        postAdapter.onUserClickListener = null
    }

    private fun loadUserProfile(userId: Int) {
        progressBar.visibility = View.VISIBLE

        thread {
            try {
                val url = URL("$API_URL/profile/profile/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "User profile response: $response")

                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val userData = jsonResponse.optJSONObject("user")

                        runOnUiThread {
                            if (userData != null) {
                                // Update UI with user data
                                val username = userData.optString("username", "Unknown User")
                                val avatarUrl = userData.optString("avatar_url", "")
                                val email = userData.optString("email", "")
                                val bio = userData.optString("bio", "")
                                val role = userData.optString("role", "")
                                val isAdmin = role == "admin"

                                textViewUsername.text = username
                                supportActionBar?.title = username

                                // Set admin badge
                                if (isAdmin) {
                                    textViewAdminBadge.visibility = View.VISIBLE

                                    // Apply red color to admin username
                                    textViewUsername.setTextColor(getColor(android.R.color.holo_red_light))
                                    textViewUsername.background = null

                                    // Set tag to indicate admin status for reference
                                    textViewUsername.tag = "admin"

                                    // For admins, always show the send message button
                                    val currentUserId = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE).getInt("USER_ID", -1)
                                    if (currentUserId != -1 && currentUserId != userData.optInt("id", -1)) {
                                        buttonSendMessage.visibility = View.VISIBLE
                                        buttonSendMessage.setOnClickListener {
                                            // Create a direct chat with the admin
                                            createDirectChat(userData.optInt("id", -1), username)
                                        }
                                    }
                                } else {
                                    textViewAdminBadge.visibility = View.GONE
                                    textViewUsername.background = null
                                    textViewUsername.setTextColor(getColor(R.color.cyberpunk_text_primary))
                                    textViewUsername.tag = null
                                }

                                // Check verification status and show badge
                                checkVerificationStatus(userData.optInt("id", -1))

                                // Additional user info - make sure "null" doesn't appear
                                if (!bio.isNullOrEmpty() && bio != "null") {
                                    textViewUserInfo.text = bio
                                } else if (!email.isNullOrEmpty() && email != "null") {
                                    textViewUserInfo.text = email
                                } else {
                                    textViewUserInfo.text = "No hay información del usuario"
                                }

                                // Load avatar
                                if (avatarUrl.isNotEmpty() && avatarUrl != "null") {
                                    val fullAvatarUrl = if (avatarUrl.startsWith("http")) {
                                        avatarUrl
                                    } else {
                                        "$API_URL$avatarUrl"
                                    }

                                    Glide.with(this)
                                        .load(fullAvatarUrl)
                                        .placeholder(R.drawable.default_avatar)
                                        .error(R.drawable.default_avatar)
                                        .circleCrop()
                                        .into(imageViewUserAvatar)
                                } else {
                                    // Use default avatar
                                    Glide.with(this)
                                        .load(R.drawable.default_avatar)
                                        .circleCrop()
                                        .into(imageViewUserAvatar)
                                }
                            }
                            progressBar.visibility = View.GONE
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Failed to load user profile")
                        showError(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error loading user: $responseCode, Body: $errorBody")
                    showError("Error del servidor: $responseCode")
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error loading user profile", e)
                showError("Error de red: ${e.message}")
            }
        }
    }

    private fun loadUserPosts(userId: Int) {
        thread {
            try {
                val url = URL("$API_URL/community/posts/user/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "User posts response: $response")

                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val jsonPosts = jsonResponse.getJSONArray("posts")
                        val loadedPosts = mutableListOf<Post>()

                        for (i in 0 until jsonPosts.length()) {
                            val jsonPost = jsonPosts.getJSONObject(i)

                            // Extract post data (similar to CommunityViewModel)
                            // This is simplified and should match your Post object structure
                            loadedPosts.add(
                                Post(
                                    id = jsonPost.optString("id", ""),
                                    userId = jsonPost.optInt("user_id", 0),
                                    username = jsonPost.optString("username", "Usuario"),
                                    userAvatar = jsonPost.optString("user_avatar", "").takeIf { it.isNotEmpty() },
                                    timestamp = parseTimestamp(jsonPost.optString("created_at", "")),
                                    textContent = jsonPost.optString("text_content", ""),
                                    imageUrl = jsonPost.optString("image_url", "").takeIf { it.isNotEmpty() },
                                    gifUrl = jsonPost.optString("gif_url", "").takeIf { it.isNotEmpty() },
                                    gifPreviewUrl = jsonPost.optString("gif_preview_url", "").takeIf { it.isNotEmpty() },
                                    voiceNoteUrl = jsonPost.optString("voice_note_url", "").takeIf { it.isNotEmpty() },
                                    voiceNoteDuration = jsonPost.optInt("voice_note_duration", 0).takeIf { it > 0 },
                                    commentCount = jsonPost.optInt("comment_count", 0),
                                    reactions = mutableListOf() // Parse reactions if needed
                                )
                            )
                        }

                        // Sort posts by timestamp (newest first)
                        loadedPosts.sortByDescending { it.timestamp }

                        runOnUiThread {
                            // Update adapter with posts
                            postAdapter.updatePosts(loadedPosts)

                            // Show message if no posts
                            if (loadedPosts.isEmpty()) {
                                // You could show a "No posts" message here
                                Toast.makeText(this, "Este usuario no tiene publicaciones", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Failed to load user posts")
                        showError(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error loading user posts: $responseCode, Body: $errorBody")
                    showError("Error del servidor: $responseCode")
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error loading user posts", e)
                showError("Error de red: ${e.message}")
            }
        }
    }

    private fun parseTimestamp(dateString: String): Long {
        if (dateString.isNullOrEmpty()) return 0L
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
            sdf.timeZone = TimeZone.getTimeZone("UTC")
            sdf.parse(dateString)?.time ?: 0L
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing timestamp: $dateString", e)
            0L
        }
    }

    private fun showError(message: String) {
        runOnUiThread {
            progressBar.visibility = View.GONE
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }

    private fun createDirectChat(otherUserId: Int, otherUsername: String?) {
        // Show loading indicator
        progressBar.visibility = View.VISIBLE

        // Get current user ID from shared preferences
        val currentUserId = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE).getInt("USER_ID", -1)

        if (currentUserId == -1) {
            showError("Error: No se pudo identificar al usuario actual")
            return
        }

        thread {
            try {
                // First check if there's already a match
                val checkUrl = URL("$API_URL/matches/check?userId=$currentUserId&otherUserId=$otherUserId")
                val checkConnection = checkUrl.openConnection() as HttpURLConnection
                checkConnection.requestMethod = "GET"
                checkConnection.connectTimeout = 10000
                checkConnection.readTimeout = 10000

                val checkResponseCode = checkConnection.responseCode
                if (checkResponseCode == HttpURLConnection.HTTP_OK) {
                    val checkResponse = checkConnection.inputStream.bufferedReader().readText()
                    val checkJsonResponse = JSONObject(checkResponse)
                    val isMatch = checkJsonResponse.optBoolean("isMatch", false)

                    if (isMatch) {
                        // There's already a match, open the chat
                        val matchId = checkJsonResponse.optInt("matchId", -1)
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            if (matchId != -1) {
                                val intent = Intent(this, ChatActivity::class.java).apply {
                                    putExtra("MATCH_ID", matchId)
                                    putExtra("OTHER_USER_ID", otherUserId)
                                    putExtra("OTHER_USER_NAME", otherUsername)
                                }
                                startActivity(intent)
                            } else {
                                Toast.makeText(this, "Error: No se pudo obtener el ID de la coincidencia", Toast.LENGTH_SHORT).show()
                            }
                        }
                        return@thread
                    }
                }

                checkConnection.disconnect()

                // Create a new match
                val createUrl = URL("$API_URL/admin/create-match")
                val createConnection = createUrl.openConnection() as HttpURLConnection
                createConnection.requestMethod = "POST"
                createConnection.doOutput = true
                createConnection.setRequestProperty("Content-Type", "application/json")
                createConnection.connectTimeout = 15000
                createConnection.readTimeout = 15000

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("otherUserId", otherUserId)

                // Send the request
                val outputStream = createConnection.outputStream
                outputStream.write(jsonPayload.toString().toByteArray())
                outputStream.close()

                val createResponseCode = createConnection.responseCode
                if (createResponseCode == HttpURLConnection.HTTP_OK) {
                    val createResponse = createConnection.inputStream.bufferedReader().readText()
                    val createJsonResponse = JSONObject(createResponse)
                    val success = createJsonResponse.optBoolean("success", false)

                    if (success) {
                        val matchId = createJsonResponse.optInt("matchId", -1)
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            if (matchId != -1) {
                                val intent = Intent(this, ChatActivity::class.java).apply {
                                    putExtra("MATCH_ID", matchId)
                                    putExtra("OTHER_USER_ID", otherUserId)
                                    putExtra("OTHER_USER_NAME", otherUsername)
                                }
                                startActivity(intent)
                            } else {
                                Toast.makeText(this, "Error: No se pudo obtener el ID de la coincidencia", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        val message = createJsonResponse.optString("message", "Error al crear el chat")
                        showError(message)
                    }
                } else {
                    val errorBody = createConnection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error creating match: $createResponseCode, Body: $errorBody")
                    showError("Error del servidor: $createResponseCode")
                }

                createConnection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error creating direct chat", e)
                showError("Error de red: ${e.message}")
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            // Handle back button in toolbar
            onBackPressed()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    private fun setupSendMessageButton(userId: Int, username: String?) {
        // Initially hide the button until we check if there's a match
        buttonSendMessage.visibility = View.GONE

        // Get current user ID from shared preferences
        val currentUserId = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE).getInt("USER_ID", -1)

        // Don't show the button if viewing your own profile
        if (userId == currentUserId) {
            return
        }

        // Check if there's a match between the users
        thread {
            try {
                val url = URL("$API_URL/matches/check?userId=$currentUserId&otherUserId=$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)
                    val isMatch = jsonResponse.optBoolean("isMatch", false)

                    runOnUiThread {
                        // Show the button if there's a match or if the user is an admin
                        if (isMatch) {
                            buttonSendMessage.visibility = View.VISIBLE

                            // Set click listener for the button
                            buttonSendMessage.setOnClickListener {
                                // Open chat activity
                                val matchId = jsonResponse.optInt("matchId", -1)
                                if (matchId != -1) {
                                    val intent = Intent(this, ChatActivity::class.java).apply {
                                        putExtra("MATCH_ID", matchId)
                                        putExtra("OTHER_USER_ID", userId)
                                        putExtra("OTHER_USER_NAME", username)
                                    }
                                    startActivity(intent)
                                } else {
                                    Toast.makeText(this, "Error: No se pudo obtener el ID de la coincidencia", Toast.LENGTH_SHORT).show()
                                }
                            }
                        }
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking match", e)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::postAdapter.isInitialized) {
            postAdapter.releaseAllPlayers()
        }
    }

    // Method to check verification status and show badge
    private fun checkVerificationStatus(userId: Int) {
        if (userId <= 0) return

        Thread {
            try {
                val url = URL("$API_URL/verification/status/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)
                    val status = jsonResponse.optString("status", "not_verified")

                    runOnUiThread {
                        if (status == "approved") {
                            textViewVerifiedBadge.visibility = View.VISIBLE
                        } else {
                            textViewVerifiedBadge.visibility = View.GONE
                        }
                    }
                } else {
                    runOnUiThread {
                        textViewVerifiedBadge.visibility = View.GONE
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking verification status", e)
                runOnUiThread {
                    textViewVerifiedBadge.visibility = View.GONE
                }
            }
        }.start()
    }
}