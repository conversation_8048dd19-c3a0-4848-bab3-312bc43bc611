<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Info Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvUsername"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="Usuario123" />

                <TextView
                    android:id="@+id/tvEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="<EMAIL>" />

                <TextView
                    android:id="@+id/tvDocumentType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/bg_document_type_chip"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:textColor="@color/accent_cyan"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="DNI/Cédula de identidad" />

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/bg_status_chip"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    tools:text="Pendiente" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvSubmittedAt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="Enviado: 31/05/2025 14:30" />

        </LinearLayout>

        <!-- Photos Section -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="Documentos enviados:"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Document Photo -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:text="Documento"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/ivDocumentPhoto"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@drawable/bg_image_placeholder"
                    android:scaleType="centerCrop"
                    android:contentDescription="Foto del documento"
                    tools:src="@drawable/ic_document_placeholder" />

            </LinearLayout>

            <!-- Selfie Photo -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp"
                    android:text="Selfie con documento"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/ivSelfiePhoto"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:background="@drawable/bg_image_placeholder"
                    android:scaleType="centerCrop"
                    android:contentDescription="Selfie con documento"
                    tools:src="@drawable/ic_person_placeholder" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnReject"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:backgroundTint="@color/error_red"
                android:text="Rechazar"
                android:textColor="@android:color/white"
                style="@style/Widget.Material3.Button" />

            <Button
                android:id="@+id/btnApprove"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:backgroundTint="@color/success_green"
                android:text="Aprobar"
                android:textColor="@android:color/white"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
