<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dark_background">

    <!-- Modern Header with Gradient Background -->
    <LinearLayout
        android:id="@+id/layoutHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/radar_header_gradient"
        android:paddingHorizontal="20dp"
        android:paddingTop="16dp"
        android:paddingBottom="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Title and Filter Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Radar de Usuarios"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="22sp"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium" />

            <ImageButton
                android:id="@+id/buttonFilter"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:background="@drawable/circular_button_background"
                android:src="@drawable/ic_filter"
                android:contentDescription="Filtros"
                android:scaleType="centerInside"
                android:tint="@color/neon_blue"
                android:elevation="4dp" />

        </LinearLayout>

        <!-- Subtitle -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Descubre personas cerca de ti"
            android:textColor="@color/cyberpunk_text_secondary"
            android:textSize="14sp"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <!-- Location Status Banner -->
    <LinearLayout
        android:id="@+id/layoutLocationStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/neon_blue_transparent"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/layoutHeader"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_location"
            android:tint="@color/neon_blue" />

        <TextView
            android:id="@+id/textViewLocationStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Obteniendo ubicación..."
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:fontFamily="sans-serif" />

        <ImageButton
            android:id="@+id/buttonRefreshLocation"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_refresh"
            android:contentDescription="Actualizar ubicación"
            android:scaleType="centerInside"
            android:tint="@color/neon_blue" />

    </LinearLayout>

    <!-- SwipeRefreshLayout with RecyclerView -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/layoutLocationStatus"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewUsers"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingTop="12dp"
            android:paddingBottom="20dp"
            android:paddingHorizontal="4dp"
            android:scrollbars="none"
            android:overScrollMode="never" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/neon_blue"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/layoutEmptyState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_radar_empty"
            android:tint="@color/text_secondary"
            android:alpha="0.6" />

        <TextView
            android:id="@+id/textViewEmptyTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="No hay usuarios cercanos"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium" />

        <TextView
            android:id="@+id/textViewEmptyMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Intenta ampliar el rango de búsqueda\no verifica tu ubicación"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:lineSpacingExtra="2dp" />

        <Button
            android:id="@+id/buttonExpandSearch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Ampliar búsqueda"
            android:textColor="@color/neon_blue"
            android:background="@drawable/button_outline_cyberpunk"
            android:fontFamily="sans-serif-medium"
            android:paddingHorizontal="24dp"
            android:paddingVertical="12dp" />

    </LinearLayout>

    <!-- Permission Request Layout -->
    <LinearLayout
        android:id="@+id/layoutPermissionRequest"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_location_permission"
            android:tint="@color/neon_blue"
            android:alpha="0.8" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Permisos de Ubicación Requeridos"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:gravity="center" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="Para descubrir usuarios cercanos, necesitamos acceso a tu ubicación. Tus datos de ubicación se mantienen seguros y privados."
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:lineSpacingExtra="2dp" />

        <Button
            android:id="@+id/buttonRequestPermission"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Conceder Permisos"
            android:textColor="@color/dark_background"
            android:background="@drawable/button_cyberpunk"
            android:fontFamily="sans-serif-medium"
            android:paddingHorizontal="32dp"
            android:paddingVertical="12dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
