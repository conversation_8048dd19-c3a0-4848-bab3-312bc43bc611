package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u0000 !2\u00020\u0001:\u0001!B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0014\u001a\u00020\f2\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0016J&\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0016J\u001a\u0010\u001d\u001a\u00020\f2\u0006\u0010\u001e\u001a\u00020\u00182\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0016J\u001a\u0010\u001f\u001a\u00020\f2\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\f0\nR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u001c\u0010\t\u001a\u0010\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\f\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/spyro/vmeet/ui/community/StoryViewersDialog;", "Landroidx/fragment/app/DialogFragment;", "()V", "adapter", "Lcom/spyro/vmeet/adapter/StoryViewersAdapter;", "closeButton", "Landroid/widget/ImageView;", "emptyText", "Landroid/widget/TextView;", "onViewerClickListener", "Lkotlin/Function1;", "Lcom/spyro/vmeet/data/community/StoryViewer;", "", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "storyId", "", "titleText", "viewers", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onViewCreated", "view", "setOnViewerClickListener", "listener", "Companion", "app_debug"})
public final class StoryViewersDialog extends androidx.fragment.app.DialogFragment {
    private android.widget.TextView titleText;
    private android.widget.ImageView closeButton;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private android.widget.TextView emptyText;
    private com.spyro.vmeet.adapter.StoryViewersAdapter adapter;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String storyId;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.data.community.StoryViewer> viewers;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.StoryViewer, kotlin.Unit> onViewerClickListener;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String ARG_STORY_ID = "arg_story_id";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String ARG_VIEWERS = "arg_viewers";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.community.StoryViewersDialog.Companion Companion = null;
    
    public StoryViewersDialog() {
        super();
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    public final void setOnViewerClickListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.StoryViewer, kotlin.Unit> listener) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\u00042\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/spyro/vmeet/ui/community/StoryViewersDialog$Companion;", "", "()V", "ARG_STORY_ID", "", "ARG_VIEWERS", "newInstance", "Lcom/spyro/vmeet/ui/community/StoryViewersDialog;", "storyId", "viewers", "", "Lcom/spyro/vmeet/data/community/StoryViewer;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.ui.community.StoryViewersDialog newInstance(@org.jetbrains.annotations.NotNull()
        java.lang.String storyId, @org.jetbrains.annotations.NotNull()
        java.util.List<com.spyro.vmeet.data.community.StoryViewer> viewers) {
            return null;
        }
    }
}