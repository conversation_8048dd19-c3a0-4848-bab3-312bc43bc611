package com.spyro.vmeet.activity

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.animation.AnimationUtils
import org.json.JSONObject
import java.net.HttpURLConnection
import java.net.URL
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.PopupMenu
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import java.io.IOException
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.spyro.vmeet.R
import com.spyro.vmeet.adapter.MessageAdapter
import com.spyro.vmeet.audio.AudioPlayer
import com.spyro.vmeet.audio.AudioRecorder
import com.spyro.vmeet.data.Message
import com.spyro.vmeet.data.MessageReaction
import com.spyro.vmeet.data.WebSocketClient
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import java.lang.reflect.Type
import java.util.UUID
import java.util.concurrent.TimeUnit
import java.time.ZonedDateTime
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.time.format.DateTimeFormatter
import com.spyro.vmeet.data.UserStatus
import androidx.emoji2.text.EmojiCompat
import androidx.emoji2.widget.EmojiTextView
import com.spyro.vmeet.dialog.EmojiPickerDialog
import com.spyro.vmeet.dialog.ImprovedEmojiPickerDialog
import androidx.emoji2.text.DefaultEmojiCompatConfig
import androidx.emoji2.text.EmojiCompat.InitCallback
import com.spyro.vmeet.activity.UserProfileActivity
import java.io.File
import com.spyro.vmeet.dialog.GifPickerDialog
import com.spyro.vmeet.data.TenorGif
import kotlin.math.abs
import com.spyro.vmeet.util.ReactionHandler
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import android.Manifest
import android.app.NotificationManager
import android.content.pm.PackageManager
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.FileProvider
import okhttp3.MultipartBody
import java.io.FileInputStream
import java.io.FileOutputStream
import okhttp3.MultipartBody.Part.Companion.createFormData
import okhttp3.RequestBody.Companion.asRequestBody

// Chat activity with proper window insets handling
class ChatActivity : BaseActivity(), SensorEventListener {

    // Constants for logging and API access
companion object {
    const val TAG = "ChatActivity"
    private const val BASE_URL = "*************"
    private const val PORT = 3000
    private const val API_URL = "http://$BASE_URL:$PORT"
    private const val WS_URL = "ws://$BASE_URL:$PORT"
    private const val NORMAL_CLOSURE_STATUS = 1000
    private const val MAX_RECONNECT_ATTEMPTS = 5
    private const val RECONNECT_DELAY_MS = 2000L  // Base delay for exponential backoff
    private const val MESSAGE_POLL_INTERVAL = 1000L  // Poll for new messages every 1 second as fallback
    private const val BUZZ_COOLDOWN_MS = 10000L  // 10 seconds cooldown between buzzes

    // Permission request codes
    private const val REQUEST_IMAGE_PERMISSIONS = 101

    // Tenor API key
    private const val TENOR_API_KEY = "AIzaSyBlyFNlZao4Stf3p9n3gkv31G-ti-rhcRQ"
    private const val TENOR_SEARCH_LIMIT = 20
}

    // Basic UI elements
private lateinit var recyclerViewMessages: RecyclerView
private lateinit var editTextMessage: EditText
private lateinit var buttonSend: ImageButton
private lateinit var buttonEmoji: ImageButton
private lateinit var buttonImage: ImageButton
private lateinit var buttonGif: TextView // Changed from ImageButton
private lateinit var progressBar: ProgressBar
private lateinit var textViewOtherUserName: TextView
private lateinit var imageViewOtherUserAvatar: ImageView
private lateinit var textViewStatus: TextView
private lateinit var imageViewMenuOptions: ImageView

    // Image handling
    private var selectedImageUri: Uri? = null
    private var lastCreatedImageFile: File? = null

    // Buzz functionality
    private var lastBuzzTime = 0L

    // Image selection from gallery launcher
    private val selectImageLauncher = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        if (uri != null) {
            selectedImageUri = uri
            uploadImage(uri)
        }
    }

    // Image capture launcher
    private val takePictureLauncher = registerForActivityResult(ActivityResultContracts.TakePicture()) { success: Boolean ->
        if (success && lastCreatedImageFile != null) {
            selectedImageUri = FileProvider.getUriForFile(
                this,
                "${applicationContext.packageName}.provider",
                lastCreatedImageFile!!
            )
            uploadImage(selectedImageUri!!)
        }
    }

    // Messages display
    private lateinit var messageAdapter: MessageAdapter
    private val messagesList = ArrayList<Message>()
    private val pendingMessages = HashMap<String, Message>()
    private val recentlySentMessageTexts = LinkedHashSet<String>() // Track recently sent message text to prevent duplicates
    private val processedMessageIds = HashSet<Int>() // Permanently track all message IDs we've processed
    private val sentMessageHashes = HashSet<Int>() // Track hash codes of sent messages for permanent deduplication
    private var disablePollingUntil = 0L // Timestamp until which polling is disabled
    private val POLLING_DISABLE_DURATION = 2000L // Disable polling for only 2 seconds after sending a message

    // User info
    private var matchId: Int = -1
    private var originalMatchId: Int = -1 // Store the original match ID from intent
    private var allMatchIds: IntArray? = null // Store all match IDs between the two users
    private var currentUserId: Int = -1
    private var otherUserId: Int = -1
    private var otherUserName: String? = null
    private var otherUserAvatar: String? = null
    private var currentUserAvatarUrl: String? = null  // Store the current user's avatar URL
    private var otherUserIsViewing: Boolean = false // Track if the other user is viewing this chat
    private var isChatActive: Boolean = false // Flag to track if chat is visible to the user
    private var chatTopic: String? = null // Store the chat topic for blind date matches

    // Networking components
    private lateinit var client: OkHttpClient
    private val gson = createGson() // Use custom GSON instance
    private var webSocket: WebSocket? = null
    private var webSocketClient: WebSocketClient? = null // Use our WebSocketClient wrapper

    // Status update handler
    private val statusHandler = Handler(Looper.getMainLooper())
    private var statusRunnable: Runnable? = null
    private var lastStatusCheck: Long = 0
    private var statusCheckInterval = 30000L // 30 seconds

    // Polling mechanism for message updates as WebSocket fallback
    private val messagePollingHandler = Handler(Looper.getMainLooper())
    private var messagePollingRunnable: Runnable? = null
    private var lastMessagePoll: Long = 0
    private var lastMessageId: Int = -1
    private var isPollingActive = false
    private val POLLING_INTERVAL = 1000L // 1 second polling for real-time updates

    // Voice message handling
    private lateinit var buttonMic: ImageButton
    private var audioRecorder: AudioRecorder? = null
    private var audioPlayer: AudioPlayer? = null
    private var isRecording = false

    // Reply functionality
    private lateinit var layoutReplyPreview: LinearLayout
    private lateinit var textViewReplyPreview: EmojiTextView
    private lateinit var buttonCancelReply: ImageButton
    private var replyingToMessage: Message? = null

    // Setup retry handler for WebSocket connections
    private lateinit var webSocketRetryHandler: Handler

    // Add a counter for reconnection attempts
    private var reconnectAttempts = 0
    private val MAX_RECONNECT_ATTEMPTS = 5
    private val RECONNECT_DELAY_MS = 3000L // 3 seconds
    // Add connection check timer
    private val connectionCheckHandler = Handler(Looper.getMainLooper())
    private var connectionCheckRunnable: Runnable? = null
    private val CONNECTION_CHECK_INTERVAL = 10000L // 10 seconds

    // Shake detection for buzz
    private lateinit var sensorManager: SensorManager
    private var accelerometer: Sensor? = null
    private var lastShakeTime = 0L
    private val SHAKE_THRESHOLD = 12.0f // Sensitivity threshold for shake detection
    private val SHAKE_COOLDOWN_MS = 2000L // 2 seconds cooldown between shake detections

    // Create a custom GSON instance with type adapters
    private fun createGson(): Gson {
        return GsonBuilder()
            .registerTypeAdapter(Boolean::class.java, IntToBooleanAdapter())
            .registerTypeAdapter(Boolean::class.javaPrimitiveType, IntToBooleanAdapter())
            .create()
    }

    // Custom adapter to handle integer to boolean conversion
    private class IntToBooleanAdapter : JsonSerializer<Boolean>, JsonDeserializer<Boolean> {
    companion object {
            private const val TAG = "IntToBooleanAdapter"
        }

        override fun serialize(src: Boolean, typeOfSrc: Type, context: JsonSerializationContext): JsonElement {
            return JsonPrimitive(if (src) 1 else 0)
        }

        override fun deserialize(json: JsonElement, typeOfT: Type, context: JsonDeserializationContext): Boolean {
            Log.d(TAG, "Deserializing boolean from: ${json}")
            return when {
                json.isJsonPrimitive -> {
                    val primitive = json.asJsonPrimitive
                    val result = when {
                        primitive.isBoolean -> primitive.asBoolean
                        primitive.isNumber -> primitive.asInt != 0
                        primitive.isString -> {
                            val stringValue = primitive.asString.lowercase()
                            stringValue == "true" || stringValue == "1"
                        }
                        else -> false
                    }
                    Log.d(TAG, "Converted ${json} to boolean: ${result}")
                    result
                }
                else -> {
                    Log.d(TAG, "Could not convert non-primitive ${json} to boolean, defaulting to false")
                    false
                }
            }
        }
    }

    // Add these properties in the class
    private lateinit var reactionHandler: ReactionHandler

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize EmojiCompat before setting content view
        initEmojiCompat()

        setContentView(R.layout.activity_chat)

        // Set up proper window insets handling for keyboard
        setupKeyboardInsets()

        Log.d(TAG, "onCreate - Starting ChatActivity")

        // Extract all intent extras for debugging
        val extras = intent.extras
        if (extras != null) {
            Log.d(TAG, "Intent extras received:")
            for (key in extras.keySet()) {
                Log.d(TAG, "  $key = ${extras.get(key)}")
            }
        } else {
            Log.e(TAG, "No intent extras received")
        }

        // Basic data retrieval - first check intent extras for currentUserId
        currentUserId = intent.getIntExtra("USER_ID", -1)

        // If not found in intent, try shared preferences
        val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
        if (currentUserId == -1) {
            currentUserId = prefs.getInt("USER_ID", -1)
            Log.d(TAG, "Retrieved USER_ID from SharedPreferences: $currentUserId")
        }

        // Get current user's avatar URL from preferences
        currentUserAvatarUrl = prefs.getString("USER_AVATAR", null)
        Log.d(TAG, "Retrieved USER_AVATAR from SharedPreferences: $currentUserAvatarUrl")

        // If avatar URL is not in preferences, fetch it from server
        if (currentUserAvatarUrl == null || currentUserAvatarUrl.isNullOrEmpty()) {
            fetchCurrentUserAvatar()
        }

        // Store the original match ID from intent for verification
        originalMatchId = intent.getIntExtra("MATCH_ID", -1)
        matchId = originalMatchId

        // Get all match IDs if available
        allMatchIds = intent.getIntArrayExtra("ALL_MATCH_IDS")

        // Log all match IDs
        if (allMatchIds != null) {
            Log.d(TAG, "Received ALL_MATCH_IDS: ${allMatchIds?.joinToString()}")
        } else {
            Log.d(TAG, "No ALL_MATCH_IDS received, using only MATCH_ID: $matchId")
        }

        // Add detailed logging for matchId
        Log.d(TAG, "Extracted MATCH_ID from intent: $matchId (raw value: ${intent.getIntExtra("MATCH_ID", -1000)})")

        otherUserId = intent.getIntExtra("OTHER_USER_ID", -1)
        otherUserName = intent.getStringExtra("OTHER_USER_NAME")
        otherUserAvatar = intent.getStringExtra("OTHER_USER_AVATAR")
        chatTopic = intent.getStringExtra("CHAT_TOPIC")

        Log.d(TAG, "Extracted parameters: USER_ID=$currentUserId, MATCH_ID=$matchId, OTHER_USER_ID=$otherUserId, NAME=$otherUserName, TOPIC=$chatTopic")

        // Validate essential parameters
        if (matchId == -1 || currentUserId == -1 || otherUserId == -1) {
            Log.e(TAG, "Missing essential parameters. matchId=$matchId, currentUserId=$currentUserId, otherUserId=$otherUserId")
            Toast.makeText(this, "Error: Faltan parámetros necesarios para el chat", Toast.LENGTH_LONG).show()
            finish()
            return
        }

        // Initialize UI
        initializeViews()

        // Initialize HttpClient
        client = OkHttpClient.Builder()
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .connectTimeout(30, TimeUnit.SECONDS)
            .pingInterval(15, TimeUnit.SECONDS)
            .build()

        // Setup RecyclerView
        setupRecyclerView()

        // Setup voice messaging (moved here, after RecyclerView setup)
        setupVoiceMessageRecording()

        // Setup other user profile
        setupUserProfile()

        // Setup send button with a minimal implementation that's safer
        setupSendButton()

        // Initialize WebSocket retry handler
        webSocketRetryHandler = Handler(Looper.getMainLooper())

        // Create a new WebSocketClient instance
        webSocketClient = WebSocketClient(
            serverUrl = WS_URL,
            userId = currentUserId,
            onOpen = {
                Log.d(TAG, "WebSocket opened via wrapper")
                // Update active chat ID after connection is open
                updateActiveChatId()
            },
            onMessage = { message ->
                // Simply delegate to the existing message handler
                Log.d(TAG, "WebSocketClient received message: $message")
                // We'll handle messages in our existing WebSocket listener code
            },
            onClose = { code, reason, remote ->
                Log.d(TAG, "WebSocket closed via wrapper: $code, $reason")
                // Trigger reconnection if needed
                if (code != 1000) {
                    attemptReconnection()
                }
            },
            onError = { error ->
                Log.e(TAG, "WebSocket error via wrapper", error)
                attemptReconnection()
            }
        )

        // Connect the WebSocketClient
        webSocketClient?.connect()

        // Set up WebSocket reaction event handlers
        webSocketClient?.setOnMessageReactionAddedListener { reaction ->
            runOnUiThread {
                updateMessageWithReaction(reaction, false)
            }
        }

        webSocketClient?.setOnMessageReactionRemovedListener { reaction ->
            runOnUiThread {
                updateMessageWithReaction(reaction, true)
            }
        }

        // Set up WebSocket chat deletion handler
        webSocketClient?.setOnChatDeletedListener { deletedMatchId, deletedBy ->
            runOnUiThread {
                if (deletedMatchId == matchId) {
                    // This chat has been deleted, close the activity and show a message
                    Toast.makeText(this, "Este chat ha sido eliminado", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }

        // Set up WebSocket chat deletion handler
        webSocketClient?.setOnChatDeletedListener { deletedMatchId, deletedBy ->
            runOnUiThread {
                if (deletedMatchId == matchId) {
                    // This chat has been deleted, close the activity and show a message
                    Toast.makeText(this, "Este chat ha sido eliminado", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        }

        // Establish traditional WebSocket connection for real-time updates
        establishWebSocketConnection()

        // Check other user's status immediately
        checkUserStatus()

        // Force load message history
        Log.d(TAG, "About to load chat history for matchId=$matchId")
        loadChatHistory()

        // Start checking user status
        startStatusUpdates()

        // Tell the server which chat we're viewing
        updateActiveChatId()

        // Clear notifications for this chat when opening
        clearNotificationsForChat()

        // Check if this chat was opened from a profile and send a welcome message if needed
        val fromProfile = intent.getBooleanExtra("FROM_PROFILE", false)
        if (fromProfile) {
            Log.d(TAG, "Chat opened from profile, will send welcome message after history loads")
            // Delay sending the welcome message to ensure history is loaded first
            Handler(Looper.getMainLooper()).postDelayed({
                sendWelcomeMessage()
            }, 1500) // 1.5 second delay
        }

        // Add after WebSocket initialization
        // Initialize reaction handler
        reactionHandler = ReactionHandler(
            baseUrl = API_URL,
            userId = currentUserId,
            matchId = originalMatchId, // Pass the match ID
            webSocketClient = webSocketClient,
            onReactionSuccess = { reactions, wasRemoved ->
                runOnUiThread {
                    if (wasRemoved) {
                        Log.d(TAG, "Reaction was removed, refreshing UI immediately")
                        // Force message list refresh to update UI immediately
                        messageAdapter.notifyDataSetChanged()
                    }
                    handleReactionResponse(reactions, wasRemoved)
                }
            },
            onReactionError = { errorMessage ->
                runOnUiThread {
                    Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show()
                }
            }
        )

        // Initialize the image button
        buttonImage = findViewById(R.id.buttonImage)
        buttonImage.setOnClickListener {
            showImagePickerOptions()
        }

        // Initialize shake detection
        setupShakeDetection()
    }

    // Add menu to the activity
    override fun onCreateOptionsMenu(menu: android.view.Menu): Boolean {
        menuInflater.inflate(R.menu.chat_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: android.view.MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_block_user -> {
                showBlockUserConfirmation()
                true
            }
            R.id.action_delete_chat -> {
                showDeleteChatConfirmation()
                true
            }
            android.R.id.home -> {
                onBackPressed()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showBlockUserConfirmation() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("Bloquear usuario")
        builder.setMessage("¿Estás seguro de que quieres bloquear a este usuario? No podrán enviarte mensajes ni ver tu perfil.")

        builder.setPositiveButton("Bloquear") { _, _ ->
            blockUser()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            dialog.dismiss()
        }

        val dialog = builder.create()
        dialog.show()
    }

    private fun blockUser() {
        if (otherUserId <= 0) {
            Toast.makeText(this, "Error: No se pudo identificar al usuario", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d(TAG, "Blocking user with ID: $otherUserId")

        // Show loading indicator
        progressBar.visibility = View.VISIBLE

        Thread {
            try {
                // Create URL for the block user endpoint
                val url = URL("$API_URL/user/block")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                connection.setRequestProperty("Content-Type", "application/json")

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("blockedUserId", otherUserId)

                // Send the request
                val outputStream = connection.outputStream
                outputStream.write(jsonPayload.toString().toByteArray())
                outputStream.close()

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Block user response: $response")

                    // Also delete the chat to clean up the conversation
                    deleteChat()

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(this, "Usuario bloqueado con éxito", Toast.LENGTH_SHORT).show()
                        finish() // Close the chat activity
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error blocking user: $responseCode, Body: $errorBody")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(this, "Error al bloquear usuario: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error blocking user", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }.start()
    }

    private fun showDeleteChatConfirmation() {
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("Eliminar chat")
        builder.setMessage("¿Estás seguro de que quieres eliminar esta conversación? Esta acción no se puede deshacer.")

        builder.setPositiveButton("Eliminar") { _, _ ->
            deleteChat()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            dialog.dismiss()
        }

        val dialog = builder.create()
        dialog.show()
    }

    private fun deleteChat() {
        Log.d(TAG, "Deleting chat with match ID: $matchId")

        Thread {
            try {
                val url = URL("$API_URL/chat/$matchId/delete?userId=$currentUserId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "DELETE"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Chat deletion response: $response")

                    runOnUiThread {
                        Toast.makeText(this, "Chat eliminado con éxito", Toast.LENGTH_SHORT).show()
                        finish()
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error deleting chat: $responseCode, Body: $errorBody")

                    runOnUiThread {
                        Toast.makeText(this, "Error al eliminar chat: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting chat", e)
                runOnUiThread {
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }.start()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)

        // Update user info from new intent
        otherUserId = intent.getIntExtra("OTHER_USER_ID", -1)
        otherUserName = intent.getStringExtra("OTHER_USER_NAME")
        otherUserAvatar = intent.getStringExtra("OTHER_USER_AVATAR")
        matchId = intent.getIntExtra("MATCH_ID", -1)
        originalMatchId = matchId
        Log.d(TAG, "onNewIntent: Updated user info: otherUserId=$otherUserId, otherUserName=$otherUserName, matchId=$matchId")
        setupUserProfile() // Refresh UI

        // Notify the server about the chat change
        updateActiveChatId()

        // Reload chat history
        loadChatHistory()
    }

    private fun initEmojiCompat() {
        try {
            val config = DefaultEmojiCompatConfig.create(this)
            if (config != null) {
                config.setReplaceAll(true)

                config.registerInitCallback(object : InitCallback() {
                    override fun onInitialized() {
                        Log.d(TAG, "EmojiCompat initialized successfully")
                    }

                    override fun onFailed(throwable: Throwable?) {
                        Log.e(TAG, "EmojiCompat initialization failed", throwable)
                    }
                })

                EmojiCompat.init(config)
            } else {
                Log.e(TAG, "Failed to create EmojiCompat config")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing EmojiCompat", e)
        }
    }

    /**
     * Set up proper window insets handling for keyboard
     * This method handles the keyboard insets properly to avoid the black margin issue
     */
    private fun setupKeyboardInsets() {
        try {
            val rootView = findViewById<View>(android.R.id.content)
            val recyclerView = findViewById<RecyclerView>(R.id.recyclerViewMessages)
            val messageInputLayout = findViewById<View>(R.id.messageInputLayout)
            val replyLayout = findViewById<View>(R.id.layoutReplyPreview)

            // Use ViewCompat to handle window insets
            ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, windowInsetsCompat ->
                val insets = windowInsetsCompat.getInsets(WindowInsetsCompat.Type.systemBars() or WindowInsetsCompat.Type.ime())

                // Apply top insets to toolbar
                findViewById<View>(R.id.toolbar).updatePadding(top = insets.top)

                // Apply bottom insets to message input layout only when keyboard is visible
                val imeVisible = windowInsetsCompat.isVisible(WindowInsetsCompat.Type.ime())
                val bottomPadding = if (imeVisible) insets.bottom else 0

                // Update padding for message input layout
                messageInputLayout.updatePadding(bottom = bottomPadding)

                // Log insets for debugging
                Log.d(TAG, "Window insets applied - top: ${insets.top}, bottom: ${insets.bottom}, keyboard visible: $imeVisible")

                // Return the insets so they're not consumed
                windowInsetsCompat
            }

            // Request applying insets
            ViewCompat.requestApplyInsets(rootView)

            Log.d(TAG, "Keyboard insets handling set up successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up keyboard insets", e)
        }
    }

    private fun initializeViews() {
        recyclerViewMessages = findViewById(R.id.recyclerViewMessages)
        editTextMessage = findViewById(R.id.editTextMessage)
        buttonSend = findViewById(R.id.buttonSend)
        buttonEmoji = findViewById(R.id.buttonEmoji) // Initialize emoji button
        buttonImage = findViewById(R.id.buttonImage) // Initialize image button
        buttonGif = findViewById(R.id.buttonGif) // Initialize GIF button, now a TextView
        progressBar = findViewById(R.id.progressBar)
        textViewOtherUserName = findViewById(R.id.textViewUsername)
        imageViewOtherUserAvatar = findViewById(R.id.imageViewAvatar)
        textViewStatus = findViewById(R.id.textViewStatus)
        imageViewMenuOptions = findViewById(R.id.imageViewMenuOptions)

        // Setup emoji button
        buttonEmoji.setOnClickListener {
            showEmojiPicker()
        }

        // Setup GIF button
        buttonGif.setOnClickListener {
            showGifPicker()
        }

        // Setup menu options button
        imageViewMenuOptions.setOnClickListener {
            showChatOptionsMenu()
        }

        // Initialize mic button for voice messages
        buttonMic = findViewById(R.id.buttonMic)

        // Initialize reply UI elements
        layoutReplyPreview = findViewById<LinearLayout>(R.id.layoutReplyPreview)
        textViewReplyPreview = findViewById(R.id.textViewReplyPreview)
        buttonCancelReply = findViewById(R.id.buttonCancelReply)

        // Setup cancel reply button
        buttonCancelReply.setOnClickListener {
            clearReplyState()
        }

        // Show loading state
        progressBar.visibility = View.VISIBLE
    }

    private fun setupRecyclerView() {
        messageAdapter = MessageAdapter(
            messages = messagesList,
            currentUserId = currentUserId,
            onReplyListener = { message ->
                setupReplyToMessage(message)
            },
            onReactionListener = { message, reactionType ->
                sendReaction(message, reactionType)
            },
            onProfileClickListener = { userId ->
                // Navigate to user profile using ProfileActivity (full profile) instead of UserProfileActivity
                val profileIntent = Intent(this, com.spyro.vmeet.ProfileActivity::class.java)
                profileIntent.putExtra("USER_ID", userId)
                profileIntent.putExtra("VIEW_ONLY_MODE", true)
                startActivity(profileIntent)
            }
        )

        // Create and configure layout manager
        val layoutManager = LinearLayoutManager(this).apply {
            stackFromEnd = true
        }
        recyclerViewMessages.layoutManager = layoutManager
        recyclerViewMessages.adapter = messageAdapter

        // Add keyboard visibility listener to scroll to bottom when keyboard appears
        val rootView = findViewById<View>(android.R.id.content)
        ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, windowInsetsCompat ->
            val imeVisible = windowInsetsCompat.isVisible(WindowInsetsCompat.Type.ime())

            // If keyboard becomes visible and we have messages, scroll to bottom
            if (imeVisible && messagesList.isNotEmpty()) {
                recyclerViewMessages.post {
                    recyclerViewMessages.scrollToPosition(messagesList.size - 1)
                }
            }

            // Return the insets so they're not consumed
            windowInsetsCompat
        }

        // Set clip to padding to false to allow scrolling behind the input area
        recyclerViewMessages.clipToPadding = false
    }

    private fun setupUserProfile() {
        // Set avatar and username in toolbar
        textViewOtherUserName = findViewById(R.id.textViewUsername)
        imageViewOtherUserAvatar = findViewById(R.id.imageViewAvatar)
        textViewStatus = findViewById(R.id.textViewStatus)

        // Get admin badge reference
        val textViewAdminBadge = findViewById<TextView>(R.id.textViewAdminBadge)

        // Get verified badge reference
        val textViewVerifiedBadge = findViewById<TextView>(R.id.textViewVerifiedBadge)

        // Set user name
        textViewOtherUserName.text = otherUserName ?: "Usuario"

        // Show topic if this is a blind date match
        if (!chatTopic.isNullOrEmpty()) {
            // Create a subtitle view for the topic
            val topicView = findViewById<TextView>(R.id.textViewTopic)
            if (topicView != null) {
                topicView.text = "Tema: $chatTopic"
                topicView.visibility = View.VISIBLE
            } else {
                Log.d(TAG, "Topic view not found in layout")
            }
        }

        // Load avatar
        if (!otherUserAvatar.isNullOrEmpty() && otherUserAvatar != "null") {
            val fullUrl = if (otherUserAvatar!!.startsWith("http")) {
                otherUserAvatar
            } else {
                "$API_URL$otherUserAvatar"
            }

            Glide.with(this)
                .load(fullUrl)
                .placeholder(R.drawable.default_avatar)
                .error(R.drawable.default_avatar)
                .circleCrop()
                .into(imageViewOtherUserAvatar)
        } else {
            // Load default avatar
            Glide.with(this)
                .load(R.drawable.default_avatar)
                .circleCrop()
                .into(imageViewOtherUserAvatar)
        }

        // Set up click listener for profile view
        val avatarClickListener = View.OnClickListener {
            openOtherUserProfile()
        }

        imageViewOtherUserAvatar.setOnClickListener(avatarClickListener)
        textViewOtherUserName.setOnClickListener(avatarClickListener)

        // Check if other user is admin
        checkIfUserIsAdmin(otherUserId)

        // Check if other user is verified
        checkIfUserIsVerified(otherUserId)
    }

    private fun checkIfUserIsAdmin(userId: Int) {
        val textViewAdminBadge = findViewById<TextView>(R.id.textViewAdminBadge)

        Thread {
            try {
                val url = URL("$API_URL/user/check-admin/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)
                    val success = jsonResponse.optBoolean("success", false)
                    val isAdmin = jsonResponse.optBoolean("isAdmin", false)

                    runOnUiThread {
                        if (success && isAdmin) {
                            // Show admin badge and apply color to username text
                            textViewAdminBadge.visibility = View.VISIBLE

                            // Use red gradient text color instead of background
                            textViewOtherUserName.setTextColor(getColor(android.R.color.holo_red_light))
                        } else {
                            textViewAdminBadge.visibility = View.GONE
                            textViewOtherUserName.background = null
                            textViewOtherUserName.setTextColor(getColor(R.color.neon_blue))
                        }
                    }
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking admin status", e)
            }
        }.start()
    }

    private fun checkIfUserIsVerified(userId: Int) {
        val textViewVerifiedBadge = findViewById<TextView>(R.id.textViewVerifiedBadge)

        Thread {
            try {
                val url = URL("$API_URL/verification/status/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)
                    val status = jsonResponse.optString("status", "not_verified")

                    runOnUiThread {
                        if (status == "approved") {
                            textViewVerifiedBadge.visibility = View.VISIBLE
                        } else {
                            textViewVerifiedBadge.visibility = View.GONE
                        }
                    }
                } else {
                    runOnUiThread {
                        textViewVerifiedBadge.visibility = View.GONE
                    }
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking verification status", e)
                runOnUiThread {
                    textViewVerifiedBadge.visibility = View.GONE
                }
            }
        }.start()
    }

    private fun sendWelcomeMessage() {
        // Desactivamos el envío automático de mensajes de bienvenida
        Log.d(TAG, "Automatic welcome message disabled")

        // Enviar broadcast para actualizar la lista de chats
        Handler(Looper.getMainLooper()).postDelayed({
            val updateIntent = Intent("com.spyro.vmeet.UPDATE_CHAT_LIST")
            // Asegurarse de que el broadcast sea explícito para Android 12+
            updateIntent.setPackage(packageName)
            sendBroadcast(updateIntent)
            Log.d(TAG, "Sent broadcast to update chat list")
        }, 1000) // Delay para dar tiempo a que el chat se registre en el servidor
    }

    private fun setupSendButton() {
        // Reset matchId to original value from intent as a safeguard against any potential changes
        if (matchId != originalMatchId) {
            Log.e(TAG, "Detected matchId drift in setupSendButton! Restoring from $matchId to $originalMatchId")
            matchId = originalMatchId
        }

        // Use a single anonymous class instead of lambda to prevent context leaks
        buttonSend.setOnClickListener(object : View.OnClickListener {
            override fun onClick(v: View) {
                Log.d(TAG, "Send button clicked")

                // Use a try-catch to identify any exceptions that might cause activity restart
                try {
                    // Get message text but be defensive with nulls
                    val messageTextRaw = editTextMessage.text?.toString() ?: ""
                    val messageText = messageTextRaw.trim()

                    // Validate
                    if (messageText.isEmpty()) {
                        Toast.makeText(applicationContext, "El mensaje no puede estar vacío", Toast.LENGTH_SHORT).show()
                        return
                    }

                    // Add message size validation to prevent app crashes with large messages
                    if (messageText.length > 5000) {
                        Toast.makeText(
                            applicationContext,
                            "El mensaje es demasiado largo. Por favor, acórtalo.",
                            Toast.LENGTH_LONG
                        ).show()
                        return
                    }

                    // Log reply state before creating message
                    if (replyingToMessage != null) {
                        Log.d(TAG, "Sending message with reply to: ${replyingToMessage?.id}")
                    }

                    // Clear text field - do this FIRST
                    editTextMessage.setText("")

                    // Request focus to maintain keyboard open but prevent black margin issue
                    editTextMessage.clearFocus()
                    editTextMessage.postDelayed({
                        editTextMessage.requestFocus()
                    }, 100)

                    // Create message ID first to avoid any potential NullPointerException
                    val tempId = UUID.randomUUID().toString()

                    // Preserve reply information before clearing UI
                    val replyId = replyingToMessage?.id
                    val replyText = replyingToMessage?.text
                    val replySenderId = replyingToMessage?.senderId

                    // If we don't have the current user's avatar URL, try to fetch it from cache
                    if (currentUserAvatarUrl.isNullOrEmpty()) {
                        try {
                            val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
                            currentUserAvatarUrl = prefs.getString("USER_AVATAR", null)
                            Log.d(TAG, "Retrieved avatar URL from prefs: $currentUserAvatarUrl")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error retrieving avatar URL from prefs", e)
                        }
                    }

                    // Create a simple temporary message with proper defaults
                    val message = Message(
                        id = -1,
                        matchId = originalMatchId, // Always use the original match ID from intent
                        senderId = currentUserId,
                        receiverId = otherUserId,
                        text = messageText,
                        createdAt = getCurrentISOTimestamp(),
                        clientMessageId = tempId,
                        isPending = true,
                        replyToId = replyId,
                        replyToText = replyText,
                        replyToSenderId = replySenderId,
                        senderAvatarUrl = currentUserAvatarUrl // Add avatar URL to ensure it shows immediately
                    )

                    // Add to pending messages and UI on the main thread to avoid synchronization issues
                    Handler(Looper.getMainLooper()).post {
                        synchronized(messagesList) {
                            val position = messagesList.size
                            messagesList.add(message)
                            pendingMessages[tempId] = message

                            // Update UI while still holding the lock
                            messageAdapter.notifyItemInserted(position)
                            recyclerViewMessages.scrollToPosition(position)
                        }
                    }

                    // Temporarily disable polling for a few seconds to avoid duplicate messages
                    // The message will still come in via WebSocket, and polling will resume later
                    disablePollingUntil = System.currentTimeMillis() + POLLING_DISABLE_DURATION // Disable for longer

                    // Add to recently sent set for additional deduplication
            recentlySentMessageTexts.add(messageText)
            // Trim set if it gets too large (keep last 20 messages)
            if (recentlySentMessageTexts.size > 20) {
                recentlySentMessageTexts.remove(recentlySentMessageTexts.iterator().next())
            }

            // Send message via HTTP POST
            sendMessageViaHttp(messageText, tempId, replyId, replyText, replySenderId)

                    // NOW clear reply state after all references to it are done
                    clearReplyState()

                } catch (e: Exception) {
                    // Log any error
                    Log.e(TAG, "Error in send button handler", e)
                    Toast.makeText(applicationContext, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        })
    }

    // Helper method to create ISO 8601 timestamp
    private fun createIsoTimestamp(): String {
        return getCurrentISOTimestamp()
    }

    // Method to send messages via HTTP
    private fun sendMessageViaHttp(messageText: String, tempId: String, replyId: Int?, replyText: String?, replySenderId: Int?, voiceDuration: Int? = null) {
        Thread {
            try {
                // Validate match ID before proceeding
                if (originalMatchId <= 0) {
                    Log.e(TAG, "Cannot send message: Invalid originalMatchId ($originalMatchId)")
                    Handler(Looper.getMainLooper()).post {
                        markMessageAsError(tempId)
                        Toast.makeText(applicationContext, "Error: ID de chat inválido", Toast.LENGTH_SHORT).show()
                    }
                    return@Thread
                }

                // Create the JSON request body
                val jsonObj = JSONObject()
                jsonObj.put("match_id", originalMatchId)
                jsonObj.put("sender_id", currentUserId)
                jsonObj.put("receiver_id", otherUserId)
                jsonObj.put("message_text", messageText)
                jsonObj.put("clientMessageId", tempId)

                // Add voice duration if provided
                if (voiceDuration != null && voiceDuration > 0) {
                    jsonObj.put("voiceDuration", voiceDuration)
                }

                // Add reply data if applicable
                if (replyId != null && replyId > 0) {
                    jsonObj.put("reply_to_id", replyId)
                    jsonObj.put("reply_to_text", replyText)
                    jsonObj.put("reply_to_sender_id", replySenderId)
                }

                val jsonBody = jsonObj.toString()

                // Log the request details for debugging
                Log.d(TAG, "Sending message to match_id=$originalMatchId, sender_id=$currentUserId, receiver_id=$otherUserId")
                Log.d(TAG, "Request body: $jsonBody")

                // Create the request
                val url = "$API_URL/chat/$originalMatchId/message"
                Log.d(TAG, "Sending message to URL: $url")

                val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
                val requestBody = jsonBody.toRequestBody(mediaType)

                val request = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()

                // Execute the request
                client.newCall(request).execute().use { response ->
                    val responseBody = response.body?.string()
                    Log.d(TAG, "HTTP Response: ${response.code} - $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        // Handle successful response
                        Handler(Looper.getMainLooper()).post {
                            confirmMessageDelivery(tempId)

                            // Parse the response to get the server-assigned message ID
                            try {
                                val responseJson = gson.fromJson(responseBody, Map::class.java)
                                if (responseJson["success"] == true) {
                                    val messageData = responseJson["message"] as? Map<*, *>
                                    val serverMessageId = messageData?.get("id") as? Double
                                    if (serverMessageId != null) {
                                        Log.d(TAG, "Message saved with server ID: ${serverMessageId.toInt()}")
                                    }
                                } else {
                                    // Server returned success=false
                                    val errorMessage = responseJson["message"] as? String
                                    Log.e(TAG, "Server returned success=false: $errorMessage")
                                    markMessageAsError(tempId)
                                    return@post
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error parsing server response", e)
                                // Don't mark as error here, as the HTTP request was successful
                            }

                            // After sending a message, ensure WebSocket connection is active for real-time updates
                            // This is critical - after sending via HTTP, ensure WebSocket is ready for incoming messages
                            ensureWebSocketConnection()

                            // Enviar broadcast para actualizar la lista de chats
                            val updateIntent = Intent("com.spyro.vmeet.UPDATE_CHAT_LIST")
                            // Asegurarse de que el broadcast sea explícito para Android 12+
                            updateIntent.setPackage(packageName)
                            sendBroadcast(updateIntent)
                            Log.d(TAG, "Sent broadcast to update chat list after sending message")
                        }
                    } else {
                        // Handle error
                        Log.e(TAG, "HTTP error: ${response.code} - $responseBody")
                        Handler(Looper.getMainLooper()).post {
                            markMessageAsError(tempId)

                            // Show more detailed error message
                            val errorMsg = when (response.code) {
                                403 -> {
                                    // This is a special case - the server is saying the user is not part of this match
                                    // We need to handle this by creating the match if it doesn't exist
                                    Log.e(TAG, "403 Forbidden: User not part of match. Attempting to create match...")
                                    createMatchIfNeeded(originalMatchId, currentUserId, otherUserId, tempId)
                                    return@post
                                }
                                404 -> "Error: Chat no encontrado (404)"
                                400 -> "Error: Solicitud inválida (400)"
                                401 -> "Error: No autorizado (401)"
                                500 -> "Error del servidor (500)"
                                else -> "Error de red (${response.code})"
                            }
                            Toast.makeText(applicationContext, errorMsg, Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending message via HTTP", e)
                Handler(Looper.getMainLooper()).post {
                    markMessageAsError(tempId)
                    Toast.makeText(applicationContext, "Error de conexión: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }.start()
    }

    private fun confirmMessageDelivery(tempId: String) {
        Log.d(TAG, "Confirming message delivery: $tempId")

        // Update UI on main thread
        Handler(Looper.getMainLooper()).post {
            synchronized(messagesList) {
                try {
                    // Find message in the list
                    val index = messagesList.indexOfFirst { it.clientMessageId == tempId }

                    if (index >= 0 && index < messagesList.size) {
                        // Update message as delivered
                        val updatedMessage = messagesList[index].copy(isPending = false)
                        messagesList[index] = updatedMessage
                        pendingMessages.remove(tempId)

                        // Update UI while still holding the lock
                        messageAdapter.notifyItemChanged(index)
                        Log.d(TAG, "Message marked as delivered: $tempId")
                    } else {
                        Log.e(TAG, "Cannot confirm message $tempId: invalid index $index (list size: ${messagesList.size})")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error confirming message delivery", e)
                }
            }
        }
    }

    private fun markMessageAsError(tempId: String) {
        Log.d(TAG, "Marking message as error: $tempId")

        // Update UI on main thread
        Handler(Looper.getMainLooper()).post {
            synchronized(messagesList) {
                try {
                    // Find message in the list
                    val index = messagesList.indexOfFirst { it.clientMessageId == tempId }

                    if (index >= 0 && index < messagesList.size) {
                        // Update message as error
                        val errorMessage = messagesList[index].copy(isError = true)
                        messagesList[index] = errorMessage
                        pendingMessages[tempId] = errorMessage

                        // Update UI while still holding the lock
                        messageAdapter.notifyItemChanged(index)
                        Log.d(TAG, "Message marked as error: $tempId")

                        // Show error to user
                        Toast.makeText(
                            applicationContext,
                            "No se pudo enviar el mensaje. Intenta de nuevo.",
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        Log.e(TAG, "Cannot mark message $tempId as error: invalid index $index (list size: ${messagesList.size})")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error marking message as error", e)
                }
            }
        }
    }

    /**
     * Create a match if it doesn't exist
     * This is used when we get a 403 error from the server, which means the user is not part of the match
     */
    private fun createMatchIfNeeded(matchId: Int, userId: Int, otherUserId: Int, tempId: String) {
        Log.d(TAG, "Creating match if needed: matchId=$matchId, userId=$userId, otherUserId=$otherUserId")

        // Show a toast to inform the user
        Toast.makeText(
            applicationContext,
            "Creando chat... Por favor, espera.",
            Toast.LENGTH_SHORT
        ).show()

        Thread {
            try {
                // Create the JSON request body
                val jsonObj = JSONObject()
                jsonObj.put("user_id_1", userId)
                jsonObj.put("user_id_2", otherUserId)
                jsonObj.put("match_id", matchId) // Send the desired match ID

                val jsonBody = jsonObj.toString()

                // Log the request details for debugging
                Log.d(TAG, "Creating match with: user_id_1=$userId, user_id_2=$otherUserId, match_id=$matchId")

                // Create the request
                val url = "$API_URL/matches/create"
                Log.d(TAG, "Sending match creation request to URL: $url")

                val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
                val requestBody = jsonBody.toRequestBody(mediaType)

                val request = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()

                // Execute the request
                client.newCall(request).execute().use { response ->
                    val responseBody = response.body?.string()
                    Log.d(TAG, "Match creation HTTP Response: ${response.code} - $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        // Handle successful response
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(
                                applicationContext,
                                "Chat creado. Intentando enviar mensaje de nuevo...",
                                Toast.LENGTH_SHORT
                            ).show()

                            // Try to send the message again
                            val message = pendingMessages[tempId]
                            if (message != null) {
                                Log.d(TAG, "Retrying to send message: $tempId")
                                sendMessageViaHttp(message.text, tempId, message.replyToId, message.replyToText, message.replyToSenderId)
                            } else {
                                Log.e(TAG, "Cannot retry sending message: message with tempId=$tempId not found in pendingMessages")
                            }
                        }
                    } else {
                        // Handle error
                        Log.e(TAG, "Match creation failed: ${response.code} - $responseBody")
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(
                                applicationContext,
                                "Error al crear el chat. Por favor, intenta de nuevo.",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error creating match", e)
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(
                        applicationContext,
                        "Error de conexión: ${e.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }.start()
    }

    private fun loadChatHistory() {
        Log.d(TAG, "Loading chat history for matchId=$matchId (originalMatchId=$originalMatchId)")

        // Show loading indicator
        progressBar.visibility = View.VISIBLE

        // Validate parameters before loading
        if (originalMatchId <= 0) {
            Log.e(TAG, "Cannot load chat history: Invalid originalMatchId ($originalMatchId)")
            showErrorOnMainThread("Error: ID de chat inválido")
            return
        }

        // Determine which match IDs to load
        val matchIdsToLoad = mutableListOf<Int>()
        matchIdsToLoad.add(originalMatchId) // Always include the original match ID

        // Add all other match IDs if available
        if (allMatchIds != null && allMatchIds!!.isNotEmpty()) {
            for (id in allMatchIds!!) {
                if (id != originalMatchId && id > 0) {
                    matchIdsToLoad.add(id)
                }
            }
        }

        Log.d(TAG, "Will load messages from these match IDs: $matchIdsToLoad")

        // Update the primary match ID on the server
        updatePrimaryMatchId()

        // In a background thread, fetch history
        Thread {
            // Create a list to hold all messages from all match IDs
            val allMessages = mutableListOf<Message>()

            // Load messages from each match ID
            for (currentMatchId in matchIdsToLoad) {
                try {
                    val historyUrl = "$API_URL/chat/$currentMatchId/messages"
                    Log.d(TAG, "Requesting chat history from: $historyUrl")

                    val request = Request.Builder()
                        .url(historyUrl)
                        .get()
                        .build()

                    val response = client.newCall(request).execute()

                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()

                        if (responseBody != null) {
                            Log.d(TAG, "History response: $responseBody")

                            try {
                                // Parse the response
                                data class HistoryResponse(val success: Boolean, val messages: List<Message>)

                                val historyResponse = gson.fromJson(responseBody, HistoryResponse::class.java)
                                Log.d(TAG, "Successfully parsed history response for matchId=$currentMatchId: success=${historyResponse.success}, messageCount=${historyResponse.messages.size}")

                                // Debug parsed messages with more detail
                                historyResponse.messages.forEachIndexed { index, message ->
                                    Log.d(TAG, "Parsed message[$index] from matchId=$currentMatchId: id=${message.id}, matchId=${message.matchId}, read=${message.isRead}")
                                }

                                // Add messages from this match ID to the combined list
                                if (historyResponse.success) {
                                    allMessages.addAll(historyResponse.messages)
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error parsing history response for matchId=$currentMatchId", e)
                            }
                        } else {
                            Log.e(TAG, "Empty response body for chat history from matchId=$currentMatchId")
                        }
                    } else {
                        Log.e(TAG, "Server error code: ${response.code} for chat history from matchId=$currentMatchId")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error loading history for matchId=$currentMatchId", e)
                }
            }

            // After loading messages from all match IDs, update the UI
            Handler(Looper.getMainLooper()).post {
                if (allMessages.isNotEmpty()) {
                    // Process all messages
                    synchronized(messagesList) {
                        // Log all received messages for debugging
                        Log.d(TAG, "Loaded a total of ${allMessages.size} messages from all match IDs")

                        // Inject sender_name for messages from otherUser
                        val messagesWithSenderName = allMessages.map { msg ->
                            if (msg.senderId == otherUserId && !otherUserName.isNullOrEmpty()) {
                                val newMetadata = msg.metadata.toMutableMap()
                                newMetadata["sender_name"] = otherUserName!!
                                newMetadata["username"] = otherUserName!!
                                msg.copy(metadata = newMetadata)
                            } else {
                                msg
                            }
                        }

                        messagesList.clear()
                        messagesList.addAll(messagesWithSenderName)

                        // Sort messages by time if needed
                        val sortedMessages = messagesList.sortedBy {
                            try {
                                // Try to parse timestamp
                                if (it.createdAt.contains("T")) {
                                    // ISO format - already chronological when sorted as string
                                    it.createdAt
                                } else {
                                    // Unix timestamp or other format
                                    it.createdAt
                                }
                            } catch (e: Exception) {
                                // Default value for sorting
                                "0"
                            }
                        }

                        // Create a copy of sorted messages to avoid modification issues
                        val messagesToDisplay = ArrayList(sortedMessages)

                        // Process GIF messages - Extract GIF URLs from text if needed
                        for (i in messagesToDisplay.indices) {
                            val message = messagesToDisplay[i]
                            // Check if this is a GIF message without a gifUrl
                            if ((message.text.startsWith("GIF:") || message.messageType == "gif") && message.gifUrl == null) {
                                // Extract GIF URL from the message text if it contains a URL
                                val urlPattern = "(https?://[^\\s]+\\.gif)".toRegex(RegexOption.IGNORE_CASE)
                                val matcher = urlPattern.find(message.text)
                                if (matcher != null) {
                                    val gifUrl = matcher.groupValues[1]
                                    Log.d(TAG, "Extracted GIF URL from message text: $gifUrl")
                                    // Create a new message with the extracted GIF URL
                                    messagesToDisplay[i] = message.copy(
                                        messageType = "gif",
                                        gifUrl = gifUrl
                                    )
                                }
                            }
                        }

                        // Find unread messages received by current user and mark them as read
                        val unreadMessageIds = mutableListOf<Int>()
                        for (message in messagesToDisplay) {
                            // Only mark messages received by current user (sent by other user)
                            if (message.receiverId == currentUserId && !message.isRead && message.id > 0) {
                                unreadMessageIds.add(message.id)
                            }
                        }

                        // Clear and set all at once
                        messagesList.clear()
                        messagesList.addAll(messagesToDisplay)

                        // If we have unread messages, mark them as read
                        if (unreadMessageIds.isNotEmpty()) {
                            markMessagesAsRead(unreadMessageIds)
                        }

                        // Notify adapter inside the synchronized block
                        messageAdapter.notifyDataSetChanged()
                    }

                    // Scroll to bottom if there are messages
                    if (messagesList.isNotEmpty()) {
                        recyclerViewMessages.scrollToPosition(messagesList.size - 1)
                        Log.d(TAG, "Successfully loaded and displayed ${messagesList.size} messages")

                        // Also check read status after loading messages
                        checkAndFixMessageReadStatus()

                        // Enviar broadcast para actualizar la lista de chats
                        val updateIntent = Intent("com.spyro.vmeet.UPDATE_CHAT_LIST")
                        // Asegurarse de que el broadcast sea explícito para Android 12+
                        updateIntent.setPackage(packageName)
                        sendBroadcast(updateIntent)
                        Log.d(TAG, "Sent broadcast to update chat list after loading history")
                    } else {
                        Log.w(TAG, "No messages found for matchId=$originalMatchId")
                    }
                } else {
                    // No messages found
                    progressBar.visibility = View.GONE
                    Log.w(TAG, "No messages found for any match ID")
                }
            }
        }.start()
    }

    // New method to mark messages as read on the server
    private fun markMessagesAsRead(messageIds: List<Int>) {
        if (messageIds.isEmpty()) return

        Log.d(TAG, "Marking messages as read: $messageIds")

        // Also send a WebSocket message for real-time updates
        try {
            if (webSocket != null) {
                val wsJson = gson.toJson(mapOf(
                    "type" to "mark_messages_read",
                    "messageIds" to messageIds,
                    "matchId" to originalMatchId
                ))
                webSocket?.send(wsJson)
                Log.d(TAG, "Sent WebSocket mark_messages_read message")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sending WebSocket mark_messages_read message", e)
        }

        Thread {
            try {
                val markReadUrl = "$API_URL/chat/mark-read"
                Log.d(TAG, "Sending mark-read request to: $markReadUrl")

                val jsonString = gson.toJson(mapOf(
                    "messageIds" to messageIds,
                    "receiverId" to currentUserId // Add the receiver ID (current user)
                ))

                // Use newer OkHttp API approach
                val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
                val requestBody = jsonString.toRequestBody(mediaType)

                val request = Request.Builder()
                    .url(markReadUrl)
                    .post(requestBody)
                    .build()

                try {
                    val response = client.newCall(request).execute()

                    if (response.isSuccessful) {
                        Log.d(TAG, "Successfully marked messages as read: $messageIds")

                        // Update local message objects to reflect read status
                        Handler(Looper.getMainLooper()).post {
                            synchronized(messagesList) {
                                var updated = false
                                for (i in messagesList.indices) {
                                    val message = messagesList[i]
                                    if (messageIds.contains(message.id)) {
                                        // Update the message with isRead=true
                                        messagesList[i] = message.copy(isRead = true)
                                        updated = true
                                    }
                                }

                                if (updated) {
                                    messageAdapter.notifyDataSetChanged()
                                    // Also run the read status check again to be sure
                                    checkAndFixMessageReadStatus()
                                }
                            }
                        }
                    } else {
                        Log.e(TAG, "Failed to mark messages as read: ${response.code}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error sending mark-read request", e)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in markMessagesAsRead", e)
            }
        }.start()
    }

    private fun showErrorOnMainThread(message: String) {
        Handler(Looper.getMainLooper()).post {
            Toast.makeText(applicationContext, message, Toast.LENGTH_SHORT).show()
            progressBar.visibility = View.GONE
        }
    }

    /**
     * Update the primary match ID on the server
     * This ensures that the match ID used in this chat becomes the primary one
     * for future interactions between these users
     */
    private fun updatePrimaryMatchId() {
        // Only proceed if we have valid user IDs and match ID
        if (currentUserId <= 0 || otherUserId <= 0 || originalMatchId <= 0) {
            Log.e(TAG, "Cannot update primary match ID: Invalid user IDs or match ID")
            // Asegurarse de que el spinner de carga se oculte
            runOnUiThread {
                progressBar.visibility = View.GONE
            }
            return
        }

        // Run in background thread
        Thread {
            try {
                val updateUrl = "$API_URL/matches/update-primary"
                Log.d(TAG, "Updating primary match ID to $originalMatchId for users $currentUserId and $otherUserId")

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("otherUserId", otherUserId)
                jsonPayload.put("primaryMatchId", originalMatchId)

                // Create request
                val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
                val requestBody = jsonPayload.toString().toRequestBody(mediaType)

                val request = Request.Builder()
                    .url(updateUrl)
                    .put(requestBody)
                    .build()

                // Execute request
                try {
                    val response = client.newCall(request).execute()

                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()

                        if (responseBody != null) {
                            val jsonResponse = JSONObject(responseBody)
                            val success = jsonResponse.optBoolean("success", false)

                            if (success) {
                                Log.d(TAG, "Successfully updated primary match ID: $responseBody")
                            } else {
                                Log.e(TAG, "Server returned success=false for update primary match ID: $responseBody")
                            }
                        }
                    } else {
                        Log.e(TAG, "Error updating primary match ID: ${response.code}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error sending update primary match ID request", e)
                } finally {
                    // Asegurarse de que el spinner de carga se oculte, independientemente del resultado
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in updatePrimaryMatchId", e)
                // Asegurarse de que el spinner de carga se oculte en caso de error
                runOnUiThread {
                    progressBar.visibility = View.GONE
                }
            }
        }.start()
    }

    override fun onStart() {
        super.onStart()
        Log.d(TAG, "onStart")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume called")

        // Start real-time polling for image messages and as WebSocket fallback
        startRealTimePolling()

        // Set chat as active when user is viewing it
        isChatActive = true

        // Ensure we have the current user's avatar URL
        if (currentUserAvatarUrl.isNullOrEmpty()) {
            Log.d(TAG, "Avatar URL not available in onResume, fetching from server")
            fetchCurrentUserAvatar()
        }

        // IMPROVED: Force re-establish WebSocket connection to ensure it's active
        rebuildWebSocketConnection()

        // Start message polling as a fallback for real-time updates
        startMessagePolling()

        // Start reaction polling
        startReactionPolling()

        // Mark unread messages as read when the user views the chat
        markUnreadMessagesAsRead()

        // Resume status updates
        startStatusUpdates()

        // Send chat visibility status to server
        sendChatVisibilityStatus(true)

        // Start periodic connection checks
        startConnectionChecks()

        // Reset any potential keyboard issues
        fixKeyboardIssues()

        // Register shake detection
        registerShakeDetection()
    }

    /**
     * Fix keyboard issues by clearing and requesting focus
     * This helps prevent the black margin issue when the keyboard is hidden
     */
    private fun fixKeyboardIssues() {
        try {
            // Clear focus and request it again after a delay
            editTextMessage.clearFocus()

            // Request layout to ensure proper UI update
            findViewById<View>(android.R.id.content).requestLayout()

            // Request window insets to be applied
            ViewCompat.requestApplyInsets(findViewById(android.R.id.content))
        } catch (e: Exception) {
            Log.e(TAG, "Error fixing keyboard issues", e)
        }
    }

    /**
     * Handle configuration changes like keyboard visibility or orientation changes
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "Configuration changed")

        // Fix any keyboard issues
        fixKeyboardIssues()

        // Request insets to be applied again
        ViewCompat.requestApplyInsets(findViewById(android.R.id.content))

        // If we have messages, scroll to the bottom to ensure visibility
        if (messagesList.isNotEmpty()) {
            recyclerViewMessages.post {
                recyclerViewMessages.scrollToPosition(messagesList.size - 1)
            }
        }
    }

    // Add a new method to start periodic connection checks
    private fun startConnectionChecks() {
        // Stop any existing checks
        stopConnectionChecks()

        connectionCheckRunnable = object : Runnable {
            override fun run() {
                Log.d(TAG, "Performing periodic WebSocket connection check")

                // Verify if WebSocket is connected
                val wsState = when (webSocket?.request()) {
                    null -> "DISCONNECTED"
                    else -> "CONNECTED" // We can't directly check the state but we can verify it's not null
                }

                if (wsState == "DISCONNECTED") {
                    Log.d(TAG, "Periodic check found WebSocket is disconnected, reconnecting")
                    rebuildWebSocketConnection()
                } else {
                    // Send a ping to keep the connection alive
                    try {
                        val pingMessage = gson.toJson(mapOf("type" to "ping"))
                        webSocket?.send(pingMessage)
                        Log.d(TAG, "Sent ping to keep WebSocket alive")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error sending ping, will reconnect", e)
                        rebuildWebSocketConnection()
                    }
                }

                // Schedule the next check
                connectionCheckHandler.postDelayed(this, CONNECTION_CHECK_INTERVAL)
            }
        }

        // Start checking immediately
        connectionCheckRunnable?.let {
            connectionCheckHandler.post(it)
        }
    }

    private fun stopConnectionChecks() {
        connectionCheckRunnable?.let {
            connectionCheckHandler.removeCallbacks(it)
        }
        connectionCheckRunnable = null
    }

    // Add a new method to rebuild WebSocket from scratch
    private fun rebuildWebSocketConnection() {
        Log.d(TAG, "Rebuilding WebSocket connection from scratch")
        // Force close any existing connection
        webSocket?.close(NORMAL_CLOSURE_STATUS, "Rebuilding connection")
        webSocket?.cancel() // Force close
        webSocket = null

        // Create a new connection
        establishWebSocketConnection()
    }

    // Update onPause to also maintain the WebSocket
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause called")

        // Stop real-time polling when app is in background
        stopRealTimePolling()

        // Set chat as inactive when user is not viewing it
        isChatActive = false

        // Stop polling mechanisms
        stopMessagePolling()

        // Stop the status update handler
        statusRunnable?.let { statusHandler.removeCallbacks(it) }

        // Send visibility update but keep connection open
        sendChatVisibilityStatus(false)

        // Keep connection check active even in background
        // Don't stop connection checks here!

        // Unregister shake detection to save battery
        unregisterShakeDetection()
    }

    // Update onStop to not close the WebSocket
    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop called")

        // Stop any audio playback/recording
        if (isRecording) {
            audioRecorder?.stopRecording()
            isRecording = false
        }
        audioPlayer?.stopAudio()

        // Let server know we're not viewing this chat anymore
        try {
            sendChatVisibilityStatus(false)
        } catch (e: Exception) {
            Log.e(TAG, "Error sending final visibility update", e)
        }

        // Stop connection checks in onStop but keep WebSocket alive
        stopConnectionChecks()
        // We still keep the WebSocket connection open for background notifications
    }

    // Update onDestroy to also clear connection check handlers
    override fun onDestroy() {
        Log.d(TAG, "onDestroy called")

        // Clean up all resources
        try {
            // Close WebSocket with normal closure status
            webSocket?.close(NORMAL_CLOSURE_STATUS, "Activity destroyed")
            webSocket?.cancel() // Force close any lingering connections
            webSocket = null

            // Remove any pending handlers
            statusHandler.removeCallbacksAndMessages(null)
            messagePollingHandler.removeCallbacksAndMessages(null)
            connectionCheckHandler.removeCallbacksAndMessages(null)

            // Clean up audio resources
            audioRecorder?.resetRecorder()
            audioPlayer?.releaseMediaPlayer()

            // Unregister shake detection sensor
            unregisterShakeDetection()
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up resources", e)
        }

        super.onDestroy()
    }

    /**
     * Start polling for new messages as a fallback mechanism for real-time updates
     * This ensures messages appear even if WebSockets aren't working properly
     */
    private fun startMessagePolling() {
        // Get the highest message ID we currently have
        val maxId = messagesList.maxOfOrNull { it.id } ?: -1
        lastMessageId = maxId

        // Cancel any existing polling
        stopMessagePolling()

        Log.d(TAG, "Starting message polling with lastMessageId=$lastMessageId")

        // Create a new polling runnable
        messagePollingRunnable = object : Runnable {
            override fun run() {
                pollForNewMessages()
                // Schedule next execution
                messagePollingHandler.postDelayed(this, MESSAGE_POLL_INTERVAL)
            }
        }

        // Start polling immediately
        messagePollingHandler.post(messagePollingRunnable!!)
    }

    /**
     * Stop polling for new messages
     */
    private fun stopMessagePolling() {
        messagePollingRunnable?.let {
            messagePollingHandler.removeCallbacks(it)
            Log.d(TAG, "Stopped message polling")
        }
        messagePollingRunnable = null
    }

    /**
     * Poll the server for new messages since our last known message ID
     */
    private fun pollForNewMessages() {
        val currentTime = System.currentTimeMillis()
        lastMessagePoll = currentTime

        // Skip polling if we've temporarily disabled it (after sending a message)
        if (currentTime < disablePollingUntil) {
            Log.d(TAG, "Skipping message polling - temporarily disabled for ${(disablePollingUntil - currentTime)/1000} more seconds")
            return
        }

        // Minimal safety check: Skip polling if we've sent a message in the last 2 seconds to prevent race conditions
        if (!recentlySentMessageTexts.isEmpty() && currentTime - disablePollingUntil < 2000) {
            Log.d(TAG, "Safety check: Skipping polling briefly since we recently sent messages")
            return
        }

        // Only proceed if we have a valid match ID
        if (matchId <= 0) return

        Log.d(TAG, "Polling for new messages since ID $lastMessageId")

        // Build the URL with a 'since_id' parameter
        val url = "$API_URL/chat/$matchId/messages?since_id=$lastMessageId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : okhttp3.Callback {
            override fun onFailure(call: okhttp3.Call, e: java.io.IOException) {
                Log.e(TAG, "Failed to poll for new messages", e)
            }

            override fun onResponse(call: okhttp3.Call, response: okhttp3.Response) {
                if (!response.isSuccessful) {
                    Log.e(TAG, "Error polling for messages: ${response.code}")
                    return
                }

                val responseBody = response.body?.string()
                if (responseBody != null) {
                    try {
                        val jsonResponse = JSONObject(responseBody)
                        if (jsonResponse.getBoolean("success")) {
                            val messagesArray = jsonResponse.getJSONArray("messages")

                            if (messagesArray.length() > 0) {
                                Log.d(TAG, "Polling found ${messagesArray.length()} new messages")

                                // Parse messages and add to our list
                                val newMessages = ArrayList<Message>()
                                var highestId = lastMessageId

                                for (i in 0 until messagesArray.length()) {
                                    val messageJson = messagesArray.getJSONObject(i)
                                    val messageString = messageJson.toString()

                                    // Debug logging for buzz messages
                                    val messageType = messageJson.optString("message_type", "text")
                                    val messageText = messageJson.optString("message_text", "")
                                    if (messageType == "buzz" || messageText.contains("BuZzZzzZzZZ")) {
                                        Log.d(TAG, "🔔 BUZZ DEBUG - Polling found buzz message: type=$messageType, text=$messageText")
                                        Log.d(TAG, "🔔 BUZZ DEBUG - Full JSON: $messageString")
                                    }

                                    val message = gson.fromJson(messageString, Message::class.java)

                                    // Debug the parsed message
                                    if (message.messageType == "buzz" || message.text.contains("BuZzZzzZzZZ")) {
                                        Log.d(TAG, "🔔 BUZZ DEBUG - Parsed message: messageType=${message.messageType}, text=${message.text}, isBuzzMessage=${message.isBuzzMessage()}")
                                    }

                                    // Update highest ID
                                    if (message.id > highestId) {
                                        highestId = message.id
                                    }

                                    newMessages.add(message)
                                }

                                // Update our last message ID
                                lastMessageId = highestId

                                // Update UI on main thread
                                if (newMessages.isNotEmpty()) {
                                    Handler(Looper.getMainLooper()).post {
                                        // Process all new messages
                                        for (message in newMessages) {
                                            processIncomingMessage(message)
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing poll response", e)
                    }
                }
            }
        })
    }

    // Mark unread messages as read when the user enters the chat
    private fun markUnreadMessagesAsRead() {
        // Only mark messages as read if we're actively viewing the chat
        if (!isChatActive) {
            Log.d(TAG, "Not marking messages as read because chat is not active")
            return
        }

        // IMPORTANT: Only mark messages as read if they were RECEIVED by the current user
        // Never mark messages sent BY the current user as read here
        val unreadMessages = messagesList
            .filter { message ->
                // Very strict filtering to ensure we only mark messages sent TO us (not BY us)
                !message.isRead && // Message is not already read
                message.receiverId == currentUserId && // Message was received by us
                message.senderId != currentUserId && // Message was NOT sent by us
                message.id > 0 // Message has a valid ID
            }
            .map { it.id }

        if (unreadMessages.isNotEmpty()) {
            Log.d(TAG, "Marking ${unreadMessages.size} unread RECEIVED messages as read on resume")
            markMessagesAsRead(unreadMessages)
        } else {
            Log.d(TAG, "No unread received messages to mark as read")
        }
    }

    // Ensure WebSocket connection is active
    private fun ensureWebSocketConnection() {
        if (webSocket == null) {
            Log.d(TAG, "WebSocket connection not found, establishing new one")
            establishWebSocketConnection()
        } else {
            // Even if WebSocket exists, verify it's in OPEN state
            val wsState = when (webSocket?.request()) {
                null -> "DISCONNECTED"
                else -> "CONNECTED" // We can't directly check the state but we can verify it's not null
            }
            Log.d(TAG, "WebSocket connection exists in state $wsState, updating active chat ID")

            try {
                // Try to send a message - if this fails, we'll catch the exception and reconnect
                val pingMessage = gson.toJson(mapOf("type" to "ping"))
                webSocket?.send(pingMessage)
                // If successful, update active chat ID
                updateActiveChatId()
            } catch (e: Exception) {
                Log.e(TAG, "Error communicating with existing WebSocket, reconnecting", e)
                webSocket?.cancel() // Force close any problematic connection
                webSocket = null // Reset to null
                establishWebSocketConnection() // Establish a new connection
            }
        }
    }

    private fun establishWebSocketConnection() {
        Log.d(TAG, "Establishing WebSocket connection")

        try {
            // Clean up any existing connection
            webSocket?.close(NORMAL_CLOSURE_STATUS, "New WebSocket connection")

            // Create a dedicated client for this WebSocket connection
            val wsClient = OkHttpClient.Builder()
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .connectTimeout(30, TimeUnit.SECONDS)
                .pingInterval(15, TimeUnit.SECONDS)
                .build()

            // Create a new request
            val request = Request.Builder().url(WS_URL).build()

            // Create and store new WebSocket with the dedicated client
            webSocket = wsClient.newWebSocket(request, object : WebSocketListener() {
                override fun onOpen(webSocket: WebSocket, response: Response) {
                    Log.d(TAG, "WebSocket connection opened successfully")
                    reconnectAttempts = 0 // Reset counter on successful connection

                    // Send identify message with active chat ID
                    try {
                        val identifyJson = gson.toJson(mapOf(
                            "type" to "identify",
                            "userId" to currentUserId,
                            "activeMatchId" to originalMatchId  // Send which chat we're currently viewing
                        ))
                        webSocket.send(identifyJson)
                        Log.d(TAG, "Sent identify message for user $currentUserId with activeMatchId=$originalMatchId")

                        // Also update chat visibility
                        sendChatVisibilityStatus(isChatActive)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error sending identify message", e)
                    }
                }

                override fun onMessage(webSocket: WebSocket, text: String) {
                    Log.d(TAG, "Received WebSocket message: $text")
                    try {
                        val response = gson.fromJson(text, Map::class.java)
                        val type = response["type"] as? String

                        // Add detailed logging
                        Log.d(TAG, "Processing WebSocket message of type: $type")

                        when (type) {
                            "identified" -> {
                                Log.d(TAG, "Successfully identified with WebSocket server")
                            }
                            "active_chat_updated" -> {
                                val activeId = response["activeMatchId"] as? Double
                                Log.d(TAG, "Server confirmed active chat update: ${activeId?.toInt()}")
                            }
                            "chat_message", "new_message", "message_saved", "voice_message", "voice_message_saved" -> {
                                // Handle all message types in a consistent way
                                // This ensures messages are received in real-time regardless of message type
                                Log.d(TAG, "🔴 RECEIVED REAL-TIME MESSAGE of type: $type")
                                val payload = response["payload"] as? Map<*, *>

                                if (payload != null) {
                                    Log.d(TAG, "Raw message payload (type=$type): $payload contains read=${payload["read"]} and isRead=${payload["isRead"]}")

                                    // Try to safely deserialize
                                    try {
                                        val payloadJson = gson.toJson(payload)
                                        Log.d(TAG, "Deserializing payload: $payloadJson")

                                        // Debug logging for buzz messages in WebSocket
                                        val messageType = (payload as? Map<*, *>)?.get("message_type") as? String ?: "text"
                                        val messageText = (payload as? Map<*, *>)?.get("message_text") as? String ?: ""
                                        if (messageType == "buzz" || messageText.contains("BuZzZzzZzZZ")) {
                                            Log.d(TAG, "🔔 BUZZ DEBUG - WebSocket received buzz message: type=$messageType, text=$messageText")
                                            Log.d(TAG, "🔔 BUZZ DEBUG - WebSocket payload: $payloadJson")
                                        }

                                        var message = gson.fromJson(payloadJson, Message::class.java)

                                        // Inject sender_name if message is from otherUser
                                        if (message.senderId == otherUserId && !otherUserName.isNullOrEmpty()) {
                                            val newMetadata = message.metadata.toMutableMap()
                                            newMetadata["sender_name"] = otherUserName!!
                                            newMetadata["username"] = otherUserName!!
                                            message = message.copy(metadata = newMetadata)
                                            Log.d(TAG, "WebSocket onMessage: Added sender_name '$otherUserName' to metadata for message ID ${message.id}")
                                        }

                                        Log.d(TAG, "🔄 Successfully deserialized message: ID=${message.id}, senderId=${message.senderId}, receiverId=${message.receiverId}, matchId=${message.matchId}")

                                        // Important: Extra validation for read status - SENT messages should never start as read
                                        val correctedMessage = if (message.senderId == currentUserId && message.isRead) {
                                            Log.d(TAG, "Correcting incorrect read status for message sent by us (ID=${message.id})")
                                            message.copy(isRead = false)
                                        } else {
                                            message
                                        }

                                        // Add matchId check and validation
                                        if (originalMatchId <= 0) {
                                            Log.e(TAG, "⚠️ Invalid originalMatchId: $originalMatchId, cannot process message")
                                        } else if (correctedMessage.matchId != originalMatchId) {
                                            Log.d(TAG, "Ignoring message for matchId=${correctedMessage.matchId}, current chat is $originalMatchId")
                                        } else {
                                            // Process on main thread to avoid threading issues
                                            Handler(Looper.getMainLooper()).post {
                                                Log.d(TAG, "🏁 Processing message on main thread: ${correctedMessage.id}, text: ${correctedMessage.text.take(20)}...")
                                                processIncomingMessage(correctedMessage)
                                            }
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG, "❌ Error deserializing message payload", e)
                                        // Try fallback parsing as a last resort
                                        try {
                                            Handler(Looper.getMainLooper()).post {
                                                processIncomingMessageFallback(payload)
                                            }
                                        } catch (e2: Exception) {
                                            Log.e(TAG, "❌ Fallback parsing also failed", e2)
                                        }
                                    }
                                } else {
                                    Log.e(TAG, "❌ Received message with null payload")
                                }
                            }
                            "messages_read" -> {
                                // Update read status when the other user marks messages as read
                                val reader = (response["reader"] as? Double)?.toInt() ?: 0
                                val matchId = (response["matchId"] as? Double)?.toInt() ?: 0

                                // Only process if it's for our match and from the other user
                                if (matchId == originalMatchId && reader == otherUserId) {
                                    val messageIds = (response["messageIds"] as? List<*>)?.mapNotNull {
                                        when (it) {
                                            is Double -> it.toInt()
                                            is Int -> it
                                            else -> null
                                        }
                                    } ?: emptyList()

                                    Log.d(TAG, "Received read receipt from user $reader for messages: $messageIds")

                                    // Update message read status in UI
                                    Handler(Looper.getMainLooper()).post {
                                        synchronized(messagesList) {
                                            var updated = false
                                            for (i in messagesList.indices) {
                                                val message = messagesList[i]
                                                // ONLY update messages SENT BY us (current user)
                                                // AND make sure we don't falsely mark unread messages as read
                                                if (message.senderId == currentUserId &&
                                                    messageIds.contains(message.id) &&
                                                    !message.isRead) {

                                                    Log.d(TAG, "Marking message ${message.id} as read because other user ${reader} read it")
                                                    messagesList[i] = message.copy(isRead = true)
                                                    updated = true
                                                }
                                            }
                                            if (updated) {
                                                messageAdapter.notifyDataSetChanged()
                                            }
                                        }
                                    }
                                }
                            }
                            "chat_visibility_update" -> {
                                // Extract info about which user is viewing which chat
                                val userId = (response["userId"] as? Double)?.toInt() ?: 0
                                val matchId = (response["matchId"] as? Double)?.toInt() ?: 0
                                val isVisible = (response["isVisible"] as? Boolean) ?: false

                                // Check if this update is about the other user viewing our current chat
                                if (userId == otherUserId && matchId == originalMatchId) {
                                    Log.d(TAG, "Other user (id=$otherUserId) is ${if (isVisible) "now viewing" else "no longer viewing"} this chat")

                                    // Update UI to reflect the other user's status
                                    // Only show Toast if the user transitions from not viewing to viewing
                                    if (isVisible && !otherUserIsViewing) {
                                        Handler(Looper.getMainLooper()).post {
                                            Toast.makeText(
                                                applicationContext,
                                                "${otherUserName ?: "Usuario"} está viendo el chat",
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    }

                                    // Actualizar el estado de visualización
                                    val oldValue = otherUserIsViewing
                                    otherUserIsViewing = isVisible

                                    // Si el estado cambió, actualizar la visualización del estado
                                    if (oldValue != isVisible) {
                                        Handler(Looper.getMainLooper()).post {
                                            // Si ahora está viendo el chat, mostrar "En línea"
                                            if (isVisible) {
                                                updateStatusDisplay(true, null)
                                            } else {
                                                // Si ya no está viendo el chat, verificar el estado real
                                                checkUserStatus()
                                            }
                                        }
                                    }
                                }
                            }
                            "ping" -> {
                                // Respond to server ping with a pong silently
                                // No logging to avoid cluttering the log
                                val pongJson = gson.toJson(mapOf("type" to "pong"))
                                webSocket.send(pongJson)
                            }
                            "error" -> {
                                // Handle error messages from server
                                val errorMessage = response["message"] as? String
                                Log.e(TAG, "Server reported WebSocket error: $errorMessage")
                            }
                            "message_reaction_added" -> {
                                Log.d(TAG, "Received reaction added event")
                                val payload = response["payload"] as? Map<*, *>

                                if (payload != null) {
                                    try {
                                        // Extract reaction data
                                        val messageId = (payload["messageId"] as? Double)?.toInt() ?: -1
                                        val userId = (payload["userId"] as? Double)?.toInt() ?: -1
                                        val reactionType = payload["reactionType"] as? String ?: ""

                                        if (messageId > 0 && userId > 0 && reactionType.isNotEmpty()) {
                                            Log.d(TAG, "Processing reaction: $reactionType from user $userId for message $messageId")

                                            val reaction = MessageReaction(
                                                id = (payload["id"] as? Double)?.toInt() ?: -1,
                                                messageId = messageId,
                                                userId = userId,
                                                reaction = reactionType,
                                                createdAt = payload["created_at"] as? String ?: getCurrentISOTimestamp()
                                            )

                                            // Process on main thread
                                            Handler(Looper.getMainLooper()).post {
                                                synchronized(messagesList) {
                                                    var updated = false
                                                    for (i in messagesList.indices) {
                                                        val message = messagesList[i]
                                                        if (message.id == messageId) {
                                                            // Initialize reactions list if needed
                                                            if (message.reactions == null) {
                                                                message.reactions = mutableListOf()
                                                            }

                                                            // Get the mutable list and add the reaction
                                                            val reactionsList = message.reactions as? MutableList<MessageReaction>
                                                            reactionsList?.let { list ->
                                                                // Remove existing reaction from this user if any
                                                                list.removeIf { it.userId == reaction.userId }
                                                                // Add the new reaction
                                                                list.add(reaction)
                                                            }

                                                            updated = true
                                                            break
                                                        }
                                                    }

                                                    if (updated) {
                                                        messageAdapter.notifyDataSetChanged()
                                                    }
                                                }
                                            }
                                        } else {
                                            Log.e(TAG, "Invalid reaction data: messageId=$messageId, userId=$userId, reaction=$reactionType")
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG, "Error processing reaction_added event", e)
                                    }
                                }
                            }
                            "message_reaction_removed" -> {
                                Log.d(TAG, "Received reaction removed event")
                                val payload = response["payload"] as? Map<*, *>

                                if (payload != null) {
                                    try {
                                        // Extract reaction data
                                        val messageId = (payload["messageId"] as? Double)?.toInt() ?: -1
                                        val userId = (payload["userId"] as? Double)?.toInt() ?: -1

                                        if (messageId > 0 && userId > 0) {
                                            Log.d(TAG, "Processing reaction removal from user $userId for message $messageId")

                                            // Process on main thread
                                            Handler(Looper.getMainLooper()).post {
                                                synchronized(messagesList) {
                                                    var updated = false
                                                    for (i in messagesList.indices) {
                                                        val message = messagesList[i]
                                                        if (message.id == messageId) {
                                                            // Get the mutable list and remove the reaction
                                                            val reactionsList = message.reactions as? MutableList<MessageReaction>
                                                            reactionsList?.let { list ->
                                                                // Remove any reactions from this user
                                                                list.removeIf { it.userId == userId }
                                                            }

                                                            updated = true
                                                            break
                                                        }
                                                    }

                                                    if (updated) {
                                                        messageAdapter.notifyDataSetChanged()
                                                    }
                                                }
                                            }
                                        } else {
                                            Log.e(TAG, "Invalid reaction removal data: messageId=$messageId, userId=$userId")
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG, "Error processing reaction_removed event", e)
                                    }
                                }
                            }
                            "error" -> {
                                // Handle error messages from server
                                val errorMessage = response["message"] as? String
                                Log.e(TAG, "Server reported WebSocket error: $errorMessage")
                            }
                            "pong" -> {
                                // Ignore pong messages silently
                                // No logging to avoid cluttering the log
                            }
                            else -> {
                                // Only log unknown message types that are not ping/pong
                                if (type != "ping" && type != "pong") {
                                    Log.d(TAG, "Received unknown WebSocket message type: $type")
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing WebSocket message", e)
                    }
                }

                override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                    Log.e(TAG, "WebSocket connection failure", t)

                    // Attempt reconnection
                    attemptReconnection()
                }

                override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                    Log.d(TAG, "WebSocket connection closed: $code / $reason")

                    // Don't reconnect on normal closure (1000)
                    if (code != 1000) {
                        attemptReconnection()
                    }
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "Error establishing WebSocket connection", e)
            attemptReconnection()
        }
    }

    // Add a fallback method to handle message parsing when normal deserialization fails
    private fun processIncomingMessageFallback(payload: Map<*, *>) {
        try {
            Log.d(TAG, "Attempting fallback message processing")

            // Extract essential fields manually
            val id = when (val rawId = payload["id"]) {
                is Double -> rawId.toInt()
                is Int -> rawId
                else -> -1
            }

            val matchId = when (val rawMatchId = payload["match_id"] ?: payload["matchId"]) {
                is Double -> rawMatchId.toInt()
                is Int -> rawMatchId
                else -> -1
            }

            val senderId = when (val rawSenderId = payload["sender_id"] ?: payload["senderId"]) {
                is Double -> rawSenderId.toInt()
                is Int -> rawSenderId
                else -> -1
            }

            val receiverId = when (val rawReceiverId = payload["receiver_id"] ?: payload["receiverId"]) {
                is Double -> rawReceiverId.toInt()
                is Int -> rawReceiverId
                else -> -1
            }

            val text = payload["message_text"]?.toString() ?: payload["text"]?.toString() ?: ""
            val createdAt = payload["created_at"]?.toString() ?: payload["createdAt"]?.toString() ?: getCurrentISOTimestamp()

            // Skip processing if essential fields are missing
            if (matchId <= 0 || senderId <= 0 || receiverId <= 0) {
                Log.e(TAG, "❌ Fallback processing failed - missing essential fields. matchId=$matchId, senderId=$senderId, receiverId=$receiverId")
                return
            }

            // Only process if this message is for the current chat
            if (matchId != originalMatchId) {
                Log.d(TAG, "Ignoring message for matchId=$matchId, current chat is $originalMatchId")
                return
            }

            Log.d(TAG, "Creating fallback message: id=$id, matchId=$matchId, senderId=$senderId, text=$text")

            // Create a minimal Message object with the extracted fields
            var message = Message(
                id = id,
                matchId = matchId,
                senderId = senderId,
                receiverId = receiverId,
                text = text,
                createdAt = createdAt,
                isRead = false,  // Default to unread for safety
                isPending = false,
                isError = false,
                clientMessageId = null,
                replyToId = null,
                replyToText = null,
                replyToSenderId = null
            )

            // Inject sender_name if message is from otherUser
            if (message.senderId == otherUserId && !otherUserName.isNullOrEmpty()) {
                val newMetadata = message.metadata.toMutableMap()
                newMetadata["sender_name"] = otherUserName!!
                newMetadata["username"] = otherUserName!!
                message = message.copy(metadata = newMetadata)
                Log.d(TAG, "processIncomingMessageFallback: Added sender_name '$otherUserName' to metadata for message ID ${message.id}")
            }

            Log.d(TAG, "Successfully created fallback message, processing now")
            processIncomingMessage(message)

        } catch (e: Exception) {
            Log.e(TAG, "Fallback message processing failed", e)
        }
    }

    private fun attemptReconnection() {
        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
            Log.e(TAG, "Maximum reconnection attempts reached")
            // Reset counter for future attempts
            reconnectAttempts = 0
            return
        }

        reconnectAttempts++
        val delayMs = RECONNECT_DELAY_MS * reconnectAttempts // Exponential backoff

        Log.d(TAG, "Attempting WebSocket reconnection in ${delayMs}ms (attempt $reconnectAttempts)")
        val handler = Handler(Looper.getMainLooper())
        handler.postDelayed({
            establishWebSocketConnection()
        }, delayMs)
    }

    private fun setupVoiceMessageRecording() {
        // Initialize audio recorder and player
        audioRecorder = AudioRecorder(applicationContext)
        audioPlayer = AudioPlayer(applicationContext)

        // Pass audio player to adapter
        messageAdapter.setAudioPlayer(audioPlayer)

        // Define initial touch position and cancel threshold variables
        var initialTouchX = 0f
        val cancelThreshold = 200f // dp distance to slide right to cancel
        var isCancellable = false

        // Set up mic button with press-and-hold behavior
        buttonMic.setOnLongClickListener {
            startVoiceRecording()
            true
        }

        buttonMic.setOnTouchListener { view, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    // Store initial touch position
                    initialTouchX = event.rawX
                    isCancellable = false
                    false // continue with long click listener
                }

                android.view.MotionEvent.ACTION_MOVE -> {
                    if (isRecording) {
                        // Calculate how much user has moved horizontally
                        val deltaX = event.rawX - initialTouchX

                        if (deltaX > cancelThreshold) {
                            // User has moved finger enough to the right to cancel
                            if (!isCancellable) {
                                // Show cancel indication (only once)
                                isCancellable = true
                                Toast.makeText(this, "Suelta para cancelar la grabación", Toast.LENGTH_SHORT).show()

                                // Change button visual to indicate cancellation mode
                                buttonMic.setBackgroundResource(R.drawable.recording_cancel_background)
                            }
                        } else if (isCancellable) {
                            // User moved back to left, out of cancel zone
                            isCancellable = false
                            buttonMic.setBackgroundResource(R.drawable.recording_button_background)
                        }
                    }
                    false
                }

                android.view.MotionEvent.ACTION_UP -> {
                    if (isRecording) {
                        if (isCancellable) {
                            // Cancel recording
                            cancelVoiceRecording()
                        } else {
                            // Normal finish of recording
                            stopVoiceRecordingAndSend()
                        }
                    }
                    false
                }

                else -> false
            }
        }
    }

    private fun cancelVoiceRecording() {
        if (!isRecording) return

        try {
            Log.d(TAG, "Canceling voice recording")
            isRecording = false

            // Reset button background
            buttonMic.setBackgroundResource(R.drawable.mic_button_selector)

            // Stop recording without sending
            audioRecorder?.resetRecorder()

            // Show confirmation to user
            Toast.makeText(this, "Grabación cancelada", Toast.LENGTH_SHORT).show()

            // Provide haptic feedback for cancellation
            try {
                val vibrator = getSystemService(android.content.Context.VIBRATOR_SERVICE) as? android.os.Vibrator
                if (vibrator != null) {
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        vibrator.vibrate(android.os.VibrationEffect.createOneShot(100, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
                    } else {
                        @Suppress("DEPRECATION")
                        vibrator.vibrate(100)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error with vibration on cancel", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error cancelling voice recording", e)
        }
    }

    private fun startVoiceRecording() {
        try {
            // Check for recording permission
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                if (checkSelfPermission(android.Manifest.permission.RECORD_AUDIO) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Requesting RECORD_AUDIO permission")
                    requestPermissions(arrayOf(android.Manifest.permission.RECORD_AUDIO), 100)
                    return
                }
            }

            // Start recording and provide visual feedback
            Log.d(TAG, "Starting voice recording")
            val outputFile = audioRecorder?.startRecording()
            if (outputFile != null) {
                isRecording = true
                buttonMic.setBackgroundResource(R.drawable.recording_button_background)

                // Provide visual feedback with subtle vibration
                try {
                    val vibrator = getSystemService(android.content.Context.VIBRATOR_SERVICE) as? android.os.Vibrator
                    if (vibrator != null) {
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                            vibrator.vibrate(android.os.VibrationEffect.createOneShot(50, android.os.VibrationEffect.DEFAULT_AMPLITUDE))
                        } else {
                            @Suppress("DEPRECATION")
                            vibrator.vibrate(50)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error with vibration", e)
                    // Continue even if vibration fails
                }

                Toast.makeText(this, "Grabando audio... suelta para enviar", Toast.LENGTH_SHORT).show()
            } else {
                Log.e(TAG, "Failed to start recording - outputFile is null")
                Toast.makeText(this, "No se pudo iniciar la grabación", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error starting voice recording", e)
            Toast.makeText(this, "Error al iniciar grabación: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun stopVoiceRecordingAndSend() {
        if (!isRecording) return

        try {
            Log.d(TAG, "Stopping voice recording")
            isRecording = false

            // Reset button background to selector (which shows blue instead of transparent)
            buttonMic.setBackgroundResource(R.drawable.mic_button_selector)

            // Stop recording and get duration
            val duration = audioRecorder?.stopRecording() ?: 0
            val outputFile = audioRecorder?.getOutputFile()

            Log.d(TAG, "Recording stopped: duration=$duration, outputFile=$outputFile")

            if (outputFile != null && duration > 0) {
                // Handle minimum duration
                if (duration < 1) {
                    Toast.makeText(this, "La grabación es demasiado corta", Toast.LENGTH_SHORT).show()
                    return
                }

                // Create a temporary message ID
                val tempId = UUID.randomUUID().toString()

                // Preserve reply information before clearing UI
                val replyId = replyingToMessage?.id
                val replyText = replyingToMessage?.text
                val replySenderId = replyingToMessage?.senderId

                // If we don't have the current user's avatar URL, try to fetch it from cache
                if (currentUserAvatarUrl.isNullOrEmpty()) {
                    try {
                        val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
                        currentUserAvatarUrl = prefs.getString("USER_AVATAR", null)
                        Log.d(TAG, "Retrieved avatar URL from prefs for voice message: $currentUserAvatarUrl")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error retrieving avatar URL from prefs", e)
                    }
                }

                // Add voice message to UI
                val voiceMessage = Message(
                    id = -1,
                    matchId = originalMatchId,
                    senderId = currentUserId,
                    receiverId = otherUserId,
                    text = "Mensaje de voz",
                    createdAt = getCurrentISOTimestamp(),
                    clientMessageId = tempId,
                    isPending = true,
                    voiceFile = outputFile,
                    voiceDuration = duration,
                    messageType = "voice",
                    replyToId = replyId,
                    replyToText = replyText,
                    replyToSenderId = replySenderId,
                    senderAvatarUrl = currentUserAvatarUrl // Add avatar URL to ensure it shows immediately
                )

                // Clear reply state after sending
                clearReplyState()

                // Add to pending messages and UI on main thread
                Handler(Looper.getMainLooper()).post {
                    synchronized(messagesList) {
                        val position = messagesList.size
                        messagesList.add(voiceMessage)
                        pendingMessages[tempId] = voiceMessage

                        // Update UI while still holding the lock
                        messageAdapter.notifyItemInserted(position)
                        recyclerViewMessages.scrollToPosition(position)
                    }
                }

                // Upload voice file and send message
                uploadVoiceMessageAndSend(outputFile, tempId, duration, replyId, replyText, replySenderId)
            } else {
                Log.e(TAG, "Invalid recording output: duration=$duration, outputFile=$outputFile")
                Toast.makeText(this, "No se pudo procesar la grabación", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in stopVoiceRecordingAndSend", e)
            Toast.makeText(this, "Error al procesar la grabación: ${e.message}", Toast.LENGTH_SHORT).show()

            // Reset recording state
            isRecording = false
            buttonMic.setBackgroundResource(R.drawable.mic_button_selector)
        }
    }

    private fun uploadVoiceMessageAndSend(filePath: String, tempId: String, duration: Int, replyId: Int?, replyText: String?, replySenderId: Int?) {
        // Start upload in background thread
        Thread {
            try {
                val file = File(filePath)
                if (!file.exists()) {
                    Log.e(TAG, "Voice file doesn't exist: $filePath")
                    markMessageAsError(tempId)
                    return@Thread
                }

                // Create multipart request with duration included
                val requestBody = okhttp3.MultipartBody.Builder()
                    .setType(okhttp3.MultipartBody.FORM)
                    .addFormDataPart(
                        "audio",
                        file.name,
                        okhttp3.RequestBody.create("audio/mp3".toMediaTypeOrNull(), file)
                    )
                    .addFormDataPart("duration", duration.toString())  // Add duration to upload request
                    .build()

                // Create the request
                val request = Request.Builder()
                    .url("$API_URL/direct/voice")
                    .post(requestBody)
                    .build()

                // Execute the request
                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        val uploadResult = gson.fromJson(responseBody, Map::class.java)
                        val serverFilePath = uploadResult["filePath"] as? String

                        if (serverFilePath != null) {
                            // Send voice message via HTTP POST with duration
                            sendMessageViaHttp("Mensaje de voz", tempId, replyId, replyText, replySenderId, duration)
                        } else {
                            Log.e(TAG, "Upload successful but no file path returned")
                            markMessageAsError(tempId)
                        }
                    } else {
                        Log.e(TAG, "Upload failed: ${response.code}")
                        markMessageAsError(tempId)
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error uploading voice message", e)
                markMessageAsError(tempId)
            }
        }.start()
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            100 -> { // Audio permission request code
                if (grantResults.isNotEmpty() && grantResults[0] == android.content.pm.PackageManager.PERMISSION_GRANTED) {
                    // Permission granted
                    Log.d(TAG, "RECORD_AUDIO permission granted")
                    Toast.makeText(this, "Permiso concedido. Mantenga presionado el micrófono para grabar.", Toast.LENGTH_SHORT).show()
                } else {
                    // Permission denied
                    Log.d(TAG, "RECORD_AUDIO permission denied")
                    Toast.makeText(this, "Se necesita permiso para grabar audio", Toast.LENGTH_SHORT).show()
                }
            }
            REQUEST_IMAGE_PERMISSIONS -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    openCamera()
                } else {
                    Toast.makeText(this, "Camera permission denied", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    // Reply functionality
    private fun setupReplyToMessage(message: Message) {
        // Store complete original message as an instance variable
        replyingToMessage = message.copy() // Make a copy to avoid issues with mutability

        // Add detailed logging to verify reply data is being set
        Log.d(TAG, "Setting up reply to message: ${message.id}")
        Log.d(TAG, "Reply details - ID: ${message.id}, Text: ${message.text}, SenderID: ${message.senderId}")

        // Show reply preview
        layoutReplyPreview.visibility = View.VISIBLE

        // Set reply text with different formatting based on who sent the original message
        if (message.senderId == currentUserId) {
            textViewReplyPreview.text = "Tú: ${message.text}"
        } else {
            textViewReplyPreview.text = message.text
        }

        // Focus on message input
        editTextMessage.requestFocus()

        // Scroll to reply section
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
        inputMethodManager.showSoftInput(editTextMessage, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
    }

    private fun clearReplyState() {
        // Add logging to track when reply state is cleared
        if (replyingToMessage != null) {
            Log.d(TAG, "Clearing reply state - was replying to message: ${replyingToMessage?.id}")
        }
        replyingToMessage = null
        layoutReplyPreview.visibility = View.GONE
        textViewReplyPreview.text = ""
    }

    private fun checkAndFixMessageReadStatus() {
        Log.d(TAG, "Checking message read status for all messages")

        Thread {
            try {
                // Get all messages with read status from the server
                val messagesUrl = "$API_URL/chat/${originalMatchId}/messages"
                Log.d(TAG, "Requesting read status from: $messagesUrl")

                val request = Request.Builder()
                    .url(messagesUrl)
                    .get()
                    .build()

                try {
                    val response = client.newCall(request).execute()

                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()

                        if (responseBody != null) {
                            // Parse the response with debug logging
                            data class HistoryResponse(val success: Boolean, val messages: List<Message>)

                            val historyResponse = gson.fromJson(responseBody, HistoryResponse::class.java)
                            Log.d(TAG, "Messages with read status: ${historyResponse.messages.size}")

                            // Update local messages with read status from server
                            Handler(Looper.getMainLooper()).post {
                                synchronized(messagesList) {
                                    var updated = false

                                    // First check server status
                                    val serverReadStatus = historyResponse.messages.associate { it.id to it.isRead }

                                    // Debug the server read status
                                    serverReadStatus.forEach { (id, isRead) ->
                                        Log.d(TAG, "Server message $id read status: $isRead")
                                    }

                                    // Manual override for messages that should be read (when server reports wrong status)
                                    // This looks at the database read=1 column which is not being properly converted to isRead=true
                                    // by examining the JSON response directly
                                    val messageReadOverrides = if (responseBody.contains("\"read\":1") || responseBody.contains("\"read\": 1")) {
                                        Log.d(TAG, "Found messages with 'read:1' in response, applying override")
                                        // Find message IDs with read:1 in the raw JSON
                                        val readIds = mutableSetOf<Int>()
                                        val pattern = "\"id\":(\\d+).*?\"read\":1".toRegex()
                                        val matches = pattern.findAll(responseBody)

                                        matches.forEach { match ->
                                            val idStr = match.groupValues[1]
                                            try {
                                                val id = idStr.toInt()
                                                readIds.add(id)
                                                Log.d(TAG, "Adding override read=true for message $id")
                                            } catch (e: NumberFormatException) {
                                                Log.e(TAG, "Error parsing message ID: $idStr", e)
                                            }
                                        }
                                        readIds
                                    } else {
                                        emptySet()
                                    }

                                    // Update local messages
                                    for (i in messagesList.indices) {
                                        val message = messagesList[i]

                                        // Be more selective about what messages get marked as read
                                        // Apply read status from server or override if needed
                                        // BUT only for messages where:
                                        // 1. We are the sender and the server says it's read
                                        // 2. We are the receiver and we're viewing the chat
                                        val messageFromUs = message.senderId == currentUserId
                                        val messageToUs = message.receiverId == currentUserId

                                        // Determine if message should be marked as read
                                        val shouldBeRead = if (messageFromUs) {
                                            // This is a message FROM us - use server status to know if other user read it
                                            // IMPORTANT: Never override server's read status for our own messages
                                            message.isRead // Keep existing read status from server - don't change it
                                        } else if (messageToUs) {
                                            // This is a message TO us - only mark as read if we're actively viewing
                                            true // We're in the chat, so we've seen it
                                        } else {
                                            // Shouldn't happen, but just in case
                                            message.isRead
                                        }

                                        if (shouldBeRead != message.isRead) {
                                            Log.d(TAG, "Updating message ${message.id} read status from ${message.isRead} to $shouldBeRead (sender: ${message.senderId}, receiver: ${message.receiverId})")
                                            messagesList[i] = message.copy(isRead = shouldBeRead)
                                            updated = true
                                        }
                                    }

                                    // Update UI if needed
                                    if (updated) {
                                        messageAdapter.notifyDataSetChanged()
                                        Log.d(TAG, "Updated message read status in UI")
                                    } else {
                                        Log.d(TAG, "No message status updates needed")
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error checking message status", e)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in checkAndFixMessageReadStatus", e)
            }
        }.start()
    }

    // Show the emoji picker using our custom dialog
    private fun showEmojiPicker() {
        try {
            Log.d(TAG, "Opening emoji picker dialog")

            // Use the improved emoji picker with ViewPager
            val dialog = ImprovedEmojiPickerDialog(this) { emoji ->
                // Insert the selected emoji at current cursor position
                val currentPosition = editTextMessage.selectionStart
                val text = editTextMessage.text.toString()
                val updatedText = StringBuilder(text).insert(currentPosition, emoji).toString()

                // Set the updated text and move cursor after the inserted emoji
                editTextMessage.setText(updatedText)
                editTextMessage.setSelection(currentPosition + emoji.length)

                Log.d(TAG, "Emoji inserted: $emoji")
            }

            // Set dialog width to match parent
            dialog.window?.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )

            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "Error showing emoji picker", e)
            Toast.makeText(this, "Error al abrir selector de emojis", Toast.LENGTH_SHORT).show()
        }
    }

    // Show the GIF picker using our custom dialog
    private fun showGifPicker() {
        try {
            Log.d(TAG, "Opening GIF picker dialog")

            val dialog = GifPickerDialog(this, TENOR_API_KEY) { gif ->
                // Send the selected GIF
                sendGifMessage(gif)
            }

            dialog.show()
        } catch (e: Exception) {
            Log.e(TAG, "Error showing GIF picker", e)
            Toast.makeText(this, "Error al abrir selector de GIFs", Toast.LENGTH_SHORT).show()
        }
    }

    // Send a GIF message
    private fun sendGifMessage(gif: TenorGif) {
        Log.d(TAG, "Sending GIF: ${gif.id} - ${gif.title}")

        // Create a temporary message ID
        val tempId = UUID.randomUUID().toString()

        // Create a temporary message to show in the UI
        val message = Message(
            id = -1,
            matchId = originalMatchId,
            senderId = currentUserId,
            receiverId = otherUserId,
            text = "GIF: ${gif.title} ${gif.gifUrl}", // Include URL in text
            createdAt = getCurrentISOTimestamp(),
            clientMessageId = tempId,
            isPending = true,
            messageType = "gif",
            gifUrl = gif.gifUrl
        )

        // Add message to UI
        synchronized(messagesList) {
            val position = messagesList.size
            messagesList.add(message)
            pendingMessages[tempId] = message

            // Update UI
            messageAdapter.notifyItemInserted(position)
            recyclerViewMessages.scrollToPosition(position)
        }

        // Send GIF via HTTP
        Thread {
            try {
                // Create JSON payload
                val jsonObj = JSONObject()
                jsonObj.put("match_id", originalMatchId)
                jsonObj.put("sender_id", currentUserId)
                jsonObj.put("receiver_id", otherUserId)
                // Include the GIF URL in the message text to ensure it can be recovered later
                jsonObj.put("message_text", "GIF: ${gif.title} ${gif.gifUrl}")
                jsonObj.put("clientMessageId", tempId)
                jsonObj.put("message_type", "gif")
                jsonObj.put("gif_url", gif.gifUrl)
                jsonObj.put("gif_preview_url", gif.previewUrl)
                jsonObj.put("gif_id", gif.id)

                val jsonBody = jsonObj.toString()

                // Create the request - use the regular message endpoint instead of a special GIF endpoint
                val url = "$API_URL/chat/$originalMatchId/message"
                val mediaType = "application/json; charset=utf-8".toMediaTypeOrNull()
                val requestBody = jsonBody.toRequestBody(mediaType)

                val request = Request.Builder()
                    .url(url)
                    .post(requestBody)
                    .build()

                // Execute the request
                client.newCall(request).execute().use { response ->
                    val responseBody = response.body?.string()
                    Log.d(TAG, "GIF message response: ${response.code} - $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        // Handle successful response
                        Handler(Looper.getMainLooper()).post {
                            confirmMessageDelivery(tempId)

                            // Ensure WebSocket connection for real-time updates
                            ensureWebSocketConnection()
                        }
                    } else {
                        // Handle error
                        Log.e(TAG, "GIF message error: ${response.code} - $responseBody")
                        Handler(Looper.getMainLooper()).post {
                            markMessageAsError(tempId)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending GIF message", e)
                Handler(Looper.getMainLooper()).post {
                    markMessageAsError(tempId)
                }
            }
        }.start()
    }

    // Process an incoming real-time message with extreme duplicate prevention
    private fun processIncomingMessage(message: Message) {
        // Skip processing if this message has an invalid receiver
        if (message.receiverId <= 0 || (message.receiverId != currentUserId && message.senderId != currentUserId)) {
            Log.d(TAG, "🚫 Message has invalid receiver=${message.receiverId}, currentUserId=$currentUserId")
            return
        }

        // Generate a hash for this message for efficient comparison
        val messageHash = generateMessageHash(message)

        // Log message details with more context
        Log.d(TAG, "Processing message with ID=${message.id}, senderId=${message.senderId}, receiverId=${message.receiverId}, matchId=${message.matchId}")

        synchronized(messagesList) {
            // ALWAYS CHECK THESE FIRST - Extremely aggressive duplicate prevention checks

            // 1. Check if this exact message ID has been processed before
            if (message.id > 0 && processedMessageIds.contains(message.id)) {
                Log.d(TAG, "🚫 Message ID ${message.id} has already been processed, ignoring duplicate")
                return
            }

            // 2. For sender's messages, check message hash to detect duplicates even with different IDs
            if (message.senderId == currentUserId && sentMessageHashes.contains(messageHash)) {
                Log.d(TAG, "🚫 This message from us with hash $messageHash has already been processed")

                // Look for any message from us that might need a status update
                val existingIndex = messagesList.indexOfFirst {
                    it.senderId == currentUserId &&
                    it.text == message.text &&
                    generateMessageHash(it) == messageHash
                }

                if (existingIndex >= 0) {
                    // Only update status if needed
                    val existing = messagesList[existingIndex]
                    if (existing.isPending || existing.isError) {
                        // Update status to confirmed
                        messagesList[existingIndex] = existing.copy(
                            id = Math.max(existing.id, message.id), // Use highest ID
                            isPending = false,
                            isError = false,
                            isRead = message.isRead
                        )
                        messageAdapter.notifyItemChanged(existingIndex)
                        Log.d(TAG, "Updated status of existing message to confirmed")
                    }
                }
                return
            }

            // For our own messages, apply additional checks
            if (message.senderId == currentUserId) {
                // 3. Check if this is a pending message being confirmed
                if (message.clientMessageId != null) {
                    val pendingIndex = messagesList.indexOfFirst {
                        it.clientMessageId == message.clientMessageId && it.isPending
                    }

                    if (pendingIndex >= 0) {
                        Log.d(TAG, "✅ Replacing pending message with confirmed one (clientId=${message.clientMessageId})")
                        messagesList[pendingIndex] = message.copy(isPending = false, isError = false)
                        messageAdapter.notifyItemChanged(pendingIndex)
                        pendingMessages.remove(message.clientMessageId)

                        // Add to our tracking sets
                        if (message.id > 0) processedMessageIds.add(message.id)
                        sentMessageHashes.add(messageHash)

                        return
                    }
                }

                // 4. Check for content-similar messages that might be duplicates
                val textMatchIndex = messagesList.indexOfFirst {
                    it.senderId == currentUserId &&
                    !it.isVoiceMessage() &&
                    it.text == message.text
                    // Note: Not comparing timestamps as they're strings not long values
                }

                if (textMatchIndex >= 0) {
                    Log.d(TAG, "✅ Found existing message with identical content, treating as duplicate")
                    // If the existing message is pending, update it
                    val existing = messagesList[textMatchIndex]
                    if (existing.isPending || existing.isError) {
                        messagesList[textMatchIndex] = message.copy(isPending = false, isError = false)
                        messageAdapter.notifyItemChanged(textMatchIndex)
                    }

                    // Track this message ID
                    if (message.id > 0) processedMessageIds.add(message.id)
                    sentMessageHashes.add(messageHash)

                    return
                }
            }

            // 5. Standard duplicate checking for all messages
            val exactMatchIndex = messagesList.indexOfFirst { it.id == message.id && it.id > 0 }
            if (exactMatchIndex >= 0) {
                // Just update read status if needed
                val existingMessage = messagesList[exactMatchIndex]
                if (existingMessage.isRead != message.isRead) {
                    messagesList[exactMatchIndex] = existingMessage.copy(isRead = message.isRead)
                    messageAdapter.notifyItemChanged(exactMatchIndex)
                }
                Log.d(TAG, "Exact duplicate of message ID ${message.id} found, ignoring")
                return
            }

            // 6. Final text-based similarity check for all messages
            val sameTextIndex = if (!message.isVoiceMessage()) {
                messagesList.indexOfFirst {
                    it.senderId == message.senderId &&
                    it.receiverId == message.receiverId &&
                    it.text == message.text &&
                    !it.isPending &&
                    it.id > 0
                }
            } else {
                -1
            }

            if (sameTextIndex >= 0) {
                Log.d(TAG, "Found message with identical text, treating as duplicate")
                return
            }

            // 7. This is a new, non-duplicate message - track and add it
            Log.d(TAG, "✓ Adding new message: ID=${message.id}, text='${message.text.take(20)}...'")

            // Add to tracking sets if it's our own message
            if (message.senderId == currentUserId) {
                if (!message.isVoiceMessage()) {
                    recentlySentMessageTexts.add(message.text)
                    // Trim set if needed
                    if (recentlySentMessageTexts.size > 50) {
                        recentlySentMessageTexts.remove(recentlySentMessageTexts.iterator().next())
                    }
                }
                sentMessageHashes.add(messageHash)
            }

            // Add to processed IDs if it has a valid ID
            if (message.id > 0) {
                processedMessageIds.add(message.id)
            }

            // Check if this is a message TO us (we're the receiver) and we need to mark it as read
            val isMessageToUs = message.receiverId == currentUserId
            val shouldMarkAsRead = isMessageToUs && message.id > 0 && !message.isRead

            // Add sender name to metadata for incoming messages
            val messageWithSenderInfo = if (message.senderId != currentUserId) {
                // For messages from other users, add their username to metadata
                val updatedMetadata = message.metadata.toMutableMap()
                if (otherUserName != null && otherUserName!!.isNotEmpty()) {
                    updatedMetadata["sender_name"] = otherUserName!!
                    updatedMetadata["username"] = otherUserName!!
                    Log.d(TAG, "processIncomingMessage: Added sender_name '$otherUserName' to metadata for message ID ${message.id}")
                }
                message.copy(metadata = updatedMetadata)
            } else {
                message
            }

            // Determine final read status - only mark as read if we're actively viewing
            // Messages TO us should be marked as read only if we're viewing the chat
            // Messages FROM us should retain their server read status, but NEVER be marked as read locally
            val finalReadStatus = if (isMessageToUs) {
                // We're the receiver - only mark read if we're actively viewing
                shouldMarkAsRead && isChatActive
            } else {
                // We're the sender - keep the server's read status, but never mark as read locally
                // If server somehow incorrectly marked it as read, force it back to false
                if (message.isRead) {
                    Log.d(TAG, "Server incorrectly marked our sent message ${message.id} as read - correcting")
                    false
                } else {
                    message.isRead
                }
            }

            // Add the message with the correct read status and sender info
            val messageToAdd = messageWithSenderInfo.copy(isRead = finalReadStatus)
            messagesList.add(messageToAdd)
            messageAdapter.notifyItemInserted(messagesList.size - 1)

            // If we're active, scroll to the new message
            if (isChatActive) {
                recyclerViewMessages.post {
                    recyclerViewMessages.scrollToPosition(messagesList.size - 1)
                }
            }

            // Play notification sound for incoming messages if enabled
            if (isMessageToUs && message.senderId != currentUserId) {
                // You can add notification sound here if needed
                Log.d(TAG, "🔔 Received new message from other user")

                // Handle buzz messages
                if (messageToAdd.isBuzzMessage()) {
                    Log.d(TAG, "🔔 BUZZ DEBUG - Processing received buzz message: ${messageToAdd.text}")
                    handleReceivedBuzz(messageToAdd)
                } else if (messageToAdd.text.contains("BuZzZzzZzZZ")) {
                    Log.w(TAG, "🔔 BUZZ DEBUG - Message contains buzz text but isBuzzMessage()=false: messageType=${messageToAdd.messageType}")
                }
            }

            // If we need to mark this message as read on the server, do it now
            if (shouldMarkAsRead) {
                Log.d(TAG, "Marking message ${message.id} as read since we are viewing the chat")
                markMessagesAsRead(listOf(message.id))
            }
        }
    }

    // Generate a unique hash code for a message to detect duplicates even with different IDs
    private fun generateMessageHash(message: Message): Int {
        // Create a string representation combining the most important parts of the message
        val hashString = "${message.senderId}_${message.receiverId}_${message.text}_${message.messageType}"
        return hashString.hashCode()
    }

    // Send updated active chat ID to the server through WebSocket
    private fun updateActiveChatId() {
        // OkHttp WebSocket doesn't have readyState property, so we just attempt to send
        // and catch any exceptions if the WebSocket is closed/not ready
        if (webSocket != null) {
            try {
                val updateJson = gson.toJson(mapOf(
                    "type" to "update_active_chat",
                    "userId" to currentUserId,
                    "activeMatchId" to originalMatchId
                ))
                webSocket?.send(updateJson)
                Log.d(TAG, "Sent active chat update: now viewing chat $originalMatchId")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending active chat update", e)
                // Re-establish the connection if needed
                establishWebSocketConnection()
            }
        } else {
            Log.d(TAG, "WebSocket not initialized, establishing connection")
            // Initialize the WebSocket connection with new chat ID
            establishWebSocketConnection()
        }
    }

    // Clear notifications for this specific chat
    private fun clearNotificationsForChat() {
        try {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Clear notifications using different possible ID patterns
            // Pattern 1: (matchId * 1000) + otherUserId (from VMeetFirebaseMessagingService)
            val notificationId1 = (originalMatchId * 1000) + otherUserId

            // Pattern 2: otherUserId (from FCMService)
            val notificationId2 = otherUserId

            // Pattern 3: matchId * 1000 (from FCMService when userId is 0)
            val notificationId3 = originalMatchId * 1000

            // Cancel all possible notification IDs for this chat
            notificationManager.cancel(notificationId1)
            notificationManager.cancel(notificationId2)
            notificationManager.cancel(notificationId3)

            // Also try to clear any notification with the sender ID
            notificationManager.cancel(otherUserId)

            Log.d(TAG, "Cleared notifications for chat - IDs: $notificationId1, $notificationId2, $notificationId3, $otherUserId")

            // Clear app badge count
            clearAppBadge()

        } catch (e: Exception) {
            Log.e(TAG, "Error clearing notifications for chat", e)
        }
    }

    // Clear the app badge count
    private fun clearAppBadge() {
        try {
            // For Android 8.0+ (API 26+), use NotificationManager to clear badge
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

                // Cancel all notifications to clear badge
                notificationManager.cancelAll()

                Log.d(TAG, "Cleared all notifications and app badge")
            }

            // Additional method for some launchers that support badge clearing
            try {
                val intent = Intent("android.intent.action.BADGE_COUNT_UPDATE")
                intent.putExtra("badge_count", 0)
                intent.putExtra("badge_count_package_name", packageName)
                sendBroadcast(intent)
                Log.d(TAG, "Sent badge clear broadcast")
            } catch (e: Exception) {
                Log.d(TAG, "Badge clear broadcast not supported on this device")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error clearing app badge", e)
        }
    }

    // Send chat visibility status to the server
    private fun sendChatVisibilityStatus(isVisible: Boolean) {
        if (webSocket != null) {
            try {
                val visibilityJson = gson.toJson(mapOf(
                    "type" to "update_chat_visibility",
                    "userId" to currentUserId,
                    "matchId" to originalMatchId,
                    "isVisible" to isVisible
                ))
                webSocket?.send(visibilityJson)
                Log.d(TAG, "Sent chat visibility update: chat $originalMatchId is ${if (isVisible) "visible" else "not visible"}")
            } catch (e: Exception) {
                Log.e(TAG, "Error sending chat visibility update", e)
            }
        }
    }

    private fun processNewMessages(newMessages: List<Message>) {
        if (newMessages.isEmpty()) return

        synchronized(messagesList) {
            // First, collect all new message IDs to mark as read, but ONLY for messages TO the current user
            val newMessageIdsToMarkAsRead = newMessages
                .filter { message ->
                    // Only mark messages sent to us (received by us), not messages sent by us
                    message.receiverId == currentUserId &&
                    message.senderId != currentUserId &&
                    !message.isRead &&
                    !processedMessageIds.contains(message.id) &&
                    message.id > 0 // Only for messages with valid IDs
                }
                .map { it.id }

            // Only mark received messages as read if we're actively viewing the chat
            if (newMessageIdsToMarkAsRead.isNotEmpty() && isChatActive) {
                Log.d(TAG, "Marking ${newMessageIdsToMarkAsRead.size} received messages as read because we're viewing them")
                // This will send the read status to the server via WebSocket
                markMessagesAsRead(newMessageIdsToMarkAsRead)
            } else if (newMessageIdsToMarkAsRead.isNotEmpty()) {
                Log.d(TAG, "Not marking ${newMessageIdsToMarkAsRead.size} messages as read because chat is not active (isChatActive=$isChatActive)")
            }

            // Add new messages to our list, handling read status properly
            var messagesAdded = false

            for (message in newMessages) {
                if (message.id > 0 && !processedMessageIds.contains(message.id)) {
                    // IMPORTANT: For messages we sent, we must NEVER modify the read status locally.
                    // We only rely on server notifications to tell us when the other user reads them.
                    val finalReadStatus = if (message.senderId == currentUserId) {
                        // For messages SENT BY us, NEVER change read status - only trust server
                        Log.d(TAG, "Message ${message.id} was sent by us - keeping original server read status: ${message.isRead}")
                        message.isRead
                    } else if (message.receiverId == currentUserId && isChatActive) {
                        // For messages RECEIVED BY us, mark as read if we're actively viewing the chat
                        Log.d(TAG, "Message ${message.id} was sent to us - marking as read since we're viewing")
                        true
                    } else {
                        // Keep original status for other cases
                        message.isRead
                    }

                    // Add to our list with the correct read status and sender info
                    // For messages from other users, make sure to include their username
                    val updatedMetadata = message.metadata.toMutableMap()
                    if (message.senderId != currentUserId && otherUserName != null && otherUserName!!.isNotEmpty()) {
                        updatedMetadata["sender_name"] = otherUserName!!
                        updatedMetadata["username"] = otherUserName!!
                        Log.d(TAG, "processNewMessages: Added sender_name '$otherUserName' to metadata for message ID ${message.id}")
                    }

                    val messageToAdd = message.copy(
                        isRead = finalReadStatus,
                        metadata = updatedMetadata
                    )
                    messagesList.add(messageToAdd)
                    processedMessageIds.add(message.id)
                    messagesAdded = true

                    // For tracking max message ID
                    if (message.id > lastMessageId) {
                        lastMessageId = message.id
                    }
                }
            }

            if (messagesAdded) {
                // Sort messages by creation time
                messagesList.sortBy { it.createdAt }

                // Update the UI
                messageAdapter.notifyDataSetChanged()
                scrollToNewestMessage()
            }
        }
    }

    // Helper function to scroll to the newest message
    private fun scrollToNewestMessage() {
        if (messagesList.isNotEmpty()) {
            recyclerViewMessages.post {
                recyclerViewMessages.scrollToPosition(messagesList.size - 1)
            }
        }
    }

    // Helper method to open the other user's profile
    private fun openOtherUserProfile() {
        Log.d(TAG, "Opening profile for otherUserId=$otherUserId")
        try {
            val profileIntent = Intent(this, com.spyro.vmeet.ProfileActivity::class.java)
            profileIntent.putExtra("USER_ID", otherUserId)
            profileIntent.putExtra("VIEW_ONLY_MODE", true)
            startActivity(profileIntent)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening profile", e)
            Toast.makeText(this, "Error al abrir perfil", Toast.LENGTH_SHORT).show()
        }
    }

    // New method to start periodic status updates
    private fun startStatusUpdates() {
        if (otherUserId <= 0) {
            Log.e(TAG, "Cannot start status updates: Invalid otherUserId: $otherUserId")
            return
        }

        statusRunnable = object : Runnable {
            override fun run() {
                checkUserStatus()
                statusHandler.postDelayed(this, statusCheckInterval)
            }
        }

        // Start immediately
        statusRunnable?.let {
            statusHandler.post(it)
        }
    }

    // Stop status updates to prevent memory leaks
    private fun stopStatusUpdates() {
        statusRunnable?.let {
            statusHandler.removeCallbacks(it)
        }
    }

    // Check user online status
    private fun checkUserStatus() {
        if (otherUserId <= 0) return

        val now = System.currentTimeMillis()
        // Avoid too frequent calls
        if (now - lastStatusCheck < 5000) return

        lastStatusCheck = now

        // Si el otro usuario está viendo este chat, considerarlo como en línea
        if (otherUserIsViewing) {
            Log.d(TAG, "Other user is viewing this chat, marking as online")
            Handler(Looper.getMainLooper()).post {
                updateStatusDisplay(true, null)
            }
            return
        }

        Thread {
            try {
                // Create a dedicated client for this request
                val statusClient = OkHttpClient.Builder()
                    .readTimeout(10, TimeUnit.SECONDS)
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .build()

                val statusUrl = "$API_URL/user/status/$otherUserId"
                Log.d(TAG, "Checking user status at: $statusUrl")

                val request = Request.Builder()
                    .url(statusUrl)
                    .get()
                    .build()

                statusClient.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()

                        if (responseBody != null) {
                            Log.d(TAG, "Status response: $responseBody")

                            // Parse the response using the custom Gson
                            val statusResponse = gson.fromJson(responseBody, Map::class.java)
                            val success = statusResponse["success"] as? Boolean ?: false

                            if (success) {
                                val online = statusResponse["online"] as? Boolean ?: false
                                val lastSeen = statusResponse["lastSeen"] as? String

                                Log.d(TAG, "Parsed status: online=$online, lastSeen=$lastSeen")

                                // Update UI on main thread
                                Handler(Looper.getMainLooper()).post {
                                    updateStatusDisplay(online, lastSeen)
                                }
                            } else {
                                Log.e(TAG, "Status response indicated failure")
                            }
                        } else {
                            Log.e(TAG, "Empty response body from status endpoint")
                        }
                    } else {
                        Log.e(TAG, "Failed to get user status: ${response.code} - ${response.message}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking user status", e)
            }
        }.start()
    }

    // Format and display the status
    private fun updateStatusDisplay(online: Boolean, lastSeen: String?) {
        // Si el otro usuario está viendo este chat, siempre mostrar "En línea"
        if (online || otherUserIsViewing) {
            // User is online
            textViewStatus.text = "En línea"
            textViewStatus.setTextColor(ContextCompat.getColor(this, android.R.color.holo_green_light))
            Log.d(TAG, "Setting status to 'En línea' (online=$online, otherUserIsViewing=$otherUserIsViewing)")
        } else {
            // User is offline, format last seen time
            try {
                val lastSeenTime = if (!lastSeen.isNullOrEmpty()) {
                    Instant.parse(lastSeen)
                } else {
                    null
                }

                if (lastSeenTime != null) {
                    val now = Instant.now()
                    val diffMinutes = ChronoUnit.MINUTES.between(lastSeenTime, now)
                    val diffHours = ChronoUnit.HOURS.between(lastSeenTime, now)
                    val diffDays = ChronoUnit.DAYS.between(lastSeenTime, now)

                    val formattedTime = when {
                        diffMinutes < 1 -> "Último acceso: hace un momento"
                        diffMinutes == 1L -> "Último acceso: hace 1 minuto"
                        diffMinutes < 60 -> "Último acceso: hace $diffMinutes minutos"
                        diffHours == 1L -> "Último acceso: hace 1 hora"
                        diffHours < 24 -> "Último acceso: hace $diffHours horas"
                        diffDays == 1L -> "Último acceso: hace 1 día"
                        else -> "Último acceso: hace $diffDays días"
                    }

                    textViewStatus.text = formattedTime
                    Log.d(TAG, "Setting status to '$formattedTime'")
                } else {
                    textViewStatus.text = "No disponible"
                    Log.d(TAG, "Setting status to 'No disponible' (lastSeen is null or empty)")
                }

                textViewStatus.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
            } catch (e: Exception) {
                Log.e(TAG, "Error formatting last seen time", e)
                textViewStatus.text = "No disponible"
                textViewStatus.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
            }
        }
    }

    // Function to send reaction to server
    private fun sendReaction(message: Message, reactionType: String) {
        Log.d(TAG, "Sending reaction $reactionType for message ${message.id}")

        // Check if message has a valid ID (cannot react to pending messages)
        if (message.id <= 0) {
            Log.e(TAG, "Cannot add reaction to message without valid ID")
            Toast.makeText(this, "No se puede reaccionar a mensajes pendientes", Toast.LENGTH_SHORT).show()
            return
        }

        // Validate the reaction
        if (!MessageReaction.isValidReaction(reactionType)) {
            Log.e(TAG, "Invalid reaction: '$reactionType'")
            Toast.makeText(this, "Reacción no válida", Toast.LENGTH_SHORT).show()
            return
        }

        // Use our ReactionHandler to handle the reaction
        reactionHandler.sendReaction(message.id, reactionType, message.reactions)
    }

    // Function to remove a reaction
    private fun removeReaction(messageId: Int) {
        Log.d(TAG, "Removing reaction for message $messageId")

        // Use our ReactionHandler to remove the reaction
        reactionHandler.removeReaction(messageId)
    }

    // Start real-time message polling (1-second interval)
    private fun startRealTimePolling() {
        if (isPollingActive) return

        isPollingActive = true
        Log.d(TAG, "Starting real-time message polling at ${POLLING_INTERVAL}ms intervals")

        messagePollingRunnable = object : Runnable {
            override fun run() {
                if (!isPollingActive) return

                // Find the highest message ID we have
                var highestId = -1
                synchronized(messagesList) {
                    for (message in messagesList) {
                        if (message.id > highestId) {
                            highestId = message.id
                        }
                    }
                }

                // Poll for new messages
                pollForNewMessages(highestId)

                // Schedule next poll
                messagePollingHandler.postDelayed(this, POLLING_INTERVAL)
            }
        }

        // Start polling immediately
        messagePollingRunnable?.let {
            messagePollingHandler.post(it)
        }
    }

    // Stop polling to prevent memory leaks
    private fun stopRealTimePolling() {
        isPollingActive = false
        messagePollingRunnable?.let {
            messagePollingHandler.removeCallbacks(it)
        }
        messagePollingRunnable = null
        Log.d(TAG, "Real-time message polling stopped")
    }

    // Poll for new messages since the specified ID
    private fun pollForNewMessages(sinceId: Int) {
        if (originalMatchId <= 0) return

        // Skip polling if just sent a message (to avoid duplicates)
        val now = System.currentTimeMillis()
        if (now < disablePollingUntil) {
            Log.d(TAG, "Skipping poll as message just sent")
            return
        }

        lastMessagePoll = now
        Thread {
            try {
                val url = if (sinceId > 0) {
                    "$API_URL/chat/$originalMatchId/messages?since_id=$sinceId"
                } else {
                    "$API_URL/chat/$originalMatchId/messages"
                }

                Log.d(TAG, "Polling for new messages since ID $sinceId: $url")

                val request = Request.Builder()
                    .url(url)
                    .get()
                    .build()

                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()

                        if (responseBody != null) {
                            // Parse the response
                            Log.d(TAG, "Poll response: $responseBody")

                            data class HistoryResponse(val success: Boolean, val messages: List<Message>)

                            val historyResponse = gson.fromJson(responseBody, HistoryResponse::class.java)

                            if (historyResponse.success && historyResponse.messages.isNotEmpty()) {
                                Log.d(TAG, "Received ${historyResponse.messages.size} new messages from polling")

                                // Process new messages on main thread
                                Handler(Looper.getMainLooper()).post {
                                    synchronized(messagesList) {
                                        var added = false
                                        val newMessages = ArrayList<Message>()

                                        // Filter messages for current match
                                        for (message in historyResponse.messages) {
                                            if (message.matchId == originalMatchId && !processedMessageIds.contains(message.id)) {
                                                // Check if we already have this message
                                                val existingIndex = messagesList.indexOfFirst { it.id == message.id }

                                                if (existingIndex == -1) {
                                                    // New message - add it
                                                    newMessages.add(message)
                                                    processedMessageIds.add(message.id)
                                                    added = true

                                                    // If it's an image message, log extra details
                                                    if (message.isImageMessage()) {
                                                        Log.d(TAG, "Received new image message from polling: id=${message.id}, imageFile=${message.imageFile}")
                                                    }
                                                }
                                            }
                                        }

                                        if (added) {
                                            // Add all new messages to our list
                                            messagesList.addAll(newMessages)

                                            // Sort messages by time
                                            val sortedMessages = ArrayList(messagesList.sortedBy { it.createdAt })
                                            messagesList.clear()
                                            messagesList.addAll(sortedMessages)

                                            // Mark messages as read if they're from the other user
                                            val unreadIds = newMessages
                                                .filter { it.senderId == otherUserId }
                                                .map { it.id }

                                            if (unreadIds.isNotEmpty()) {
                                                markMessagesAsRead(unreadIds)
                                            }

                                            // Update UI
                                            messageAdapter.notifyDataSetChanged()

                                            // Scroll to bottom if we were already at bottom
                                            val layoutManager = recyclerViewMessages.layoutManager as LinearLayoutManager
                                            val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
                                            if (lastVisiblePosition >= messagesList.size - 2) {
                                                recyclerViewMessages.scrollToPosition(messagesList.size - 1)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        Log.e(TAG, "Error polling for messages: ${response.code}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error during message polling", e)
            }
        }.start()
    }

    // Handle the reaction response from the server
    private fun handleReactionResponse(reactions: List<MessageReaction>?, wasRemoved: Boolean) {
        if (reactions == null && wasRemoved) {
            // This was a removal and we don't have updated reactions
            Log.d(TAG, "Reaction removed successfully - refreshing all messages")

            // Since we don't know which message had the reaction removed,
            // we'll refresh all visible messages to ensure UI is updated
            synchronized(messagesList) {
                messageAdapter.notifyDataSetChanged()
            }
            return
        }

        // Find the message and update its reactions
        if (reactions != null && reactions.isNotEmpty()) {
            val messageId = reactions[0].messageId
            val messagePosition = messagesList.indexOfFirst { it.id == messageId }

            if (messagePosition != -1) {
                val message = messagesList[messagePosition]

                // Update reactions list
                message.reactions = reactions

                // Update UI
                messageAdapter.notifyItemChanged(messagePosition)
                Log.d(TAG, "Updated message #$messageId with ${reactions.size} reactions")
            }
        }
    }

    // Update a message with a reaction from WebSocket event
    private fun updateMessageWithReaction(reaction: MessageReaction, isRemoval: Boolean) {
        Log.d(TAG, "Updating message with reaction: ${reaction.reaction}, messageId=${reaction.messageId}, userId=${reaction.userId}, isRemoval=$isRemoval")

        val messagePosition = messagesList.indexOfFirst { it.id == reaction.messageId }

        if (messagePosition != -1) {
            val message = messagesList[messagePosition]

            if (isRemoval) {
                // Initialize mutable list if needed
                val mutableReactions = message.reactions?.toMutableList() ?: mutableListOf()

                Log.d(TAG, "Before removal: message has ${mutableReactions.size} reactions")

                // Remove this reaction
                val removed = mutableReactions.removeIf {
                    it.userId == reaction.userId && (reaction.reaction.isEmpty() || it.reaction == reaction.reaction)
                }

                Log.d(TAG, "Reaction removal result: $removed for user ${reaction.userId}")

                // Update the message's reactions list
                message.reactions = mutableReactions

                Log.d(TAG, "Removed reaction '${reaction.reaction}' from message #${reaction.messageId}. Remaining: ${mutableReactions.size} reactions")

                // Force immediate UI update for this specific message
                messageAdapter.notifyItemChanged(messagePosition)

                // Even though we've updated locally, force a server refresh to ensure consistency
                refreshMessageReactions(message.id)
            } else {
                // Initialize reactions list if needed
                if (message.reactions == null) {
                    message.reactions = mutableListOf()
                    Log.d(TAG, "Created new reactions list for message ${message.id}")
                }

                // Get as mutable list
                val mutableReactions = message.reactions?.toMutableList() ?: mutableListOf()

                Log.d(TAG, "Before adding: message has ${mutableReactions.size} reactions")

                // Check if user already has a reaction on this message
                val existingReactionIndex = mutableReactions.indexOfFirst {
                    it.userId == reaction.userId
                }

                if (existingReactionIndex != -1) {
                    // Replace existing reaction
                    Log.d(TAG, "Replacing existing reaction from user ${reaction.userId}")
                    mutableReactions[existingReactionIndex] = reaction
                } else {
                    // Add new reaction
                    Log.d(TAG, "Adding new reaction from user ${reaction.userId}: ${reaction.reaction}")
                    mutableReactions.add(reaction)
                }

                // Update the message's reactions list
                message.reactions = mutableReactions

                Log.d(TAG, "Added reaction '${reaction.reaction}' to message #${reaction.messageId}. Total: ${mutableReactions.size} reactions")

                // Update UI
                messageAdapter.notifyItemChanged(messagePosition)
            }
        } else {
            Log.e(TAG, "Could not find message with ID ${reaction.messageId} to update reaction")
        }
    }

    // Force a refresh of message reactions from server for specific message
    private fun refreshMessageReactions(messageId: Int) {
        Thread {
            try {
                Log.d(TAG, "Refreshing reactions for message $messageId")
                val url = "$API_URL/chat/message/$messageId/reactions"

                val request = Request.Builder()
                    .url(url)
                    .get()
                    .build()

                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        if (responseBody != null) {
                            try {
                                val jsonResponse = JSONObject(responseBody)
                                Log.d(TAG, "Reaction refresh response: $responseBody")

                                if (jsonResponse.optBoolean("success", false)) {
                                    val reactionsArray = jsonResponse.optJSONArray("reactions")

                                    // Create an empty list by default
                                    val reactions = mutableListOf<MessageReaction>()

                                    // If there are reactions, parse them
                                    if (reactionsArray != null && reactionsArray.length() > 0) {
                                        for (i in 0 until reactionsArray.length()) {
                                            val reactionObj = reactionsArray.getJSONObject(i)
                                            reactions.add(
                                                MessageReaction(
                                                    id = reactionObj.optInt("id", 0),
                                                    messageId = reactionObj.optInt("message_id", messageId),
                                                    userId = reactionObj.optInt("user_id", 0),
                                                    reaction = reactionObj.optString("reaction", ""),
                                                    createdAt = reactionObj.optString("created_at", "")
                                                )
                                            )
                                        }
                                    }

                                    // Process reactions (even if empty) to update UI
                                    Handler(Looper.getMainLooper()).post {
                                        updateMessageReactionsInUI(messageId, reactions)
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error parsing reaction refresh response", e)
                            }
                        }
                    } else {
                        Log.e(TAG, "Failed to refresh reactions: ${response.code}")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing reactions", e)
            }
        }.start()
    }

    // Direct UI update function for reactions
    private fun updateMessageReactionsInUI(messageId: Int, reactions: List<MessageReaction>) {
        // Find the message in our list
        val messagePosition = messagesList.indexOfFirst { it.id == messageId }

        if (messagePosition != -1) {
            val message = messagesList[messagePosition]

            // Check if reactions actually changed
            val currentReactions = message.reactions ?: emptyList()
            val changed = currentReactions.size != reactions.size ||
                          !currentReactions.containsAll(reactions) ||
                          !reactions.containsAll(currentReactions)

            if (changed) {
                Log.d(TAG, "Updating message #$messageId with ${reactions.size} reactions (was ${currentReactions.size})")

                // Update reactions list
                message.reactions = reactions

                // Update UI
                messageAdapter.notifyItemChanged(messagePosition)
            }
        } else {
            Log.e(TAG, "Could not find message with ID $messageId to update reactions")
        }
    }

    private fun getCurrentISOTimestamp(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        dateFormat.timeZone = TimeZone.getTimeZone("UTC")
        return dateFormat.format(Date())
    }

    // Add a dedicated method to poll for reactions periodically
    private fun startReactionPolling() {
        // Create and start a background thread for polling reactions
        Thread {
            try {
                while (true) {
                    // Poll every 1 second for faster updates
                    Thread.sleep(1000)

                    // Skip if the app is in background
                    if (!isChatActive) continue

                    // Find all message IDs that we want to refresh reactions for
                    val messageIds = synchronized(messagesList) {
                        // We can limit to last 10-20 messages for efficiency
                        messagesList.takeLast(20).map { it.id }.filter { it > 0 }
                    }

                    if (messageIds.isEmpty()) continue

                    // Log what we're doing
                    Log.d(TAG, "Polling for reaction updates on ${messageIds.size} messages")

                    // Poll each message for updated reactions
                    for (msgId in messageIds) {
                        try {
                            refreshMessageReactions(msgId)
                            // Smaller pause between messages to avoid overloading server
                            Thread.sleep(100)  // Reduced from 250ms to 100ms
                        } catch (e: Exception) {
                            Log.e(TAG, "Error refreshing reactions for message $msgId", e)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Reaction polling thread error", e)
            }
        }.start()
    }

    // Shows options to select an image from gallery or take a picture
    private fun showImagePickerOptions() {
        val options = arrayOf("Take Photo", "Choose from Gallery", "Cancel")
        val builder = androidx.appcompat.app.AlertDialog.Builder(this)
        builder.setTitle("Choose Image Source")
        builder.setItems(options) { dialog, which ->
            when (which) {
                0 -> checkCameraPermissionsAndOpenCamera()
                1 -> checkGalleryPermissionsAndOpenGallery()
                2 -> dialog.dismiss()
            }
        }
        builder.show()
    }

    // Check permissions and open camera
    private fun checkCameraPermissionsAndOpenCamera() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.CAMERA), REQUEST_IMAGE_PERMISSIONS)
        } else {
            openCamera()
        }
    }

    // Check permissions and open gallery
    private fun checkGalleryPermissionsAndOpenGallery() {
        selectImageLauncher.launch("image/*")
    }

    // This section was removed to merge with the existing onRequestPermissionsResult method below

    // Open camera to take picture
    private fun openCamera() {
        lastCreatedImageFile = createImageFile()
        if (lastCreatedImageFile != null) {
            val uri = FileProvider.getUriForFile(
                this,
                "${applicationContext.packageName}.provider",
                lastCreatedImageFile!!
            )
            takePictureLauncher.launch(uri)
        } else {
            Toast.makeText(this, "Error creating image file", Toast.LENGTH_SHORT).show()
        }
    }

    // Create a file to store the image
    private fun createImageFile(): File? {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        val storageDir = getExternalFilesDir(null)

        return try {
            File.createTempFile(imageFileName, ".jpg", storageDir)
        } catch (e: IOException) {
            Log.e(TAG, "Error creating image file", e)
            null
        }
    }

    // Utility methods for UI updates
    private fun updateUI() {
        messageAdapter.notifyDataSetChanged()
    }

    private fun scrollToBottom() {
        if (messagesList.isNotEmpty()) {
            recyclerViewMessages.scrollToPosition(messagesList.size - 1)
        }
    }

    // Upload the selected image to the server
    private fun uploadImage(imageUri: Uri) {
        // Show loading indicator
        progressBar.visibility = View.VISIBLE

        // Generate a client message ID
        val clientMessageId = UUID.randomUUID().toString()

        // If we don't have the current user's avatar URL, try to fetch it from cache
        if (currentUserAvatarUrl.isNullOrEmpty()) {
            try {
                val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
                currentUserAvatarUrl = prefs.getString("USER_AVATAR", null)
                Log.d(TAG, "Retrieved avatar URL from prefs for image message: $currentUserAvatarUrl")
            } catch (e: Exception) {
                Log.e(TAG, "Error retrieving avatar URL from prefs", e)
            }
        }

        // Create a temporary message to show in the UI
        val tempMessage = Message(
            senderId = currentUserId,
            receiverId = otherUserId,
            text = "",
            messageType = "image",
            matchId = matchId,
            clientMessageId = clientMessageId,
            createdAt = getCurrentISOTimestamp(),
            isPending = true,
            senderAvatarUrl = currentUserAvatarUrl // Add avatar URL to ensure it shows immediately
        )

        // Add the message to the list
        messagesList.add(tempMessage)
        pendingMessages[clientMessageId] = tempMessage
        updateUI()

        // Prepare and send the image in background
        Thread {
            try {
                // Get the file from the URI
                val contentResolver = applicationContext.contentResolver
                val inputStream = contentResolver.openInputStream(imageUri)
                val file = File(cacheDir, "image_${clientMessageId}.jpg")
                val outputStream = FileOutputStream(file)

                // Copy the file
                inputStream?.use { input ->
                    outputStream.use { output ->
                        input.copyTo(output)
                    }
                }

                // Create multipart request body
                val requestFile = file.asRequestBody("image/jpeg".toMediaTypeOrNull())
                val imagePart = createFormData("image", file.name, requestFile)

                // Create the request body with additional data
                val requestBody = MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("sender_id", currentUserId.toString())
                    .addFormDataPart("receiver_id", otherUserId.toString())
                    .addFormDataPart("match_id", matchId.toString())
                    .addFormDataPart("message_type", "image")
                    .addFormDataPart("clientMessageId", clientMessageId)
                    .addPart(imagePart)
                    .build()

                // Create the request
                val request = Request.Builder()
                    .url("$API_URL/messages/upload-image")
                    .post(requestBody)
                    .build()

                // Execute the request
                client.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()
                        val jsonResponse = JSONObject(responseBody ?: "{}")
                        val messageId = jsonResponse.optInt("id", -1)
                        val imageFile = jsonResponse.optString("image_file", "")

                        // Update the pending message with the server response
                                                        runOnUiThread {
                            val pendingMessage = pendingMessages[clientMessageId]
                            if (pendingMessage != null) {
                                // Update additional properties from server response
                                messagesList.replaceAll { msg ->
                                    if (msg.clientMessageId == clientMessageId) {
                                        // Create a new message but preserve all properties we want to keep
                                        val updatedMsg = Message(
                                            id = messageId,
                                            senderId = msg.senderId,
                                            receiverId = msg.receiverId,
                                            text = msg.text,
                                            matchId = msg.matchId,
                                            clientMessageId = msg.clientMessageId,
                                            createdAt = msg.createdAt,
                                            messageType = "image",
                                            imageFile = imageFile,
                                            isPending = false,
                                            senderAvatarUrl = msg.senderAvatarUrl, // Preserve avatar URL
                                            replyToId = msg.replyToId,
                                            replyToText = msg.replyToText,
                                            replyToSenderId = msg.replyToSenderId,
                                            reactions = msg.reactions
                                        )

                                        // Log the update with avatar info for debugging
                                        Log.d(TAG, "Updating message with avatar URL: ${msg.senderAvatarUrl}")

                                        updatedMsg
                                    } else {
                                        msg
                                    }
                                }
                                updateUI()
                                scrollToBottom()

                                // Remove from pending messages
                                pendingMessages.remove(clientMessageId)
                            }
                            progressBar.visibility = View.GONE
                        }
                    } else {
                        // Handle upload failure
                        runOnUiThread {
                            // Find and update the pending message
                            messagesList.replaceAll { msg ->
                                if (msg.clientMessageId == clientMessageId) {
                                    msg.copy(isPending = false, isError = true)
                                } else {
                                    msg
                                }
                            }
                            updateUI()
                            Toast.makeText(this@ChatActivity, "Failed to upload image", Toast.LENGTH_SHORT).show()
                            progressBar.visibility = View.GONE
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error uploading image", e)
                runOnUiThread {
                    // Find and update the pending message
                    messagesList.replaceAll { msg ->
                        if (msg.clientMessageId == clientMessageId) {
                            msg.copy(isPending = false, isError = true)
                        } else {
                            msg
                        }
                    }
                    updateUI()
                    Toast.makeText(this@ChatActivity, "Error uploading image: ${e.message}", Toast.LENGTH_SHORT).show()
                    progressBar.visibility = View.GONE
                }
            }
        }.start()
    }

    // Method to fetch the current user's avatar URL from the server
    private fun fetchCurrentUserAvatar() {
        if (currentUserId <= 0) {
            Log.e(TAG, "Cannot fetch avatar: Invalid currentUserId")
            return
        }

        Thread {
            try {
                val url = URL("$API_URL/profile/profile/$currentUserId")
                Log.d(TAG, "Fetching current user avatar from: $url")

                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)

                    if (jsonResponse.optBoolean("success", false)) {
                        val userData = jsonResponse.optJSONObject("user")
                        if (userData != null) {
                            val avatarUrl = userData.optString("avatar_url", "")

                            if (!avatarUrl.isNullOrEmpty() && avatarUrl != "null") {
                                Log.d(TAG, "Retrieved avatar URL for current user: $avatarUrl")

                                // Save to SharedPreferences and update current value
                                currentUserAvatarUrl = avatarUrl
                                val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
                                prefs.edit().putString("USER_AVATAR", avatarUrl).apply()

                                Log.d(TAG, "Saved avatar URL to SharedPreferences: $avatarUrl")
                            } else {
                                Log.w(TAG, "User has no avatar URL")
                            }
                        }
                    } else {
                        Log.e(TAG, "Failed to retrieve user data: ${jsonResponse.optString("message", "Unknown error")}")
                    }
                } else {
                    Log.e(TAG, "HTTP error while fetching user avatar: $responseCode")
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching user avatar", e)
            }
        }.start()
    }

    // Show chat options menu
    private fun showChatOptionsMenu() {
        val popup = PopupMenu(this, imageViewMenuOptions)
        popup.menuInflater.inflate(R.menu.chat_menu, popup.menu)

        popup.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_send_buzz -> {
                    sendBuzz()
                    true
                }
                R.id.action_block_user -> {
                    showBlockUserConfirmation()
                    true
                }
                R.id.action_delete_chat -> {
                    showDeleteChatConfirmation()
                    true
                }
                else -> false
            }
        }

        popup.show()
    }

    // Send buzz message
    private fun sendBuzz() {
        val currentTime = System.currentTimeMillis()

        // Check cooldown period
        if (currentTime - lastBuzzTime < BUZZ_COOLDOWN_MS) {
            val remainingTime = (BUZZ_COOLDOWN_MS - (currentTime - lastBuzzTime)) / 1000
            Toast.makeText(this, "Debes esperar $remainingTime segundos antes de enviar otro zumbido", Toast.LENGTH_SHORT).show()
            return
        }

        lastBuzzTime = currentTime

        // Create buzz message
        val buzzMessage = Message(
            id = 0, // Will be set by server
            matchId = originalMatchId,
            senderId = currentUserId,
            receiverId = otherUserId,
            text = "¡BuZzZzzZzZZ!",
            messageType = "buzz",
            createdAt = getCurrentISOTimestamp(),
            isRead = false,
            isPending = true,
            isError = false,
            metadata = mutableMapOf()
        )

        // Add to local list immediately for UI feedback
        synchronized(messagesList) {
            messagesList.add(buzzMessage)
        }
        messageAdapter.notifyItemInserted(messagesList.size - 1)
        recyclerViewMessages.scrollToPosition(messagesList.size - 1)

        // Trigger vibration and screen shake
        triggerBuzzEffects()

        // Send to server
        sendBuzzToServer(buzzMessage)
    }

    // Trigger buzz effects (vibration and screen shake)
    private fun triggerBuzzEffects() {
        // Vibrate device for 3 seconds with intense pattern
        val vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val vibrationEffect = VibrationEffect.createWaveform(
                longArrayOf(0, 200, 50, 200, 50, 300, 100, 200, 50, 400, 100, 200, 50, 300, 100, 200),
                -1 // Don't repeat
            )
            vibrator.vibrate(vibrationEffect)
        } else {
            @Suppress("DEPRECATION")
            vibrator.vibrate(3000) // 3 seconds
        }

        // Animate screen shake - apply to multiple views for maximum effect
        val earthquakeAnimation = AnimationUtils.loadAnimation(this, R.anim.buzz_earthquake)
        val intenseShakeAnimation = AnimationUtils.loadAnimation(this, R.anim.buzz_intense_shake)
        val normalShakeAnimation = AnimationUtils.loadAnimation(this, R.anim.buzz_shake)

        // Get all the main views
        val contentView = findViewById<View>(android.R.id.content)
        val recyclerView = findViewById<RecyclerView>(R.id.recyclerViewMessages)
        val headerView = findViewById<View>(R.id.toolbar)
        val inputView = findViewById<View>(R.id.messageInputLayout)

        // Apply the most intense animation to the main content
        contentView.startAnimation(earthquakeAnimation)

        // Apply different animations to different sections for layered effect
        recyclerView?.startAnimation(intenseShakeAnimation)
        headerView?.startAnimation(normalShakeAnimation)
        inputView?.startAnimation(normalShakeAnimation)

        // Add additional waves of animation for prolonged effect
        Handler(Looper.getMainLooper()).postDelayed({
            recyclerView?.startAnimation(normalShakeAnimation)
        }, 500)

        Handler(Looper.getMainLooper()).postDelayed({
            contentView.startAnimation(intenseShakeAnimation)
        }, 1000)

        // Final shake wave
        Handler(Looper.getMainLooper()).postDelayed({
            recyclerView?.startAnimation(normalShakeAnimation)
            headerView?.startAnimation(normalShakeAnimation)
            inputView?.startAnimation(normalShakeAnimation)
        }, 1500)

        // Add flash effect for extra visual impact
        val flashAnimation = AnimationUtils.loadAnimation(this, R.anim.buzz_flash)
        contentView.startAnimation(flashAnimation)

        // Change background color temporarily for flash effect
        val originalBackground = contentView.background
        Handler(Looper.getMainLooper()).post {
            contentView.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
            Handler(Looper.getMainLooper()).postDelayed({
                contentView.setBackgroundColor(ContextCompat.getColor(this, R.color.black))
            }, 100)
            Handler(Looper.getMainLooper()).postDelayed({
                contentView.setBackgroundColor(ContextCompat.getColor(this, R.color.white))
            }, 200)
            Handler(Looper.getMainLooper()).postDelayed({
                contentView.background = originalBackground
            }, 300)
        }

        Log.d(TAG, "Buzz effects triggered - vibration, screen shake, and flash applied")
    }

    // Send buzz message to server
    private fun sendBuzzToServer(buzzMessage: Message) {
        Thread {
            try {
                val url = URL("$API_URL/chat/$originalMatchId/message")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.doOutput = true

                val requestBody = JSONObject().apply {
                    put("sender_id", currentUserId)
                    put("receiver_id", otherUserId)
                    put("message_text", buzzMessage.text)
                    put("message_type", "buzz")
                }

                connection.outputStream.use { os ->
                    os.write(requestBody.toString().toByteArray())
                }

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)

                    if (jsonResponse.getBoolean("success")) {
                        val messageObject = jsonResponse.getJSONObject("message")
                        val messageId = messageObject.getInt("id")

                        // Update message with server ID
                        Handler(Looper.getMainLooper()).post {
                            synchronized(messagesList) {
                                for (i in messagesList.indices.reversed()) {
                                    val msg = messagesList[i]
                                    if (msg.isPending && msg.messageType == "buzz" && msg.senderId == currentUserId) {
                                        messagesList[i] = msg.copy(
                                            id = messageId,
                                            isPending = false,
                                            isError = false
                                        )
                                        messageAdapter.notifyItemChanged(i)
                                        break
                                    }
                                }
                            }
                        }

                        Log.d(TAG, "Buzz message sent successfully with ID: $messageId")
                    } else {
                        markBuzzMessageAsError()
                        Log.e(TAG, "Failed to send buzz message: ${jsonResponse.optString("message")}")
                    }
                } else {
                    markBuzzMessageAsError()
                    Log.e(TAG, "HTTP error sending buzz message: $responseCode")
                }

                connection.disconnect()
            } catch (e: Exception) {
                markBuzzMessageAsError()
                Log.e(TAG, "Error sending buzz message", e)
            }
        }.start()
    }

    // Mark buzz message as error
    private fun markBuzzMessageAsError() {
        Handler(Looper.getMainLooper()).post {
            synchronized(messagesList) {
                for (i in messagesList.indices.reversed()) {
                    val msg = messagesList[i]
                    if (msg.isPending && msg.messageType == "buzz" && msg.senderId == currentUserId) {
                        messagesList[i] = msg.copy(
                            isPending = false,
                            isError = true
                        )
                        messageAdapter.notifyItemChanged(i)
                        break
                    }
                }
            }
        }
    }

    // Handle received buzz message
    private fun handleReceivedBuzz(message: Message) {
        Log.d(TAG, "🔔 BUZZ DEBUG - handleReceivedBuzz called for message: ${message.text}")

        // Trigger buzz effects for received buzz
        triggerBuzzEffects()

        // Show toast notification
        Handler(Looper.getMainLooper()).post {
            Toast.makeText(this, "${otherUserName ?: "Usuario"} te ha enviado un zumbido!", Toast.LENGTH_SHORT).show()
            Log.d(TAG, "🔔 BUZZ DEBUG - Toast shown for received buzz")
        }
    }

    // Setup shake detection for buzz
    private fun setupShakeDetection() {
        try {
            sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
            accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)

            if (accelerometer != null) {
                Log.d(TAG, "Accelerometer sensor found, shake detection enabled")
            } else {
                Log.w(TAG, "No accelerometer sensor found, shake detection disabled")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up shake detection", e)
        }
    }

    // Register shake detection sensor
    private fun registerShakeDetection() {
        try {
            accelerometer?.let { sensor ->
                sensorManager.registerListener(this, sensor, SensorManager.SENSOR_DELAY_UI)
                Log.d(TAG, "Shake detection sensor registered")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error registering shake detection sensor", e)
        }
    }

    // Unregister shake detection sensor
    private fun unregisterShakeDetection() {
        try {
            sensorManager.unregisterListener(this)
            Log.d(TAG, "Shake detection sensor unregistered")
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering shake detection sensor", e)
        }
    }

    // SensorEventListener implementation
    override fun onSensorChanged(event: SensorEvent?) {
        if (event?.sensor?.type == Sensor.TYPE_ACCELEROMETER) {
            val x = event.values[0]
            val y = event.values[1]
            val z = event.values[2]

            // Calculate the magnitude of acceleration
            val acceleration = kotlin.math.sqrt((x * x + y * y + z * z).toDouble()).toFloat()

            // Remove gravity (approximately 9.8 m/s²)
            val accelerationWithoutGravity = kotlin.math.abs(acceleration - SensorManager.GRAVITY_EARTH)

            // Check if shake is detected
            if (accelerationWithoutGravity > SHAKE_THRESHOLD) {
                val currentTime = System.currentTimeMillis()

                // Check cooldown to prevent multiple shake detections
                if (currentTime - lastShakeTime > SHAKE_COOLDOWN_MS) {
                    lastShakeTime = currentTime
                    onShakeDetected()
                }
            }
        }
    }

    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        // Not needed for shake detection
    }

    // Handle shake detection
    private fun onShakeDetected() {
        Log.d(TAG, "Shake detected! Attempting to send buzz...")

        // Check if we can send a buzz (respects buzz cooldown)
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastBuzzTime < BUZZ_COOLDOWN_MS) {
            val remainingTime = (BUZZ_COOLDOWN_MS - (currentTime - lastBuzzTime)) / 1000
            Toast.makeText(this, "🤳 Agitación detectada! Espera $remainingTime segundos para enviar otro zumbido", Toast.LENGTH_SHORT).show()
            return
        }

        // Send buzz via shake
        Handler(Looper.getMainLooper()).post {
            Toast.makeText(this, "🤳 ¡Agitación detectada! Enviando zumbido...", Toast.LENGTH_SHORT).show()
            sendBuzz()
        }
    }
}