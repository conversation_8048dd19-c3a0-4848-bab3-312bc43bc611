<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingVertical="8dp">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/viewerAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_default_avatar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/viewerUsername"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/viewerAvatar"
        app:layout_constraintTop_toTopOf="@id/viewerAvatar"
        app:layout_constraintBottom_toBottomOf="@id/viewerAvatar"
        app:layout_constraintEnd_toStartOf="@id/viewTimeText"
        android:maxLines="1"
        android:ellipsize="end"
        tools:text="username" />

    <TextView
        android:id="@+id/viewTimeText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/viewerAvatar"
        app:layout_constraintBottom_toBottomOf="@id/viewerAvatar"
        tools:text="5h" />

</androidx.constraintlayout.widget.ConstraintLayout> 