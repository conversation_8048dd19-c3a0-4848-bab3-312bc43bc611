%com.spyro.vmeet.activity.BaseActivity(androidx.appcompat.app.AppCompatActivity1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder;com.spyro.vmeet.adapter.ReportsAdapter.ReportActionListener0androidx.viewpager2.adapter.FragmentStateAdapterandroid.app.Applicationkotlin.Enum$android.hardware.SensorEventListenercom.google.gson.JsonSerializer com.google.gson.JsonDeserializer8com.spyro.vmeet.adapter.MessageAdapter.MessageViewHolder=com.spyro.vmeet.adapter.MessageAdapter.ImageMessageViewHolder=com.spyro.vmeet.adapter.MessageAdapter.VoiceMessageViewHolderandroid.os.Parcelableandroid.app.Dialogandroidx.fragment.app.Fragment$com.spyro.vmeet.ui.base.BaseFragment6com.google.firebase.messaging.FirebaseMessagingService!android.content.BroadcastReceiverandroid.app.Service)<EMAIL>#androidx.lifecycle.AndroidViewModel$androidx.fragment.app.DialogFragmentDcom.spyro.vmeet.ui.community.ImageUploadHelper.ImageSelectedListenerEcom.spyro.vmeet.ui.community.MusicSearchDialog.MusicSelectionListener+androidx.appcompat.widget.AppCompatTextViewAcom.google.android.material.bottomsheet.BottomSheetDialogFragmentandroid.widget.ImageView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      