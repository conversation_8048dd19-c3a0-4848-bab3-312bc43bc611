package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u0014J\b\u0010\u000f\u001a\u00020\fH\u0014J\b\u0010\u0010\u001a\u00020\fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/spyro/vmeet/CommunityHostActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "()V", "TAG", "", "bottomNavView", "Lcom/google/android/material/bottomnavigation/BottomNavigationView;", "isSettingSelectedItemProgrammatically", "", "userId", "", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onResume", "setupBottomNavigation", "app_debug"})
public final class CommunityHostActivity extends com.spyro.vmeet.activity.BaseActivity {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "CommunityHostActivity";
    private int userId = -1;
    private com.google.android.material.bottomnavigation.BottomNavigationView bottomNavView;
    private boolean isSettingSelectedItemProgrammatically = false;
    
    public CommunityHostActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupBottomNavigation() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
}