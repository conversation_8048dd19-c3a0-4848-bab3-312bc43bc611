http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/rotate.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_earthquake.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_flash.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_intense_shake.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/text_slide_in_left.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_shake.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/pulse.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/slide_up.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/text_fade_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/text_rotate.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/attrs.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-v29/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_vibration.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/cyberpunk_circle_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_container_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_image_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/voice_note_player_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_alt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/message_received_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/radar_header_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/swipe_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/story_ring_unseen_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_status_chip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_unselected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/background_reply_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_read.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/unread_counter_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/story_ring_seen.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_button_outline_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_mic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/youtube_play_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_button_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circular_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_person_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_document_type_chip.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/recording_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_dot_selected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_admin_crown.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/section_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_eye.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_permission.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_2.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/story_ring_unseen.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_1.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_send.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_3.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_square_shape.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_send_comment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_matches.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_dialog_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_community.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_vert.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_circle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_pending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circular_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_music_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/distance_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/trait_selected_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_outlined.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_mark_read.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reply.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/image_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/background_reply_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_radar_empty.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_background_own.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/cyberpunk_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/glowing_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_default_avatar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_button_alt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification_vmeet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-v26/ic_notification_vmeet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_heart.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_selector_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/message_input_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_verified.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chats.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_rounded_accent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_dot.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/online_status_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_buzz_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/trait_unselected_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/post_edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_preview_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_admin.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_arrow.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_emoji.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/unread_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close_circle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_verified_check.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_topic_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/online_status_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_selected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_document_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/default_room_icon.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_gif.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_audiotrack.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/login_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_outline_cyberpunk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chat_bubble.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/progress_bar_cyberpunk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_comment_outline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/glowing_neon_ring.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_edit_text.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/mic_button_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/default_avatar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_buzz_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/avatar_circle_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/selected_color_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_mute.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_gif.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient_2.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient_1.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_sheet_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_swatch_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_swipe.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient_3.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stop_mic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_bottom_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/recording_cancel_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/preview_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reply_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/image_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_cyberpunk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_online_status.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_like_outline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_post.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_dark_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_edittext_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/buzz_pulse_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/indicator_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_placeholder_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/comment_input_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/message_sent_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_edittext_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_dot_default.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/knightwarriorw16n8.otf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/combackhomeregularjemd9.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/interitalic.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/orbitron.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/inter.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/supremespikekvo8d.otf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/app_font.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/greatsracingfreeforpersonalitalicbll.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/steelarj9vnj.otf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/veniteadoremusstraightyzo6v.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/wowdinog33vp.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/veniteadoremusrgrba.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_reveal.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_join_room_prompt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings_compat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_create_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_blind_date.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_ban_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_report_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_color_palette_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_received_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_sent_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_gif_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_gif_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_image_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_image_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_received_gif_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_sent_gif_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_music_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_match.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_comments.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_tutorial.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_upload_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_verification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_voice_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_voice_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_chat_rooms.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_community_host.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_matches.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_swipe.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_video_feed.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_private_chats.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_radar_filters.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_post.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_user_muted.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/profile_dropdown_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_admin_panel.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_story_editor.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_create_post.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_story_viewer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chat_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_global_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_text_with_style.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_verification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_mute_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_chat_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_story.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_user_card.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_community.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_story_viewer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comments_bottom_sheet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_profile_image_slider.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_personality_trait.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_custom_color_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/room_chat_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_delete_room_confirmation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_music_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_topic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_full_screen_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_reaction_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_radar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_gif_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_rules_acceptance.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_password.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_my_videos.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_radar_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_verification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profiles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_forgot_password.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_message_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_edit_personality_traits.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_result.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_reaction.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_blind_date.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_my_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_community_guidelines.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_buzz_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_buzz_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/tutorial_page.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_waiting.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_verification_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_username.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_mention_suggestion.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_room_loader.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_email.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blocked_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_match_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/grid_emoji_category.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_swipe.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_blocked_users.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_reports.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/profile_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_location_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_story_viewers.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_gif.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_profile_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_emoji_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_improved_emoji_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_reject_verification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_personality_trait_category.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/menu_community_dropdown.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_room_admin.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_emoji.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_post_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_empty.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dropdown_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/raw/welcome.mp3,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/text_styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:rotate,0,F;buzz_earthquake,1,F;buzz_flash,2,F;buzz_intense_shake,3,F;text_slide_in_left,4,F;buzz_shake,5,F;pulse,6,F;fade_in,7,F;slide_up,8,F;text_fade_in,9,F;text_rotate,10,F;+array:document_types,11,V4003c0cd3,1300420dd5,;DNI/Cédula de identidad,Pasaporte,Carnet de conducir,Tarjeta de residencia,Credencial para votar,;document_type_values,11,V400440ddb,13004a0eb7,;dni,passport,driving_license,residence_card,voter_id,;+attr:isAlt,12,V80003006a,2e00030090,;boolean:;android\:text,12,V8000700eb,**********,;:;android\:textSize,12,V800080111,**********,;:;android\:textColor,12,V80009013b,290009015c,;:;+color:colorPrimaryDark,13,V4000b01dc,34000b020c,;"#FF3700B3";cyberpunk_accent_voice_thumb,13,V400350a6a,3e00350aa4,;"#A4FFC9";cyberpunk_card_background,13,V4002a079c,3b002a07d3,;"#162447";dark_red,13,V4001403b2,2a001403d8,;"#C62828";buzz_text_color,13,V4003f0c5a,31003f0c87,;"#FFFFFF";text_primary,13,V4004a0e61,2e004a0e8b,;"#E0E0E0";cyberpunk_divider,13,V400300941,**********,;"#E0E0E0";neon_pink,13,V4000d026b,2b000d0292,;"#FF00E4";cyberpunk_purple,13,V4003a0b93,32003a0bc1,;"#B39DDB";emoji_picker_tab_selected,13,V40023064b,3b00230682,;"#9C27B0";cyberpunk_shadow,13,V4003209ad,32003209db,;"#0A0A15";dark_blue,13,V4001002f5,2b0010031c,;"#0B0B2B";read_message_bg,13,V4001a04ce,31001a04fb,;"#E0E0E0";neon_blue,13,V38000b0210,5f000b0237,;"#4DB6E6";white,13,V400080150,**********,;"#FFFFFFFF";teal_700,13,V4000600f7,2c0006011f,;"#FF018786";purple_700,13,V400040099,2e000400c3,;"#FF3700B3";emoji_picker_tab_indicator,13,V400240688,3c002406c0,;"#9C27B0";cyberpunk_text_secondary,13,V4002d0862,3a002d0898,;"#A0A0A0";buzz_received_primary,13,V400430d2d,3700430d60,;"#9C27B0";purple_500,13,V400030069,2e00030093,;"#FF6200EE";primary_dark,13,V4005910d2,2e005910fc,;"#162447";neon_blue_transparent,13,V400490e26,3900490e5b,;"#224DB6E6";buzz_received_secondary,13,V400440d66,3900440d9b,;"#673AB7";colorPrimary,13,V4000a01aa,30000a01d6,;"#FF6200EE";cyberpunk_accent,13,V400380b2c,3200380b5a,;"#FFEB3B";mention_highlight_color,13,V4003c0bfb,39003c0c30,;"#3F51B5";neon_purple,13,V4000e0298,2d000e02c1,;"#9D00FF";cyberpunk_accent_voice,13,V400340a1c,3800340a50,;"#00FF66";card_background,13,V40013037f,31001303ac,;"#101033";unread_message_bg,13,V4001b0501,33001b0530,;"#C2C2FF";neon_blue_dark,13,V4001f05bc,30001f05e8,;"#2E86AB";cyberpunk_accent_primary,13,V4002e089e,3a002e08d4,;"#FF00E4";success_green,13,V400540fe0,2f0054100b,;"#00FF66";comfy_blue,13,V4000c023d,2c000c0265,;"#5DADE2";background,13,V400120351,2c00120379,;"#0A0A25";cyberpunk_green,13,V400390b60,3100390b8d,;"#00FFAB";gray_dark,13,V400510f88,2b00510faf,;"#2A2A2A";warning_orange,13,V40056103e,300056106a,;"#FFA500";buzz_sent_primary,13,V400410cc1,3300410cf0,;"#FF6B35";purple_200,13,V400020039,2e00020063,;"#FFBB86FC";neon_green,13,V4000f02c7,2c000f02ef,;"#00FF66";emoji_picker_bg,13,V400220618,3100220645,;"#333333";cyberpunk_card_stroke,13,V4002b07d9,37002b080c,;"#4DB6E6";emoji_picker_title_text,13,V4002606ff,3900260734,;"#FFFFFF";cyberpunk_highlight,13,V400310976,35003109a7,;"#3A3A5D";teal_200,13,V4000500c9,2c000500f1,;"#FF03DAC5";colorAccent,13,V40009017b,2d000901a4,;"#FF00E4";dark_purple,13,V4001e058d,2d001e05b6,;"#2A2A60";emoji_picker_tab_text,13,V4002506c6,37002506f9,;"#CCCCCC";gray_light,13,V400500f5a,2c00500f82,;"#A0A0A0";cyberpunk_orange,13,V4003b0bc7,32003b0bf5,;"#FFAB40";dark_background,13,V4004c0ec3,31004c0ef0,;"#0A0A25";background_dark,13,V40058109f,31005810cc,;"#0A0A25";login_button_color,13,V400170436,3400170466,;"#BB86FC";cyberpunk_background,13,V400290764,3600290796,;"#1A1A2E";accent_cyan,13,V400571070,2d00571099,;"#4DB6E6";status_delivered,13,V4001c0536,32001c0564,;"#888888";cyberpunk_accent_secondary,13,V4002f08ee,3c002f0926,;"#00FF66";buzz_sent_secondary,13,V400420cf6,3500420d27,;"#F7931E";comfy_red,13,V4001503de,2b00150405,;"#E74C3C";cyberpunk_text,13,V400370afa,3000370b26,;"#E0E0E0";button_background,13,V4004d0ef6,33004d0f25,;"#162447";cyberpunk_text_primary,13,V4002c0828,38002c085c,;"#E0E0E0";black,13,V400070125,290007014a,;"#FF000000";offline_gray,13,V400480df6,2e00480e20,;"#888888";register_button_color,13,V40018046c,370018049f,;"#A066F0";darker_blue,13,V400110322,2d0011034b,;"#050518";error_red,13,V400551011,2b00551038,;"#FF4444";cyberpunk_yellow,13,V400360ac6,3200360af4,;"#FFFF00";online_green,13,V400470dc6,2e00470df0,;"#00FF66";text_secondary,13,V4004b0e91,30004b0ebd,;"#A0A0A0";buzz_text_shadow,13,V400400c8d,3200400cbb,;"#000000";+dimen:status_bar_height,14,V4000600af,30000600db,;"24dp";status_bar_height,15,V400030071,4d000300ba,;"@*android\:dimen/status_bar_height";story_item_spacing,14,V40003005a,3000030086,;"8dp";+drawable:ic_vibration,16,F;status_error,17,F;cyberpunk_circle_button,18,F;reaction_container_background,19,F;bg_image_placeholder,20,F;voice_note_player_background,21,F;neon_button_alt,22,F;message_received_background,23,F;radar_header_gradient,24,F;swipe_background,25,F;story_ring_unseen_gradient,26,F;ic_profile_placeholder,27,F;bg_status_chip,28,F;tab_unselected,29,F;background_reply_sent,30,F;ic_add_image,31,F;neon_button_red,32,F;status_read,33,F;unread_counter_background,34,F;status_sent,35,F;admin_name_background,36,F;search_background,37,F;story_ring_seen,38,F;ic_notification,39,F;rounded_button_outline_background,40,F;circle_background,41,F;ic_profile,42,F;ic_add_mic,43,F;youtube_play_button_background,44,F;ic_launcher_background,45,F;circle_button_bg,46,F;circular_button,47,F;reaction_background,48,F;ic_person_placeholder,49,F;bg_document_type_chip,50,F;recording_button_background,51,F;tab_dot_selected,52,F;ic_admin_crown,53,F;section_background,54,F;ic_eye,55,F;ic_location_permission,56,F;admin_gradient_2,57,F;story_ring_unseen,58,F;admin_gradient_1,59,F;ic_send,60,F;admin_gradient_3,61,F;ic_arrow_forward,62,F;modern_edit_text_background,63,F;color_square_shape,64,F;neon_button,65,F;ic_send_comment,66,F;ic_pause,67,F;ic_matches,68,F;rounded_dialog_background,69,F;bg_message_received,70,F;ic_community,71,F;ic_more_vert,72,F;neon_circle,73,F;status_pending,74,F;circular_button_background,75,F;ic_music_placeholder,76,F;admin_gradient_animation,77,F;gradient_message_sent,78,F;distance_badge_background,79,F;trait_selected_background,80,F;ic_check_outlined,81,F;ic_refresh,82,F;ic_mark_read,83,F;ic_reply,84,F;image_error,85,F;background_reply_received,86,F;ic_radar_empty,87,F;reaction_background_own,88,F;cyberpunk_gradient,89,F;glowing_border,90,F;ic_default_avatar,91,F;modern_button_alt,92,F;ic_notification_vmeet,93,F;ic_notification_vmeet,94,F;ic_check,95,F;gradient_message_received,96,F;gradient_overlay,97,F;ic_heart,98,F;neon_button_green,99,F;reaction_selector_background,100,F;badge_user,101,F;ic_delete,102,F;message_input_background,103,F;ic_arrow_back,104,F;admin_name_gradient,105,F;badge_verified,106,F;spinner_background,107,F;ic_chats,108,F;button_rounded_accent,109,F;circle_dot,110,F;online_status_background,111,F;rounded_corner_background,112,F;bg_buzz_message_received,113,F;ic_chat,114,F;rounded_button_background,115,F;trait_unselected_background,116,F;post_edit_text_background,117,F;color_preview_background,118,F;badge_admin,119,F;ic_play_arrow,120,F;ic_emoji,121,F;unread_indicator,122,F;ic_filter,123,F;ic_close_circle,124,F;ic_verified_check,125,F;ic_topic_placeholder,126,F;online_status_indicator,127,F;tab_selected,128,F;ic_document_placeholder,129,F;default_room_icon,130,F;ic_settings,131,F;ic_add_gif,132,F;ic_audiotrack,133,F;login_background,134,F;button_outline_cyberpunk,135,F;ic_chat_bubble,136,F;progress_bar_cyberpunk,137,F;ic_comment_outline,138,F;bg_message_sent,139,F;glowing_neon_ring,140,F;bg_edit_text,141,F;mic_button_selector,142,F;default_avatar,143,F;bg_buzz_message_sent,144,F;avatar_circle_border,145,F;selected_color_border,146,F;ic_location,147,F;ic_mute,148,F;ic_gif,149,F;admin_name_gradient_2,150,F;admin_name_gradient_1,151,F;bottom_sheet_background,152,F;color_swatch_background,153,F;ic_swipe,154,F;admin_name_gradient_3,155,F;ic_stop_mic,156,F;gradient_bottom_overlay,157,F;recording_cancel_background,158,F;preview_background,159,F;reply_background,160,F;image_placeholder,161,F;neon_button_blue,162,F;tab_selector,163,F;button_cyberpunk,164,F;circle_online_status,165,F;ic_like_outline,166,F;ic_add_post,167,F;rounded_dark_background,168,F;rounded_edittext_background,169,F;buzz_pulse_animation,170,F;indicator_background,171,F;ic_launcher_foreground,172,F;ic_play,173,F;ic_placeholder_image,174,F;comment_input_background,175,F;message_sent_background,176,F;rounded_edittext_bg,177,F;ic_search,178,F;modern_button,179,F;tab_dot_default,180,F;+font:knightwarriorw16n8,181,F;combackhomeregularjemd9,182,F;interitalic,183,F;orbitron,184,F;inter,185,F;supremespikekvo8d,186,F;app_font,187,F;greatsracingfreeforpersonalitalicbll,188,F;steelarj9vnj,189,F;veniteadoremusstraightyzo6v,190,F;wowdinog33vp,191,F;veniteadoremusrgrba,192,F;+id:textViewWaitingForOther,193,F;checkboxPrivacyPolicy,194,F;buttonJoinRoom,195,F;layoutMaxDistance,196,F;layoutMaxDistance,197,F;switchCustomRules,198,F;buttonSendChat,199,F;editTextBanReason,200,F;textViewMemberCount,201,F;radioGroupReportReason,202,F;colorNeonGreen,203,F;cardViewMessage,204,F;cardViewMessage,205,F;cardViewMessage,206,F;cardViewMessage,207,F;cardViewMessage,208,F;cardViewMessage,209,F;cardViewMessage,210,F;cardViewMessage,211,F;cardViewMessage,212,F;cardViewMessage,212,F;cardViewMessage,212,F;cardViewMessage,213,F;cardViewMessage,213,F;cardViewMessage,213,F;cardViewMessage,214,F;cardViewMessage,215,F;imageViewCoverArt,216,F;imageViewCoverArt,216,F;imageViewCoverArt,216,F;textViewTime,208,F;textViewTime,209,F;textViewTime,210,F;textViewTime,211,F;textViewTime,217,F;textViewTime,212,F;textViewTime,213,F;textViewTime,214,F;textViewTime,215,F;nestedScrollView,218,F;nestedScrollView,219,F;buttonNext,220,F;buttonUpload,221,F;navigation_community,222,F;buttonResend,223,F;textViewVoiceTime,224,F;textViewVoiceTime,225,F;fabCreateRoom,226,F;progressBarPreview,216,F;bottom_navigation,227,F;bottom_navigation,228,F;bottom_navigation,229,F;bottom_navigation,229,F;bottom_navigation,229,F;bottom_navigation,219,F;bottom_navigation,219,F;bottom_navigation,196,F;bottom_navigation,197,F;bottom_navigation,230,F;bottom_navigation,230,F;bottom_navigation,230,F;bottom_navigation,230,F;bottom_navigation,231,F;recyclerViewChatList,232,F;seekBarAudio,224,F;seekBarAudio,225,F;switchGlobalSearch,233,F;textViewPostReactionCount,234,F;textViewMuteRemainingTime,235,F;textViewMuteRemainingTime,236,F;switchMatchesOnly,196,F;switchMatchesOnly,197,F;layoutSettings,237,F;btnViewVerifications,238,F;textInputLayoutGender,194,F;textViewChatLastMessage,239,F;textViewReplyPreview,240,F;textViewReplyPreview,235,F;btnDismiss,241,F;textViewRoomMembers,235,F;addMusicButton,242,F;imageLike,243,F;cardViewImagePreview,244,F;deleteStoryButton,245,F;action_block_user,246,F;etNotificationMessage,247,F;tvLastIP,248,F;italicCheckbox,249,F;storyEditorImageView,242,F;recyclerViewMentionSuggestions,235,F;publishingStatusText,242,F;frameLayoutJoinPrompt,195,F;textInputLayoutConfirmPassword,250,F;textInputLayoutConfirmPassword,250,F;radioPermanent,200,F;tvSubmittedAt,251,F;textViewUserXP,219,F;textViewUserXP,252,F;radio7days,253,F;imageViewVoiceNoteIcon,234,F;cardViewEditText,254,F;avatarIndicator,217,F;colorBlue,203,F;story_avatar_ring_seen,255,F;buttonRemoveVoiceNote,244,F;imageViewUserCardAvatar,256,F;musicCoverArt,245,F;fabCreatePost,257,F;btnTakeAction,241,F;imageViewResultUserAvatar,199,F;textViewEmail,223,F;textViewEmail,223,F;viewerAvatar,258,F;editTextComment,218,F;editTextComment,259,F;layoutLikes,243,F;viewPagerImages,260,F;buttonResetPassword,250,F;containerTrait,261,F;tabLayoutDots,220,F;tabLayoutDots,260,F;seekBarBlue,262,F;action_leave_room,263,F;buttonResultNewMatch,199,F;buttonCancel,264,F;buttonCancel,265,F;buttonCancel,266,F;buttonCancel,202,F;textViewTopicTitle,267,F;buttonClose,268,F;tvDocumentType,251,F;textViewResultUserName,199,F;editTextNotes,241,F;textViewTypingIndicator,254,F;buttonRevealYes,199,F;buttonRevealYes,193,F;inputLayoutFavGame,219,F;layoutChangeUsername,196,F;layoutChangeUsername,197,F;reactionThumbsDown,269,F;buttonBlockUser,219,F;storyViewsContainer,245,F;textViewChatTimestamp,239,F;textViewChatTimestamp,239,F;buttonSend,240,F;buttonSend,259,F;storyContainer,245,F;buttonFilter,270,F;btnApprove,251,F;editTextGifSearch,271,F;reactionLove,269,F;buttonContainer,194,F;editTextBio,219,F;textMusicTitle,242,F;ivSelfiePhoto,251,F;storyEditorFrame,242,F;textViewRulesTitle,272,F;imageViewJoinRoom,195,F;inputLayoutConfirmPassword,273,F;layoutAdministration,237,F;rbOther,202,F;switchOnlineOnly,233,F;textEmpty,274,F;inputFieldsContainer,194,F;tvActiveToday,238,F;viewUnreadIndicator,201,F;layoutAgeStatus,275,F;textInstructions,276,F;editDescription,221,F;layoutPermissionRequest,270,F;textViewUserLevel,219,F;textViewUserLevel,252,F;buttonImage,240,F;reactionsContainer,208,F;reactionsContainer,209,F;reactionsContainer,210,F;reactionsContainer,211,F;reactionsContainer,212,F;reactionsContainer,213,F;reactionsContainer,214,F;reactionsContainer,215,F;reactionsContainer,224,F;reactionsContainer,225,F;navigation_chats,222,F;spinnerGender,233,F;buttonPostReact,234,F;colorRed,203,F;textViewAnnouncement,226,F;textViewSearchTitle,266,F;textViewSearchTitle,266,F;textViewReplyLabel,208,F;textViewReplyLabel,209,F;textViewReplyLabel,210,F;textViewReplyLabel,211,F;textViewReplyLabel,212,F;textViewReplyLabel,213,F;textViewReplyLabel,224,F;textViewReplyLabel,225,F;buttonToggleMode,194,F;textViewRulesDescription,272,F;textViewVoiceNoteCurrentTime,234,F;imageViews,243,F;textViewTraitName,261,F;rightTapArea,245,F;rightTapArea,245,F;buttonPlayPreview,216,F;buttonPlayPreview,216,F;buttonPlayPreview,216,F;buttonPlayPreview,216,F;buttonPlayPreview,216,F;buttonPlayPreview,216,F;buttonPlayPreview,216,F;buttonLike,277,F;frameLayoutAvatar,275,F;textViewResultText,199,F;inputLayoutFavPlatform,219,F;textInputLayoutEmail,278,F;textInputLayoutEmail,278,F;textInputLayoutEmail,194,F;editorProgressBar,242,F;recyclerViewChatRooms,226,F;buttonReply,279,F;recyclerViewTraits,280,F;etSearch,281,F;searchLayout,281,F;searchLayout,281,F;searchLayout,281,F;searchLayout,281,F;buttonSearch,266,F;textViewRevealQuestion,199,F;textViewRevealQuestion,193,F;btnUnban,248,F;fabUpload,231,F;textViewBirthdateValue,219,F;imageComment,243,F;imageViewHeartIcon,282,F;layoutVoiceNotePlayer,234,F;textViewResultMessage,282,F;textViewUnreadCount,201,F;recyclerViewUserPosts,252,F;textViewReaction,283,F;reactionLike,269,F;reactionAngry,269,F;imageViewReadStatus,209,F;imageViewReadStatus,215,F;colorBrown,203,F;textViewTagline,284,F;textViewTagline,284,F;textViewTopic,240,F;addTextButton,242,F;textViewRoomDescription,235,F;textViewRoomDescription,201,F;colorOrange,203,F;layoutComments,243,F;textViews,285,F;textViews,243,F;scrollView,286,F;textViewCreateRoomTitle,198,F;textViewBuzzMessage,287,F;textViewBuzzMessage,288,F;textViewStatus,240,F;editTextGender,219,F;buttonEditTraits,219,F;fontSpinner,249,F;colorPreviewInDialog,203,F;colorPreviewInDialog,262,F;cardViewResultUser,199,F;layoutBuzzMessage,287,F;layoutBuzzMessage,288,F;stories_divider,257,F;tvIpHistory,248,F;cardInputMessage,235,F;progressBarRooms,226,F;tvReportDate,241,F;layoutCommunityGuidelines,237,F;coordinator_layout,219,F;onlineStatusIndicator,256,F;editTextMessage,240,F;emptyStateCard,229,F;buttonMic,240,F;buttonMic,235,F;banInfoLayout,248,F;imageDni,276,F;imageView,289,F;messageContentLayout,210,F;messageContentLayout,210,F;messageContentLayout,211,F;messageContentLayout,211,F;buttonCancelWaiting,199,F;buttonCancelWaiting,290,F;textUsername,243,F;viewCountText,245,F;recyclerViewVerifications,291,F;inputLayoutCurrentUsername,292,F;reactionLaugh,269,F;textViewDescriptionLabel,198,F;imageViewMentionAvatar,293,F;progressBarLoader,294,F;progressBarLoader,294,F;textViewLookingForValue,219,F;inputLayoutCurrentEmail,295,F;colorBlack,203,F;viewPagerChat,227,F;fabMyVideos,231,F;recyclerViewGifs,271,F;tilReportDetails,202,F;radioGroupMuteDuration,253,F;textViewLocation,275,F;buttonCloseGifPicker,271,F;ivDocumentPhoto,251,F;textViewWaitingTopic,199,F;textViewWaitingTopic,290,F;textViewWaitingTopic,290,F;textViewUserPostsHeader,252,F;textViewUsername,240,F;textViewUsername,219,F;textViewUsername,296,F;textViewUsername,217,F;textViewUsername,275,F;tvMatchesHeader,229,F;action_unmatch,297,F;recyclerViewMatches,229,F;btnViewUsers,238,F;inputLayoutSexuality,219,F;colorCyberpunkYellow,203,F;imageViewSwipeIndicator,239,F;containerRoomMessages,235,F;buttonSendComment,218,F;tvBannedUsers,238,F;app_bar_layout,219,F;textViewLastMessageTime,201,F;layoutYoutubeThumbnail,234,F;textViewUserNames,282,F;buttonPlayPauseVoiceNote,234,F;cardViewPostImageContainer,234,F;cardVoiceMessage,224,F;cardVoiceMessage,224,F;cardVoiceMessage,225,F;cardVoiceMessage,225,F;buttonLogin,194,F;buttonBlockedUsers,219,F;userAvatar,245,F;buttonSubmit,276,F;tvStatus,248,F;tvStatus,241,F;tvStatus,251,F;messageInputLayout,240,F;messageInputLayout,240,F;imageViewProfileAvatar,252,F;textViewReplyText,208,F;textViewReplyText,209,F;textViewReplyText,210,F;textViewReplyText,211,F;textViewReplyText,212,F;textViewReplyText,213,F;textViewReplyText,224,F;textViewReplyText,225,F;cardViewInputs,194,F;colorNeonBlue,203,F;buttonTakeDni,276,F;buttonDelete,264,F;buttonDelete,279,F;buttonDelete,285,F;recyclerViewRoomMessages,235,F;textAccuracyLevel,196,F;textAccuracyLevel,197,F;emojiRecyclerView,298,F;imageViewOtherUserAvatar,282,F;muteInfoLayout,248,F;buttonRequestReset,278,F;buttonRequestReset,278,F;layoutLogout,237,F;etButtonText,247,F;editTextMusicSearch,266,F;colorPink,203,F;textViewPronounsValue,219,F;buttonRefreshLocation,270,F;textViewArtistName,216,F;inputLayoutNotes,241,F;action_delete_chat,246,F;textViewChatUsername,239,F;textOverlayContainer,245,F;inputLayoutSchool,219,F;pickColorButton,249,F;editTextCurrentEmail,295,F;spinnerDocumentType,276,F;collapsing_toolbar_layout,219,F;textViewJoinTitle,195,F;textViewFavPlatformValue,219,F;viewPagerDiscover,230,F;textInputLayoutIconUrl,198,F;buttonEmoji,240,F;colorPurple,203,F;textViewOnlineStatus,219,F;textViewOnlineStatus,275,F;textViewOnlineStatus,256,F;rbSpam,202,F;reactionClap,269,F;buttonPlayPause,224,F;buttonPlayPause,225,F;progressBarMusicSearch,266,F;buttonAddStory,257,F;textMaxDistance,196,F;textMaxDistance,197,F;editTextLocation,219,F;imageViewUserProfile,208,F;imageViewUserProfile,208,F;imageViewUserProfile,209,F;imageViewUserProfile,209,F;imageViewUserProfile,210,F;imageViewUserProfile,210,F;imageViewUserProfile,211,F;imageViewUserProfile,211,F;imageViewUserProfile,212,F;imageViewUserProfile,212,F;imageViewUserProfile,212,F;imageViewUserProfile,213,F;imageViewUserProfile,213,F;imageViewUserProfile,213,F;imageViewUserProfile,224,F;imageViewUserProfile,224,F;imageViewUserProfile,225,F;imageViewUserProfile,225,F;inputLayoutBio,219,F;recyclerViewTopics,199,F;recyclerViewTopics,284,F;btnSearch,281,F;btnSearch,281,F;reactionHundred,269,F;layoutMatchResult,282,F;imageViewMessageStatus,206,F;imageViewMessageStatus,207,F;buttonApplyFilters,233,F;buttonAddImage,244,F;imageViewVerificationBadge,237,F;tvUsername,248,F;tvUsername,251,F;textViewLastMessage,201,F;textViewLastMessage,217,F;textViewUserName,237,F;editTextFavoriteGame,219,F;buttonAddVoice,244,F;tvDetails,241,F;appBarLayout,238,F;appBarLayout,238,F;appBarLayout,218,F;appBarLayout,281,F;appBarLayout,281,F;appBarLayout,291,F;appBarLayout,291,F;appBarLayout,291,F;appBarLayout,291,F;inputLayoutPassword,295,F;inputLayoutPassword,292,F;textViewVoiceNoteInfo,244,F;btnBan,248,F;layoutChangeEmail,196,F;layoutChangeEmail,197,F;textViewDistance,275,F;playerView,221,F;playerView,243,F;textViewMuteDescription,253,F;textViewEmptyMessage,270,F;menu_admin_panel,299,F;btnReject,251,F;buttonAddGif,244,F;cardDniPhoto,276,F;editTextEmail,278,F;editTextEmail,194,F;progressBar,300,F;progressBar,240,F;progressBar,280,F;progressBar,223,F;progressBar,278,F;progressBar,194,F;progressBar,229,F;progressBar,274,F;progressBar,250,F;progressBar,219,F;progressBar,301,F;progressBar,196,F;progressBar,197,F;progressBar,245,F;progressBar,221,F;progressBar,281,F;progressBar,276,F;progressBar,291,F;progressBar,284,F;progressBar,270,F;recyclerViewPersonalityTraits,219,F;menu_logout,302,F;buttonPostOptions,234,F;editTextUsername,194,F;rbInappropriateContent,202,F;viewTimeText,258,F;imageViewAvatar,240,F;imageViewAvatar,248,F;imageViewAvatar,296,F;imageViewAvatar,217,F;imageViewAvatar,287,F;imageViewAvatar,275,F;viewBuzzStatus,288,F;textViewSubtitle,223,F;textViewSubtitle,223,F;textViewSubtitle,278,F;textViewSubtitle,278,F;textViewSubtitle,194,F;textViewSubtitle,250,F;textViewSubtitle,250,F;textViewSubtitle,284,F;textViewSubtitle,284,F;textViewChatTopic,303,F;textViewChatTopic,254,F;reactionSparkles,269,F;descriptionTextView,289,F;descriptionTextView,289,F;recyclerViewPosts,257,F;linearLayoutButtons,277,F;linearLayoutButtons,277,F;action_use_gps,304,F;buttonCancelRoom,198,F;radio30d,200,F;toolbarCommunity,257,F;bottomLine2,194,F;bottomLine1,194,F;layoutChat,199,F;layoutChat,284,F;buttonRevealNo,199,F;buttonRevealNo,193,F;radio7d,200,F;viewOnlineStatus,275,F;etNotificationTitle,247,F;recyclerViewStories,257,F;editTextCurrentUsername,292,F;textViewTitle,199,F;textViewTitle,286,F;textViewTitle,280,F;textViewTitle,223,F;textViewTitle,223,F;textViewTitle,278,F;textViewTitle,278,F;textViewTitle,194,F;textViewTitle,250,F;textViewTitle,250,F;textViewTitle,284,F;textViewTitle,284,F;textViewTitle,270,F;layoutViews,243,F;editTextFavoritePlatform,219,F;layoutResult,199,F;layoutResult,284,F;inputLayoutNewPassword,273,F;textViewChatTitle,254,F;imageViewChatAvatar,239,F;closeButton,245,F;closeButton,305,F;imageViewCommentUserAvatar,306,F;textViewTimestamp,205,F;textViewTimestamp,207,F;imageThumbnail,285,F;layoutAccuracyLevel,196,F;layoutAccuracyLevel,197,F;scrollViewRules,272,F;imageViewRoomIcon,235,F;imageViewRoomIcon,201,F;action_use_ip,304,F;seekBarRed,262,F;buttonCancelMute,253,F;textViewCityCountryValue,219,F;textViewProfileUsername,252,F;emptyViewersText,305,F;textViewIconLabel,198,F;story_avatar,255,F;layout_voice_note_controls_create_post,244,F;navigation_swipe,222,F;reactionPray,269,F;toolbarDivider,218,F;inputLayoutNewEmail,295,F;topBar,245,F;textViewVoiceNoteDuration,234,F;spinnerDistance,233,F;textViewLocationStatus,270,F;buttonNewMatch,282,F;layoutReplyPreview,240,F;layoutReplyPreview,235,F;textViewJoinNote,195,F;colorYellow,203,F;textViewBuzzTime,287,F;textViewBuzzTime,288,F;textViewProfileInfo,252,F;reactionFire,269,F;reactionSad,269,F;textViewSender,204,F;textViewSender,205,F;textViewSender,207,F;buttonSetAsMain,260,F;action_mark_all_read,263,F;tvBanExpiration,248,F;editTextRoomDescription,198,F;editTextRoomDescription,265,F;editTextStoryText,249,F;textComments,243,F;editTextJob,219,F;editTextNewUsername,292,F;colorNeonPink,203,F;textViewAdminBadge,240,F;textViewAdminBadge,219,F;textViewAdminBadge,252,F;radio5min,253,F;buttonSelectSong,216,F;buttonSelectSong,216,F;reactionThinking,269,F;swipeRefreshLayout,270,F;colorGray,203,F;closeEditorButton,242,F;doneButton,242,F;buttonBack,278,F;imageSelfie,276,F;editTextRoomName,198,F;editTextRoomName,265,F;imageViewYoutubePlayButton,234,F;buttonCancelReveal,307,V400020037,**********,;"";layoutLocationStatus,270,F;layoutMyProfile,237,F;inputLayoutGender,219,F;fragment_container,230,F;cardViewJoin,195,F;gradientOverlay,256,F;layoutReveal,199,F;layoutReveal,284,F;progressBarUserLevel,219,F;progressBarUserLevel,252,F;textViewRevealInfo,193,F;photoView,268,F;textViewGifTitle,308,F;textViewRulesLabel,198,F;layoutHeader,270,F;youtubePlayerView,234,F;buttonAdminPanel,219,F;buttonEditSave,219,F;layoutMuted,235,F;story_time_ago,255,F;buttonCommentLike,306,F;action_room_info,263,F;buttonSkip,220,F;tvRole,248,F;tvReportedUser,241,F;buttonSave,280,F;buttonSave,265,F;contentLayout,208,F;contentLayout,208,F;contentLayout,209,F;contentLayout,209,F;textViewUserHandle,237,F;dropdownSexuality,194,F;viewPagerUsers,230,F;viewPagerUsers,277,F;imageViewUserAvatar,282,F;imageViewUserAvatar,237,F;recyclerViewMessages,240,F;imageViewProfile,309,F;imageViewPhoto,210,F;imageViewPhoto,211,F;progressIndicatorContainer,245,F;buttonViewProfile,275,F;editTextPronouns,219,F;textViewNoResults,271,F;textViewNoResults,266,F;menu_settings,299,F;menu_settings,302,F;imageViewMenuOptions,240,F;recyclerViewMusicResults,266,F;recyclerViewMusicResults,266,F;recyclerViewMusicResults,266,F;recyclerViewMusicResults,266,F;recyclerViewMusicResults,266,F;recyclerViewMusicResults,266,F;logoRing,194,F;deleteTextTrashCan,242,F;textInputLayoutDescription,198,F;buttonClearFilters,233,F;radio24h,200,F;tvActiveWeek,238,F;navigation_profile,222,F;buttonVerify,223,F;buttonVerify,223,F;buttonSendMessage,219,F;buttonSendMessage,252,F;buttonSendMessage,303,F;buttonSendMessage,254,F;musicPreviewLayout,242,F;videoView,245,F;tvReason,241,F;tvMuteExpiration,248,F;buttonPostComment,234,F;tvReporter,241,F;storyImage,245,F;layoutActions,243,F;btnHistory,248,F;dropdownGender,194,F;story_username,255,F;reactionParty,269,F;recyclerViewReports,301,F;inputLayoutJob,219,F;textViewWaitingTitle,199,F;textViewWaitingTitle,290,F;textViewWaitingTitle,290,F;buttonReportUser,219,F;layoutReply,208,F;layoutReply,209,F;layoutReply,210,F;layoutReply,211,F;layoutReply,212,F;layoutReply,213,F;layoutReply,224,F;layoutReply,225,F;textViewBioValue,219,F;buttonMenuMatch,217,F;textViewSexualityValue,219,F;tvTotalPosts,238,F;progressBarProfile,252,F;toolbarRoom,235,F;textViewUserCardNameAge,256,F;inputLayoutBirthdate,219,F;textViewWaitingInfo,290,F;textViewWaitingInfo,290,F;textViewNameLabel,198,F;btnMute,248,F;textViewJoinDescription,195,F;layoutTopicSelection,199,F;layoutTopicSelection,284,F;leftTapArea,245,F;leftTapArea,245,F;buttonRemoveImage,244,F;tvBanReason,248,F;textViewRevealTitle,199,F;textViewRevealTitle,193,F;textInputLayoutRules,198,F;tvNewUsers,238,F;layoutBottom,220,F;layoutBottom,220,F;btnSendNotification,238,F;btnSaveNotes,241,F;textMusicArtist,242,F;textViewCommentTimestamp,306,F;musicInfoContainer,245,F;colorGreen,203,F;progressBarReveal,193,F;colorWhite,203,F;imageViewPreview,244,F;textViewTimer,223,F;textViewTimer,223,F;editTextRoomMessage,235,F;textViewSchoolValue,219,F;imageViewTraitIcon,261,F;textDescription,243,F;bottomControlsLayout,242,F;textInputLayoutName,198,F;buttonLeaveChat,199,F;buttonLeaveChat,303,F;buttonLeaveChat,254,F;textViewForgotPassword,194,F;cardViewUserProfile,252,F;container,228,F;tvDeviceInfo,248,F;buttonLogout,219,F;editTextMinAge,233,F;buttonTakeSelfie,276,F;textViewInstructions,223,F;textViewInstructions,223,F;buttonPostSubmit,244,F;logoContainer,194,F;titleTextView,289,F;titleTextView,289,F;recyclerViewEmojis,310,F;recyclerViewChat,199,F;recyclerViewChat,303,F;recyclerViewChat,254,F;editModeDetailsLayout,219,F;buttonMuteUser,279,F;textViewJobValue,219,F;titleText,305,F;reactionHeart,269,F;menu_profile,299,F;buttonDislike,277,F;action_send_buzz,246,F;layoutSendMessage,235,F;switchReceiveMessages,196,F;switchReceiveMessages,197,F;tabLayoutCategories,310,F;tabLayoutCategories,311,F;textPreview,249,F;progressBarWaiting,199,F;progressBarWaiting,290,F;progressBarWaiting,290,F;layoutEmptyState,270,F;cardViewYoutubeContainer,234,F;topControlsLayout,242,F;textViewGenderValue,219,F;imageSlider,219,F;textViewPostUsername,218,F;textViewPostUsername,234,F;radio1hour,253,F;buttonRegister,194,F;editTextMaxAge,233,F;colorCyan,203,F;textViewAge,219,F;textViewAge,275,F;viewPagerEmojis,311,F;editTextRejectReason,312,F;textViewPostCommentCount,234,F;tvDeviceHistoryTitle,248,F;topLine2,194,F;buttonSelectVideo,221,F;textSelfieStatus,276,F;buttonResultContinue,199,F;topLine1,194,F;textViewBio,275,F;reactionSurprised,269,F;usernameText,245,F;progressBarChats,232,F;inputLayoutLocation,219,F;textViewEmptyRooms,226,F;editTextBirthdate,194,F;editTextBirthdate,219,F;buttonCancelReply,240,F;buttonCancelReply,235,F;boldCheckbox,249,F;buttonRequestPermission,270,F;seekBarVoiceNote,234,F;textCommentsCount,259,F;recyclerUsers,281,F;textViewDuration,224,F;textViewDuration,225,F;musicArtistText,245,F;editTextDetails,202,F;textViewCategoryTitle,313,F;recyclerViewBlockedUsers,300,F;etButtonUrl,247,F;progressBarCommunity,257,F;textViewRules,272,F;layoutCommunity,314,F;layoutCommentInput,218,F;textViewEmptyChats,232,F;viewMainIndicator,309,F;progressBarComments,218,F;textViewCommentLikes,306,F;layoutWaiting,199,F;layoutWaiting,284,F;textViewRoomName,235,F;textViewRoomName,264,F;textViewRoomName,201,F;textViewResultTitle,282,F;viewPagerVideos,231,F;editTextNewEmail,295,F;editTextBanDuration,200,F;textViewCommentContent,306,F;textViewPostTimestamp,218,F;textViewPostTimestamp,234,F;imageUserAvatar,243,F;tvTotalUsers,238,F;textInputLayoutPassword,250,F;textInputLayoutPassword,250,F;radioGroupBanDuration,200,F;textViewUserCardBio,256,F;layoutChangePassword,196,F;layoutChangePassword,197,F;recyclerViewUsers,270,F;navigation_matches,222,F;textViewToolbarTitle,218,F;editTextRoomRules,198,F;editTextRoomRules,265,F;layoutChatInput,199,F;layoutChatInput,303,F;iconClose,286,F;tvEmptyList,301,F;tvEmptyList,281,F;tvEmptyList,291,F;buttonUnblock,296,F;layoutVerification,237,F;toolbar,238,F;toolbar,300,F;toolbar,240,F;toolbar,227,F;toolbar,218,F;toolbar,286,F;toolbar,280,F;toolbar,229,F;toolbar,274,F;toolbar,219,F;toolbar,301,F;toolbar,196,F;toolbar,197,F;toolbar,230,F;toolbar,221,F;toolbar,281,F;toolbar,276,F;toolbar,291,F;buttonDeclineRules,272,F;rbHarassment,202,F;tabLayoutChat,227,F;musicTitleText,245,F;inputLayoutPronouns,219,F;etImageUrl,247,F;radio24hours,253,F;fabAddImage,219,F;fabAddImage,260,F;buttonCreateRoom,198,F;viewPagerTutorial,220,F;textViewCommentUsername,306,F;animationSpinner,249,F;textViewSongTitle,216,F;textViewSongTitle,216,F;buttonGif,240,F;textViewYoutubeTitle,234,F;inputLayoutCurrentPassword,273,F;cardViewAvatar,217,F;inputLayoutLookingFor,219,F;viewersRecyclerView,305,F;viewModeDetailsLayout,219,F;textBenefits,276,F;action_edit_room,315,F;editTextPostContent,244,F;textViewMentionUsername,293,F;textInputLayoutSexuality,194,F;cardViewOriginalPost,218,F;editDescriptionText,221,F;switchLocationSharing,196,F;switchLocationSharing,197,F;textViewPostContent,218,F;tvEmail,248,F;tvEmail,251,F;editTextChatMessage,199,F;editTextChatMessage,303,F;editTextChatMessage,254,F;checkBoxAcceptRules,272,F;fabChangeAvatar,219,F;recyclerView,274,F;textLikes,285,F;textLikes,243,F;progressBarTopics,199,F;textViewTopicSelectionTitle,199,F;recyclerViewComments,218,F;recyclerViewComments,259,F;buttonExpandSearch,270,F;editTextCurrentPassword,273,F;viewMessageStatus,211,F;viewMessageStatus,213,F;viewMessageStatus,224,F;textViewSenderName,212,F;textViewSenderName,213,F;textViewFavGameValue,219,F;textViewPostTextContent,234,F;cardViewChatInput,254,F;inputLayoutNewUsername,292,F;rbFakeProfile,202,F;textViewMessage,204,F;textViewMessage,205,F;textViewMessage,206,F;textViewMessage,206,F;textViewMessage,206,F;textViewMessage,207,F;textViewMessage,207,F;textViewMessage,207,F;textViewMessage,208,F;textViewMessage,209,F;textViewMessage,212,F;textViewMessage,213,F;textViewMessage,214,F;textViewMessage,215,F;radio15min,253,F;textEmoji,316,F;progressBarRoom,235,F;editTextSexuality,219,F;logoImage,194,F;searchContainer,266,F;searchContainer,266,F;tabLayoutDiscover,230,F;action_delete_post,317,F;editTextNewPassword,250,F;editTextNewPassword,273,F;storyTimestampText,245,F;textViewEmptyTitle,270,F;fabDeleteImage,309,F;layoutUserInfo,237,F;imageViewGif,308,F;imageViewGif,208,F;imageViewGif,209,F;imageViewGif,214,F;imageViewGif,215,F;layoutRecruitAndEarn,237,F;editTextSchool,219,F;buttonRemoveMusic,242,F;tilSearch,281,F;tilSearch,281,F;tilSearch,281,F;textViewContent,286,F;textViewVerifiedBadge,219,F;textViewVerifiedBadge,252,F;imageMusicCover,242,F;textDniStatus,276,F;seekBarGreen,262,F;story_avatar_ring_unseen,255,F;editTextDigit6,223,F;buttonConfirmMute,253,F;imageViewPostImage,234,F;cardSelfiePhoto,276,F;cardViewHeader,254,F;layoutVideos,314,F;deviceHistoryContainer,248,F;buttonAcceptRules,272,F;editTextLookingFor,219,F;textViewEmpty,300,F;textViewEmpty,229,F;textViewEmpty,284,F;textViewLoading,294,F;textViewLoading,294,F;buttonSendReport,202,F;textViewBioLabel,219,F;textViewLocationValue,219,F;btnUnmute,248,F;buttonContinueToChat,282,F;buttonContinueToChat,282,F;viewerUsername,258,F;cardViewMentionSuggestions,235,F;editTextConfirmPassword,250,F;editTextConfirmPassword,273,F;editTextPassword,194,F;editTextPassword,295,F;editTextPassword,292,F;menu_block_user,302,F;linearLayoutCode,223,F;linearLayoutCode,223,F;imageViewPostUserAvatar,218,F;imageViewPostUserAvatar,234,F;editTextRoomIconUrl,198,F;editTextRoomIconUrl,265,F;btnViewReports,238,F;imageViewYoutubeThumbnail,234,F;action_delete_room,315,F;selectedColorPreview,249,F;editTextDigit5,223,F;textInputLayoutBirthdate,194,F;editTextDigit4,223,F;editTextDigit3,223,F;textViewChatTimer,199,F;textViewChatTimer,303,F;textViewChatTimer,254,F;buttonRoomSend,235,F;editTextDigit2,223,F;editTextDigit1,223,F;viewIcon,245,F;progressBarGifs,271,F;textViewTopicDescription,267,F;recyclerViewCategoryTraits,313,F;+layout:activity_verification_management,291,F;dialog_comments_bottom_sheet,259,F;tutorial_page,289,F;activity_main_empty,318,F;item_blind_date_message_sent_modern,207,F;layout_blind_date_result,282,F;dialog_delete_room_confirmation,264,F;item_post,234,F;activity_full_screen_image,268,F;item_sent_gif_message,215,F;activity_room_loader,294,F;item_blind_date_message_sent,206,F;activity_blocked_users,300,F;activity_story_editor,242,F;layout_blind_date_chat_modern,254,F;item_story_viewer,258,F;dialog_improved_emoji_picker,311,F;item_mention_suggestion,293,F;layout_user_muted,236,F;activity_tutorial,220,F;dialog_gif_picker,271,F;dialog_create_room,198,F;activity_my_videos,274,F;layout_blind_date_chat,303,F;activity_settings_compat,197,F;activity_admin_panel,238,F;item_received_gif_message,214,F;item_verification,251,F;activity_login,194,F;layout_join_room_prompt,195,F;activity_community_guidelines,286,F;item_blind_date_message_received_modern,205,F;dialog_mute_user,253,F;item_blind_date_message_received,204,F;activity_story_viewer,245,F;fragment_chat_rooms,226,F;item_report,241,F;activity_verification,276,F;dialog_create_post,244,F;activity_matches,229,F;activity_chat_room,235,F;menu_community_dropdown,314,F;activity_user_management,281,F;item_chat_list,239,F;item_gif_message_sent,209,F;activity_community_host,228,F;activity_comments,218,F;activity_upload_video,221,F;activity_swipe,230,F;layout_profile_image_slider,260,F;item_voice_message_received,225,F;activity_password_reset,250,F;item_radar_user,275,F;item_story,255,F;item_my_video,285,F;activity_user_profile,252,F;fragment_blind_date,284,F;item_image_message_sent,211,F;item_gif_message_received,208,F;item_video,243,F;item_personality_trait_category,313,F;item_message_buzz_received,287,F;dialog_change_email,295,F;dialog_change_password,273,F;item_message_reaction,283,F;dropdown_item,319,F;activity_chat_list,227,F;dialog_music_search,266,F;dialog_color_palette_picker,203,F;item_user_card,256,F;profile_dropdown_menu,237,F;item_personality_trait,261,F;layout_reaction_selector,269,F;dialog_global_notification,247,F;fragment_radar,270,F;dialog_message_options,279,F;dialog_rules_acceptance,272,F;item_chat_room,201,F;item_gif,308,F;activity_profile,219,F;activity_video_feed,231,F;dialog_story_viewers,305,F;layout_blind_date_waiting,290,F;item_music_search,216,F;item_blind_date_topic,267,F;activity_chat,240,F;layout_blind_date_reveal,193,F;fragment_private_chats,232,F;dialog_radar_filters,233,F;dialog_add_text_with_style,249,F;dialog_custom_color_picker,262,F;item_emoji,316,F;item_message_sent,213,F;activity_edit_personality_traits,280,F;item_match,217,F;grid_emoji_category,298,F;item_admin_user,248,F;item_blocked_user,296,F;fragment_community,257,F;item_image_message_received,210,F;item_message_received,212,F;dialog_reject_verification,312,F;activity_blind_date,199,F;item_voice_message,224,F;activity_forgot_password,278,F;dialog_edit_room,265,F;item_comment,306,F;dialog_report_user,202,F;dialog_ban_user,200,F;activity_email_verification,223,F;dialog_change_username,292,F;fragment_profiles,277,F;dialog_emoji_picker,310,F;item_message_buzz_sent,288,F;item_profile_image,309,F;activity_reports,301,F;activity_settings,196,F;+menu:menu_post_options,317,F;profile_menu,302,F;room_chat_menu,263,F;chat_menu,246,F;menu_location_options,304,F;menu_match_item,297,F;bottom_nav_menu,222,F;menu_swipe,299,F;menu_room_admin,315,F;+mipmap:ic_launcher_round,320,F;ic_launcher_round,321,F;ic_launcher_round,322,F;ic_launcher_round,323,F;ic_launcher_round,324,F;ic_launcher_round,325,F;ic_launcher_foreground,326,F;ic_launcher_foreground,327,F;ic_launcher_foreground,328,F;ic_launcher_foreground,329,F;ic_launcher_foreground,330,F;ic_launcher,331,F;ic_launcher,332,F;ic_launcher,333,F;ic_launcher,334,F;ic_launcher,335,F;ic_launcher,336,F;+raw:welcome,337,F;+string:mark_as_read,11,V4002607e1,3a00260817,;"MARCAR COMO LEÍDO";gcm_defaultSenderId,338,V400020037,5100020084,;"988461270314";personality_traits_title,11,V400320a4a,4b00320a91,;"Rasgos de Personalidad";title_activity_main_menu_cyber_punk,11,V4002c08fd,51002c094a,;"MainMenuCyberPunk";avatar_update_success,11,V4001e0630,4e001e067a,;"Avatar actualizado con éxito";reply_label,11,V4002507a4,3c002507dc,;"Escribe tu respuesta";trait_icon_description,11,V400340ad8,4200340b16,;"Icono del rasgo";project_id,338,V40007023d,4700070280,;"vmeet-5c6a2";title_activity_main_menu,11,V4002b08b7,45002b08f8,;"MainMenuActivity";reply,11,V400240778,2b0024079f,;"RESPONDER";profile_save_success,11,V400210703,4a00210749,;"Perfil guardado con éxito";title_chats,11,V40007014e,2d00070177,;"Chats";about_me_label,11,V4000e02e6,38000e031a,;"Acerca de mí\:";favorite_games_label,11,V4000c0265,42000c02a3,;"Juegos favoritos\:";error_loading_traits,11,V400390c5d,4700390ca0,;"Error al cargar rasgos";title_swipe,11,V4000400b9,31000400e6,;"Descubrir";error_saving_traits,11,V400380c15,4700380c58,;"Error al guardar rasgos";title_profile,11,V40006011d,3000060149,;"Perfil";sample_admob_app_id,11,V4000f031f,56000f0371,;"ca-app-pub-3940256099942544~3347511713";title_activity_swipe,11,V40003007a,3e000300b4,;"SwipeActivity";email_label,11,V4000a01ff,2e000a0229,;"Email\:";title_community,11,V400290880,35002908b1,;"Comunidad";google_storage_bucket,338,V4000601d6,6600060238,;"vmeet-5c6a2.firebasestorage.app";hello_blank_fragment,11,V40008017c,44000801bc,;"Fragmento en blanco";interests_label,11,V4000b022e,36000b0260,;"Intereses\:";next,11,V4001504b9,2a001504df,;"Siguiente";permission_denied_gallery,11,V4001d05cf,60001d062b,;"Permiso denegado para acceder a la galería";profile_button_save,11,V4001c0597,37001c05ca,;"GUARDAR";google_app_id,338,V4000400f1,6c00040159,;"1\:988461270314\:android\:d0242913a2e29332704a53";google_api_key,338,V400030089,67000300ec,;"AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg";second_fragment_label,11,V400140475,43001404b4,;"Segundo Fragmento";edit_profile_button,11,V4000d02a8,3d000d02e1,;"Editar Perfil";traits_saved_successfully,11,V400370bc1,5300370c10,;"Rasgos guardados exitosamente";title_matches,11,V4000500eb,3100050118,;"Matches";personality_traits_subtitle,11,V400350b1b,6e00350b85,;"Estos rasgos ayudan a otros usuarios a conocerte mejor";edit_personality_traits,11,V400330a96,4100330ad3,;"Editar Rasgos";avatar_update_error,11,V4001f067f,4d001f06c8,;"Error al actualizar el avatar";username_label,11,V4000901c1,3d000901fa,;"Nombre de usuario\:";previous,11,V4001604e4,2d0016050d,;"Anterior";error_loading_gifs,11,V40027081c,620027087a,;"Error cargando GIFs. Por favor\, inténtalo de nuevo.";default_notification_channel_id,11,V4002d094f,48002d0993,;"VMeetChannel";google_crash_reporting_api_key,338,V40005015e,77000501d1,;"AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg";title_activity_profile,11,V4001103b9,42001103f7,;"ProfileActivity";profile_button_edit,11,V4001b0560,36001b0592,;"EDITAR";save_traits,11,V400360b8a,3600360bbc,;"Guardar Rasgos";app_name,11,V400010010,2a00010036,;"VMeet";first_fragment_label,11,V400130433,4100130470,;"Primer Fragmento";title_activity_matches,11,V400100376,42001003b4,;"MatchesActivity";create_new_post,11,V4002e0998,43002e09d7,;"Crear nueva publicación";title_activity_comments,11,V4002f09dc,44002f0a1c,;"CommentsActivity";logged_out,11,V4002006cd,35002006fe,;"Sesión cerrada";title_activity_login,11,V40002003b,3e00020075,;"LoginActivity";+style:MenuTextStyle,339,V4001303d6,c00160476,;Nandroid\:textColor:#FFFFFF,android\:fontFamily:@font/app_font,;ShapeAppearance.App.RoundedGif,339,V400580ff0,c005b1093,;EcornerFamily:rounded,cornerSize:8dp,;CustomSwitchStyle,340,V40017051c,c001c0676,;DTheme.AppCompat,colorAccent:@color/neon_blue,colorControlActivated:@color/neon_blue,colorSwitchThumbNormal:@color/text_secondary,android\:colorForeground:@color/text_secondary,;TextAppearance.VMeet.Button,341,V40027050b,c002b05e0,;Nandroid\:textSize:14sp,android\:textStyle:bold,android\:textAllCaps:true,;VMeetToolbar,339,V4006912e6,c00751601,;DWidget.MaterialComponents.Toolbar,android\:layout_width:0dp,android\:layout_height:wrap_content,android\:background:#1A1A3A,android\:elevation:4dp,android\:paddingTop:0dp,android\:minHeight:?attr/actionBarSize,android\:fontFamily:@font/app_font,titleTextColor:@color/comfy_blue,titleTextAppearance:@style/TextAppearance.VMeet.Headline2,fontFamily:@font/app_font,;Theme.VMeet.NoActionBar,339,V4005e10d5,c00641245,;DTheme.VMeet,windowActionBar:false,windowNoTitle:true,android\:windowFullscreen:false,android\:windowTranslucentStatus:true,android\:windowTranslucentNavigation:true,;Theme.VMeet.Dialog.FullWidth,340,V400030055,c000901f8,;DTheme.AppCompat.Dialog,android\:windowMinWidthMajor:90%,android\:windowMinWidthMinor:90%,android\:windowBackground:@android\:color/transparent,android\:windowIsFloating:true,android\:windowCloseOnTouchOutside:true,;Theme.VMeet,339,V40003003b,c001103ce,;DTheme.Material3.DayNight.NoActionBar,android\:itemTextAppearance:@style/MenuTextStyle,android\:fontFamily:@font/app_font,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:statusBarColor:@color/darker_blue,android\:navigationBarColor:@android\:color/transparent,android\:windowLightStatusBar:false,android\:windowLayoutInDisplayCutoutMode:shortEdges,popupMenuStyle:@style/PopupMenu,fontFamily:@font/app_font,;VMeetTextStyle.Title,339,V4001e055a,c002105f2,;Nandroid\:textSize:20sp,android\:textStyle:bold,;Theme.VMeet.Dialog.Alert,340,V10000901fc,c0010042e,;DTheme.AppCompat.Light.Dialog.Alert,android\:background:@color/darker_blue,android\:textColorPrimary:@android\:color/white,android\:textColorSecondary:@color/neon_blue,android\:buttonBarPositiveButtonStyle:@style/AlertDialogButtonStyle,android\:buttonBarNegativeButtonStyle:@style/AlertDialogButtonStyle,colorAccent:@color/neon_pink,;VMeetTextStyle.Body,339,V400270669,c002906cc,;Nandroid\:textSize:14sp,;TextAppearance.VMeet.Body1,341,V40019036e,c001b03d8,;Nandroid\:textSize:16sp,;TextAppearance.VMeet.Body2,341,V4001d03e0,c001f044a,;Nandroid\:textSize:14sp,;TextAppearance.VMeet.Caption,341,V400220477,c002404e3,;Nandroid\:textSize:12sp,;TextAppearance.VMeet.Headline1,341,V400090151,c000c01f3,;Nandroid\:textSize:24sp,android\:textStyle:bold,;CustomAltButton,339,V400470cb2,c00510f39,;DWidget.AppCompat.Button.Borderless,android\:background:@drawable/modern_button_alt,android\:textColor:@color/white,android\:textSize:16sp,android\:textStyle:bold,android\:textAllCaps:true,android\:stateListAnimator:@null,android\:fontFamily:@font/app_font,android\:textAppearance:@style/TextAppearance.VMeet.Button,fontFamily:@font/app_font,;CustomBottomNavigation,339,V4002f076a,c003809e2,;DWidget.Material3.BottomNavigationView,android\:background:#1A1A3A,android\:fontFamily:@font/app_font,itemIconTint:@color/comfy_blue,itemTextColor:@color/comfy_blue,labelVisibilityMode:labeled,fontFamily:@font/app_font,itemTextAppearanceActive:@style/TextAppearance.VMeet.Caption,itemTextAppearanceInactive:@style/TextAppearance.VMeet.Caption,;ShapeAppearance.App.RoundedButton,339,V400530f41,c00560fe8,;EcornerFamily:rounded,cornerSize:12dp,;VMeetTextStyle,339,V4001904b1,c001c0552,;Nandroid\:fontFamily:@font/app_font,fontFamily:@font/app_font,;AlertDialogButtonStyle,340,V400120436,c001404e0,;DWidget.AppCompat.Button.ButtonBar.AlertDialog,android\:textColor:@color/neon_pink,;TextAppearance.VMeet,341,V400030063,c0006012c,;DTextAppearance.AppCompat,android\:fontFamily:@font/app_font,fontFamily:@font/app_font,;TextAppearance.VMeet.Headline3,341,V4001302a5,c00160347,;Nandroid\:textSize:18sp,android\:textStyle:bold,;TextAppearance.VMeet.Headline2,341,V4000e01fb,c0011029d,;Nandroid\:textSize:20sp,android\:textStyle:bold,;VMeetTextStyle.Subtitle,339,V4002305fa,c00250661,;Nandroid\:textSize:16sp,;CustomPurpleButton,339,V4003b0a24,c00450caa,;DWidget.AppCompat.Button.Borderless,android\:background:@drawable/modern_button,android\:textColor:@color/white,android\:textSize:16sp,android\:textStyle:bold,android\:textAllCaps:true,android\:stateListAnimator:@null,android\:fontFamily:@font/app_font,android\:textAppearance:@style/TextAppearance.VMeet.Button,fontFamily:@font/app_font,;PopupMenu,339,V4002b06d4,c002d0762,;D@style/Widget.Material3.PopupMenu,android\:popupBackground:#212121,;+styleable:PurpleButton,12,V400020039,18000400aa,;-isAlt:boolean:;PixelPerfectTextView,12,V4000600b2,18000a0176,;-text::-textSize::-textColor::;+xml:file_paths,342,F;network_security_config,343,F;data_extraction_rules,344,F;backup_rules,345,F;