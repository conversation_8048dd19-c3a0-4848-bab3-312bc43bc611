http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/rotate.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_earthquake.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_flash.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_intense_shake.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/text_slide_in_left.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/buzz_shake.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/pulse.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/slide_up.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/text_fade_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/text_rotate.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/attrs.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-v29/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_vibration.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/cyberpunk_circle_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_container_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/voice_note_player_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_alt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/message_received_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/radar_header_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/swipe_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/story_ring_unseen_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_unselected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/background_reply_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_red.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_read.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/unread_counter_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/story_ring_seen.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_button_outline_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_mic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/youtube_play_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_button_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circular_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/recording_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_dot_selected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_admin_crown.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/section_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_eye.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_permission.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_2.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/story_ring_unseen.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_1.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_send.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_3.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_square_shape.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_send_comment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_pause.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_matches.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_dialog_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_community.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more_vert.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_circle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_pending.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circular_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_music_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_gradient_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/distance_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/trait_selected_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_outlined.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_refresh.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_mark_read.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_reply.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/image_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/background_reply_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_radar_empty.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_background_own.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/cyberpunk_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/glowing_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_default_avatar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_button_alt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification_vmeet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable-v26/ic_notification_vmeet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_heart.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_green.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reaction_selector_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_delete.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/message_input_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chats.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_rounded_accent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_dot.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/online_status_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_corner_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_buzz_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/trait_unselected_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/post_edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_preview_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_admin.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play_arrow.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_emoji.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/unread_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_filter.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close_circle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_topic_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/online_status_indicator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_selected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/default_room_icon.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_gif.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_audiotrack.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/login_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_outline_cyberpunk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chat_bubble.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/progress_bar_cyberpunk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_comment_outline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/glowing_neon_ring.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/mic_button_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/default_avatar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_buzz_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/avatar_circle_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/selected_color_border.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_mute.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_gif.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient_2.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient_1.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_sheet_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/color_swatch_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_swipe.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/admin_name_gradient_3.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_stop_mic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/gradient_bottom_overlay.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/recording_cancel_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/preview_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/reply_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/image_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/neon_button_blue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_cyberpunk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_online_status.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_like_outline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add_post.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_dark_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_edittext_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/buzz_pulse_animation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/indicator_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_play.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_placeholder_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/comment_input_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/message_sent_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/rounded_edittext_bg.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modern_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/tab_dot_default.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/knightwarriorw16n8.otf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/combackhomeregularjemd9.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/interitalic.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/orbitron.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/inter.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/supremespikekvo8d.otf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/app_font.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/greatsracingfreeforpersonalitalicbll.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/steelarj9vnj.otf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/veniteadoremusstraightyzo6v.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/wowdinog33vp.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/veniteadoremusrgrba.ttf,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_reveal.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_login.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_join_room_prompt.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_settings_compat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_create_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_blind_date.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_ban_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_report_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_color_palette_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_received_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_message_sent_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_gif_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_gif_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_image_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_image_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_received_gif_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_sent_gif_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_music_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_match.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_comments.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_tutorial.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_upload_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_email_verification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_voice_message.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_voice_message_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_chat_rooms.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_community_host.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_matches.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_swipe.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_video_feed.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_private_chats.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_radar_filters.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_post.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_user_muted.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/profile_dropdown_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_chat_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_report.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_story_editor.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_create_post.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_story_viewer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/chat_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_global_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_admin_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_text_with_style.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_mute_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_chat_modern.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_story.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_user_card.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_community.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_story_viewer.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_comments_bottom_sheet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_profile_image_slider.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_personality_trait.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_custom_color_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/room_chat_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_delete_room_confirmation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_edit_room.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_music_search.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blind_date_topic.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_full_screen_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_reaction_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_radar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_gif_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_rules_acceptance.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_password.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_my_videos.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_admin_panel.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_radar_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profiles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_forgot_password.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_message_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_edit_personality_traits.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_user_management.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_result.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_reaction.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/fragment_blind_date.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_my_video.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_community_guidelines.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_buzz_received.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_message_buzz_sent.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/tutorial_page.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_waiting.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_username.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_mention_suggestion.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_room_loader.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_change_email.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_blocked_user.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_match_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/grid_emoji_category.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_swipe.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_blocked_users.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_reports.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/profile_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/layout_blind_date_chat.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_location_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_story_viewers.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_comment.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/ids.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_gif.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_profile_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_emoji_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_improved_emoji_picker.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_personality_trait_category.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/menu_community_dropdown.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_room_admin.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_emoji.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/menu_post_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main_empty.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dropdown_item.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_foreground.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/raw/welcome.mp3,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*buildDir}/generated/res/processReleaseGoogleServices/values/values.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/text_styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:rotate,0,F;buzz_earthquake,1,F;buzz_flash,2,F;buzz_intense_shake,3,F;text_slide_in_left,4,F;buzz_shake,5,F;pulse,6,F;fade_in,7,F;slide_up,8,F;text_fade_in,9,F;text_rotate,10,F;+attr:isAlt,11,V80003006a,2e00030090,;boolean:;android\:text,11,V8000700eb,**********,;:;android\:textSize,11,V800080111,**********,;:;android\:textColor,11,V80009013b,290009015c,;:;+color:colorPrimaryDark,12,V4000b01dc,34000b020c,;"#FF3700B3";cyberpunk_accent_voice_thumb,12,V400350a6a,3e00350aa4,;"#A4FFC9";cyberpunk_card_background,12,V4002a079c,3b002a07d3,;"#162447";dark_red,12,V4001403b2,2a001403d8,;"#C62828";buzz_text_color,12,V4003f0c5a,31003f0c87,;"#FFFFFF";text_primary,12,V4004a0e61,2e004a0e8b,;"#E0E0E0";cyberpunk_divider,12,V400300941,**********,;"#E0E0E0";neon_pink,12,V4000d026b,2b000d0292,;"#FF00E4";cyberpunk_purple,12,V4003a0b93,32003a0bc1,;"#B39DDB";emoji_picker_tab_selected,12,V40023064b,3b00230682,;"#9C27B0";cyberpunk_shadow,12,V4003209ad,32003209db,;"#0A0A15";dark_blue,12,V4001002f5,2b0010031c,;"#0B0B2B";read_message_bg,12,V4001a04ce,31001a04fb,;"#E0E0E0";neon_blue,12,V38000b0210,5f000b0237,;"#4DB6E6";white,12,V400080150,2900080175,;"#FFFFFFFF";teal_700,12,V4000600f7,2c0006011f,;"#FF018786";purple_700,12,V400040099,2e000400c3,;"#FF3700B3";emoji_picker_tab_indicator,12,V400240688,3c002406c0,;"#9C27B0";cyberpunk_text_secondary,12,V4002d0862,3a002d0898,;"#A0A0A0";buzz_received_primary,12,V400430d2d,3700430d60,;"#9C27B0";purple_500,12,V400030069,2e00030093,;"#FF6200EE";neon_blue_transparent,12,V400490e26,3900490e5b,;"#224DB6E6";buzz_received_secondary,12,V400440d66,3900440d9b,;"#673AB7";colorPrimary,12,V4000a01aa,30000a01d6,;"#FF6200EE";cyberpunk_accent,12,V400380b2c,3200380b5a,;"#FFEB3B";mention_highlight_color,12,V4003c0bfb,39003c0c30,;"#3F51B5";neon_purple,12,V4000e0298,2d000e02c1,;"#9D00FF";cyberpunk_accent_voice,12,V400340a1c,3800340a50,;"#00FF66";card_background,12,V40013037f,31001303ac,;"#101033";unread_message_bg,12,V4001b0501,33001b0530,;"#C2C2FF";neon_blue_dark,12,V4001f05bc,30001f05e8,;"#2E86AB";cyberpunk_accent_primary,12,V4002e089e,3a002e08d4,;"#FF00E4";comfy_blue,12,V4000c023d,2c000c0265,;"#5DADE2";background,12,V400120351,2c00120379,;"#0A0A25";cyberpunk_green,12,V400390b60,3100390b8d,;"#00FFAB";gray_dark,12,V400510f88,2b00510faf,;"#2A2A2A";buzz_sent_primary,12,V400410cc1,3300410cf0,;"#FF6B35";purple_200,12,V400020039,2e00020063,;"#FFBB86FC";neon_green,12,V4000f02c7,2c000f02ef,;"#00FF66";emoji_picker_bg,12,V400220618,3100220645,;"#333333";cyberpunk_card_stroke,12,V4002b07d9,37002b080c,;"#4DB6E6";emoji_picker_title_text,12,V4002606ff,3900260734,;"#FFFFFF";cyberpunk_highlight,12,V400310976,35003109a7,;"#3A3A5D";teal_200,12,V4000500c9,2c000500f1,;"#FF03DAC5";colorAccent,12,V40009017b,2d000901a4,;"#FF00E4";dark_purple,12,V4001e058d,2d001e05b6,;"#2A2A60";emoji_picker_tab_text,12,V4002506c6,37002506f9,;"#CCCCCC";gray_light,12,V400500f5a,2c00500f82,;"#A0A0A0";cyberpunk_orange,12,V4003b0bc7,32003b0bf5,;"#FFAB40";dark_background,12,V4004c0ec3,31004c0ef0,;"#0A0A25";login_button_color,12,V400170436,3400170466,;"#BB86FC";cyberpunk_background,12,V400290764,3600290796,;"#1A1A2E";status_delivered,12,V4001c0536,32001c0564,;"#888888";cyberpunk_accent_secondary,12,V4002f08ee,3c002f0926,;"#00FF66";buzz_sent_secondary,12,V400420cf6,3500420d27,;"#F7931E";comfy_red,12,V4001503de,2b00150405,;"#E74C3C";cyberpunk_text,12,V400370afa,3000370b26,;"#E0E0E0";button_background,12,V4004d0ef6,33004d0f25,;"#162447";cyberpunk_text_primary,12,V4002c0828,38002c085c,;"#E0E0E0";black,12,V400070125,290007014a,;"#FF000000";offline_gray,12,V400480df6,2e00480e20,;"#888888";register_button_color,12,V40018046c,370018049f,;"#A066F0";darker_blue,12,V400110322,2d0011034b,;"#050518";cyberpunk_yellow,12,V400360ac6,3200360af4,;"#FFFF00";online_green,12,V400470dc6,2e00470df0,;"#00FF66";text_secondary,12,V4004b0e91,30004b0ebd,;"#A0A0A0";buzz_text_shadow,12,V400400c8d,3200400cbb,;"#000000";+dimen:status_bar_height,13,V4000600af,30000600db,;"24dp";status_bar_height,14,V400030071,4d000300ba,;"@*android\:dimen/status_bar_height";story_item_spacing,13,V40003005a,3000030086,;"8dp";+drawable:ic_vibration,15,F;status_error,16,F;cyberpunk_circle_button,17,F;reaction_container_background,18,F;voice_note_player_background,19,F;neon_button_alt,20,F;message_received_background,21,F;radar_header_gradient,22,F;swipe_background,23,F;story_ring_unseen_gradient,24,F;ic_profile_placeholder,25,F;tab_unselected,26,F;background_reply_sent,27,F;ic_add_image,28,F;neon_button_red,29,F;status_read,30,F;unread_counter_background,31,F;status_sent,32,F;admin_name_background,33,F;search_background,34,F;story_ring_seen,35,F;ic_notification,36,F;rounded_button_outline_background,37,F;circle_background,38,F;ic_profile,39,F;ic_add_mic,40,F;youtube_play_button_background,41,F;ic_launcher_background,42,F;circle_button_bg,43,F;circular_button,44,F;reaction_background,45,F;recording_button_background,46,F;tab_dot_selected,47,F;ic_admin_crown,48,F;section_background,49,F;ic_eye,50,F;ic_location_permission,51,F;admin_gradient_2,52,F;story_ring_unseen,53,F;admin_gradient_1,54,F;ic_send,55,F;admin_gradient_3,56,F;ic_arrow_forward,57,F;modern_edit_text_background,58,F;color_square_shape,59,F;neon_button,60,F;ic_send_comment,61,F;ic_pause,62,F;ic_matches,63,F;rounded_dialog_background,64,F;bg_message_received,65,F;ic_community,66,F;ic_more_vert,67,F;neon_circle,68,F;status_pending,69,F;circular_button_background,70,F;ic_music_placeholder,71,F;admin_gradient_animation,72,F;gradient_message_sent,73,F;distance_badge_background,74,F;trait_selected_background,75,F;ic_check_outlined,76,F;ic_refresh,77,F;ic_mark_read,78,F;ic_reply,79,F;image_error,80,F;background_reply_received,81,F;ic_radar_empty,82,F;reaction_background_own,83,F;cyberpunk_gradient,84,F;glowing_border,85,F;ic_default_avatar,86,F;modern_button_alt,87,F;ic_notification_vmeet,88,F;ic_notification_vmeet,89,F;ic_check,90,F;gradient_message_received,91,F;gradient_overlay,92,F;ic_heart,93,F;neon_button_green,94,F;reaction_selector_background,95,F;badge_user,96,F;ic_delete,97,F;message_input_background,98,F;ic_arrow_back,99,F;admin_name_gradient,100,F;spinner_background,101,F;ic_chats,102,F;button_rounded_accent,103,F;circle_dot,104,F;online_status_background,105,F;rounded_corner_background,106,F;bg_buzz_message_received,107,F;ic_chat,108,F;rounded_button_background,109,F;trait_unselected_background,110,F;post_edit_text_background,111,F;color_preview_background,112,F;badge_admin,113,F;ic_play_arrow,114,F;ic_emoji,115,F;unread_indicator,116,F;ic_filter,117,F;ic_close_circle,118,F;ic_topic_placeholder,119,F;online_status_indicator,120,F;tab_selected,121,F;default_room_icon,122,F;ic_settings,123,F;ic_add_gif,124,F;ic_audiotrack,125,F;login_background,126,F;button_outline_cyberpunk,127,F;ic_chat_bubble,128,F;progress_bar_cyberpunk,129,F;ic_comment_outline,130,F;bg_message_sent,131,F;glowing_neon_ring,132,F;mic_button_selector,133,F;default_avatar,134,F;bg_buzz_message_sent,135,F;avatar_circle_border,136,F;selected_color_border,137,F;ic_location,138,F;ic_mute,139,F;ic_gif,140,F;admin_name_gradient_2,141,F;admin_name_gradient_1,142,F;bottom_sheet_background,143,F;color_swatch_background,144,F;ic_swipe,145,F;admin_name_gradient_3,146,F;ic_stop_mic,147,F;gradient_bottom_overlay,148,F;recording_cancel_background,149,F;preview_background,150,F;reply_background,151,F;image_placeholder,152,F;neon_button_blue,153,F;tab_selector,154,F;button_cyberpunk,155,F;circle_online_status,156,F;ic_like_outline,157,F;ic_add_post,158,F;rounded_dark_background,159,F;rounded_edittext_background,160,F;buzz_pulse_animation,161,F;indicator_background,162,F;ic_launcher_foreground,163,F;ic_play,164,F;ic_placeholder_image,165,F;comment_input_background,166,F;message_sent_background,167,F;rounded_edittext_bg,168,F;ic_search,169,F;modern_button,170,F;tab_dot_default,171,F;+font:knightwarriorw16n8,172,F;combackhomeregularjemd9,173,F;interitalic,174,F;orbitron,175,F;inter,176,F;supremespikekvo8d,177,F;app_font,178,F;greatsracingfreeforpersonalitalicbll,179,F;steelarj9vnj,180,F;veniteadoremusstraightyzo6v,181,F;wowdinog33vp,182,F;veniteadoremusrgrba,183,F;+id:textViewWaitingForOther,184,F;checkboxPrivacyPolicy,185,F;buttonJoinRoom,186,F;layoutMaxDistance,187,F;layoutMaxDistance,188,F;switchCustomRules,189,F;buttonSendChat,190,F;editTextBanReason,191,F;textViewMemberCount,192,F;radioGroupReportReason,193,F;colorNeonGreen,194,F;cardViewMessage,195,F;cardViewMessage,196,F;cardViewMessage,197,F;cardViewMessage,198,F;cardViewMessage,199,F;cardViewMessage,200,F;cardViewMessage,201,F;cardViewMessage,202,F;cardViewMessage,203,F;cardViewMessage,203,F;cardViewMessage,203,F;cardViewMessage,204,F;cardViewMessage,204,F;cardViewMessage,204,F;cardViewMessage,205,F;cardViewMessage,206,F;imageViewCoverArt,207,F;imageViewCoverArt,207,F;imageViewCoverArt,207,F;textViewTime,199,F;textViewTime,200,F;textViewTime,201,F;textViewTime,202,F;textViewTime,208,F;textViewTime,203,F;textViewTime,204,F;textViewTime,205,F;textViewTime,206,F;nestedScrollView,209,F;nestedScrollView,210,F;buttonNext,211,F;buttonUpload,212,F;navigation_community,213,F;buttonResend,214,F;textViewVoiceTime,215,F;textViewVoiceTime,216,F;fabCreateRoom,217,F;progressBarPreview,207,F;bottom_navigation,218,F;bottom_navigation,219,F;bottom_navigation,220,F;bottom_navigation,220,F;bottom_navigation,220,F;bottom_navigation,210,F;bottom_navigation,210,F;bottom_navigation,187,F;bottom_navigation,188,F;bottom_navigation,221,F;bottom_navigation,221,F;bottom_navigation,221,F;bottom_navigation,221,F;bottom_navigation,222,F;recyclerViewChatList,223,F;seekBarAudio,215,F;seekBarAudio,216,F;switchGlobalSearch,224,F;textViewPostReactionCount,225,F;textViewMuteRemainingTime,226,F;textViewMuteRemainingTime,227,F;switchMatchesOnly,187,F;switchMatchesOnly,188,F;layoutSettings,228,F;textInputLayoutGender,185,F;textViewChatLastMessage,229,F;textViewReplyPreview,230,F;textViewReplyPreview,226,F;btnDismiss,231,F;textViewRoomMembers,226,F;addMusicButton,232,F;imageLike,233,F;cardViewImagePreview,234,F;deleteStoryButton,235,F;action_block_user,236,F;etNotificationMessage,237,F;tvLastIP,238,F;italicCheckbox,239,F;storyEditorImageView,232,F;recyclerViewMentionSuggestions,226,F;publishingStatusText,232,F;frameLayoutJoinPrompt,186,F;textInputLayoutConfirmPassword,240,F;textInputLayoutConfirmPassword,240,F;radioPermanent,191,F;textViewUserXP,210,F;textViewUserXP,241,F;radio7days,242,F;imageViewVoiceNoteIcon,225,F;cardViewEditText,243,F;avatarIndicator,208,F;colorBlue,194,F;story_avatar_ring_seen,244,F;buttonRemoveVoiceNote,234,F;imageViewUserCardAvatar,245,F;musicCoverArt,235,F;fabCreatePost,246,F;btnTakeAction,231,F;imageViewResultUserAvatar,190,F;textViewEmail,214,F;textViewEmail,214,F;viewerAvatar,247,F;editTextComment,209,F;editTextComment,248,F;layoutLikes,233,F;viewPagerImages,249,F;buttonResetPassword,240,F;containerTrait,250,F;tabLayoutDots,211,F;tabLayoutDots,249,F;seekBarBlue,251,F;action_leave_room,252,F;buttonResultNewMatch,190,F;buttonCancel,253,F;buttonCancel,254,F;buttonCancel,255,F;buttonCancel,193,F;textViewTopicTitle,256,F;buttonClose,257,F;textViewResultUserName,190,F;editTextNotes,231,F;textViewTypingIndicator,243,F;buttonRevealYes,190,F;buttonRevealYes,184,F;inputLayoutFavGame,210,F;layoutChangeUsername,187,F;layoutChangeUsername,188,F;reactionThumbsDown,258,F;buttonBlockUser,210,F;storyViewsContainer,235,F;textViewChatTimestamp,229,F;textViewChatTimestamp,229,F;buttonSend,230,F;buttonSend,248,F;storyContainer,235,F;buttonFilter,259,F;editTextGifSearch,260,F;reactionLove,258,F;buttonContainer,185,F;editTextBio,210,F;textMusicTitle,232,F;storyEditorFrame,232,F;textViewRulesTitle,261,F;imageViewJoinRoom,186,F;inputLayoutConfirmPassword,262,F;layoutAdministration,228,F;rbOther,193,F;switchOnlineOnly,224,F;textEmpty,263,F;inputFieldsContainer,185,F;tvActiveToday,264,F;viewUnreadIndicator,192,F;layoutAgeStatus,265,F;editDescription,212,F;layoutPermissionRequest,259,F;textViewUserLevel,210,F;textViewUserLevel,241,F;buttonImage,230,F;reactionsContainer,199,F;reactionsContainer,200,F;reactionsContainer,201,F;reactionsContainer,202,F;reactionsContainer,203,F;reactionsContainer,204,F;reactionsContainer,205,F;reactionsContainer,206,F;reactionsContainer,215,F;reactionsContainer,216,F;navigation_chats,213,F;spinnerGender,224,F;buttonPostReact,225,F;colorRed,194,F;textViewAnnouncement,217,F;textViewSearchTitle,255,F;textViewSearchTitle,255,F;textViewReplyLabel,199,F;textViewReplyLabel,200,F;textViewReplyLabel,201,F;textViewReplyLabel,202,F;textViewReplyLabel,203,F;textViewReplyLabel,204,F;textViewReplyLabel,215,F;textViewReplyLabel,216,F;buttonToggleMode,185,F;textViewRulesDescription,261,F;textViewVoiceNoteCurrentTime,225,F;imageViews,233,F;textViewTraitName,250,F;rightTapArea,235,F;rightTapArea,235,F;buttonPlayPreview,207,F;buttonPlayPreview,207,F;buttonPlayPreview,207,F;buttonPlayPreview,207,F;buttonPlayPreview,207,F;buttonPlayPreview,207,F;buttonPlayPreview,207,F;buttonLike,266,F;frameLayoutAvatar,265,F;textViewResultText,190,F;inputLayoutFavPlatform,210,F;textInputLayoutEmail,267,F;textInputLayoutEmail,267,F;textInputLayoutEmail,185,F;editorProgressBar,232,F;recyclerViewChatRooms,217,F;buttonReply,268,F;recyclerViewTraits,269,F;etSearch,270,F;searchLayout,270,F;searchLayout,270,F;searchLayout,270,F;searchLayout,270,F;buttonSearch,255,F;textViewRevealQuestion,190,F;textViewRevealQuestion,184,F;btnUnban,238,F;fabUpload,222,F;textViewBirthdateValue,210,F;imageComment,233,F;imageViewHeartIcon,271,F;layoutVoiceNotePlayer,225,F;textViewResultMessage,271,F;textViewUnreadCount,192,F;recyclerViewUserPosts,241,F;textViewReaction,272,F;reactionLike,258,F;reactionAngry,258,F;imageViewReadStatus,200,F;imageViewReadStatus,206,F;colorBrown,194,F;textViewTagline,273,F;textViewTagline,273,F;textViewTopic,230,F;addTextButton,232,F;textViewRoomDescription,226,F;textViewRoomDescription,192,F;colorOrange,194,F;layoutComments,233,F;textViews,274,F;textViews,233,F;scrollView,275,F;textViewCreateRoomTitle,189,F;textViewBuzzMessage,276,F;textViewBuzzMessage,277,F;textViewStatus,230,F;editTextGender,210,F;buttonEditTraits,210,F;fontSpinner,239,F;colorPreviewInDialog,194,F;colorPreviewInDialog,251,F;cardViewResultUser,190,F;layoutBuzzMessage,276,F;layoutBuzzMessage,277,F;stories_divider,246,F;tvIpHistory,238,F;cardInputMessage,226,F;progressBarRooms,217,F;tvReportDate,231,F;layoutCommunityGuidelines,228,F;coordinator_layout,210,F;onlineStatusIndicator,245,F;editTextMessage,230,F;emptyStateCard,220,F;buttonMic,230,F;buttonMic,226,F;banInfoLayout,238,F;imageView,278,F;messageContentLayout,201,F;messageContentLayout,201,F;messageContentLayout,202,F;messageContentLayout,202,F;buttonCancelWaiting,190,F;buttonCancelWaiting,279,F;textUsername,233,F;viewCountText,235,F;inputLayoutCurrentUsername,280,F;reactionLaugh,258,F;textViewDescriptionLabel,189,F;imageViewMentionAvatar,281,F;progressBarLoader,282,F;progressBarLoader,282,F;textViewLookingForValue,210,F;inputLayoutCurrentEmail,283,F;colorBlack,194,F;viewPagerChat,218,F;fabMyVideos,222,F;recyclerViewGifs,260,F;tilReportDetails,193,F;radioGroupMuteDuration,242,F;textViewLocation,265,F;buttonCloseGifPicker,260,F;textViewWaitingTopic,190,F;textViewWaitingTopic,279,F;textViewWaitingTopic,279,F;textViewUserPostsHeader,241,F;textViewUsername,230,F;textViewUsername,210,F;textViewUsername,284,F;textViewUsername,208,F;textViewUsername,265,F;tvMatchesHeader,220,F;action_unmatch,285,F;recyclerViewMatches,220,F;btnViewUsers,264,F;inputLayoutSexuality,210,F;colorCyberpunkYellow,194,F;imageViewSwipeIndicator,229,F;containerRoomMessages,226,F;buttonSendComment,209,F;tvBannedUsers,264,F;app_bar_layout,210,F;textViewLastMessageTime,192,F;layoutYoutubeThumbnail,225,F;textViewUserNames,271,F;buttonPlayPauseVoiceNote,225,F;cardViewPostImageContainer,225,F;cardVoiceMessage,215,F;cardVoiceMessage,215,F;cardVoiceMessage,216,F;cardVoiceMessage,216,F;buttonLogin,185,F;buttonBlockedUsers,210,F;userAvatar,235,F;tvStatus,238,F;tvStatus,231,F;messageInputLayout,230,F;messageInputLayout,230,F;imageViewProfileAvatar,241,F;textViewReplyText,199,F;textViewReplyText,200,F;textViewReplyText,201,F;textViewReplyText,202,F;textViewReplyText,203,F;textViewReplyText,204,F;textViewReplyText,215,F;textViewReplyText,216,F;cardViewInputs,185,F;colorNeonBlue,194,F;buttonDelete,253,F;buttonDelete,268,F;buttonDelete,274,F;recyclerViewRoomMessages,226,F;textAccuracyLevel,187,F;textAccuracyLevel,188,F;emojiRecyclerView,286,F;imageViewOtherUserAvatar,271,F;muteInfoLayout,238,F;buttonRequestReset,267,F;buttonRequestReset,267,F;layoutLogout,228,F;etButtonText,237,F;editTextMusicSearch,255,F;colorPink,194,F;textViewPronounsValue,210,F;buttonRefreshLocation,259,F;textViewArtistName,207,F;inputLayoutNotes,231,F;action_delete_chat,236,F;textViewChatUsername,229,F;textOverlayContainer,235,F;inputLayoutSchool,210,F;pickColorButton,239,F;editTextCurrentEmail,283,F;collapsing_toolbar_layout,210,F;textViewJoinTitle,186,F;textViewFavPlatformValue,210,F;viewPagerDiscover,221,F;textInputLayoutIconUrl,189,F;buttonEmoji,230,F;colorPurple,194,F;textViewOnlineStatus,210,F;textViewOnlineStatus,265,F;textViewOnlineStatus,245,F;rbSpam,193,F;reactionClap,258,F;buttonPlayPause,215,F;buttonPlayPause,216,F;progressBarMusicSearch,255,F;buttonAddStory,246,F;textMaxDistance,187,F;textMaxDistance,188,F;editTextLocation,210,F;imageViewUserProfile,199,F;imageViewUserProfile,199,F;imageViewUserProfile,200,F;imageViewUserProfile,200,F;imageViewUserProfile,201,F;imageViewUserProfile,201,F;imageViewUserProfile,202,F;imageViewUserProfile,202,F;imageViewUserProfile,203,F;imageViewUserProfile,203,F;imageViewUserProfile,203,F;imageViewUserProfile,204,F;imageViewUserProfile,204,F;imageViewUserProfile,204,F;imageViewUserProfile,215,F;imageViewUserProfile,215,F;imageViewUserProfile,216,F;imageViewUserProfile,216,F;inputLayoutBio,210,F;recyclerViewTopics,190,F;recyclerViewTopics,273,F;btnSearch,270,F;btnSearch,270,F;reactionHundred,258,F;layoutMatchResult,271,F;imageViewMessageStatus,197,F;imageViewMessageStatus,198,F;buttonApplyFilters,224,F;buttonAddImage,234,F;tvUsername,238,F;textViewLastMessage,192,F;textViewLastMessage,208,F;textViewUserName,228,F;editTextFavoriteGame,210,F;buttonAddVoice,234,F;tvDetails,231,F;appBarLayout,264,F;appBarLayout,264,F;appBarLayout,209,F;appBarLayout,270,F;appBarLayout,270,F;inputLayoutPassword,283,F;inputLayoutPassword,280,F;textViewVoiceNoteInfo,234,F;btnBan,238,F;layoutChangeEmail,187,F;layoutChangeEmail,188,F;textViewDistance,265,F;playerView,233,F;textViewMuteDescription,242,F;textViewEmptyMessage,259,F;menu_admin_panel,287,F;buttonAddGif,234,F;editTextEmail,267,F;editTextEmail,185,F;progressBar,288,F;progressBar,230,F;progressBar,269,F;progressBar,214,F;progressBar,267,F;progressBar,185,F;progressBar,220,F;progressBar,263,F;progressBar,240,F;progressBar,210,F;progressBar,289,F;progressBar,187,F;progressBar,188,F;progressBar,235,F;progressBar,212,F;progressBar,270,F;progressBar,273,F;progressBar,259,F;recyclerViewPersonalityTraits,210,F;menu_logout,290,F;buttonPostOptions,225,F;editTextUsername,185,F;rbInappropriateContent,193,F;viewTimeText,247,F;imageViewAvatar,230,F;imageViewAvatar,238,F;imageViewAvatar,284,F;imageViewAvatar,208,F;imageViewAvatar,276,F;imageViewAvatar,265,F;viewBuzzStatus,277,F;textViewSubtitle,214,F;textViewSubtitle,214,F;textViewSubtitle,267,F;textViewSubtitle,267,F;textViewSubtitle,185,F;textViewSubtitle,240,F;textViewSubtitle,240,F;textViewSubtitle,273,F;textViewSubtitle,273,F;textViewChatTopic,291,F;textViewChatTopic,243,F;reactionSparkles,258,F;descriptionTextView,278,F;descriptionTextView,278,F;recyclerViewPosts,246,F;linearLayoutButtons,266,F;linearLayoutButtons,266,F;action_use_gps,292,F;buttonCancelRoom,189,F;radio30d,191,F;toolbarCommunity,246,F;bottomLine2,185,F;bottomLine1,185,F;layoutChat,190,F;layoutChat,273,F;buttonRevealNo,190,F;buttonRevealNo,184,F;radio7d,191,F;viewOnlineStatus,265,F;etNotificationTitle,237,F;recyclerViewStories,246,F;editTextCurrentUsername,280,F;textViewTitle,190,F;textViewTitle,275,F;textViewTitle,269,F;textViewTitle,214,F;textViewTitle,214,F;textViewTitle,267,F;textViewTitle,267,F;textViewTitle,185,F;textViewTitle,240,F;textViewTitle,240,F;textViewTitle,273,F;textViewTitle,273,F;textViewTitle,259,F;layoutViews,233,F;editTextFavoritePlatform,210,F;layoutResult,190,F;layoutResult,273,F;inputLayoutNewPassword,262,F;textViewChatTitle,243,F;imageViewChatAvatar,229,F;closeButton,235,F;closeButton,293,F;imageViewCommentUserAvatar,294,F;textViewTimestamp,196,F;textViewTimestamp,198,F;imageThumbnail,274,F;layoutAccuracyLevel,187,F;layoutAccuracyLevel,188,F;scrollViewRules,261,F;imageViewRoomIcon,226,F;imageViewRoomIcon,192,F;action_use_ip,292,F;seekBarRed,251,F;buttonCancelMute,242,F;textViewCityCountryValue,210,F;textViewProfileUsername,241,F;emptyViewersText,293,F;textViewIconLabel,189,F;story_avatar,244,F;layout_voice_note_controls_create_post,234,F;navigation_swipe,213,F;reactionPray,258,F;toolbarDivider,209,F;inputLayoutNewEmail,283,F;topBar,235,F;textViewVoiceNoteDuration,225,F;spinnerDistance,224,F;textViewLocationStatus,259,F;buttonNewMatch,271,F;layoutReplyPreview,230,F;layoutReplyPreview,226,F;textViewJoinNote,186,F;colorYellow,194,F;textViewBuzzTime,276,F;textViewBuzzTime,277,F;textViewProfileInfo,241,F;reactionFire,258,F;reactionSad,258,F;textViewSender,195,F;textViewSender,196,F;textViewSender,198,F;buttonSetAsMain,249,F;action_mark_all_read,252,F;tvBanExpiration,238,F;editTextRoomDescription,189,F;editTextRoomDescription,254,F;editTextStoryText,239,F;textComments,233,F;editTextJob,210,F;editTextNewUsername,280,F;colorNeonPink,194,F;textViewAdminBadge,230,F;textViewAdminBadge,210,F;textViewAdminBadge,241,F;radio5min,242,F;buttonSelectSong,207,F;buttonSelectSong,207,F;reactionThinking,258,F;swipeRefreshLayout,259,F;colorGray,194,F;closeEditorButton,232,F;doneButton,232,F;buttonBack,267,F;editTextRoomName,189,F;editTextRoomName,254,F;imageViewYoutubePlayButton,225,F;buttonCancelReveal,295,V400020037,**********,;"";layoutLocationStatus,259,F;layoutMyProfile,228,F;inputLayoutGender,210,F;fragment_container,221,F;cardViewJoin,186,F;gradientOverlay,245,F;layoutReveal,190,F;layoutReveal,273,F;progressBarUserLevel,210,F;progressBarUserLevel,241,F;textViewRevealInfo,184,F;photoView,257,F;textViewGifTitle,296,F;textViewRulesLabel,189,F;layoutHeader,259,F;youtubePlayerView,225,F;buttonAdminPanel,210,F;buttonEditSave,210,F;layoutMuted,226,F;story_time_ago,244,F;buttonCommentLike,294,F;action_room_info,252,F;buttonSkip,211,F;tvRole,238,F;tvReportedUser,231,F;buttonSave,269,F;buttonSave,254,F;contentLayout,199,F;contentLayout,199,F;contentLayout,200,F;contentLayout,200,F;textViewUserHandle,228,F;dropdownSexuality,185,F;viewPagerUsers,221,F;viewPagerUsers,266,F;imageViewUserAvatar,271,F;imageViewUserAvatar,228,F;recyclerViewMessages,230,F;imageViewProfile,297,F;imageViewPhoto,201,F;imageViewPhoto,202,F;progressIndicatorContainer,235,F;buttonViewProfile,265,F;editTextPronouns,210,F;textViewNoResults,260,F;textViewNoResults,255,F;menu_settings,287,F;menu_settings,290,F;imageViewMenuOptions,230,F;recyclerViewMusicResults,255,F;recyclerViewMusicResults,255,F;recyclerViewMusicResults,255,F;recyclerViewMusicResults,255,F;recyclerViewMusicResults,255,F;recyclerViewMusicResults,255,F;logoRing,185,F;deleteTextTrashCan,232,F;textInputLayoutDescription,189,F;buttonClearFilters,224,F;radio24h,191,F;tvActiveWeek,264,F;navigation_profile,213,F;buttonVerify,214,F;buttonVerify,214,F;buttonSendMessage,210,F;buttonSendMessage,241,F;buttonSendMessage,291,F;buttonSendMessage,243,F;musicPreviewLayout,232,F;videoView,235,F;videoView,212,F;tvReason,231,F;tvMuteExpiration,238,F;buttonPostComment,225,F;tvReporter,231,F;storyImage,235,F;layoutActions,233,F;btnHistory,238,F;dropdownGender,185,F;story_username,244,F;reactionParty,258,F;recyclerViewReports,289,F;inputLayoutJob,210,F;textViewWaitingTitle,190,F;textViewWaitingTitle,279,F;textViewWaitingTitle,279,F;buttonReportUser,210,F;layoutReply,199,F;layoutReply,200,F;layoutReply,201,F;layoutReply,202,F;layoutReply,203,F;layoutReply,204,F;layoutReply,215,F;layoutReply,216,F;textViewBioValue,210,F;buttonMenuMatch,208,F;textViewSexualityValue,210,F;tvTotalPosts,264,F;progressBarProfile,241,F;toolbarRoom,226,F;textViewUserCardNameAge,245,F;inputLayoutBirthdate,210,F;textViewWaitingInfo,279,F;textViewWaitingInfo,279,F;textViewNameLabel,189,F;btnMute,238,F;textViewJoinDescription,186,F;layoutTopicSelection,190,F;layoutTopicSelection,273,F;leftTapArea,235,F;leftTapArea,235,F;buttonRemoveImage,234,F;tvBanReason,238,F;textViewRevealTitle,190,F;textViewRevealTitle,184,F;textInputLayoutRules,189,F;tvNewUsers,264,F;layoutBottom,211,F;layoutBottom,211,F;btnSendNotification,264,F;btnSaveNotes,231,F;textMusicArtist,232,F;textViewCommentTimestamp,294,F;musicInfoContainer,235,F;colorGreen,194,F;progressBarReveal,184,F;colorWhite,194,F;imageViewPreview,234,F;textViewTimer,214,F;textViewTimer,214,F;editTextRoomMessage,226,F;textViewSchoolValue,210,F;imageViewTraitIcon,250,F;textDescription,233,F;bottomControlsLayout,232,F;textInputLayoutName,189,F;buttonLeaveChat,190,F;buttonLeaveChat,291,F;buttonLeaveChat,243,F;textViewForgotPassword,185,F;cardViewUserProfile,241,F;container,219,F;tvDeviceInfo,238,F;buttonLogout,210,F;editTextMinAge,224,F;textViewInstructions,214,F;textViewInstructions,214,F;buttonPostSubmit,234,F;logoContainer,185,F;titleTextView,278,F;titleTextView,278,F;recyclerViewEmojis,298,F;recyclerViewChat,190,F;recyclerViewChat,291,F;recyclerViewChat,243,F;editModeDetailsLayout,210,F;buttonMuteUser,268,F;textViewJobValue,210,F;titleText,293,F;reactionHeart,258,F;menu_profile,287,F;buttonDislike,266,F;action_send_buzz,236,F;layoutSendMessage,226,F;switchReceiveMessages,187,F;switchReceiveMessages,188,F;tabLayoutCategories,298,F;tabLayoutCategories,299,F;textPreview,239,F;progressBarWaiting,190,F;progressBarWaiting,279,F;progressBarWaiting,279,F;layoutEmptyState,259,F;cardViewYoutubeContainer,225,F;topControlsLayout,232,F;textViewGenderValue,210,F;imageSlider,210,F;textViewPostUsername,209,F;textViewPostUsername,225,F;radio1hour,242,F;buttonRegister,185,F;editTextMaxAge,224,F;colorCyan,194,F;textViewAge,210,F;textViewAge,265,F;viewPagerEmojis,299,F;textViewPostCommentCount,225,F;tvDeviceHistoryTitle,238,F;topLine2,185,F;buttonSelectVideo,212,F;buttonResultContinue,190,F;topLine1,185,F;textViewBio,265,F;reactionSurprised,258,F;usernameText,235,F;progressBarChats,223,F;inputLayoutLocation,210,F;textViewEmptyRooms,217,F;editTextBirthdate,185,F;editTextBirthdate,210,F;buttonCancelReply,230,F;buttonCancelReply,226,F;boldCheckbox,239,F;buttonRequestPermission,259,F;seekBarVoiceNote,225,F;textCommentsCount,248,F;recyclerUsers,270,F;textViewDuration,215,F;textViewDuration,216,F;musicArtistText,235,F;editTextDetails,193,F;textViewCategoryTitle,300,F;recyclerViewBlockedUsers,288,F;etButtonUrl,237,F;progressBarCommunity,246,F;textViewRules,261,F;layoutCommunity,301,F;layoutCommentInput,209,F;textViewEmptyChats,223,F;viewMainIndicator,297,F;progressBarComments,209,F;textViewCommentLikes,294,F;layoutWaiting,190,F;layoutWaiting,273,F;textViewRoomName,226,F;textViewRoomName,253,F;textViewRoomName,192,F;textViewResultTitle,271,F;viewPagerVideos,222,F;editTextNewEmail,283,F;editTextBanDuration,191,F;textViewCommentContent,294,F;textViewPostTimestamp,209,F;textViewPostTimestamp,225,F;imageUserAvatar,233,F;tvTotalUsers,264,F;textInputLayoutPassword,240,F;textInputLayoutPassword,240,F;radioGroupBanDuration,191,F;textViewUserCardBio,245,F;layoutChangePassword,187,F;layoutChangePassword,188,F;recyclerViewUsers,259,F;navigation_matches,213,F;textViewToolbarTitle,209,F;editTextRoomRules,189,F;editTextRoomRules,254,F;layoutChatInput,190,F;layoutChatInput,291,F;iconClose,275,F;tvEmptyList,289,F;tvEmptyList,270,F;buttonUnblock,284,F;toolbar,264,F;toolbar,288,F;toolbar,230,F;toolbar,218,F;toolbar,209,F;toolbar,275,F;toolbar,269,F;toolbar,220,F;toolbar,263,F;toolbar,210,F;toolbar,289,F;toolbar,187,F;toolbar,188,F;toolbar,221,F;toolbar,212,F;toolbar,270,F;buttonDeclineRules,261,F;rbHarassment,193,F;tabLayoutChat,218,F;musicTitleText,235,F;inputLayoutPronouns,210,F;etImageUrl,237,F;radio24hours,242,F;fabAddImage,210,F;fabAddImage,249,F;buttonCreateRoom,189,F;viewPagerTutorial,211,F;textViewCommentUsername,294,F;animationSpinner,239,F;textViewSongTitle,207,F;textViewSongTitle,207,F;buttonGif,230,F;textViewYoutubeTitle,225,F;inputLayoutCurrentPassword,262,F;cardViewAvatar,208,F;inputLayoutLookingFor,210,F;viewersRecyclerView,293,F;viewModeDetailsLayout,210,F;action_edit_room,302,F;editTextPostContent,234,F;textViewMentionUsername,281,F;textInputLayoutSexuality,185,F;cardViewOriginalPost,209,F;editDescriptionText,212,F;switchLocationSharing,187,F;switchLocationSharing,188,F;textViewPostContent,209,F;tvEmail,238,F;editTextChatMessage,190,F;editTextChatMessage,291,F;editTextChatMessage,243,F;checkBoxAcceptRules,261,F;fabChangeAvatar,210,F;recyclerView,263,F;textLikes,274,F;textLikes,233,F;progressBarTopics,190,F;textViewTopicSelectionTitle,190,F;recyclerViewComments,209,F;recyclerViewComments,248,F;buttonExpandSearch,259,F;editTextCurrentPassword,262,F;viewMessageStatus,202,F;viewMessageStatus,204,F;viewMessageStatus,215,F;textViewSenderName,203,F;textViewSenderName,204,F;textViewFavGameValue,210,F;textViewPostTextContent,225,F;cardViewChatInput,243,F;inputLayoutNewUsername,280,F;rbFakeProfile,193,F;textViewMessage,195,F;textViewMessage,196,F;textViewMessage,197,F;textViewMessage,197,F;textViewMessage,197,F;textViewMessage,198,F;textViewMessage,198,F;textViewMessage,198,F;textViewMessage,199,F;textViewMessage,200,F;textViewMessage,203,F;textViewMessage,204,F;textViewMessage,205,F;textViewMessage,206,F;radio15min,242,F;textEmoji,303,F;progressBarRoom,226,F;editTextSexuality,210,F;logoImage,185,F;searchContainer,255,F;searchContainer,255,F;tabLayoutDiscover,221,F;action_delete_post,304,F;editTextNewPassword,240,F;editTextNewPassword,262,F;storyTimestampText,235,F;textViewEmptyTitle,259,F;fabDeleteImage,297,F;layoutUserInfo,228,F;imageViewGif,296,F;imageViewGif,199,F;imageViewGif,200,F;imageViewGif,205,F;imageViewGif,206,F;layoutRecruitAndEarn,228,F;editTextSchool,210,F;buttonRemoveMusic,232,F;tilSearch,270,F;tilSearch,270,F;tilSearch,270,F;textViewContent,275,F;imageMusicCover,232,F;seekBarGreen,251,F;story_avatar_ring_unseen,244,F;editTextDigit6,214,F;buttonConfirmMute,242,F;imageViewPostImage,225,F;cardViewHeader,243,F;layoutVideos,301,F;deviceHistoryContainer,238,F;buttonAcceptRules,261,F;editTextLookingFor,210,F;textViewEmpty,288,F;textViewEmpty,220,F;textViewEmpty,273,F;textViewLoading,282,F;textViewLoading,282,F;buttonSendReport,193,F;textViewBioLabel,210,F;textViewLocationValue,210,F;btnUnmute,238,F;buttonContinueToChat,271,F;buttonContinueToChat,271,F;viewerUsername,247,F;cardViewMentionSuggestions,226,F;editTextConfirmPassword,240,F;editTextConfirmPassword,262,F;editTextPassword,185,F;editTextPassword,283,F;editTextPassword,280,F;menu_block_user,290,F;linearLayoutCode,214,F;linearLayoutCode,214,F;imageViewPostUserAvatar,209,F;imageViewPostUserAvatar,225,F;editTextRoomIconUrl,189,F;editTextRoomIconUrl,254,F;btnViewReports,264,F;imageViewYoutubeThumbnail,225,F;action_delete_room,302,F;selectedColorPreview,239,F;editTextDigit5,214,F;textInputLayoutBirthdate,185,F;editTextDigit4,214,F;editTextDigit3,214,F;textViewChatTimer,190,F;textViewChatTimer,291,F;textViewChatTimer,243,F;buttonRoomSend,226,F;editTextDigit2,214,F;editTextDigit1,214,F;viewIcon,235,F;progressBarGifs,260,F;textViewTopicDescription,256,F;recyclerViewCategoryTraits,300,F;+layout:dialog_comments_bottom_sheet,248,F;tutorial_page,278,F;activity_main_empty,305,F;item_blind_date_message_sent_modern,198,F;layout_blind_date_result,271,F;dialog_delete_room_confirmation,253,F;item_post,225,F;activity_full_screen_image,257,F;item_sent_gif_message,206,F;activity_room_loader,282,F;item_blind_date_message_sent,197,F;activity_blocked_users,288,F;activity_story_editor,232,F;layout_blind_date_chat_modern,243,F;item_story_viewer,247,F;dialog_improved_emoji_picker,299,F;item_mention_suggestion,281,F;layout_user_muted,227,F;activity_tutorial,211,F;dialog_gif_picker,260,F;dialog_create_room,189,F;activity_my_videos,263,F;layout_blind_date_chat,291,F;activity_settings_compat,188,F;activity_admin_panel,264,F;item_received_gif_message,205,F;activity_login,185,F;layout_join_room_prompt,186,F;activity_community_guidelines,275,F;item_blind_date_message_received_modern,196,F;dialog_mute_user,242,F;item_blind_date_message_received,195,F;activity_story_viewer,235,F;fragment_chat_rooms,217,F;item_report,231,F;dialog_create_post,234,F;activity_matches,220,F;activity_chat_room,226,F;menu_community_dropdown,301,F;activity_user_management,270,F;item_chat_list,229,F;item_gif_message_sent,200,F;activity_community_host,219,F;activity_comments,209,F;activity_upload_video,212,F;activity_swipe,221,F;layout_profile_image_slider,249,F;item_voice_message_received,216,F;activity_password_reset,240,F;item_radar_user,265,F;item_story,244,F;item_my_video,274,F;activity_user_profile,241,F;fragment_blind_date,273,F;item_image_message_sent,202,F;item_gif_message_received,199,F;item_video,233,F;item_personality_trait_category,300,F;item_message_buzz_received,276,F;dialog_change_email,283,F;dialog_change_password,262,F;item_message_reaction,272,F;dropdown_item,306,F;activity_chat_list,218,F;dialog_music_search,255,F;dialog_color_palette_picker,194,F;item_user_card,245,F;profile_dropdown_menu,228,F;item_personality_trait,250,F;layout_reaction_selector,258,F;dialog_global_notification,237,F;fragment_radar,259,F;dialog_message_options,268,F;dialog_rules_acceptance,261,F;item_chat_room,192,F;item_gif,296,F;activity_profile,210,F;activity_video_feed,222,F;dialog_story_viewers,293,F;layout_blind_date_waiting,279,F;item_music_search,207,F;item_blind_date_topic,256,F;activity_chat,230,F;layout_blind_date_reveal,184,F;fragment_private_chats,223,F;dialog_radar_filters,224,F;dialog_add_text_with_style,239,F;dialog_custom_color_picker,251,F;item_emoji,303,F;item_message_sent,204,F;activity_edit_personality_traits,269,F;item_match,208,F;grid_emoji_category,286,F;item_admin_user,238,F;item_blocked_user,284,F;fragment_community,246,F;item_image_message_received,201,F;item_message_received,203,F;activity_blind_date,190,F;item_voice_message,215,F;activity_forgot_password,267,F;dialog_edit_room,254,F;item_comment,294,F;dialog_report_user,193,F;dialog_ban_user,191,F;activity_email_verification,214,F;dialog_change_username,280,F;fragment_profiles,266,F;dialog_emoji_picker,298,F;item_message_buzz_sent,277,F;item_profile_image,297,F;activity_reports,289,F;activity_settings,187,F;+menu:menu_post_options,304,F;profile_menu,290,F;room_chat_menu,252,F;chat_menu,236,F;menu_location_options,292,F;menu_match_item,285,F;bottom_nav_menu,213,F;menu_swipe,287,F;menu_room_admin,302,F;+mipmap:ic_launcher_round,307,F;ic_launcher_round,308,F;ic_launcher_round,309,F;ic_launcher_round,310,F;ic_launcher_round,311,F;ic_launcher_round,312,F;ic_launcher_foreground,313,F;ic_launcher_foreground,314,F;ic_launcher_foreground,315,F;ic_launcher_foreground,316,F;ic_launcher_foreground,317,F;ic_launcher,318,F;ic_launcher,319,F;ic_launcher,320,F;ic_launcher,321,F;ic_launcher,322,F;ic_launcher,323,F;+raw:welcome,324,F;+string:mark_as_read,325,V4002607e1,3a00260817,;"MARCAR COMO LEÍDO";gcm_defaultSenderId,326,V400020037,5100020084,;"988461270314";personality_traits_title,325,V400320a4a,4b00320a91,;"Rasgos de Personalidad";title_activity_main_menu_cyber_punk,325,V4002c08fd,51002c094a,;"MainMenuCyberPunk";avatar_update_success,325,V4001e0630,4e001e067a,;"Avatar actualizado con éxito";reply_label,325,V4002507a4,3c002507dc,;"Escribe tu respuesta";trait_icon_description,325,V400340ad8,4200340b16,;"Icono del rasgo";project_id,326,V40007023d,4700070280,;"vmeet-5c6a2";title_activity_main_menu,325,V4002b08b7,45002b08f8,;"MainMenuActivity";reply,325,V400240778,2b0024079f,;"RESPONDER";profile_save_success,325,V400210703,4a00210749,;"Perfil guardado con éxito";title_chats,325,V40007014e,2d00070177,;"Chats";about_me_label,325,V4000e02e6,38000e031a,;"Acerca de mí\:";favorite_games_label,325,V4000c0265,42000c02a3,;"Juegos favoritos\:";error_loading_traits,325,V400390c5d,4700390ca0,;"Error al cargar rasgos";title_swipe,325,V4000400b9,31000400e6,;"Descubrir";error_saving_traits,325,V400380c15,4700380c58,;"Error al guardar rasgos";title_profile,325,V40006011d,3000060149,;"Perfil";sample_admob_app_id,325,V4000f031f,56000f0371,;"ca-app-pub-3940256099942544~3347511713";title_activity_swipe,325,V40003007a,3e000300b4,;"SwipeActivity";email_label,325,V4000a01ff,2e000a0229,;"Email\:";title_community,325,V400290880,35002908b1,;"Comunidad";google_storage_bucket,326,V4000601d6,6600060238,;"vmeet-5c6a2.firebasestorage.app";hello_blank_fragment,325,V40008017c,44000801bc,;"Fragmento en blanco";interests_label,325,V4000b022e,36000b0260,;"Intereses\:";next,325,V4001504b9,2a001504df,;"Siguiente";permission_denied_gallery,325,V4001d05cf,60001d062b,;"Permiso denegado para acceder a la galería";profile_button_save,325,V4001c0597,37001c05ca,;"GUARDAR";google_app_id,326,V4000400f1,6c00040159,;"1\:988461270314\:android\:d0242913a2e29332704a53";google_api_key,326,V400030089,67000300ec,;"AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg";second_fragment_label,325,V400140475,43001404b4,;"Segundo Fragmento";edit_profile_button,325,V4000d02a8,3d000d02e1,;"Editar Perfil";traits_saved_successfully,325,V400370bc1,5300370c10,;"Rasgos guardados exitosamente";title_matches,325,V4000500eb,3100050118,;"Matches";personality_traits_subtitle,325,V400350b1b,6e00350b85,;"Estos rasgos ayudan a otros usuarios a conocerte mejor";edit_personality_traits,325,V400330a96,4100330ad3,;"Editar Rasgos";avatar_update_error,325,V4001f067f,4d001f06c8,;"Error al actualizar el avatar";username_label,325,V4000901c1,3d000901fa,;"Nombre de usuario\:";previous,325,V4001604e4,2d0016050d,;"Anterior";error_loading_gifs,325,V40027081c,620027087a,;"Error cargando GIFs. Por favor\, inténtalo de nuevo.";default_notification_channel_id,325,V4002d094f,48002d0993,;"VMeetChannel";google_crash_reporting_api_key,326,V40005015e,77000501d1,;"AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg";title_activity_profile,325,V4001103b9,42001103f7,;"ProfileActivity";profile_button_edit,325,V4001b0560,36001b0592,;"EDITAR";save_traits,325,V400360b8a,3600360bbc,;"Guardar Rasgos";app_name,325,V400010010,2a00010036,;"VMeet";first_fragment_label,325,V400130433,4100130470,;"Primer Fragmento";title_activity_matches,325,V400100376,42001003b4,;"MatchesActivity";create_new_post,325,V4002e0998,43002e09d7,;"Crear nueva publicación";title_activity_comments,325,V4002f09dc,44002f0a1c,;"CommentsActivity";logged_out,325,V4002006cd,35002006fe,;"Sesión cerrada";title_activity_login,325,V40002003b,3e00020075,;"LoginActivity";+style:MenuTextStyle,327,V4001303d6,c00160476,;Nandroid\:textColor:#FFFFFF,android\:fontFamily:@font/app_font,;ShapeAppearance.App.RoundedGif,327,V400580ff0,c005b1093,;EcornerFamily:rounded,cornerSize:8dp,;CustomSwitchStyle,328,V40017051c,c001c0676,;DTheme.AppCompat,colorAccent:@color/neon_blue,colorControlActivated:@color/neon_blue,colorSwitchThumbNormal:@color/text_secondary,android\:colorForeground:@color/text_secondary,;TextAppearance.VMeet.Button,329,V40027050b,c002b05e0,;Nandroid\:textSize:14sp,android\:textStyle:bold,android\:textAllCaps:true,;VMeetToolbar,327,V4006912e6,c00751601,;DWidget.MaterialComponents.Toolbar,android\:layout_width:0dp,android\:layout_height:wrap_content,android\:background:#1A1A3A,android\:elevation:4dp,android\:paddingTop:0dp,android\:minHeight:?attr/actionBarSize,android\:fontFamily:@font/app_font,titleTextColor:@color/comfy_blue,titleTextAppearance:@style/TextAppearance.VMeet.Headline2,fontFamily:@font/app_font,;Theme.VMeet.NoActionBar,327,V4005e10d5,c00641245,;DTheme.VMeet,windowActionBar:false,windowNoTitle:true,android\:windowFullscreen:false,android\:windowTranslucentStatus:true,android\:windowTranslucentNavigation:true,;Theme.VMeet.Dialog.FullWidth,328,V400030055,c000901f8,;DTheme.AppCompat.Dialog,android\:windowMinWidthMajor:90%,android\:windowMinWidthMinor:90%,android\:windowBackground:@android\:color/transparent,android\:windowIsFloating:true,android\:windowCloseOnTouchOutside:true,;Theme.VMeet,327,V40003003b,c001103ce,;DTheme.Material3.DayNight.NoActionBar,android\:itemTextAppearance:@style/MenuTextStyle,android\:fontFamily:@font/app_font,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:statusBarColor:@color/darker_blue,android\:navigationBarColor:@android\:color/transparent,android\:windowLightStatusBar:false,android\:windowLayoutInDisplayCutoutMode:shortEdges,popupMenuStyle:@style/PopupMenu,fontFamily:@font/app_font,;VMeetTextStyle.Title,327,V4001e055a,c002105f2,;Nandroid\:textSize:20sp,android\:textStyle:bold,;Theme.VMeet.Dialog.Alert,328,V10000901fc,c0010042e,;DTheme.AppCompat.Light.Dialog.Alert,android\:background:@color/darker_blue,android\:textColorPrimary:@android\:color/white,android\:textColorSecondary:@color/neon_blue,android\:buttonBarPositiveButtonStyle:@style/AlertDialogButtonStyle,android\:buttonBarNegativeButtonStyle:@style/AlertDialogButtonStyle,colorAccent:@color/neon_pink,;VMeetTextStyle.Body,327,V400270669,c002906cc,;Nandroid\:textSize:14sp,;TextAppearance.VMeet.Body1,329,V40019036e,c001b03d8,;Nandroid\:textSize:16sp,;TextAppearance.VMeet.Body2,329,V4001d03e0,c001f044a,;Nandroid\:textSize:14sp,;TextAppearance.VMeet.Caption,329,V400220477,c002404e3,;Nandroid\:textSize:12sp,;TextAppearance.VMeet.Headline1,329,V400090151,c000c01f3,;Nandroid\:textSize:24sp,android\:textStyle:bold,;CustomAltButton,327,V400470cb2,c00510f39,;DWidget.AppCompat.Button.Borderless,android\:background:@drawable/modern_button_alt,android\:textColor:@color/white,android\:textSize:16sp,android\:textStyle:bold,android\:textAllCaps:true,android\:stateListAnimator:@null,android\:fontFamily:@font/app_font,android\:textAppearance:@style/TextAppearance.VMeet.Button,fontFamily:@font/app_font,;CustomBottomNavigation,327,V4002f076a,c003809e2,;DWidget.Material3.BottomNavigationView,android\:background:#1A1A3A,android\:fontFamily:@font/app_font,itemIconTint:@color/comfy_blue,itemTextColor:@color/comfy_blue,labelVisibilityMode:labeled,fontFamily:@font/app_font,itemTextAppearanceActive:@style/TextAppearance.VMeet.Caption,itemTextAppearanceInactive:@style/TextAppearance.VMeet.Caption,;ShapeAppearance.App.RoundedButton,327,V400530f41,c00560fe8,;EcornerFamily:rounded,cornerSize:12dp,;VMeetTextStyle,327,V4001904b1,c001c0552,;Nandroid\:fontFamily:@font/app_font,fontFamily:@font/app_font,;AlertDialogButtonStyle,328,V400120436,c001404e0,;DWidget.AppCompat.Button.ButtonBar.AlertDialog,android\:textColor:@color/neon_pink,;TextAppearance.VMeet,329,V400030063,c0006012c,;DTextAppearance.AppCompat,android\:fontFamily:@font/app_font,fontFamily:@font/app_font,;TextAppearance.VMeet.Headline3,329,V4001302a5,c00160347,;Nandroid\:textSize:18sp,android\:textStyle:bold,;TextAppearance.VMeet.Headline2,329,V4000e01fb,c0011029d,;Nandroid\:textSize:20sp,android\:textStyle:bold,;VMeetTextStyle.Subtitle,327,V4002305fa,c00250661,;Nandroid\:textSize:16sp,;CustomPurpleButton,327,V4003b0a24,c00450caa,;DWidget.AppCompat.Button.Borderless,android\:background:@drawable/modern_button,android\:textColor:@color/white,android\:textSize:16sp,android\:textStyle:bold,android\:textAllCaps:true,android\:stateListAnimator:@null,android\:fontFamily:@font/app_font,android\:textAppearance:@style/TextAppearance.VMeet.Button,fontFamily:@font/app_font,;PopupMenu,327,V4002b06d4,c002d0762,;D@style/Widget.Material3.PopupMenu,android\:popupBackground:#212121,;+styleable:PurpleButton,11,V400020039,18000400aa,;-isAlt:boolean:;PixelPerfectTextView,11,V4000600b2,18000a0176,;-text::-textSize::-textColor::;+xml:file_paths,330,F;network_security_config,331,F;data_extraction_rules,332,F;backup_rules,333,F;