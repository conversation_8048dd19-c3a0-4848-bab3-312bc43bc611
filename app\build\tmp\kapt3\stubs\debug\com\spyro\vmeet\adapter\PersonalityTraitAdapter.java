package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u001eB5\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\u0016\b\u0002\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u00a2\u0006\u0002\u0010\u000bJ\b\u0010\f\u001a\u00020\rH\u0016J\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fJ\u0018\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u00022\u0006\u0010\u0012\u001a\u00020\rH\u0016J\u0018\u0010\u0013\u001a\u00020\u00022\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\rH\u0016J\u0014\u0010\u0017\u001a\u00020\n2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\u000fJ\u0018\u0010\u001a\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u00022\u0006\u0010\u001b\u001a\u00020\u0005H\u0002J\u0014\u0010\u001c\u001a\u00020\n2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/spyro/vmeet/adapter/PersonalityTraitAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/PersonalityTraitAdapter$TraitViewHolder;", "traits", "", "Lcom/spyro/vmeet/data/PersonalityTrait;", "isEditMode", "", "onTraitClick", "Lkotlin/Function1;", "", "(Ljava/util/List;ZLkotlin/jvm/functions/Function1;)V", "getItemCount", "", "getSelectedTraits", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setSelectedTraits", "selectedTraitIds", "", "updateTraitAppearance", "trait", "updateTraits", "newTraits", "TraitViewHolder", "app_debug"})
public final class PersonalityTraitAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.PersonalityTraitAdapter.TraitViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.PersonalityTrait> traits = null;
    private final boolean isEditMode = false;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.PersonalityTrait, kotlin.Unit> onTraitClick = null;
    
    public PersonalityTraitAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.PersonalityTrait> traits, boolean isEditMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.PersonalityTrait, kotlin.Unit> onTraitClick) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.PersonalityTraitAdapter.TraitViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.PersonalityTraitAdapter.TraitViewHolder holder, int position) {
    }
    
    private final void updateTraitAppearance(com.spyro.vmeet.adapter.PersonalityTraitAdapter.TraitViewHolder holder, com.spyro.vmeet.data.PersonalityTrait trait) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void updateTraits(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.PersonalityTrait> newTraits) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.data.PersonalityTrait> getSelectedTraits() {
        return null;
    }
    
    public final void setSelectedTraits(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedTraitIds) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/adapter/PersonalityTraitAdapter$TraitViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "containerView", "getContainerView", "()Landroid/view/View;", "iconImageView", "Landroid/widget/ImageView;", "getIconImageView", "()Landroid/widget/ImageView;", "nameTextView", "Landroid/widget/TextView;", "getNameTextView", "()Landroid/widget/TextView;", "app_debug"})
    public static final class TraitViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView iconImageView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView nameTextView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View containerView = null;
        
        public TraitViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getIconImageView() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getNameTextView() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.view.View getContainerView() {
            return null;
        }
    }
}