package com.spyro.vmeet.audio;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u0000 #2\u00020\u0001:\u0001#B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0018\u001a\u0004\u0018\u00010\tJ\u0006\u0010\u0011\u001a\u00020\u0010J\u0006\u0010\u0019\u001a\u00020\u0007J\u000e\u0010\u001a\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\tJ\u0006\u0010\u001c\u001a\u00020\u0007J<\u0010\u001d\u001a\u00020\u00072\b\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u001a\u0010\n\u001a\u0016\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u000b2\u000e\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006J\b\u0010\u001e\u001a\u00020\u0007H\u0002J\u0006\u0010\u001f\u001a\u00020\u0007J\b\u0010 \u001a\u00020\u0007H\u0002J\u0006\u0010!\u001a\u00020\u0007J\b\u0010\"\u001a\u00020\u0007H\u0002R\u0016\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\n\u001a\u0016\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\f\u0012\u0004\u0012\u00020\u0007\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/spyro/vmeet/audio/AudioPlayer;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "completionListener", "Lkotlin/Function0;", "", "currentFilePath", "", "durationCallback", "Lkotlin/Function2;", "", "handler", "Landroid/os/Handler;", "isPaused", "", "isPlaying", "mediaPlayer", "Landroid/media/MediaPlayer;", "progressRunnable", "Ljava/lang/Runnable;", "seekBar", "Landroid/widget/SeekBar;", "getCurrentFilePath", "pauseAudio", "playAudio", "filePath", "releaseMediaPlayer", "setUiControls", "startProgressUpdates", "stopAudio", "stopProgressUpdates", "togglePlayPause", "updateDurationCallback", "Companion", "app_debug"})
public final class AudioPlayer {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer mediaPlayer;
    private boolean isPlaying = false;
    private boolean isPaused = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String currentFilePath;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler handler = null;
    @org.jetbrains.annotations.Nullable()
    private android.widget.SeekBar seekBar;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> durationCallback;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function0<kotlin.Unit> completionListener;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable progressRunnable;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AudioPlayer";
    private static final long UPDATE_INTERVAL = 100L;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.audio.AudioPlayer.Companion Companion = null;
    
    public AudioPlayer(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Set the SeekBar and callbacks for controlling/updating UI
     */
    public final void setUiControls(@org.jetbrains.annotations.Nullable()
    android.widget.SeekBar seekBar, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> durationCallback, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> completionListener) {
    }
    
    /**
     * Play audio from the given file path
     */
    public final void playAudio(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath) {
    }
    
    /**
     * Pause the currently playing audio
     */
    public final void pauseAudio() {
    }
    
    /**
     * Stop and release resources
     */
    public final void stopAudio() {
    }
    
    /**
     * Toggle between play and pause
     */
    public final void togglePlayPause() {
    }
    
    /**
     * Release and cleanup media player resources
     */
    public final void releaseMediaPlayer() {
    }
    
    /**
     * Check if audio is currently playing
     */
    public final boolean isPlaying() {
        return false;
    }
    
    /**
     * Get the current file path
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCurrentFilePath() {
        return null;
    }
    
    /**
     * Update UI with current position and duration
     */
    private final void updateDurationCallback() {
    }
    
    /**
     * Start periodic updates for progress
     */
    private final void startProgressUpdates() {
    }
    
    /**
     * Stop periodic progress updates
     */
    private final void stopProgressUpdates() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/spyro/vmeet/audio/AudioPlayer$Companion;", "", "()V", "TAG", "", "UPDATE_INTERVAL", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}