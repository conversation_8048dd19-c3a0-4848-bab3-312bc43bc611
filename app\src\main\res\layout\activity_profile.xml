<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient"
    android:fitsSystemWindows="true">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/coordinator_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fitsSystemWindows="true"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/collapsing_toolbar_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fitsSystemWindows="true"
                app:contentScrim="#1A1A3A"
                app:expandedTitleMarginEnd="64dp"
                app:expandedTitleMarginStart="48dp"
                app:layout_scrollFlags="scroll|exitUntilCollapsed">

                <!-- Profile Image as Toolbar Background - Replace with ViewPager -->
                <include
                    android:id="@+id/imageSlider"
                    layout="@layout/layout_profile_image_slider"
                    android:layout_width="match_parent"
                    android:layout_height="400dp"
                    android:fitsSystemWindows="true"
                    app:layout_collapseMode="parallax" />

                <!-- Optional: Toolbar for Edit Button -->
                 <androidx.appcompat.widget.Toolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:layout_collapseMode="pin"
                    app:popupTheme="@style/ThemeOverlay.AppCompat.Light"/>

            </com.google.android.material.appbar.CollapsingToolbarLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Basic Info: Name, Age, Status -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardBackgroundColor="#1A1A3A"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="8dp">

                            <TextView
                                android:id="@+id/textViewUsername"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/comfy_blue"
                                android:textSize="26sp"
                                android:textStyle="bold"
                                tools:text="Username" />

                            <TextView
                                android:id="@+id/textViewAge"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:textColor="@color/white"
                                android:textSize="24sp"
                                tools:text="25" />

                            <TextView
                                android:id="@+id/textViewAdminBadge"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:background="@drawable/badge_admin"
                                android:drawableStart="@drawable/ic_admin_crown"
                                android:drawablePadding="4dp"
                                android:text="Administrador"
                                android:textColor="@android:color/white"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />

                            <TextView
                                android:id="@+id/textViewVerifiedBadge"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:background="@drawable/badge_verified"
                                android:drawableStart="@drawable/ic_verified_check"
                                android:drawablePadding="4dp"
                                android:text="Verificado"
                                android:textColor="@android:color/white"
                                android:textSize="12sp"
                                android:textStyle="bold"
                                android:visibility="gone"
                                tools:visibility="visible" />
                        </LinearLayout>

                        <!-- Nivel y XP -->
                        <TextView
                            android:id="@+id/textViewUserLevel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Nivel 1"
                            android:textColor="@color/neon_blue"
                            android:textStyle="bold"
                            android:textSize="18sp"
                            android:layout_gravity="center_horizontal"
                            android:layout_marginTop="8dp"
                            tools:text="Nivel 5" />

                        <ProgressBar
                            android:id="@+id/progressBarUserLevel"
                            style="@android:style/Widget.ProgressBar.Horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="12dp"
                            android:progress="30"
                            android:max="100"
                            android:progressDrawable="@drawable/progress_bar_cyberpunk"
                            android:layout_marginTop="8dp"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/textViewUserXP"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="XP: 300/500"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:layout_gravity="center_horizontal"
                            tools:text="XP: 300/500" />

                        <!-- Add the online status right after the horizontal layout -->
                            <TextView
                                android:id="@+id/textViewOnlineStatus"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="En línea"
                                android:textAlignment="center"
                                android:textColor="#4CAF50"
                                android:textSize="14sp"/>


                         <!-- Pronouns (View Mode) -->
                        <TextView
                            android:id="@+id/textViewPronounsValue"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:drawableStart="@android:drawable/ic_menu_info_details"
                            android:drawablePadding="12dp"
                            android:drawableTint="@color/neon_blue"
                            android:gravity="center_vertical"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            tools:text="He/Him" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- EDIT MODE: Pronouns Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/inputLayoutPronouns"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Pronombres"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginBottom="12dp">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextPronouns"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                </com.google.android.material.textfield.TextInputLayout>

                <!-- About Me Section -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardBackgroundColor="#1A1A3A"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/textViewBioLabel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Acerca de mí"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/comfy_blue"
                            android:layout_marginBottom="8dp"/>

                        <TextView
                            android:id="@+id/textViewBioValue"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/white"
                            android:textSize="16sp"
                            tools:text="Descripción larga sobre mi..." />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- EDIT MODE: Bio Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/inputLayoutBio"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Acerca de mí"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginBottom="12dp">
                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/editTextBio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:minLines="3"
                        android:gravity="top" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- Other Details Section -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardBackgroundColor="#1A1A3A"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Detalles"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/comfy_blue"
                            android:layout_marginBottom="12dp"/>

                        <LinearLayout
                            android:id="@+id/viewModeDetailsLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <!-- Job (View Mode) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:src="@android:drawable/ic_menu_recent_history"
                                    android:background="@drawable/circular_button_background"
                                    android:padding="6dp"
                                    android:tint="@color/neon_blue"/>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Trabajo"
                                        android:textSize="14sp"
                                        android:textColor="@color/comfy_blue"/>

                                    <TextView
                                        android:id="@+id/textViewJobValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="16sp"
                                        tools:text="Game Developer" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- School (View Mode) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:src="@android:drawable/ic_menu_edit"
                                    android:background="@drawable/circular_button_background"
                                    android:padding="6dp"
                                    android:tint="@color/neon_green"/>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Escuela"
                                        android:textSize="14sp"
                                        android:textColor="@color/neon_green"/>

                                    <TextView
                                        android:id="@+id/textViewSchoolValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="16sp"
                                        tools:text="Universidad" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- Location (View Mode) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:src="@android:drawable/ic_dialog_map"
                                    android:background="@drawable/circular_button_background"
                                    android:padding="6dp"
                                    android:tint="@color/neon_purple"/>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Ubicación"
                                        android:textSize="14sp"
                                        android:textColor="@color/neon_purple"/>

                                    <TextView
                                        android:id="@+id/textViewLocationValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="16sp"
                                        tools:text="Ciudad" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- Favorite Game (View Mode) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:src="@android:drawable/ic_menu_slideshow"
                                    android:background="@drawable/circular_button_background"
                                    android:padding="6dp"
                                    android:tint="@color/neon_blue"/>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Juego Favorito"
                                        android:textSize="14sp"
                                        android:textColor="@color/neon_blue"/>

                                    <TextView
                                        android:id="@+id/textViewFavGameValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="16sp"
                                        tools:text="Cyberpunk 2077" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- Favorite Platform (View Mode) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:src="@android:drawable/ic_menu_preferences"
                                    android:background="@drawable/circular_button_background"
                                    android:padding="6dp"
                                    android:tint="@color/neon_pink"/>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Plataforma Favorita"
                                        android:textSize="14sp"
                                        android:textColor="@color/neon_pink"/>

                                    <TextView
                                        android:id="@+id/textViewFavPlatformValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="16sp"
                                        tools:text="PC" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- Looking For (View Mode) -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="12dp">

                                <ImageView
                                    android:layout_width="36dp"
                                    android:layout_height="36dp"
                                    android:src="@android:drawable/ic_menu_search"
                                    android:background="@drawable/circular_button_background"
                                    android:padding="6dp"
                                    android:tint="@color/neon_green"/>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:layout_marginStart="12dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Buscando"
                                        android:textSize="14sp"
                                        android:textColor="@color/neon_green"/>

                                    <TextView
                                        android:id="@+id/textViewLookingForValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="16sp"
                                        tools:text="Compañeros de juego" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- Extra Info Section - Birthdate & Gender -->
                            <View
                                android:layout_width="match_parent"
                                android:layout_height="1dp"
                                android:background="#303060"
                                android:layout_marginTop="4dp"
                                android:layout_marginBottom="12dp"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <!-- Birthdate -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1.5"
                                    android:orientation="vertical"
                                    android:layout_marginEnd="8dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Fecha Nacimiento"
                                        android:textSize="12sp"
                                        android:textColor="@color/comfy_blue"/>

                                    <TextView
                                        android:id="@+id/textViewBirthdateValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        android:singleLine="true"
                                        android:ellipsize="end"
                                        tools:text="1995-05-15" />
                                </LinearLayout>

                                <!-- Gender -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="0.8"
                                    android:orientation="vertical"
                                    android:layout_marginStart="8dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Género"
                                        android:textSize="12sp"
                                        android:textColor="@color/comfy_blue"/>

                                    <TextView
                                        android:id="@+id/textViewGenderValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        android:singleLine="true"
                                        android:ellipsize="end"
                                        tools:text="Masculino" />
                                </LinearLayout>
                            </LinearLayout>

                            <!-- Sexuality and Location Row -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginTop="16dp">

                                <!-- Sexuality -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:layout_marginEnd="8dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Orientación sexual"
                                        android:textSize="12sp"
                                        android:textColor="@color/comfy_blue"/>

                                    <TextView
                                        android:id="@+id/textViewSexualityValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        tools:text="Heterosexual" />
                                </LinearLayout>

                                <!-- Location (City/Country) -->
                                <LinearLayout
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="vertical"
                                    android:layout_marginStart="8dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="Ubicación"
                                        android:textSize="12sp"
                                        android:textColor="@color/neon_purple"/>

                                    <TextView
                                        android:id="@+id/textViewCityCountryValue"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:textColor="@color/white"
                                        android:textSize="14sp"
                                        tools:text="Madrid, España" />
                                </LinearLayout>
                            </LinearLayout>
                        </LinearLayout>
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- EDIT MODE: Details Input -->
                <LinearLayout
                    android:id="@+id/editModeDetailsLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone"
                    tools:visibility="visible">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutJob"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Trabajo"
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextJob"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutSchool"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Escuela"
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextSchool"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutLocation"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Ubicación"
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextLocation"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutFavGame"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Juego favorito"
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextFavoriteGame"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </com.google.android.material.textfield.TextInputLayout>

                     <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutFavPlatform"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Plataforma favorita"
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextFavoritePlatform"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </com.google.android.material.textfield.TextInputLayout>

                     <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutLookingFor"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Buscando..."
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextLookingFor"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"/>
                    </com.google.android.material.textfield.TextInputLayout>

                     <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutBirthdate"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Fecha nacimiento (YYYY-MM-DD)"
                        android:layout_marginBottom="12dp">
                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/editTextBirthdate"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusableInTouchMode="false"
                            android:clickable="true"
                            android:inputType="date"/>
                    </com.google.android.material.textfield.TextInputLayout>

                     <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutGender"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Género"
                        android:layout_marginBottom="16dp">
                        <AutoCompleteTextView
                            android:id="@+id/editTextGender"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"/>
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/inputLayoutSexuality"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Orientación sexual"
                        android:layout_marginBottom="16dp">
                        <AutoCompleteTextView
                            android:id="@+id/editTextSexuality"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"/>
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <!-- Personality Traits Section -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardBackgroundColor="#1A1A3A"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/personality_traits_title"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/comfy_blue" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/buttonEditTraits"
                                android:layout_width="wrap_content"
                                android:layout_height="36dp"
                                android:text="@string/edit_personality_traits"
                                android:textSize="12sp"
                                android:textColor="@color/cyberpunk_accent"
                                android:backgroundTint="@android:color/transparent"
                                app:strokeColor="@color/cyberpunk_accent"
                                app:strokeWidth="1dp"
                                app:cornerRadius="18dp"
                                android:minWidth="0dp"
                                android:paddingStart="12dp"
                                android:paddingEnd="12dp"
                                android:visibility="gone"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/personality_traits_subtitle"
                            android:textSize="14sp"
                            android:textColor="@color/cyberpunk_text_secondary"
                            android:layout_marginBottom="16dp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerViewPersonalityTraits"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:clipToPadding="false"
                            android:padding="4dp"
                            android:minHeight="50dp" />

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonSendMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="ENVIAR MENSAJE"
                    android:padding="12dp"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    app:strokeWidth="2dp"
                    app:strokeColor="@color/neon_blue"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/neon_blue"
                    app:icon="@drawable/ic_chat_bubble"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"
                    app:iconTint="@android:color/white"
                    android:visibility="visible"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>

                <!-- Block User Button - Only visible when viewing other profiles -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonBlockUser"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="BLOQUEAR USUARIO"
                    android:padding="12dp"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    app:strokeWidth="2dp"
                    app:strokeColor="@color/dark_red"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/dark_red"
                    app:icon="@android:drawable/ic_lock_lock"
                    app:iconGravity="textStart"
                    app:iconPadding="8dp"
                    app:iconTint="@android:color/white"
                    android:visibility="gone"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />


                            <!-- Report User Button -->
                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/buttonReportUser"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:padding="12dp"
                               android:textSize="16sp"
                                android:textStyle="bold"
                                android:text="REPORTAR USUARIO"
                                app:icon="@android:drawable/ic_lock_lock"
                                app:iconGravity="textStart"
                                app:iconPadding="8dp"
                                app:iconTint="#FFC107"
                                app:cornerRadius="8dp"
                                android:textColor="@android:color/white"
                                android:backgroundTint="@android:color/holo_red_dark"
                                android:visibility="gone"
                                tools:visibility="visible"
                                style="@style/Widget.MaterialComponents.Button.OutlinedButton"/>


                <!-- Buttons (Consider moving Edit to Toolbar/FAB later) -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonAdminPanel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="PANEL DE ADMINISTRACIÓN"
                    android:padding="12dp"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    app:icon="@android:drawable/ic_menu_manage"
                    app:iconTint="@android:color/white"
                    app:iconGravity="textStart"
                    app:strokeWidth="2dp"
                    app:strokeColor="@color/dark_red"
                    app:cornerRadius="8dp"
                    app:backgroundTint="@color/dark_red"
                    android:visibility="gone"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonEditSave"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/profile_button_edit"
                    android:padding="12dp"
                    android:textSize="16sp"
                    android:textColor="@color/neon_blue"
                    android:textStyle="bold"
                    app:strokeWidth="2dp"
                    app:strokeColor="@color/neon_blue"
                    app:cornerRadius="8dp"
                    app:backgroundTint="#151540"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonLogout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="16dp"
                    android:text="CERRAR SESIÓN"
                    android:padding="12dp"
                    android:textSize="16sp"
                    android:textColor="@color/neon_pink"
                    android:textStyle="bold"
                    app:strokeWidth="2dp"
                    app:strokeColor="@color/neon_pink"
                    app:cornerRadius="8dp"
                    app:backgroundTint="#2D0A0A"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                <!-- Blocked Users Button - Only visible on own profile -->
                <Button
                    android:id="@+id/buttonBlockedUsers"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Lista de usuarios bloqueados"
                    android:textColor="@color/white"
                    android:backgroundTint="@color/dark_red"
                    app:layout_constraintTop_toBottomOf="@id/buttonAdminPanel"
                    app:layout_constraintStart_toStartOf="@id/buttonLogout"
                    app:layout_constraintEnd_toEndOf="@id/buttonLogout" />
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <!-- Add ProgressBar for loading states -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone"
            android:indeterminateTint="@color/neon_blue"/>

        <!-- Floating Action Button for Avatar Change (Only visible in Edit Mode) -->
        <com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
            android:id="@+id/fabChangeAvatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:backgroundTint="@color/neon_purple"
            android:rotationY="5"
            android:text="Cambiar Foto"
            android:textColor="@color/white"
            android:visibility="gone"
            app:icon="@android:drawable/ic_menu_camera"
            app:iconTint="@color/white"
            app:layout_anchor="@id/app_bar_layout"
            app:layout_anchorGravity="bottom|end"
            tools:visibility="visible" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabAddImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:srcCompat="@android:drawable/ic_input_add"
            app:tint="@color/white"
            app:backgroundTint="@color/neon_blue"
            app:layout_anchor="@id/app_bar_layout"
            app:layout_anchorGravity="bottom|start"
            android:visibility="gone"
            tools:visibility="visible"/>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Bottom Navigation - Now outside the CoordinatorLayout and fixed at bottom -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#1A1A3A"
        android:elevation="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:menu="@menu/bottom_nav_menu"
        style="@style/CustomBottomNavigation" />

</androidx.constraintlayout.widget.ConstraintLayout>