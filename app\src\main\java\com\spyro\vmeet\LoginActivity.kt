package com.spyro.vmeet

import android.app.DatePickerDialog
import android.content.Intent
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.textfield.TextInputLayout
import org.json.JSONObject
import java.io.BufferedReader
import java.io.OutputStreamWriter
import java.net.HttpURLConnection
import java.net.URL
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.concurrent.thread
import android.content.SharedPreferences
import android.util.Log
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.result.contract.ActivityResultContracts
import com.spyro.vmeet.notifications.NotificationService
import com.google.firebase.messaging.FirebaseMessaging
import android.content.Context
import com.spyro.vmeet.activity.EmailVerificationActivity
import com.spyro.vmeet.activity.ForgotPasswordActivity
import com.spyro.vmeet.activity.BaseActivity
import com.spyro.vmeet.utils.EmailUtils
import com.spyro.vmeet.utils.VerificationCodeUtils
import com.spyro.vmeet.utils.TokenManager
import com.spyro.vmeet.util.DeviceInfoUtils

class LoginActivity : BaseActivity() {
    private var isLoginMode = true
    private val API_URL = "http://77.110.116.89:3000"
    private lateinit var tokenManager: TokenManager

    // Listas de opciones para género y sexualidad
    private val genderOptions = listOf(
        "Hombre", "Mujer", "No binario", "Género fluido", "Agénero",
        "Bigénero", "Transgénero", "Genderqueer", "Demigénero", "Otro"
    )

    private val sexualityOptions = listOf(
        "Heterosexual", "Homosexual", "Bisexual", "Pansexual", "Asexual",
        "Demisexual", "Queer", "Polisexual", "Androsexual", "Ginosexual", "Otro"
    )

    // Request permission launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            Log.d("LoginActivity", "Notification permission granted")
            if (isLoginMode) {
                handleLogin()
            } else {
                handleRegister()
            }
        } else {
            Log.d("LoginActivity", "Notification permission denied")
            Toast.makeText(
                this,
                "No se pudo obtener acceso a la red",
                Toast.LENGTH_SHORT
            ).show()
            if (isLoginMode) {
                handleLogin()
            } else {
                handleRegister()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        // Initialize TokenManager
        tokenManager = TokenManager(this)

        // Check if user is already logged in
        if (tokenManager.isLoggedIn()) {
            // Verify token is still valid
            tokenManager.verifyTokenAsync { isValid ->
                runOnUiThread {
                    if (isValid) {
                        // Token is valid, redirect to main activity
                        val userId = tokenManager.getUserId()
                        val username = tokenManager.getUsername() ?: ""
                        onLoginSuccess(userId, username)
                    } else {
                        // Token is invalid, clear tokens and continue with login
                        tokenManager.clearTokens()
                    }
                }
            }
        }

        val usernameEditText = findViewById<EditText>(R.id.editTextUsername)
        val emailEditText = findViewById<EditText>(R.id.editTextEmail)
        val emailLayout = findViewById<TextInputLayout>(R.id.textInputLayoutEmail)
        val passwordEditText = findViewById<EditText>(R.id.editTextPassword)
        val loginButton = findViewById<Button>(R.id.buttonLogin)
        val registerButton = findViewById<Button>(R.id.buttonRegister)
        val toggleButton = findViewById<Button>(R.id.buttonToggleMode)
        val forgotPasswordText = findViewById<TextView>(R.id.textViewForgotPassword)
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        val titleText = findViewById<TextView>(R.id.textViewTitle)

        // Nuevos campos para género y sexualidad
        val genderLayout = findViewById<TextInputLayout>(R.id.textInputLayoutGender)
        val sexualityLayout = findViewById<TextInputLayout>(R.id.textInputLayoutSexuality)
        val genderDropdown = findViewById<AutoCompleteTextView>(R.id.dropdownGender)
        val sexualityDropdown = findViewById<AutoCompleteTextView>(R.id.dropdownSexuality)

        // Campo para fecha de nacimiento
        val birthdateLayout = findViewById<TextInputLayout>(R.id.textInputLayoutBirthdate)
        val birthdateEditText = findViewById<EditText>(R.id.editTextBirthdate)

        // Casilla de política de privacidad
        val privacyPolicyCheckbox = findViewById<CheckBox>(R.id.checkboxPrivacyPolicy)

        // Configurar los adaptadores para los dropdowns
        val genderAdapter = ArrayAdapter(this, R.layout.dropdown_item, genderOptions)
        val sexualityAdapter = ArrayAdapter(this, R.layout.dropdown_item, sexualityOptions)

        genderDropdown.setAdapter(genderAdapter)
        sexualityDropdown.setAdapter(sexualityAdapter)

        // Configurar el selector de fecha para el campo de fecha de nacimiento
        birthdateEditText.setOnClickListener {
            showDatePickerDialog()
        }

        // Hacer que el texto de la política de privacidad sea clickeable
        val privacyPolicyText = SpannableString("Acepto la política de privacidad")
        privacyPolicyText.setSpan(
            object : ClickableSpan() {
                override fun onClick(widget: View) {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://rangerpowerx.github.io/vmeet/"))
                    startActivity(intent)
                }

                override fun updateDrawState(ds: TextPaint) {
                    super.updateDrawState(ds)
                    ds.isUnderlineText = true
                    ds.color = getColor(R.color.comfy_blue)
                }
            },
            20, // Inicio de "política de privacidad"
            privacyPolicyText.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        privacyPolicyCheckbox.text = privacyPolicyText
        privacyPolicyCheckbox.movementMethod = LinkMovementMethod.getInstance()

        val logoContainer = findViewById<FrameLayout>(R.id.logoContainer)
        val logoRing = findViewById<ImageView>(R.id.logoRing)
        val logoImage = findViewById<ImageView>(R.id.logoImage)
        val inputFieldsContainer = findViewById<LinearLayout>(R.id.inputFieldsContainer)
        val buttonContainer = findViewById<LinearLayout>(R.id.buttonContainer)

        val pulseAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.pulse)
        val rotateAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.rotate)
        val slideUpAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.slide_up)
        val fadeInAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.fade_in)

        logoImage.startAnimation(pulseAnimation)
        logoRing.startAnimation(rotateAnimation)
        inputFieldsContainer.startAnimation(slideUpAnimation)
        buttonContainer.startAnimation(fadeInAnimation)

        animateDecorationLines()

        fun setMode(login: Boolean) {
            isLoginMode = login
            if (login) {
                loginButton.visibility = View.VISIBLE
                registerButton.visibility = View.GONE
                emailLayout.visibility = View.GONE
                genderLayout.visibility = View.GONE
                sexualityLayout.visibility = View.GONE
                birthdateLayout.visibility = View.GONE
                privacyPolicyCheckbox.visibility = View.GONE
                forgotPasswordText.visibility = View.VISIBLE
                toggleButton.text = "¿No tienes cuenta? Regístrate"
                titleText.text = "Iniciar Sesión"
            } else {
                loginButton.visibility = View.GONE
                registerButton.visibility = View.VISIBLE
                emailLayout.visibility = View.VISIBLE
                genderLayout.visibility = View.VISIBLE
                sexualityLayout.visibility = View.VISIBLE
                birthdateLayout.visibility = View.VISIBLE
                privacyPolicyCheckbox.visibility = View.VISIBLE
                forgotPasswordText.visibility = View.GONE
                toggleButton.text = "¿Ya tienes cuenta? Inicia sesión"
                titleText.text = "Registrarse"
            }

            inputFieldsContainer.startAnimation(slideUpAnimation)
            buttonContainer.startAnimation(fadeInAnimation)
        }
        setMode(true)

        toggleButton.setOnClickListener {
            setMode(!isLoginMode)
        }

        loginButton.setOnClickListener {
            requestNotificationPermission()
        }

        registerButton.setOnClickListener {
            requestNotificationPermission()
        }

        // Configurar el listener para el texto de "¿Olvidaste tu contraseña?"
        forgotPasswordText.setOnClickListener {
            // Abrir la actividad de recuperación de contraseña
            val intent = Intent(this, com.spyro.vmeet.activity.ForgotPasswordActivity::class.java)
            startActivity(intent)
        }
    }

    private fun handleLogin() {
        val usernameEditText = findViewById<EditText>(R.id.editTextUsername)
        val passwordEditText = findViewById<EditText>(R.id.editTextPassword)

        val username = usernameEditText.text.toString()
        val password = passwordEditText.text.toString()

        if (username.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "Por favor complete todos los campos", Toast.LENGTH_SHORT).show()
            return
        }

        val deviceSerial = DeviceInfoUtils.getDeviceSerial(this)

        // Get device IP asynchronously before proceeding with login
        DeviceInfoUtils.getDeviceIp(this) { deviceIp ->
            thread {
            try {
                val url = URL("$API_URL/auth/login")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "POST"
                conn.doOutput = true
                conn.setRequestProperty("Content-Type", "application/json")

                val data = JSONObject().apply {
                    put("username", username)
                    put("password", password)
                    put("deviceSerial", deviceSerial)
                    put("lastIp", deviceIp)
                }

                val jsonString = data.toString()
                Log.d("LoginUser", "Sending JSON: $jsonString")

                val outputStream = conn.outputStream
                val writer = OutputStreamWriter(outputStream, "UTF-8")
                writer.write(jsonString)
                writer.flush()
                writer.close()
                outputStream.close()

                val responseCode = conn.responseCode
                Log.d("LoginUser", "Response code: $responseCode")

                val inputStream = if (responseCode >= 400) conn.errorStream else conn.inputStream
                val reader = BufferedReader(inputStream.reader())
                val response = reader.readText()
                reader.close()

                Log.d("LoginUser", "Response: $response")

                if (responseCode == 200) {
                    try {
                        val jsonResponse = JSONObject(response)
                        Log.d("LoginUser", "Parsing response: $jsonResponse")

                        // Check if login was successful
                        val success = jsonResponse.optBoolean("success", false)
                        if (!success) {
                            Handler(Looper.getMainLooper()).post {
                                Toast.makeText(this@LoginActivity, "Error de inicio de sesión", Toast.LENGTH_SHORT).show()
                            }
                            return@thread
                        }

                        // Extract tokens
                        val accessToken = jsonResponse.optString("accessToken", "")
                        val refreshToken = jsonResponse.optString("refreshToken", "")

                        if (accessToken.isEmpty() || refreshToken.isEmpty()) {
                            Log.e("LoginUser", "Missing tokens in response")
                            Handler(Looper.getMainLooper()).post {
                                Toast.makeText(this@LoginActivity, "Error de autenticación", Toast.LENGTH_SHORT).show()
                            }
                            return@thread
                        }

                        // Extract user info
                        var userId = jsonResponse.optInt("userId", 0)
                        if (userId == 0) {
                            userId = jsonResponse.optInt("user_id", 0)
                        }

                        if (userId == 0 && jsonResponse.has("user")) {
                            val userObj = jsonResponse.getJSONObject("user")
                            userId = userObj.optInt("id", 0)
                            Log.d("LoginUser", "Found userId $userId in user object")
                        }

                        Log.d("LoginUser", "Final userId extracted: $userId")

                        if (userId > 0) {
                            // Verificar si el correo electrónico está verificado
                            var emailVerified = true // Por defecto asumimos que está verificado
                            var email = ""

                            // Imprimir la respuesta completa para depuración
                            Log.d("LoginUser", "Full response JSON: $jsonResponse")

                            // Intentar obtener la información del objeto user
                            if (jsonResponse.has("user")) {
                                val userObj = jsonResponse.getJSONObject("user")
                                Log.d("LoginUser", "User object: $userObj")

                                // Verificar si el objeto user tiene la propiedad email_verified
                                if (userObj.has("email_verified")) {
                                    // Intentar obtener como booleano
                                    emailVerified = userObj.optBoolean("email_verified", true)
                                    Log.d("LoginUser", "Email verified from JSON as boolean: $emailVerified")
                                } else {
                                    Log.d("LoginUser", "email_verified property not found in user object")
                                }

                                // Verificar si hay una propiedad email_verified como número (0 o 1)
                                if (!userObj.isNull("email_verified")) {
                                    try {
                                        val emailVerifiedInt = userObj.optInt("email_verified", 1)
                                        emailVerified = emailVerifiedInt == 1
                                        Log.d("LoginUser", "Email verified from JSON as int: $emailVerifiedInt, converted to boolean: $emailVerified")
                                    } catch (e: Exception) {
                                        Log.e("LoginUser", "Error parsing email_verified as int", e)
                                    }
                                }

                                email = userObj.optString("email", "")
                                Log.d("LoginUser", "Email from user object: $email")
                            } else {
                                Log.d("LoginUser", "User object not found in response")
                            }

                            // Forzar la redirección a la pantalla de verificación para pruebas
                            // Comentar o eliminar esta línea en producción
                            // emailVerified = false

                            // Extract additional user info for token storage
                            var userEmail = ""
                            var userRole = "user"
                            var isAdmin = false

                            if (jsonResponse.has("user")) {
                                val userObj = jsonResponse.getJSONObject("user")
                                userEmail = userObj.optString("email", "")
                                userRole = userObj.optString("role", "user")
                                isAdmin = userObj.optBoolean("isAdmin", false)

                                // Check email verification
                                emailVerified = userObj.optBoolean("email_verified", true)
                                if (!userObj.isNull("email_verified")) {
                                    try {
                                        val emailVerifiedInt = userObj.optInt("email_verified", 1)
                                        emailVerified = emailVerifiedInt == 1
                                    } catch (e: Exception) {
                                        Log.e("LoginUser", "Error parsing email_verified as int", e)
                                    }
                                }

                                email = userEmail
                            }

                            if (!emailVerified && email.isNotEmpty()) {
                                // Si el correo no está verificado, redirigir a la pantalla de verificación
                                Log.d("LoginUser", "Email not verified, redirecting to verification screen")
                                Handler(Looper.getMainLooper()).post {
                                    // Generar un nuevo código de verificación
                                    val verificationCode = VerificationCodeUtils.generateVerificationCode()

                                    // Guardar el código localmente
                                    VerificationCodeUtils.saveVerificationCode(this@LoginActivity, email, verificationCode, "verification")

                                    // Solicitar al servidor que envíe un nuevo código
                                    thread {
                                        try {
                                            val resendUrl = URL("$API_URL/auth/resend_verification")
                                            val resendConn = resendUrl.openConnection() as HttpURLConnection
                                            resendConn.requestMethod = "POST"
                                            resendConn.doOutput = true
                                            resendConn.setRequestProperty("Content-Type", "application/json")

                                            val resendData = JSONObject().apply {
                                                put("email", email)
                                            }

                                            val resendWr = OutputStreamWriter(resendConn.outputStream)
                                            resendWr.write(resendData.toString())
                                            resendWr.flush()

                                            resendConn.disconnect()
                                        } catch (e: Exception) {
                                            Log.e("LoginUser", "Error requesting verification code", e)
                                        }
                                    }

                                    // Redirigir a la pantalla de verificación
                                    val intent = Intent(this@LoginActivity, EmailVerificationActivity::class.java)
                                    intent.putExtra("USER_ID", userId)
                                    intent.putExtra("USERNAME", username)
                                    intent.putExtra("EMAIL", email)
                                    intent.putExtra("VERIFICATION_CODE", verificationCode)
                                    intent.putExtra("IS_PASSWORD_RESET", false)
                                    startActivity(intent)

                                    showSpamWarningDialog("Por favor verifica tu correo electrónico para continuar")
                                }
                            } else {
                                // Save tokens securely
                                tokenManager.saveTokens(
                                    accessToken = accessToken,
                                    refreshToken = refreshToken,
                                    userId = userId,
                                    username = username,
                                    email = userEmail,
                                    role = userRole,
                                    isAdmin = isAdmin
                                )

                                Log.d("LoginUser", "Tokens saved successfully")

                                // Si el correo está verificado, proceder normalmente
                                onLoginSuccess(userId, username)
                            }
                        } else {
                            Log.e("LoginUser", "Could not extract valid User ID from login response: $response")
                            Handler(Looper.getMainLooper()).post {
                                Toast.makeText(this@LoginActivity, "Error de inicio de sesión", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("LoginUser", "Error parsing login response", e)
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(this@LoginActivity, "Error de inicio de sesión", Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    try {
                        val jsonResponse = JSONObject(response)
                        val errorMsg = jsonResponse.optString("error", "Error desconocido")
                        Log.e("LoginUser", "Login error: $errorMsg")
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(this@LoginActivity, errorMsg, Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("LoginUser", "Error parsing error response", e)
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(this@LoginActivity, "Error de inicio de sesión", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("LoginUser", "Exception during login", e)
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(this@LoginActivity, "Error de conexión", Toast.LENGTH_SHORT).show()
                }
            }
        }
        }
    }

    private fun handleRegister() {
        val usernameEditText = findViewById<EditText>(R.id.editTextUsername)
        val emailEditText = findViewById<EditText>(R.id.editTextEmail)
        val passwordEditText = findViewById<EditText>(R.id.editTextPassword)
        val genderDropdown = findViewById<AutoCompleteTextView>(R.id.dropdownGender)
        val sexualityDropdown = findViewById<AutoCompleteTextView>(R.id.dropdownSexuality)
        val birthdateEditText = findViewById<EditText>(R.id.editTextBirthdate)

        val username = usernameEditText.text.toString()
        val email = emailEditText.text.toString()
        val password = passwordEditText.text.toString()
        val gender = genderDropdown.text.toString()
        val sexuality = sexualityDropdown.text.toString()
        val birthdate = birthdateEditText.text.toString()

        if (username.isEmpty() || email.isEmpty() || password.isEmpty()) {
            Toast.makeText(this, "Por favor complete los campos obligatorios", Toast.LENGTH_SHORT).show()
            return
        }

        if (birthdate.isEmpty()) {
            Toast.makeText(this, "Por favor seleccione su fecha de nacimiento", Toast.LENGTH_SHORT).show()
            return
        }

        // Verificar que la casilla de política de privacidad esté marcada
        val privacyPolicyAccepted = findViewById<CheckBox>(R.id.checkboxPrivacyPolicy).isChecked
        if (!privacyPolicyAccepted) {
            Toast.makeText(this, "Debe aceptar la política de privacidad para registrarse", Toast.LENGTH_SHORT).show()
            return
        }

        val deviceSerial = DeviceInfoUtils.getDeviceSerial(this)

        // Get device IP asynchronously before proceeding with registration
        DeviceInfoUtils.getDeviceIp(this) { deviceIp ->

        thread {
            try {
                val url = URL("$API_URL/auth/register")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "POST"
                conn.doOutput = true
                conn.setRequestProperty("Content-Type", "application/json")

                val data = JSONObject().apply {
                    put("username", username)
                    put("email", email)
                    put("password", password)
                    put("deviceSerial", deviceSerial)
                    put("lastIp", deviceIp)
                    put("birthdate", birthdate)
                    // Añadir los nuevos campos solo si no están vacíos
                    if (gender.isNotEmpty()) put("gender", gender)
                    if (sexuality.isNotEmpty()) put("sexuality", sexuality)
                }

                val jsonString = data.toString()
                Log.d("RegisterUser", "Sending JSON: $jsonString")

                val outputStream = conn.outputStream
                val writer = OutputStreamWriter(outputStream, "UTF-8")
                writer.write(jsonString)
                writer.flush()
                writer.close()
                outputStream.close()

                val responseCode = conn.responseCode
                Log.d("RegisterUser", "Response code: $responseCode")

                val inputStream = if (responseCode >= 400) conn.errorStream else conn.inputStream
                val reader = BufferedReader(inputStream.reader())
                val response = reader.readText()
                reader.close()

                Log.d("RegisterUser", "Response: $response")

                if (responseCode == 200) {
                    try {
                        val jsonResponse = JSONObject(response)
                        Log.d("RegisterUser", "JSON Response: $jsonResponse")

                        val success = jsonResponse.optBoolean("success", false)

                        if (success) {
                            val userId = jsonResponse.optInt("userId", 0)
                            Log.d("RegisterUser", "Extracted userId from response: $userId")

                            if (userId > 0) {
                                // Generar código de verificación
                                val verificationCode = VerificationCodeUtils.generateVerificationCode()

                                // Guardar código de verificación
                                VerificationCodeUtils.saveVerificationCode(this@LoginActivity, email, verificationCode, "verification")

                                // Enviar correo de verificación
                                Handler(Looper.getMainLooper()).post {
                                    showSpamWarningDialog("Registro exitoso. Se ha enviado un correo de verificación a tu dirección de email.")
                                }

                                EmailUtils.sendVerificationEmail(this@LoginActivity, email, username, verificationCode) { success, errorMsg ->
                                    Handler(Looper.getMainLooper()).post {
                                        if (success) {
                                            // Reproducir sonido de bienvenida
                                            try {
                                                val mediaPlayer = MediaPlayer.create(this@LoginActivity, R.raw.welcome)
                                                mediaPlayer.setOnCompletionListener { mp ->
                                                    mp.release()
                                                }
                                                mediaPlayer.start()
                                                Log.d("RegisterUser", "Welcome sound played successfully")
                                            } catch (e: Exception) {
                                                Log.e("RegisterUser", "Error playing welcome sound", e)
                                            }

                                            // Ir a la actividad de verificación
                                            val intent = Intent(this@LoginActivity, EmailVerificationActivity::class.java)
                                            intent.putExtra("USER_ID", userId)
                                            intent.putExtra("USERNAME", username)
                                            intent.putExtra("EMAIL", email)
                                            intent.putExtra("VERIFICATION_CODE", verificationCode)
                                            startActivity(intent)
                                            finish()
                                        } else {
                                            Toast.makeText(this@LoginActivity, "Error al enviar correo de verificación: $errorMsg", Toast.LENGTH_SHORT).show()

                                            // Guardar datos de sesión de todos modos
                                            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
                                            val editor = prefs.edit()
                                            editor.putInt("USER_ID", userId)
                                            editor.putString("USERNAME", username)
                                            editor.putBoolean("IS_FIRST_LOGIN", true)
                                            editor.apply()

                                            // Ir a la actividad principal
                                            val intent = Intent(this@LoginActivity, SwipeActivity::class.java)
                                            intent.putExtra("USER_ID", userId)
                                            startActivity(intent)
                                            finish()
                                        }
                                    }
                                }
                            } else {
                                Log.e("RegisterUser", "Registration successful but no valid userId returned: $response")
                                Handler(Looper.getMainLooper()).post {
                                    Toast.makeText(this@LoginActivity, "Registro exitoso, pero no se pudo obtener tu ID de usuario.", Toast.LENGTH_SHORT).show()
                                }
                            }
                        } else {
                            val message = jsonResponse.optString("message", "Registro completado pero con un error desconocido")
                            Log.w("RegisterUser", "Registration response did not indicate success: $message")
                            Handler(Looper.getMainLooper()).post {
                                Toast.makeText(this@LoginActivity, message, Toast.LENGTH_SHORT).show()
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("RegisterUser", "Error parsing registration response", e)
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(this@LoginActivity, "Error procesando la respuesta: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    try {
                        val jsonResponse = JSONObject(response)
                        val errorMsg = jsonResponse.optString("error", "Error desconocido")
                        Log.e("RegisterUser", "Registration error: $errorMsg")
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(this@LoginActivity, errorMsg, Toast.LENGTH_SHORT).show()
                        }
                    } catch (e: Exception) {
                        Log.e("RegisterUser", "Error parsing error response", e)
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(this@LoginActivity, "Error de respuesta: $response", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("RegisterUser", "Exception during registration", e)
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(this@LoginActivity, "Error de conexión: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
        }
    }

    private fun startNotificationService(userId: Int) {
        try {
            Log.d("LoginActivity", "Starting notification service for user $userId")
            val serviceIntent = Intent(this, NotificationService::class.java).apply {
                putExtra("USER_ID", userId)
            }
            startService(serviceIntent)

            registerCurrentFCMToken(userId)
        } catch (e: Exception) {
            Log.e("LoginActivity", "Error starting notification service", e)
        }
    }

    private fun registerCurrentFCMToken(userId: Int) {
        val sharedPreferences = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
        val fcmToken = sharedPreferences.getString("fcm_token", null)

        if (!fcmToken.isNullOrEmpty()) {
            Log.d("LoginActivity", "Registering FCM token for user $userId")
            com.spyro.vmeet.notifications.TokenUtils.registerTokenWithBackend(applicationContext, userId, fcmToken)
        } else {
            Log.d("LoginActivity", "FCM token not found, requesting new token for user $userId")
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    val token = task.result
                    sharedPreferences.edit().putString("fcm_token", token).apply()
                    com.spyro.vmeet.notifications.TokenUtils.registerTokenWithBackend(applicationContext, userId, token)
                    Log.d("LoginActivity", "New FCM token registered for user $userId")
                } else {
                    Log.e("LoginActivity", "Failed to get FCM token", task.exception)
                }
            }
        }
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            when {
                checkSelfPermission(Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED -> {
                    Log.d("LoginActivity", "Notification permission already granted")
                    if (isLoginMode) {
                        handleLogin()
                    } else {
                        handleRegister()
                    }
                }
                shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS) -> {
                    Toast.makeText(
                        this,
                        "Las notificaciones son necesarias para recibir mensajes nuevos",
                        Toast.LENGTH_LONG
                    ).show()
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
                else -> {
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
            }
        } else {
            if (isLoginMode) {
                handleLogin()
            } else {
                handleRegister()
            }
        }
    }

    private fun animateDecorationLines() {
        val topLine1 = findViewById<View>(R.id.topLine1)
        val topLine2 = findViewById<View>(R.id.topLine2)
        val bottomLine1 = findViewById<View>(R.id.bottomLine1)
        val bottomLine2 = findViewById<View>(R.id.bottomLine2)

        animateLine(topLine1, 100f, 180f, 0f, 30f, 3000, 0)
        animateLine(topLine2, 50f, 100f, 50f, 20f, 4000, 1000)

        animateLine(bottomLine1, 150f, 200f, -30f, 0f, 3500, 500)
        animateLine(bottomLine2, 80f, 120f, -60f, 0f, 4500, 1500)
    }

    private fun animateLine(line: View, minWidth: Float, maxWidth: Float, minMargin: Float, maxMargin: Float, duration: Long, delay: Long) {
        val widthAnimator = android.animation.ValueAnimator.ofFloat(minWidth, maxWidth)
        widthAnimator.addUpdateListener { valueAnimator ->
            val value = valueAnimator.animatedValue as Float
            val params = line.layoutParams
            params.width = value.toInt()
            line.layoutParams = params
        }
        widthAnimator.duration = duration
        widthAnimator.repeatMode = android.animation.ValueAnimator.REVERSE
        widthAnimator.repeatCount = android.animation.ValueAnimator.INFINITE
        widthAnimator.startDelay = delay
        widthAnimator.start()

        val marginAnimator = android.animation.ValueAnimator.ofFloat(minMargin, maxMargin)
        marginAnimator.addUpdateListener { valueAnimator ->
            val value = valueAnimator.animatedValue as Float
            val params = line.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

            if (line.id == R.id.topLine1 || line.id == R.id.topLine2) {
                params.marginStart = value.toInt()
            } else {
                params.marginEnd = value.toInt()
            }

            line.layoutParams = params
        }
        marginAnimator.duration = duration + 500
        marginAnimator.repeatMode = android.animation.ValueAnimator.REVERSE
        marginAnimator.repeatCount = android.animation.ValueAnimator.INFINITE
        marginAnimator.startDelay = delay + 250
        marginAnimator.start()
    }

    private fun showDatePickerDialog() {
        val calendar = Calendar.getInstance()

        // Default to 18 years ago
        calendar.add(Calendar.YEAR, -18)

        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(
            this,
            { _, selectedYear, selectedMonth, selectedDayOfMonth ->
                // Format the selected date
                val selectedDate = Calendar.getInstance()
                selectedDate.set(selectedYear, selectedMonth, selectedDayOfMonth)
                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val formattedDate = sdf.format(selectedDate.time)

                // Verificar que la edad sea al menos 18 años
                val today = Calendar.getInstance()
                val age = today.get(Calendar.YEAR) - selectedYear -
                    if (today.get(Calendar.MONTH) < selectedMonth ||
                        (today.get(Calendar.MONTH) == selectedMonth && today.get(Calendar.DAY_OF_MONTH) < selectedDayOfMonth)) 1 else 0

                if (age < 18) {
                    Toast.makeText(this, "Debes tener al menos 18 años para registrarte", Toast.LENGTH_SHORT).show()
                } else {
                    findViewById<EditText>(R.id.editTextBirthdate).setText(formattedDate)
                }
            },
            year,
            month,
            day
        )

        // Calcular la fecha máxima (18 años atrás desde hoy)
        val maxDate = Calendar.getInstance()
        maxDate.add(Calendar.YEAR, -18)

        // Establecer la fecha máxima (no se pueden seleccionar fechas posteriores a 18 años atrás)
        datePickerDialog.datePicker.maxDate = maxDate.timeInMillis

        datePickerDialog.show()
    }

    private fun onLoginSuccess(userId: Int, username: String) {
        val sharedPreferences = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putInt("USER_ID", userId)
        editor.putString("USERNAME", username)
        editor.apply()

        startNotificationService(userId)
        registerCurrentFCMToken(userId)

        val intent = Intent(this, SwipeActivity::class.java)
        intent.putExtra("USER_ID", userId)
        startActivity(intent)
        finish()
    }

    private fun showSpamWarningDialog(message: String) {
        AlertDialog.Builder(this)
            .setTitle("Correo enviado")
            .setMessage("$message\n\nSi no lo encuentras en tu bandeja de entrada, revisa la carpeta de spam o correo no deseado. Marca el correo como 'No es spam' para recibir futuros mensajes correctamente.")
            .setPositiveButton("Entendido") { dialog, _ ->
                dialog.dismiss()
            }
            .setIcon(android.R.drawable.ic_dialog_email)
            .show()
    }

    /**
     * Logout user and clear all tokens
     */
    fun logout() {
        thread {
            try {
                val refreshToken = tokenManager.getRefreshToken()

                // Call logout endpoint if we have a refresh token
                if (!refreshToken.isNullOrEmpty()) {
                    val url = URL("$API_URL/auth/logout")
                    val conn = url.openConnection() as HttpURLConnection
                    conn.requestMethod = "POST"
                    conn.doOutput = true
                    conn.setRequestProperty("Content-Type", "application/json")
                    conn.setRequestProperty("Authorization", "Bearer ${tokenManager.getAccessToken()}")

                    val data = JSONObject().apply {
                        put("refreshToken", refreshToken)
                    }

                    val outputStream = conn.outputStream
                    val writer = OutputStreamWriter(outputStream, "UTF-8")
                    writer.write(data.toString())
                    writer.flush()
                    writer.close()
                    outputStream.close()

                    conn.disconnect()
                }
            } catch (e: Exception) {
                Log.e("LoginActivity", "Error during logout", e)
            } finally {
                // Always clear local tokens
                tokenManager.clearTokens()

                // Redirect to login
                runOnUiThread {
                    val intent = Intent(this@LoginActivity, LoginActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    startActivity(intent)
                    finish()
                }
            }
        }
    }
}