package com.spyro.vmeet.utils;

/**
 * Utilidad para formatear fechas en formato español europeo
 * Garantiza que todas las fechas se muestren en formato dd/MM/yyyy
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u0004J\u000e\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0004J\u000e\u0010\u0014\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0004J\u000e\u0010\u0015\u001a\u00020\u00042\u0006\u0010\u0016\u001a\u00020\u0004J\u000e\u0010\u0017\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0004J\u000e\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u0004J\u000e\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u001bJ\u0006\u0010\u001c\u001a\u00020\u000fJ\u0018\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u0002J\u0018\u0010\"\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020 H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/spyro/vmeet/utils/SpanishDateFormatter;", "", "()V", "TAG", "", "inputFormatDate", "Ljava/text/SimpleDateFormat;", "inputFormatISO", "outputFormatDate", "outputFormatDateShort", "outputFormatDateTime", "outputFormatDayName", "outputFormatMonthYear", "outputFormatTime", "spanishLocale", "Ljava/util/Locale;", "formatBirthdate", "birthdateString", "formatDate", "isoDateString", "formatDateTime", "formatSimpleDate", "dateString", "formatSmartTimestamp", "formatTime", "formatTimestamp", "epochMillis", "", "getSpanishLocale", "isThisWeek", "", "now", "Ljava/util/Calendar;", "messageTime", "isYesterday", "app_debug"})
public final class SpanishDateFormatter {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "SpanishDateFormatter";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.Locale spanishLocale = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat inputFormatISO = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat inputFormatDate = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat outputFormatDate = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat outputFormatDateTime = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat outputFormatTime = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat outputFormatDateShort = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat outputFormatDayName = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.text.SimpleDateFormat outputFormatMonthYear = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.utils.SpanishDateFormatter INSTANCE = null;
    
    private SpanishDateFormatter() {
        super();
    }
    
    /**
     * Formatea una fecha ISO 8601 a formato español dd/MM/yyyy HH:mm
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatDateTime(@org.jetbrains.annotations.NotNull()
    java.lang.String isoDateString) {
        return null;
    }
    
    /**
     * Formatea una fecha ISO 8601 a formato español dd/MM/yyyy
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatDate(@org.jetbrains.annotations.NotNull()
    java.lang.String isoDateString) {
        return null;
    }
    
    /**
     * Formatea una fecha simple yyyy-MM-dd a formato español dd/MM/yyyy
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatSimpleDate(@org.jetbrains.annotations.NotNull()
    java.lang.String dateString) {
        return null;
    }
    
    /**
     * Formatea solo la hora de una fecha ISO 8601
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatTime(@org.jetbrains.annotations.NotNull()
    java.lang.String isoDateString) {
        return null;
    }
    
    /**
     * Formatea una fecha con lógica inteligente (hoy, ayer, día de la semana, fecha)
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatSmartTimestamp(@org.jetbrains.annotations.NotNull()
    java.lang.String isoDateString) {
        return null;
    }
    
    /**
     * Formatea una fecha de nacimiento en formato español legible
     * Ejemplo: "21 de enero de 1996"
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatBirthdate(@org.jetbrains.annotations.NotNull()
    java.lang.String birthdateString) {
        return null;
    }
    
    /**
     * Formatea un timestamp en milisegundos
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatTimestamp(long epochMillis) {
        return null;
    }
    
    /**
     * Obtiene el locale español para usar en otros formatters
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Locale getSpanishLocale() {
        return null;
    }
    
    private final boolean isYesterday(java.util.Calendar now, java.util.Calendar messageTime) {
        return false;
    }
    
    private final boolean isThisWeek(java.util.Calendar now, java.util.Calendar messageTime) {
        return false;
    }
}