<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="#1A1A3A"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">
        
        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@android:drawable/ic_dialog_alert"
                android:tint="#FFC107"
                android:layout_marginEnd="12dp"/>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Reportar Usuario"
                android:textSize="24sp"
                android:textColor="@android:color/white"
                android:textStyle="bold"/>
        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#303060"
            android:layout_marginBottom="16dp"/>

        <!-- Report reason section -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Selecciona el motivo del reporte:"
            android:textColor="#B4B4FF"
            android:textSize="16sp"
            android:layout_marginBottom="16dp"/>
            
        <RadioGroup
            android:id="@+id/radioGroupReportReason"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp">

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:id="@+id/rbInappropriateContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Contenido inapropiado"
                android:textColor="@android:color/white"
                android:buttonTint="#FF4081"
                android:padding="8dp"/>

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:id="@+id/rbHarassment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Acoso o comportamiento abusivo"
                android:textColor="@android:color/white"
                android:buttonTint="#FF4081"
                android:padding="8dp"/>

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:id="@+id/rbFakeProfile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Perfil falso o suplantación"
                android:textColor="@android:color/white"
                android:buttonTint="#FF4081"
                android:padding="8dp"/>

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:id="@+id/rbSpam"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Spam o contenido comercial"
                android:textColor="@android:color/white"
                android:buttonTint="#FF4081"
                android:padding="8dp"/>

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:id="@+id/rbOther"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Otro motivo"
                android:textColor="@android:color/white"
                android:buttonTint="#FF4081"
                android:padding="8dp"/>
            
        </RadioGroup>
        
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/tilReportDetails"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="Detalles adicionales (opcional)"
            app:boxBackgroundColor="#252550"
            app:boxStrokeColor="#FF4081"
            app:hintTextColor="#FF4081"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextDetails"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:minLines="3"
                android:gravity="top"
                android:textColor="@android:color/white"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonCancel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cancelar"
                android:textColor="#B4B4FF"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_marginEnd="8dp"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonSendReport"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Enviar Reporte"
                android:textColor="@android:color/white"
                app:backgroundTint="#FF4081"
                app:cornerRadius="8dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
