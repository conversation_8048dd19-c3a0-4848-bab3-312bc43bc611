<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:duration="1500"
    android:fillAfter="true"
    android:interpolator="@android:anim/accelerate_decelerate_interpolator"
    android:repeatCount="infinite"
    android:repeatMode="restart">
    
    <!-- Animación de brillo (alpha) -->
    <alpha
        android:fromAlpha="0.7"
        android:toAlpha="1.0"
        android:duration="750" />
    
    <!-- Animación de escala suave -->
    <scale
        android:fromXScale="0.98"
        android:toXScale="1.02"
        android:fromYScale="0.98"
        android:toYScale="1.02"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="1500" />
        
    <!-- Rotación sutil -->
    <rotate
        android:fromDegrees="-1"
        android:toDegrees="1"
        android:pivotX="50%"
        android:pivotY="50%"
        android:duration="1500" />
</set>
