package com.spyro.vmeet.ui.community

import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.spyro.vmeet.R
import com.spyro.vmeet.data.community.Post
import com.spyro.vmeet.utils.SpanishDateFormatter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import android.media.MediaPlayer
import android.widget.LinearLayout
import android.widget.SeekBar
import android.os.Handler
import android.os.Looper
import java.io.IOException
import android.util.Log
import android.widget.Toast
import android.content.Intent
import android.net.Uri
import android.view.MenuItem
import android.widget.PopupMenu
import android.content.Context
import java.util.regex.Pattern
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.AbstractYouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.fragment.app.FragmentActivity

class PostAdapter(private var posts: MutableList<Post>) : RecyclerView.Adapter<PostAdapter.PostViewHolder>() {

    // Define listeners for interactions
    var onLikeClickListener: ((Post) -> Unit)? = null
    var onCommentClickListener: ((Post) -> Unit)? = null
    var onUserClickListener: ((Post) -> Unit)? = null
    var onImageClickListener: ((Post) -> Unit)? = null
    var onDeleteClickListener: ((Post) -> Unit)? = null

    private val API_URL = "http://77.110.116.89:3000" // Added base URL
    private val activePlayers = mutableMapOf<Int, MediaPlayer>() // To manage active players by position

    // Reference to RecyclerView for cleanup purposes
    private var recyclerView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        this.recyclerView = null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PostViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_post, parent, false)
        return PostViewHolder(view)
    }

    override fun onBindViewHolder(holder: PostViewHolder, position: Int) {
        val post = posts[position]
        // Release any player that might be active for this holder's previous item
        holder.releaseMediaPlayer()
        holder.bind(post)
    }

    override fun getItemCount(): Int = posts.size

    override fun onViewRecycled(holder: PostViewHolder) {
        super.onViewRecycled(holder)
        holder.releaseMediaPlayer() // Release player when view is recycled
        activePlayers.remove(holder.bindingAdapterPosition)
    }

    fun releaseAllPlayers() {
        // Release MediaPlayer instances
        activePlayers.values.forEach { player ->
            try {
                if (player.isPlaying) {
                    player.stop()
                }
                player.release()
            } catch (e: Exception) {
                Log.e("PostAdapter", "Error releasing player: ${e.message}")
            }
        }
        activePlayers.clear()

        // Find and release all YouTube players
        try {
            for (i in 0 until itemCount) {
                val holder = recyclerView?.findViewHolderForAdapterPosition(i) as? PostViewHolder
                holder?.let {
                    if (it.isYoutubePlayerInitialized) {
                        it.youtubePlayerView.release()
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("PostAdapter", "Error releasing YouTube players: ${e.message}")
        }
    }

    fun updatePosts(newPosts: List<Post>) {
        posts.clear()
        posts.addAll(newPosts)
        notifyDataSetChanged()
    }

    fun addPost(post: Post) {
        posts.add(0, post) // Add to the top
        notifyItemInserted(0)
    }

    private fun getSharedPreferences(context: Context) =
        context.getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)

    inner class PostViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val userAvatar: ImageView = itemView.findViewById(R.id.imageViewPostUserAvatar)
        private val username: TextView = itemView.findViewById(R.id.textViewPostUsername)
        private val timestamp: TextView = itemView.findViewById(R.id.textViewPostTimestamp)
        private val textContent: TextView = itemView.findViewById(R.id.textViewPostTextContent)
        private val postImageContainer: androidx.cardview.widget.CardView = itemView.findViewById(R.id.cardViewPostImageContainer)
        private val postImage: ImageView = itemView.findViewById(R.id.imageViewPostImage)
        private val likeButton: ImageButton = itemView.findViewById(R.id.buttonPostReact)
        private val reactionCount: TextView = itemView.findViewById(R.id.textViewPostReactionCount)
        private val commentButton: ImageButton = itemView.findViewById(R.id.buttonPostComment)
        private val commentCount: TextView = itemView.findViewById(R.id.textViewPostCommentCount)

        // YouTube Preview UI
        private val youtubeContainer: androidx.cardview.widget.CardView = itemView.findViewById(R.id.cardViewYoutubeContainer)
        private val youtubeThumbnail: ImageView = itemView.findViewById(R.id.imageViewYoutubeThumbnail)
        private val youtubePlayButton: ImageView = itemView.findViewById(R.id.imageViewYoutubePlayButton)
        private val youtubeTitle: TextView = itemView.findViewById(R.id.textViewYoutubeTitle)
        private val youtubeThumbnailLayout: LinearLayout = itemView.findViewById(R.id.layoutYoutubeThumbnail)
        val youtubePlayerView: YouTubePlayerView = itemView.findViewById(R.id.youtubePlayerView)

        // Voice Note Player UI
        private val voiceNotePlayerLayout: LinearLayout = itemView.findViewById(R.id.layoutVoiceNotePlayer)
        private val playPauseVoiceNoteButton: ImageButton = itemView.findViewById(R.id.buttonPlayPauseVoiceNote)
        private val voiceNoteCurrentTimeTextView: TextView = itemView.findViewById(R.id.textViewVoiceNoteCurrentTime)
        private val voiceNoteSeekBar: SeekBar = itemView.findViewById(R.id.seekBarVoiceNote)
        private val voiceNoteDurationTextView: TextView = itemView.findViewById(R.id.textViewVoiceNoteDuration)
        private val voiceNoteIcon: ImageView = itemView.findViewById(R.id.imageViewVoiceNoteIcon)

        private var mediaPlayer: MediaPlayer? = null
        private val handler = Handler(Looper.getMainLooper())
        private var currentPlayingPost: Post? = null
        private var youtubePlayer: YouTubePlayer? = null
        var isYoutubePlayerInitialized = false

        fun bind(post: Post) {
            currentPlayingPost = post // Keep track of the current post for this viewholder
            username.text = post.username ?: "Usuario"
            timestamp.text = formatTimestamp(post.timestamp)

            // User Avatar
            val currentAvatar = post.userAvatar // Fix for smart cast
            android.util.Log.d("PostAdapterDebug", "Binding post by user: ${post.username}, Avatar path from Post object: $currentAvatar")
            if (!currentAvatar.isNullOrEmpty()) {
                val fullAvatarUrl = if (currentAvatar.startsWith("http")) {
                    currentAvatar // Already a full URL
                } else {
                    API_URL + currentAvatar // Prepend API_URL if it's a relative path
                }
                android.util.Log.d("PostAdapterDebug", "Attempting to load user avatar from: $fullAvatarUrl")
                Glide.with(itemView.context)
                    .load(fullAvatarUrl)
                    .placeholder(R.drawable.ic_profile_placeholder)
                    .error(R.drawable.ic_profile_placeholder)
                    .circleCrop()
                    .into(userAvatar)
            } else {
                Glide.with(itemView.context).load(R.drawable.ic_profile_placeholder).circleCrop().into(userAvatar)
            }

            // Set click listener on user avatar
            userAvatar.setOnClickListener {
                onUserClickListener?.invoke(post)
            }

            // Text Content & YouTube Link Detection
            if (!post.textContent.isNullOrEmpty()) {
                textContent.text = post.textContent
                textContent.visibility = View.VISIBLE

                // Check for YouTube links in text content
                if (post.youtubeVideoId == null) {
                    val youtubeId = extractYoutubeId(post.textContent)
                    if (youtubeId != null) {
                        post.youtubeVideoId = youtubeId
                    }
                }
            } else {
                textContent.visibility = View.GONE
            }

            // YouTube video embedding
            if (post.youtubeVideoId != null) {
                youtubeContainer.visibility = View.VISIBLE
                youtubeThumbnailLayout.visibility = View.VISIBLE
                youtubePlayerView.visibility = View.GONE

                // Reset YouTube player if it was initialized
                if (isYoutubePlayerInitialized) {
                    youtubePlayer = null
                    isYoutubePlayerInitialized = false
                }

                // Load YouTube thumbnail
                val thumbnailUrl = "https://img.youtube.com/vi/${post.youtubeVideoId}/hqdefault.jpg"
                Glide.with(itemView.context)
                    .load(thumbnailUrl)
                    .placeholder(R.drawable.image_placeholder)
                    .error(R.drawable.image_error)
                    .into(youtubeThumbnail)

                // Title can be set to something generic since we can't easily get the real title
                youtubeTitle.text = "YouTube Video"

                // Set click listener to play the video in-app
                val videoId = post.youtubeVideoId // Make a local copy for the lambda
                youtubePlayButton.setOnClickListener {
                    playYoutubeVideo(videoId!!)
                }
                youtubeThumbnail.setOnClickListener {
                    playYoutubeVideo(videoId!!)
                }
            } else {
                youtubeContainer.visibility = View.GONE
            }

            // Image Content or GIF Content
            val currentImageUrl = post.imageUrl // Fix for smart cast
            val currentGifUrl = post.gifUrl
            val currentGifPreviewUrl = post.gifPreviewUrl

            android.util.Log.d("PostAdapterDebug", "Post ${post.id} by ${post.username}: imageUrl='${post.imageUrl}', gifUrl='${post.gifUrl}', gifPreviewUrl='${post.gifPreviewUrl}'")

            if (!currentGifUrl.isNullOrEmpty()) {
                // Display GIF
                postImageContainer.visibility = View.VISIBLE
                postImage.visibility = View.VISIBLE

                // Use preview URL if available, otherwise use the full GIF
                val gifUrlToLoad = currentGifPreviewUrl ?: currentGifUrl

                // Extra validation to ensure we don't pass null to Glide
                if (!gifUrlToLoad.isNullOrEmpty() && gifUrlToLoad != "null") {
                    android.util.Log.d("PostAdapter", "Attempting to load GIF from URL: $gifUrlToLoad")

                    try {
                        // Make sure we're loading from a valid URL, not a local path
                        android.util.Log.d("PostAdapter", "Loading GIF using Glide: $gifUrlToLoad")

                        Glide.with(itemView.context)
                            .asGif() // Request animated GIF
                            .load(gifUrlToLoad) // Load directly from URL string
                            .placeholder(R.drawable.image_placeholder)
                            .error(R.drawable.image_error)
                            .into(postImage)

                        android.util.Log.d("PostAdapter", "Displaying GIF: $gifUrlToLoad")
                    } catch (e: Exception) {
                        android.util.Log.e("PostAdapter", "Error loading GIF: ${e.message}")

                        // Fallback to loading without asGif() which may display the first frame only
                        Glide.with(itemView.context)
                            .load(gifUrlToLoad)
                            .placeholder(R.drawable.image_placeholder)
                            .error(R.drawable.image_error)
                            .into(postImage)
                    }
                } else {
                    android.util.Log.e("PostAdapter", "Attempted to load invalid GIF URL: $gifUrlToLoad")
                    postImageContainer.visibility = View.GONE
                    postImage.visibility = View.GONE
                }
            }
            else if (!currentImageUrl.isNullOrEmpty() && currentImageUrl != "null") {
                postImageContainer.visibility = View.VISIBLE
                postImage.visibility = View.VISIBLE
                val fullImageUrl = if (currentImageUrl.startsWith("http")) {
                    currentImageUrl
                } else {
                    API_URL + currentImageUrl
                }
                android.util.Log.d("PostAdapterDebug", "Attempting to load post image (valid URL expected): $fullImageUrl")

                if (fullImageUrl.isNotEmpty() && fullImageUrl != "null") {
                    Glide.with(itemView.context)
                        .load(fullImageUrl)
                        .placeholder(R.drawable.image_placeholder)
                        .error(R.drawable.image_error)
                        .into(postImage)
                } else {
                    android.util.Log.e("PostAdapter", "Invalid full image URL: $fullImageUrl")
                    postImageContainer.visibility = View.GONE
                    postImage.visibility = View.GONE
                }
            } else {
                android.util.Log.d("PostAdapterDebug", "No valid image URL for post by ${post.username}, hiding image view. URL was: '${post.imageUrl}'")
                postImageContainer.visibility = View.GONE
                postImage.visibility = View.GONE
            }

            // Voice Note
            val currentVoiceNoteUrl = post.voiceNoteUrl // Fix for smart cast
            val currentVoiceNoteDuration = post.voiceNoteDuration // Fix for smart cast

            if (!currentVoiceNoteUrl.isNullOrEmpty() && currentVoiceNoteDuration != null && currentVoiceNoteDuration > 0) {
                voiceNotePlayerLayout.visibility = View.VISIBLE
                voiceNoteIcon.visibility = View.VISIBLE // Or manage as per your preference

                val fullVoiceNoteUrl = if (currentVoiceNoteUrl.startsWith("http")) {
                    currentVoiceNoteUrl
                } else {
                    API_URL + currentVoiceNoteUrl
                }
                Log.d("PostAdapter", "Voice Note URL: $fullVoiceNoteUrl, Duration: $currentVoiceNoteDuration")

                voiceNoteDurationTextView.text = formatDuration(currentVoiceNoteDuration * 1000)
                voiceNoteCurrentTimeTextView.text = formatDuration(0)
                voiceNoteSeekBar.max = currentVoiceNoteDuration * 1000 // SeekBar in milliseconds
                voiceNoteSeekBar.progress = 0
                playPauseVoiceNoteButton.setImageResource(R.drawable.ic_play_arrow) // Default to play icon

                playPauseVoiceNoteButton.setOnClickListener {
                    if (mediaPlayer != null && mediaPlayer!!.isPlaying) {
                        mediaPlayer?.pause()
                        playPauseVoiceNoteButton.setImageResource(R.drawable.ic_play_arrow)
                        stopSeekBarUpdate()
                    } else {
                        // If player exists and is paused, resume
                        if (mediaPlayer != null && !mediaPlayer!!.isPlaying && mediaPlayer!!.currentPosition > 0) {
                             mediaPlayer?.start()
                             playPauseVoiceNoteButton.setImageResource(R.drawable.ic_pause)
                             updateSeekBar()
                        } else { // Otherwise, prepare and start a new player
                            releaseMediaPlayer() // Release any existing player first
                            // Ensure fullVoiceNoteUrl is non-null before passing
                            prepareAndPlay(fullVoiceNoteUrl, post)
                        }
                    }
                }

                voiceNoteSeekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                        if (fromUser && mediaPlayer != null) {
                            // mediaPlayer?.seekTo(progress) // This might be problematic if player is not prepared or already playing.
                            // Let's only update text for now, seek will be handled onStopTrackingTouch
                            voiceNoteCurrentTimeTextView.text = formatDuration(progress)
                        }
                    }
                    override fun onStartTrackingTouch(seekBar: SeekBar?) {
                        // User started dragging, pause updates if playing
                        if (mediaPlayer?.isPlaying == true) {
                             // No need to pause media player, just the UI updates
                            stopSeekBarUpdate()
                        }
                    }
                    override fun onStopTrackingTouch(seekBar: SeekBar?) {
                        seekBar?.let {
                            mediaPlayer?.seekTo(it.progress)
                            if (mediaPlayer?.isPlaying == true) { // If it was playing, resume UI updates
                                updateSeekBar()
                            } else { // If paused, just update the current time text once
                                voiceNoteCurrentTimeTextView.text = formatDuration(it.progress)
                            }
                        }
                    }
                })
            } else {
                voiceNotePlayerLayout.visibility = View.GONE
                releaseMediaPlayer() // Ensure player is released if no voice note
            }

            // Reactions
            val totalReactions = post.reactions?.count() ?: 0 // Simple count for now
            reactionCount.text = "$totalReactions Likes" // Customize as needed

            // Comments
            commentCount.text = "${post.commentCount} Comentarios"

            // Click Listeners
            likeButton.setOnClickListener { onLikeClickListener?.invoke(post) }
            commentButton.setOnClickListener { onCommentClickListener?.invoke(post) }
            username.setOnClickListener { onUserClickListener?.invoke(post) }
            userAvatar.setOnClickListener { onUserClickListener?.invoke(post) }

            // Options menu (three dots)
            val optionsButton = itemView.findViewById<ImageButton>(R.id.buttonPostOptions)

            // Only show options button to the post owner
            val currentUserId = getSharedPreferences(itemView.context).getInt("USER_ID", -1)
            if (post.userId == currentUserId) {
                optionsButton.visibility = View.VISIBLE
                optionsButton.setOnClickListener { view ->
                    showPostOptionsMenu(view, post)
                }
            } else {
                optionsButton.visibility = View.GONE
            }

            // Image click handler for both regular images and GIFs
            postImage.setOnClickListener {
                if (!post.gifUrl.isNullOrEmpty()) {
                    // For GIFs, open in browser or in a custom dialog
                    try {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(post.gifUrl))
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        itemView.context.startActivity(intent)
                    } catch (e: Exception) {
                        Log.e("PostAdapter", "Error opening GIF: ${e.message}")
                        Toast.makeText(itemView.context, "Error al abrir el GIF", Toast.LENGTH_SHORT).show()

                        // Fallback to onImageClickListener
                        onImageClickListener?.invoke(post)
                    }
                } else {
                    // Regular image
                    onImageClickListener?.invoke(post)
                }
            }
        }

        private fun formatTimestamp(epochMillis: Long): String {
            return SpanishDateFormatter.formatTimestamp(epochMillis)
        }

        private fun formatDuration(durationMillis: Int): String {
            val minutes = (durationMillis / 1000) / 60
            val seconds = (durationMillis / 1000) % 60
            return String.format(Locale.getDefault(), "%d:%02d", minutes, seconds)
        }

        private val updateSeekBarRunnable = object : Runnable {
            override fun run() {
                mediaPlayer?.let {
                    if (it.isPlaying) {
                        val currentPosition = it.currentPosition
                        voiceNoteSeekBar.progress = currentPosition
                        voiceNoteCurrentTimeTextView.text = formatDuration(currentPosition)
                        handler.postDelayed(this, 1000) // Update every second
                    }
                }
            }
        }

        private fun updateSeekBar() {
            handler.post(updateSeekBarRunnable)
        }

        private fun stopSeekBarUpdate() {
            handler.removeCallbacks(updateSeekBarRunnable)
        }

        private fun prepareAndPlay(url: String, postData: Post) {
            if (currentPlayingPost != postData) { // If a different post is now in this viewholder
                releaseMediaPlayer()
            }
            currentPlayingPost = postData

            mediaPlayer = MediaPlayer().apply {
                try {
                    setDataSource(url)
                    setOnPreparedListener { mp ->
                        Log.d("PostAdapter", "MediaPlayer prepared for URL: $url")
                        // Check if the post associated with this player is still the one being bound
                        // and if the viewholder is still at a valid position
                        if (currentPlayingPost == postData && bindingAdapterPosition != RecyclerView.NO_POSITION) {
                            activePlayers[bindingAdapterPosition] = mp // Changed from adapterPosition
                            mp.start()
                            playPauseVoiceNoteButton.setImageResource(R.drawable.ic_pause)
                            updateSeekBar()
                        }
                    }
                    setOnCompletionListener { mp ->
                        Log.d("PostAdapter", "MediaPlayer completed for URL: $url")
                        playPauseVoiceNoteButton.setImageResource(R.drawable.ic_play_arrow)
                        voiceNoteSeekBar.progress = 0 // Reset progress
                        voiceNoteCurrentTimeTextView.text = formatDuration(0)
                        stopSeekBarUpdate()
                        // Optionally release here or keep prepared for replay: for now, just reset UI
                        // releaseMediaPlayer() // Uncomment if you want to release on completion
                    }
                    setOnErrorListener { mp, what, extra ->
                        Log.e("PostAdapter", "MediaPlayer Error for URL: $url - what: $what, extra: $extra")
                        Toast.makeText(itemView.context, "Error al reproducir nota de voz.", Toast.LENGTH_SHORT).show()
                        releaseMediaPlayer() // Release on error
                        true // Indicate error handled
                    }
                    prepareAsync() // Prepare asynchronously for network stream
                    playPauseVoiceNoteButton.isEnabled = false // Disable button until prepared
                    playPauseVoiceNoteButton.alpha = 0.5f // Indicate loading
                    Log.d("PostAdapter", "MediaPlayer preparing for URL: $url")
                } catch (e: IOException) {
                    Log.e("PostAdapter", "MediaPlayer setDataSource failed for URL: $url", e)
                    Toast.makeText(itemView.context, "No se puede reproducir la nota de voz.", Toast.LENGTH_SHORT).show()
                    releaseMediaPlayer()
                } catch (e: IllegalStateException) {
                    Log.e("PostAdapter", "MediaPlayer IllegalStateException for URL: $url", e)
                     Toast.makeText(itemView.context, "Error interno del reproductor.", Toast.LENGTH_SHORT).show()
                    releaseMediaPlayer()
                }
            }
             mediaPlayer?.setOnPreparedListener { mp -> // This block might be redundant if the one inside apply is correctly processing
                Log.d("PostAdapter", "MediaPlayer prepared for URL: $url")
                playPauseVoiceNoteButton.isEnabled = true
                playPauseVoiceNoteButton.alpha = 1.0f
                // Check if the post associated with this player is still the one being bound
                if (currentPlayingPost == postData) {
                    mp.start()
                    playPauseVoiceNoteButton.setImageResource(R.drawable.ic_pause)
                    updateSeekBar()
                } else {
                    // Post has changed, release this player
                    mp.release()
                    Log.d("PostAdapter", "MediaPlayer released because post changed during preparation for URL: $url")
                    if (mediaPlayer == mp) { // ensure we nullify the correct instance if it was assigned
                        mediaPlayer = null
                    }
                }
            }
        }

        fun releaseMediaPlayer() {
            stopSeekBarUpdate()
            mediaPlayer?.let {
                try {
                    if (it.isPlaying) {
                        it.stop()
                    }
                    it.reset() // Resets the player to its uninitialized state.
                    it.release() // Releases resources associated with this MediaPlayer object.
                     Log.d("PostAdapter", "MediaPlayer released for post: ${currentPlayingPost?.id}")
                } catch (e: IllegalStateException) {
                    Log.e("PostAdapter", "Error releasing MediaPlayer: ${e.message}")
                }
            }
            mediaPlayer = null
            // Reset UI elements related to player
            playPauseVoiceNoteButton.setImageResource(R.drawable.ic_play_arrow)
            playPauseVoiceNoteButton.isEnabled = true
            playPauseVoiceNoteButton.alpha = 1.0f
            voiceNoteSeekBar.progress = 0
            voiceNoteCurrentTimeTextView.text = formatDuration(0)
            // Remove from active players map if it was there for this position
            // This is better handled in onViewRecycled or when new item is bound

            // Also release YouTube player if it exists
            if (isYoutubePlayerInitialized && youtubePlayer != null) {
                youtubePlayerView.release()
                youtubePlayer = null
                isYoutubePlayerInitialized = false
            }
        }

        private fun extractYoutubeId(text: String): String? {
            // Match standard YouTube URLs
            val standardPattern = Pattern.compile(
                "https?://(?:www\\.)?youtube\\.com/watch\\?v=([\\w-]+)(?:&\\S*)?",
                Pattern.CASE_INSENSITIVE
            )
            val standardMatcher = standardPattern.matcher(text)
            if (standardMatcher.find()) {
                return standardMatcher.group(1)
            }

            // Match YouTube short URLs
            val shortPattern = Pattern.compile(
                "https?://(?:www\\.)?youtu\\.be/([\\w-]+)(?:\\?\\S*)?",
                Pattern.CASE_INSENSITIVE
            )
            val shortMatcher = shortPattern.matcher(text)
            if (shortMatcher.find()) {
                return shortMatcher.group(1)
            }

            // Match YouTube mobile app URLs
            val mobilePattern = Pattern.compile(
                "https?://(?:www\\.)?youtube\\.com/embed/([\\w-]+)(?:\\?\\S*)?",
                Pattern.CASE_INSENSITIVE
            )
            val mobileMatcher = mobilePattern.matcher(text)
            if (mobileMatcher.find()) {
                return mobileMatcher.group(1)
            }

            return null
        }

        private fun playYoutubeVideo(videoId: String) {
            try {
                // Initialize and setup YouTube player
                val activity = itemView.context as? FragmentActivity
                if (activity != null) {
                    // Show YouTube player view and hide thumbnail
                    youtubeThumbnailLayout.visibility = View.GONE
                    youtubePlayerView.visibility = View.VISIBLE

                    // Add YouTube player as a lifecycle observer to handle lifecycle events
                    activity.lifecycle.addObserver(youtubePlayerView)

                    // Initialize YouTube player if not already initialized
                    youtubePlayerView.addYouTubePlayerListener(object : AbstractYouTubePlayerListener() {
                        override fun onReady(player: YouTubePlayer) {
                            youtubePlayer = player
                            isYoutubePlayerInitialized = true
                            player.cueVideo(videoId, 0f)
                            player.play()
                        }
                    })
                } else {
                    // Fallback to external YouTube app/browser if not in a FragmentActivity
                    openYoutubeVideoExternally(videoId)
                }
            } catch (e: Exception) {
                Log.e("PostAdapter", "Error playing YouTube video: ${e.message}")
                Toast.makeText(itemView.context, "Error playing YouTube video", Toast.LENGTH_SHORT).show()
                // Fallback to external player
                openYoutubeVideoExternally(videoId)
            }
        }

        private fun openYoutubeVideoExternally(videoId: String) {
            try {
                // First try to open in the YouTube app
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("vnd.youtube:$videoId"))
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                if (intent.resolveActivity(itemView.context.packageManager) != null) {
                    itemView.context.startActivity(intent)
                } else {
                    // If YouTube app is not installed, open in browser
                    val webIntent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.youtube.com/watch?v=$videoId"))
                    webIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    itemView.context.startActivity(webIntent)
                }
            } catch (e: Exception) {
                Log.e("PostAdapter", "Error opening YouTube video: ${e.message}")
                Toast.makeText(itemView.context, "Error opening YouTube video", Toast.LENGTH_SHORT).show()
            }
        }

        private fun showPostOptionsMenu(view: View, post: Post) {
            val popup = PopupMenu(itemView.context, view)
            popup.menuInflater.inflate(R.menu.menu_post_options, popup.menu)

            popup.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.action_delete_post -> {
                        // Confirm before deleting
                        showDeleteConfirmationDialog(post)
                        true
                    }
                    else -> false
                }
            }

            popup.show()
        }

        private fun showDeleteConfirmationDialog(post: Post) {
            androidx.appcompat.app.AlertDialog.Builder(itemView.context)
                .setTitle("Borrar publicación")
                .setMessage("¿Estás seguro de que quieres borrar esta publicación?")
                .setPositiveButton("Borrar") { _, _ ->
                    onDeleteClickListener?.invoke(post)
                }
                .setNegativeButton("Cancelar", null)
                .show()
        }
    }
}