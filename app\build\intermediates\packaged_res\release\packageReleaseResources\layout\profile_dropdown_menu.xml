<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rounded_dialog_background"
    android:backgroundTint="@color/cyberpunk_card_background"
    android:elevation="8dp"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- User Info Header -->
    <LinearLayout
        android:id="@+id/layoutUserInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/imageViewUserAvatar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/glowing_border"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_default_avatar" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Usuario"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textViewUserHandle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="@usuario"
                android:textColor="@color/cyberpunk_text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="16dp"
        android:background="@color/cyberpunk_divider" />

    <!-- Menu Options -->

    <!-- Mi perfil -->
    <LinearLayout
        android:id="@+id/layoutMyProfile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_profile"
            android:tint="@color/cyberpunk_text_primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/inter"
            android:text="Mi perfil"
            android:textColor="@color/cyberpunk_text_primary"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Veríficate -->
    <LinearLayout
        android:id="@+id/layoutVerification"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_check"
            android:tint="@color/neon_blue" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Veríficate"
                android:textColor="@color/neon_blue"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Aumenta tu credibilidad y matches"
                android:textColor="@color/cyberpunk_text_secondary"
                android:textSize="12sp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/imageViewVerificationBadge"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_check"
            android:tint="@color/neon_green"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Configuración y privacidad -->
    <LinearLayout
        android:id="@+id/layoutSettings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_settings"
            android:tint="@color/cyberpunk_text_primary" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Configuración y privacidad"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="16sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Recluta y gana -->
    <LinearLayout
        android:id="@+id/layoutRecruitAndEarn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_matches"
            android:tint="@color/cyberpunk_text_primary" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Recluta y gana"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter"
                android:text="Obtén recompensas invitando a tus amigos"
                android:textColor="@color/cyberpunk_text_secondary"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Normas de la comunidad -->
    <LinearLayout
        android:id="@+id/layoutCommunityGuidelines"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_community"
            android:tint="@color/cyberpunk_text_primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/inter"
            android:text="Normas de la comunidad"
            android:textColor="@color/cyberpunk_text_primary"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Administración (solo para admins) -->
    <LinearLayout
        android:id="@+id/layoutAdministration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_admin_crown"
            android:tint="@color/cyberpunk_accent_primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/inter"
            android:text="Administración"
            android:textColor="@color/cyberpunk_accent_primary"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="8dp"
        android:background="@color/cyberpunk_divider" />

    <!-- Cerrar sesión -->
    <LinearLayout
        android:id="@+id/layoutLogout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@android:drawable/ic_lock_power_off"
            android:tint="@color/comfy_red" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/inter"
            android:text="Cerrar sesión"
            android:textColor="@color/comfy_red"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>
