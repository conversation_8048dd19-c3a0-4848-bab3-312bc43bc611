<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cyberpunk_background"
    android:fitsSystemWindows="true"
    tools:context=".activity.CommunityGuidelinesActivity">

    <!-- Custom Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/darker_blue"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Close Icon -->
            <ImageView
                android:id="@+id/iconClose"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp"
                android:src="@drawable/ic_close_circle"
                android:tint="@color/cyberpunk_text_primary"
                tools:ignore="UseAppTint" />

            <!-- Title -->
            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/inter"
                android:text="Normas de la comunidad"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>

    </androidx.appcompat.widget.Toolbar>

    <!-- Content ScrollView -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Warning Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rounded_corner_background"
                android:backgroundTint="@color/cyberpunk_accent_primary"
                android:fontFamily="@font/inter"
                android:gravity="center"
                android:padding="16dp"
                android:text="El incumplimiento de estas reglas conllevará a la expulsión temporal o permanente de la aplicación."
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- Guidelines Content -->
            <TextView
                android:id="@+id/textViewContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:fontFamily="@font/inter"
                android:lineSpacingExtra="4dp"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="16sp"
                tools:text="Content will be set programmatically" />

            <!-- Footer -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:background="@drawable/rounded_corner_background"
                android:backgroundTint="@color/darker_blue"
                android:fontFamily="@font/inter"
                android:gravity="center"
                android:padding="16dp"
                android:text="En caso de dudas con alguna norma consulta con un moderador/administrador."
                android:textColor="@color/cyberpunk_accent_secondary"
                android:textSize="14sp"
                android:textStyle="italic" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
