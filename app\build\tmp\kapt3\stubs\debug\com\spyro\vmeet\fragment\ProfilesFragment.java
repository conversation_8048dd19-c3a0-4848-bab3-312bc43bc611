package com.spyro.vmeet.fragment;

/**
 * Fragment for the Profiles tab in the Discover section
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0005\b\u0007\u0018\u0000 \'2\u00020\u0001:\u0001\'B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0011\u001a\u00020\u0012H\u0002J\b\u0010\u0013\u001a\u00020\u0012H\u0002J\u0010\u0010\u0014\u001a\u00020\u00122\u0006\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010\u0015\u001a\u00020\u0012H\u0002J\b\u0010\u0016\u001a\u00020\u0012H\u0002J&\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0016J\b\u0010\u001f\u001a\u00020\u0012H\u0016J\b\u0010 \u001a\u00020\u0012H\u0016J\u0010\u0010!\u001a\u00020\u00122\u0006\u0010\"\u001a\u00020#H\u0002J\u0018\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020\u000e2\u0006\u0010\"\u001a\u00020#H\u0002J\b\u0010&\u001a\u00020\u0012H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/spyro/vmeet/fragment/ProfilesFragment;", "Landroidx/fragment/app/Fragment;", "()V", "adapter", "Lcom/spyro/vmeet/UserCardAdapter;", "buttonDislike", "Landroid/widget/ImageButton;", "buttonLike", "currentUsers", "", "Lcom/spyro/vmeet/User;", "gestureDetector", "Landroidx/core/view/GestureDetectorCompat;", "userId", "", "viewPagerUsers", "Landroidx/viewpager2/widget/ViewPager2;", "disableMainViewPagerSwipe", "", "enableMainViewPagerSwipe", "loadUsers", "navigateToNext", "navigateToPrevious", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroyView", "onPause", "performAction", "liked", "", "sendMatchAction", "targetUserId", "setupGestureDetector", "Companion", "app_debug"})
public final class ProfilesFragment extends androidx.fragment.app.Fragment {
    private androidx.viewpager2.widget.ViewPager2 viewPagerUsers;
    private android.widget.ImageButton buttonLike;
    private android.widget.ImageButton buttonDislike;
    private com.spyro.vmeet.UserCardAdapter adapter;
    private androidx.core.view.GestureDetectorCompat gestureDetector;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.User> currentUsers = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ProfilesFragment";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.fragment.ProfilesFragment.Companion Companion = null;
    
    public ProfilesFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void performAction(boolean liked) {
    }
    
    private final void sendMatchAction(int targetUserId, boolean liked) {
    }
    
    private final void loadUsers(int userId) {
    }
    
    private final void setupGestureDetector() {
    }
    
    private final void navigateToNext() {
    }
    
    private final void navigateToPrevious() {
    }
    
    private final void disableMainViewPagerSwipe() {
    }
    
    private final void enableMainViewPagerSwipe() {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/spyro/vmeet/fragment/ProfilesFragment$Companion;", "", "()V", "API_URL", "", "TAG", "newInstance", "Lcom/spyro/vmeet/fragment/ProfilesFragment;", "userId", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.fragment.ProfilesFragment newInstance(int userId) {
            return null;
        }
    }
}