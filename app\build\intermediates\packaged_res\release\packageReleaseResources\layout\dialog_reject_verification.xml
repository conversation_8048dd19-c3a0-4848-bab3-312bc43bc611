<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Motivo del rechazo:"
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/editTextRejectReason"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@drawable/bg_edit_text"
        android:gravity="top|start"
        android:hint="Describe el motivo por el cual se rechaza la verificación (ej: documento ilegible, foto borrosa, documento no válido, etc.)"
        android:inputType="textMultiLine|textCapSentences"
        android:maxLines="5"
        android:padding="12dp"
        android:textColor="@color/text_primary"
        android:textColorHint="@color/text_secondary"
        android:textSize="14sp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="El usuario recibirá una notificación con este motivo y podrá enviar una nueva solicitud."
        android:textColor="@color/text_secondary"
        android:textSize="12sp" />

</LinearLayout>
