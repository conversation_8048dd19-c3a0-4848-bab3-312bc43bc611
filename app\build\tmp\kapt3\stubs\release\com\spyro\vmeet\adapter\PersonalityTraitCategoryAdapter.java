package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0001\u001eB5\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\u0016\b\u0002\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\t\u00a2\u0006\u0002\u0010\fJ\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\n0\u000eJ\b\u0010\u000f\u001a\u00020\u0010H\u0016J\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\n0\u000eJ\u0018\u0010\u0012\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\u00022\u0006\u0010\u0014\u001a\u00020\u0010H\u0016J\u0018\u0010\u0015\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u0010H\u0016J\u0014\u0010\u0019\u001a\u00020\u000b2\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u000eJ\u0014\u0010\u001c\u001a\u00020\u000b2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00050\u000eR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u000b\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/spyro/vmeet/adapter/PersonalityTraitCategoryAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/PersonalityTraitCategoryAdapter$CategoryViewHolder;", "categories", "", "Lcom/spyro/vmeet/adapter/TraitCategory;", "isEditMode", "", "onTraitClick", "Lkotlin/Function1;", "Lcom/spyro/vmeet/data/PersonalityTrait;", "", "(Ljava/util/List;ZLkotlin/jvm/functions/Function1;)V", "getAllTraits", "", "getItemCount", "", "getSelectedTraits", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setSelectedTraits", "selectedTraitIds", "", "updateCategories", "newCategories", "CategoryViewHolder", "app_release"})
public final class PersonalityTraitCategoryAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.PersonalityTraitCategoryAdapter.CategoryViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.adapter.TraitCategory> categories = null;
    private final boolean isEditMode = false;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.PersonalityTrait, kotlin.Unit> onTraitClick = null;
    
    public PersonalityTraitCategoryAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.adapter.TraitCategory> categories, boolean isEditMode, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.PersonalityTrait, kotlin.Unit> onTraitClick) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.PersonalityTraitCategoryAdapter.CategoryViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.PersonalityTraitCategoryAdapter.CategoryViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void updateCategories(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.adapter.TraitCategory> newCategories) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.data.PersonalityTrait> getAllTraits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.data.PersonalityTrait> getSelectedTraits() {
        return null;
    }
    
    public final void setSelectedTraits(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedTraitIds) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\f\u00a8\u0006\r"}, d2 = {"Lcom/spyro/vmeet/adapter/PersonalityTraitCategoryAdapter$CategoryViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "categoryTitle", "Landroid/widget/TextView;", "getCategoryTitle", "()Landroid/widget/TextView;", "traitsRecyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "getTraitsRecyclerView", "()Landroidx/recyclerview/widget/RecyclerView;", "app_release"})
    public static final class CategoryViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView categoryTitle = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.recyclerview.widget.RecyclerView traitsRecyclerView = null;
        
        public CategoryViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getCategoryTitle() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.recyclerview.widget.RecyclerView getTraitsRecyclerView() {
            return null;
        }
    }
}