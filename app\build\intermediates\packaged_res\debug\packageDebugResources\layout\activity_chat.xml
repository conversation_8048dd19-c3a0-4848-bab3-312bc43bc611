<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dark_blue"
    android:fitsSystemWindows="true">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#1A1A3A"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/imageViewAvatar"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/glowing_border"
                android:scaleType="centerCrop"
                android:padding="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <TextView
                android:id="@+id/textViewUsername"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Username"
                android:textColor="@color/neon_blue"
                android:textStyle="bold"
                android:textSize="18sp"
                android:layout_marginStart="12dp"
                app:layout_constraintStart_toEndOf="@id/imageViewAvatar"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/textViewStatus"
                app:layout_constraintVertical_chainStyle="packed"/>

            <TextView
                android:id="@+id/textViewAdminBadge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/badge_admin"
                android:drawableStart="@drawable/ic_admin_crown"
                android:drawablePadding="4dp"
                android:text="Admin"
                android:textSize="10sp"
                android:textColor="@android:color/white"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:layout_marginStart="8dp"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@id/textViewUsername"
                app:layout_constraintTop_toTopOf="@id/textViewUsername"
                app:layout_constraintBottom_toBottomOf="@id/textViewUsername" />

            <TextView
                android:id="@+id/textViewStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="En línea"
                android:textColor="#4CAF50"
                android:textSize="12sp"
                android:layout_marginStart="12dp"
                app:layout_constraintStart_toEndOf="@id/imageViewAvatar"
                app:layout_constraintTop_toBottomOf="@id/textViewUsername"
                app:layout_constraintBottom_toTopOf="@id/textViewTopic"/>

            <TextView
                android:id="@+id/textViewTopic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tema: "
                android:textColor="#FFEB3B"
                android:textSize="12sp"
                android:textStyle="italic"
                android:layout_marginStart="12dp"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@id/imageViewAvatar"
                app:layout_constraintTop_toBottomOf="@id/textViewStatus"
                app:layout_constraintBottom_toBottomOf="parent"/>

            <ImageView
                android:id="@+id/imageViewMenuOptions"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_more_vert"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:layout_marginEnd="16dp"
                android:contentDescription="Opciones del chat"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewMessages"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="8dp"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/messageInputLayout"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/messageInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:background="#1A1A3A"
        android:clipToPadding="false"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/buttonEmoji"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_emoji"
            android:background="@android:color/transparent"
            android:contentDescription="Insert emoji"
            android:padding="8dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageButton
            android:id="@+id/buttonMic"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_btn_speak_now"
            android:background="@drawable/mic_button_selector"
            android:contentDescription="Record voice note"
            android:padding="8dp"
            app:layout_constraintStart_toEndOf="@id/buttonEmoji"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="4dp"/>

        <ImageButton
            android:id="@+id/buttonImage"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@android:drawable/ic_menu_gallery"
            android:background="@android:color/transparent"
            android:contentDescription="Send image"
            android:padding="8dp"
            app:layout_constraintStart_toEndOf="@id/buttonMic"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="4dp"/>

        <TextView
            android:id="@+id/buttonGif"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:text="GIF"
            android:textColor="@color/neon_blue"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:background="@android:color/transparent"
            android:contentDescription="Send GIF"
            app:layout_constraintStart_toEndOf="@id/buttonImage"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="4dp"/>

        <EditText
            android:id="@+id/editTextMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:hint="Escribe un mensaje..."
            android:textColorHint="#80FFFFFF"
            android:textColor="@color/white"
            android:padding="12dp"
            android:background="@drawable/message_input_background"
            android:maxLength="5000"
            android:inputType="textMultiLine"
            android:maxLines="6"
            app:layout_constraintStart_toEndOf="@id/buttonGif"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/buttonSend"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"/>

        <ImageButton
            android:id="@+id/buttonSend"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_send"
            android:background="@drawable/neon_button"
            android:tint="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/neon_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/recyclerViewMessages"
        app:layout_constraintBottom_toBottomOf="@id/recyclerViewMessages"/>

    <LinearLayout
        android:id="@+id/layoutReplyPreview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#3f5f6f"
        android:padding="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/messageInputLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <View
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="#64B5F6" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Respondiendo a"
                android:textColor="#64B5F6"
                android:textSize="12sp"
                android:textStyle="italic" />

            <androidx.emoji2.widget.EmojiTextView
                android:id="@+id/textViewReplyPreview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/buttonCancelReply"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:contentDescription="Cancelar respuesta" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>