{"logs": [{"outputFile": "com.spyro.vmeet.test.app-mergeDebugAndroidTestResources-32:/values-hu/values-hu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40bc0ded6ebc3196905947594b68c381\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,406,507,610,717,2020", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "197,299,401,502,605,712,822,2116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f80b51d56d568461b2fe773f95a970d9\\transformed\\ui-release\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,288,385,484,571,653,749,838,925,989,1053,1136,1224,1298,1377,1443", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "195,283,380,479,566,648,744,833,920,984,1048,1131,1219,1293,1372,1438,1559"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "827,922,1010,1107,1206,1293,1375,1471,1560,1647,1711,1775,1858,1946,2121,2200,2266", "endColumns": "94,87,96,98,86,81,95,88,86,63,63,82,87,73,78,65,120", "endOffsets": "917,1005,1102,1201,1288,1370,1466,1555,1642,1706,1770,1853,1941,2015,2195,2261,2382"}}]}]}