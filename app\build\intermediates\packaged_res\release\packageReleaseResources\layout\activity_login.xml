<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background">

    <!-- Animated Lines Background (top) -->
    <View
        android:id="@+id/topLine1"
        android:layout_width="100dp"
        android:layout_height="2dp"
        android:layout_marginTop="30dp"
        android:layout_marginStart="30dp"
        android:background="@color/neon_blue"
        android:alpha="0.4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/topLine2"
        android:layout_width="50dp"
        android:layout_height="2dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="50dp"
        android:background="@color/neon_purple"
        android:alpha="0.4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topLine1" />

    <!-- ScrollView to make content scrollable -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:scrollbars="none"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- Logo Container -->
            <FrameLayout
                android:id="@+id/logoContainer"
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_marginTop="80dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <!-- Animated Neon Ring -->
                <ImageView
                    android:id="@+id/logoRing"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/glowing_neon_ring"
                    android:contentDescription="@string/app_name" />

                <!-- Logo -->
                <ImageView
                    android:id="@+id/logoImage"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:layout_gravity="center"
                    android:src="@mipmap/ic_launcher"
                    android:contentDescription="@string/app_name" />
            </FrameLayout>

            <!-- App Title -->
            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:fontFamily="sans-serif-medium"
                android:text="VMeet"
                android:textColor="@color/white"
                android:textSize="38sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/logoContainer" />

            <!-- Subtitle -->
            <TextView
                android:id="@+id/textViewSubtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-light"
                android:text="ENCUENTRA GAMERS COMO TÚ"
                android:textColor="@color/neon_blue"
                android:textSize="16sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewTitle" />

            <!-- Input Container -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewInputs"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="40dp"
                android:layout_marginEnd="24dp"
                app:cardBackgroundColor="#10103A"
                app:cardCornerRadius="20dp"
                app:cardElevation="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/textViewSubtitle">

        <LinearLayout
            android:id="@+id/inputFieldsContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Username -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Usuario"
                android:textColorHint="@color/comfy_blue"
                app:boxStrokeWidth="0dp"
                app:boxBackgroundColor="@android:color/transparent"
                app:hintTextColor="@color/comfy_blue">

                <EditText
                    android:id="@+id/editTextUsername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_edit_text_background"
                    android:inputType="text"
                    android:padding="16dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/comfy_blue"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Email (Visible in register mode) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Email"
                android:textColorHint="@color/comfy_blue"
                android:visibility="gone"
                app:boxStrokeWidth="0dp"
                app:boxBackgroundColor="@android:color/transparent"
                app:hintTextColor="@color/comfy_blue"
                tools:visibility="visible">

                <EditText
                    android:id="@+id/editTextEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_edit_text_background"
                    android:inputType="textEmailAddress"
                    android:padding="16dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/comfy_blue"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Gender (Visible in register mode) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutGender"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Género"
                android:textColorHint="@color/comfy_blue"
                android:visibility="gone"
                app:boxStrokeWidth="0dp"
                app:boxBackgroundColor="@android:color/transparent"
                app:hintTextColor="@color/comfy_blue"
                tools:visibility="visible">

                <AutoCompleteTextView
                    android:id="@+id/dropdownGender"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_edit_text_background"
                    android:inputType="none"
                    android:padding="16dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/comfy_blue"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Sexuality (Visible in register mode) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutSexuality"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Orientación sexual"
                android:textColorHint="@color/comfy_blue"
                android:visibility="gone"
                app:boxStrokeWidth="0dp"
                app:boxBackgroundColor="@android:color/transparent"
                app:hintTextColor="@color/comfy_blue"
                tools:visibility="visible">

                <AutoCompleteTextView
                    android:id="@+id/dropdownSexuality"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_edit_text_background"
                    android:inputType="none"
                    android:padding="16dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/comfy_blue"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Birthdate (Visible in register mode) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayoutBirthdate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Fecha de nacimiento"
                android:textColorHint="@color/comfy_blue"
                android:visibility="gone"
                app:boxStrokeWidth="0dp"
                app:boxBackgroundColor="@android:color/transparent"
                app:hintTextColor="@color/comfy_blue"
                tools:visibility="visible">

                <EditText
                    android:id="@+id/editTextBirthdate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_edit_text_background"
                    android:focusable="false"
                    android:inputType="none"
                    android:padding="16dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/comfy_blue"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Contraseña"
                android:textColorHint="@color/comfy_blue"
                app:boxStrokeWidth="0dp"
                app:boxBackgroundColor="@android:color/transparent"
                app:hintTextColor="@color/comfy_blue"
                app:passwordToggleEnabled="true"
                app:passwordToggleTint="@color/comfy_blue">

                <EditText
                    android:id="@+id/editTextPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/modern_edit_text_background"
                    android:inputType="textPassword"
                    android:padding="16dp"
                    android:textColor="@color/white"
                    android:textColorHint="@color/comfy_blue"
                    android:textSize="16sp" />
            </com.google.android.material.textfield.TextInputLayout>

            <!-- Forgot Password Link (Only visible in login mode) -->
            <TextView
                android:id="@+id/textViewForgotPassword"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:text="¿Olvidaste tu contraseña?"
                android:textColor="@color/neon_blue"
                android:textSize="14sp" />

            <!-- Privacy Policy Checkbox (Visible in register mode) -->
            <CheckBox
                android:id="@+id/checkboxPrivacyPolicy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp"
                android:text="Acepto la política de privacidad"
                android:textColor="@color/white"
                android:buttonTint="@color/comfy_blue"
                android:visibility="gone"
                tools:visibility="visible" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:indeterminate="true"
        android:indeterminateTint="@color/neon_blue"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cardViewInputs"
        tools:visibility="visible" />

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="@id/cardViewInputs"
        app:layout_constraintStart_toStartOf="@id/cardViewInputs"
        app:layout_constraintTop_toBottomOf="@id/cardViewInputs">

        <!-- Login Button -->
        <com.spyro.vmeet.ui.PurpleButton
            android:id="@+id/buttonLogin"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:gravity="center"
            android:text="INICIAR SESIÓN" />

        <!-- Register Button -->
        <com.spyro.vmeet.ui.PurpleButton
            android:id="@+id/buttonRegister"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:gravity="center"
            app:isAlt="true"
            android:text="REGISTRARSE"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

    <!-- Toggle Button -->
    <Button
        android:id="@+id/buttonToggleMode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="80dp"
        android:background="?attr/selectableItemBackground"
        android:text="¿No tienes cuenta? Regístrate"
        android:textColor="@color/neon_pink"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/buttonContainer"
        app:layout_constraintBottom_toTopOf="@id/bottomLine1" />

            <!-- Animated Lines Background (bottom) -->
            <View
                android:id="@+id/bottomLine1"
                android:layout_width="150dp"
                android:layout_height="2dp"
                android:layout_marginEnd="30dp"
                android:layout_marginBottom="40dp"
                android:alpha="0.4"
                android:background="@color/neon_pink"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <View
                android:id="@+id/bottomLine2"
                android:layout_width="80dp"
                android:layout_height="2dp"
                android:layout_marginEnd="60dp"
                android:layout_marginBottom="50dp"
                android:alpha="0.4"
                android:background="@color/neon_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>