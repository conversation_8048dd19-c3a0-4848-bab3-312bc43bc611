<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/imageViewAvatar"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@android:drawable/ic_menu_gallery"
                app:civ_border_color="#FFFFFF"
                app:civ_border_width="0dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvUsername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="username123" />

                <TextView
                    android:id="@+id/tvEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    tools:text="<EMAIL>" />

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#4CAF50"
                    android:textSize="12sp"
                    tools:text="Último acceso: 12/12/2023" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvRole"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="#E6E6E6"
                android:padding="4dp"
                android:text="Usuario"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- Connection Info Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp"
            android:background="#F5F5F5"
            android:padding="8dp">

            <TextView
                android:id="@+id/tvLastIP"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#666666"
                tools:text="IP actual: ***********" />

            <TextView
                android:id="@+id/tvDeviceInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginTop="4dp"
                tools:text="Dispositivo: Android 11 - Samsung Galaxy S21" />

            <TextView
                android:id="@+id/tvIpHistory"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textSize="14sp"
                android:textColor="#666666"
                android:visibility="gone"
                tools:visibility="visible"
                tools:text="IPs registradas:\n• ***********\n• ********" />

            <TextView
                android:id="@+id/tvDeviceHistoryTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Dispositivos registrados:"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="#666666"
                android:visibility="gone"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/deviceHistoryContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="4dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    tools:text="• Device123456789" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/banInfoLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="#FFF9E7"
            android:orientation="vertical"
            android:padding="8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Usuario baneado"
                android:textColor="#FF5722"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvBanReason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                tools:text="Comportamiento inadecuado" />

            <TextView
                android:id="@+id/tvBanExpiration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                tools:text="Hasta: 20/05/2025" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/muteInfoLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="#F5F5F5"
            android:orientation="vertical"
            android:padding="8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Usuario silenciado"
                android:textColor="#FF9800"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvMuteExpiration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                tools:text="Silenciado hasta: 12/12/2023 15:30" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical">

            <!-- Primera fila: Silenciar/Desilenciar -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnMute"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    android:text="Silenciar"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:textColor="@color/design_default_color_error" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnUnmute"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:text="Desilenciar"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:textColor="@color/design_default_color_primary" />

            </LinearLayout>

            <!-- Segunda fila: Banear/Desbanear/Historial -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBan"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    android:text="Banear"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:textColor="@color/design_default_color_error" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnUnban"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:layout_marginEnd="4dp"
                    android:text="Desbanear"
                    android:visibility="gone"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:textColor="@color/design_default_color_primary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnHistory"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    android:text="Historial"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            </LinearLayout>

            <!-- Tercera fila: Verificar -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnVerify"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Verificar Usuario"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:textColor="#00C853"
                    app:strokeColor="#00C853"
                    app:icon="@drawable/ic_verified_check"
                    app:iconTint="#00C853" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>