<?xml version="1.0" encoding="utf-8"?>
<animation-list xmlns:android="http://schemas.android.com/apk/res/android"
    android:oneshot="false">
    
    <!-- Frame 1: Normal state -->
    <item android:duration="500">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FF6B35"
                android:endColor="#F7931E"
                android:angle="45" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="#E65100" />
        </shape>
    </item>
    
    <!-- Frame 2: Slightly brighter -->
    <item android:duration="300">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FF8A50"
                android:endColor="#FFB74D"
                android:angle="45" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="#FF5722" />
        </shape>
    </item>
    
    <!-- Frame 3: Brightest -->
    <item android:duration="200">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFAB40"
                android:endColor="#FFC107"
                android:angle="45" />
            <corners android:radius="12dp" />
            <stroke
                android:width="3dp"
                android:color="#FF9800" />
        </shape>
    </item>
    
</animation-list>
