<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimaryDark">

    <!-- Header with topic and timer -->
    <TextView
        android:id="@+id/textViewChatTopic"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:textAlignment="center"
        android:textColor="@color/comfy_blue"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Tema: Videojuegos" />

    <TextView
        android:id="@+id/textViewChatTimer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/rounded_corner_background"
        android:paddingStart="12dp"
        android:paddingTop="4dp"
        android:paddingEnd="12dp"
        android:paddingBottom="4dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewChatTopic"
        tools:text="2:30" />

    <!-- Chat messages -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewChat"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/layoutChatInput"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewChatTimer"
        tools:listitem="@layout/item_blind_date_message_received" />

    <!-- Input area -->
    <LinearLayout
        android:id="@+id/layoutChatInput"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#1A1A3A"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <EditText
            android:id="@+id/editTextChatMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/rounded_edittext_background"
            android:hint="Escribe un mensaje..."
            android:inputType="textMultiLine"
            android:maxLines="4"
            android:padding="12dp"
            android:textColor="@color/white"
            android:textColorHint="#AAAAAA"
            android:textSize="16sp" />

        <ImageButton
            android:id="@+id/buttonSendMessage"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="bottom"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Enviar mensaje"
            android:src="@drawable/ic_send"
            android:tint="@color/comfy_blue" />
    </LinearLayout>

    <!-- Leave chat button -->
    <Button
        android:id="@+id/buttonLeaveChat"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/neon_button_red"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:text="Abandonar"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
