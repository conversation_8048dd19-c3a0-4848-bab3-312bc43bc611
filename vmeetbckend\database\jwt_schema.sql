-- V<PERSON><PERSON><PERSON> JWT Authentication System Database Schema

-- Create table for refresh tokens
CREATE TABLE IF NOT EXISTS user_refresh_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_created_at (created_at)
);

-- Create table for security audit logs
CREATE TABLE IF NOT EXISTS security_audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    event_type ENUM('login', 'logout', 'token_refresh', 'failed_login', 'admin_access', 'suspicious_activity') NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    details JSO<PERSON> NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at)
);

-- Create table for failed login attempts tracking
CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(255) NULL,
    attempt_count INT DEFAULT 1,
    last_attempt_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    blocked_until TIMESTAMP NULL,
    
    UNIQUE KEY unique_ip_username (ip_address, username),
    INDEX idx_ip_address (ip_address),
    INDEX idx_last_attempt (last_attempt_at),
    INDEX idx_blocked_until (blocked_until)
);

-- Create table for active sessions tracking
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_session_token (session_token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_activity (last_activity_at),
    INDEX idx_is_active (is_active)
);

-- Clean up expired refresh tokens (run periodically)
-- DELETE FROM user_refresh_tokens WHERE expires_at < NOW();

-- Clean up old security logs (keep last 90 days)
-- DELETE FROM security_audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- Clean up old failed login attempts (keep last 30 days)
-- DELETE FROM failed_login_attempts WHERE last_attempt_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean up expired sessions
-- DELETE FROM user_sessions WHERE expires_at < NOW() OR is_active = FALSE;
