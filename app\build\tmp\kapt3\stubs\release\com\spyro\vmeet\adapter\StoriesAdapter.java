package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0002\u0018\u0019B\'\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0002\u0010\tJ\b\u0010\f\u001a\u00020\rH\u0016J\u001c\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00042\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004H\u0002J\u001c\u0010\u000f\u001a\u00020\b2\n\u0010\u0010\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0011\u001a\u00020\rH\u0016J\u001c\u0010\u0012\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\rH\u0016J\u0014\u0010\u0016\u001a\u00020\b2\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/spyro/vmeet/adapter/StoriesAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/StoriesAdapter$StoryViewHolder;", "stories", "", "Lcom/spyro/vmeet/data/community/Story;", "onStoryClick", "Lkotlin/Function1;", "", "(Ljava/util/List;Lkotlin/jvm/functions/Function1;)V", "userStories", "Lcom/spyro/vmeet/adapter/StoriesAdapter$UserStories;", "getItemCount", "", "groupStoriesByUser", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "updateStories", "newStories", "StoryViewHolder", "UserStories", "app_release"})
public final class StoriesAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.StoriesAdapter.StoryViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.data.community.Story> stories;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.community.Story, kotlin.Unit> onStoryClick = null;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.adapter.StoriesAdapter.UserStories> userStories;
    
    public StoriesAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.community.Story> stories, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.community.Story, kotlin.Unit> onStoryClick) {
        super();
    }
    
    private final java.util.List<com.spyro.vmeet.adapter.StoriesAdapter.UserStories> groupStoriesByUser(java.util.List<com.spyro.vmeet.data.community.Story> stories) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.StoriesAdapter.StoryViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.StoriesAdapter.StoryViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void updateStories(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.community.Story> newStories) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/adapter/StoriesAdapter$StoryViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/StoriesAdapter;Landroid/view/View;)V", "avatar", "Lde/hdodenhof/circleimageview/CircleImageView;", "ringSeen", "ringUnseen", "timeAgo", "Landroid/widget/TextView;", "username", "bind", "", "userStories", "Lcom/spyro/vmeet/adapter/StoriesAdapter$UserStories;", "app_release"})
    public final class StoryViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView avatar = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView username = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView timeAgo = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View ringUnseen = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View ringSeen = null;
        
        public StoryViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.adapter.StoriesAdapter.UserStories userStories) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0018\b\u0087\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\rH\u00c6\u0003JM\u0010 \u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010!\u001a\u00020\u000b2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010#\u001a\u00020\u0003H\u00d6\u0001J\t\u0010$\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016\u00a8\u0006%"}, d2 = {"Lcom/spyro/vmeet/adapter/StoriesAdapter$UserStories;", "", "userId", "", "username", "", "userAvatar", "stories", "", "Lcom/spyro/vmeet/data/community/Story;", "hasUnseenStories", "", "latestTimestamp", "", "(ILjava/lang/String;Ljava/lang/String;Ljava/util/List;ZJ)V", "getHasUnseenStories", "()Z", "getLatestTimestamp", "()J", "getStories", "()Ljava/util/List;", "getUserAvatar", "()Ljava/lang/String;", "getUserId", "()I", "getUsername", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "toString", "app_release"})
    public static final class UserStories {
        private final int userId = 0;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String username = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String userAvatar = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.spyro.vmeet.data.community.Story> stories = null;
        private final boolean hasUnseenStories = false;
        private final long latestTimestamp = 0L;
        
        public UserStories(int userId, @org.jetbrains.annotations.NotNull()
        java.lang.String username, @org.jetbrains.annotations.Nullable()
        java.lang.String userAvatar, @org.jetbrains.annotations.NotNull()
        java.util.List<com.spyro.vmeet.data.community.Story> stories, boolean hasUnseenStories, long latestTimestamp) {
            super();
        }
        
        public final int getUserId() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUsername() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getUserAvatar() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.spyro.vmeet.data.community.Story> getStories() {
            return null;
        }
        
        public final boolean getHasUnseenStories() {
            return false;
        }
        
        public final long getLatestTimestamp() {
            return 0L;
        }
        
        public final int component1() {
            return 0;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.util.List<com.spyro.vmeet.data.community.Story> component4() {
            return null;
        }
        
        public final boolean component5() {
            return false;
        }
        
        public final long component6() {
            return 0L;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.adapter.StoriesAdapter.UserStories copy(int userId, @org.jetbrains.annotations.NotNull()
        java.lang.String username, @org.jetbrains.annotations.Nullable()
        java.lang.String userAvatar, @org.jetbrains.annotations.NotNull()
        java.util.List<com.spyro.vmeet.data.community.Story> stories, boolean hasUnseenStories, long latestTimestamp) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}