<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="6dp"
    android:background="@color/dark_background"
    app:cardBackgroundColor="@color/darker_blue"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    app:strokeColor="@color/neon_blue"
    app:strokeWidth="1dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="88dp"
        android:padding="16dp">

        <!-- Profile Picture Container -->
        <FrameLayout
            android:id="@+id/frameLayoutAvatar"
            android:layout_width="56dp"
            android:layout_height="56dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- Profile Picture -->
            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/imageViewAvatar"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/default_avatar"
                app:civ_border_width="2dp"
                app:civ_border_color="@color/neon_blue" />

            <!-- Online Status Indicator -->
            <View
                android:id="@+id/viewOnlineStatus"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:layout_gravity="bottom|end"
                android:layout_marginEnd="2dp"
                android:layout_marginBottom="2dp"
                android:background="@drawable/circle_online_status"
                android:visibility="gone" />

        </FrameLayout>

        <!-- Username -->
        <TextView
            android:id="@+id/textViewUsername"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:text="Username"
            android:textColor="@color/cyberpunk_text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            android:fontFamily="sans-serif-medium"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/frameLayoutAvatar"
            app:layout_constraintTop_toTopOf="@id/frameLayoutAvatar"
            app:layout_constraintEnd_toStartOf="@id/buttonViewProfile" />

        <!-- Age and Online Status Row -->
        <LinearLayout
            android:id="@+id/layoutAgeStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintStart_toEndOf="@id/frameLayoutAvatar"
            app:layout_constraintTop_toBottomOf="@id/textViewUsername"
            app:layout_constraintEnd_toStartOf="@id/buttonViewProfile">

            <TextView
                android:id="@+id/textViewAge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="25 años"
                android:textColor="@color/cyberpunk_text_secondary"
                android:textSize="13sp"
                android:fontFamily="sans-serif" />

            <View
                android:layout_width="3dp"
                android:layout_height="3dp"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/circle_dot"
                android:backgroundTint="@color/cyberpunk_text_secondary" />

            <TextView
                android:id="@+id/textViewOnlineStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="En línea"
                android:textColor="@color/online_green"
                android:textSize="13sp"
                android:fontFamily="sans-serif" />

        </LinearLayout>        <!-- Distance Info -->
        <TextView
            android:id="@+id/textViewDistance"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="8dp"
            android:text="A 1.2 km de distancia"
            android:textColor="@color/neon_blue"
            android:textSize="11sp"
            android:fontFamily="sans-serif-medium"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            app:layout_constraintStart_toEndOf="@id/frameLayoutAvatar"
            app:layout_constraintTop_toBottomOf="@id/layoutAgeStatus"
            app:layout_constraintEnd_toStartOf="@id/buttonViewProfile" />

        <!-- Location Info -->
        <TextView
            android:id="@+id/textViewLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="2dp"
            android:layout_marginEnd="8dp"
            android:text="Madrid, España"
            android:textColor="@color/cyberpunk_text_secondary"
            android:textSize="10sp"
            android:fontFamily="sans-serif"
            android:maxLines="1"
            android:ellipsize="end"
            android:includeFontPadding="false"
            app:layout_constraintStart_toEndOf="@id/frameLayoutAvatar"
            app:layout_constraintTop_toBottomOf="@id/textViewDistance"
            app:layout_constraintEnd_toStartOf="@id/buttonViewProfile"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.0" />        <!-- Bio (Optional) -->
        <TextView
            android:id="@+id/textViewBio"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:text="Bio del usuario aquí..."
            android:textColor="@color/cyberpunk_text_secondary"
            android:textSize="12sp"
            android:fontFamily="sans-serif"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="1dp"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/frameLayoutAvatar"
            app:layout_constraintTop_toBottomOf="@id/textViewLocation"
            app:layout_constraintEnd_toStartOf="@id/buttonViewProfile" />

        <!-- Action Button -->
        <ImageButton
            android:id="@+id/buttonViewProfile"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/circular_button_background"
            android:src="@drawable/ic_arrow_forward"
            android:contentDescription="Ver perfil"
            android:scaleType="centerInside"
            android:tint="@color/neon_blue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/frameLayoutAvatar"
            app:layout_constraintBottom_toBottomOf="@id/frameLayoutAvatar" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
