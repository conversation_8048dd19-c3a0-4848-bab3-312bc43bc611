package com.spyro.vmeet.util;

/**
 * Utility class for handling window insets and edge-to-edge display
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ6\u0010\n\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\fJ\u000e\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0012R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/spyro/vmeet/util/WindowInsetsUtil;", "", "()V", "TAG", "", "applyBottomInsets", "", "view", "Landroid/view/View;", "applyTopInsets", "applyWindowInsets", "applyTop", "", "applyBottom", "applyLeft", "applyRight", "setupEdgeToEdgeDisplay", "activity", "Landroid/app/Activity;", "app_debug"})
public final class WindowInsetsUtil {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "WindowInsetsUtil";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.util.WindowInsetsUtil INSTANCE = null;
    
    private WindowInsetsUtil() {
        super();
    }
    
    /**
     * Sets up edge-to-edge display by making the app draw under system bars
     * and handling insets properly
     */
    public final void setupEdgeToEdgeDisplay(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Apply window insets to a specific view
     *
     * @param view The view to apply insets to
     * @param applyTop Whether to apply top insets (status bar)
     * @param applyBottom Whether to apply bottom insets (navigation bar)
     * @param applyLeft Whether to apply left insets
     * @param applyRight Whether to apply right insets
     */
    public final void applyWindowInsets(@org.jetbrains.annotations.NotNull()
    android.view.View view, boolean applyTop, boolean applyBottom, boolean applyLeft, boolean applyRight) {
    }
    
    /**
     * Apply window insets to a toolbar or any view that should only have top insets
     */
    public final void applyTopInsets(@org.jetbrains.annotations.NotNull()
    android.view.View view) {
    }
    
    /**
     * Apply window insets to a bottom navigation or any view that should only have bottom insets
     */
    public final void applyBottomInsets(@org.jetbrains.annotations.NotNull()
    android.view.View view) {
    }
}