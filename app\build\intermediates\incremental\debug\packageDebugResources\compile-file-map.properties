#Sat May 31 16:24:03 CEST 2025
com.spyro.vmeet.app-main-6\:/anim/buzz_earthquake.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\buzz_earthquake.xml
com.spyro.vmeet.app-main-6\:/anim/buzz_flash.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\buzz_flash.xml
com.spyro.vmeet.app-main-6\:/anim/buzz_intense_shake.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\buzz_intense_shake.xml
com.spyro.vmeet.app-main-6\:/anim/buzz_shake.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\buzz_shake.xml
com.spyro.vmeet.app-main-6\:/anim/fade_in.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\fade_in.xml
com.spyro.vmeet.app-main-6\:/anim/pulse.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\pulse.xml
com.spyro.vmeet.app-main-6\:/anim/rotate.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\rotate.xml
com.spyro.vmeet.app-main-6\:/anim/slide_up.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_up.xml
com.spyro.vmeet.app-main-6\:/anim/text_fade_in.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\text_fade_in.xml
com.spyro.vmeet.app-main-6\:/anim/text_rotate.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\text_rotate.xml
com.spyro.vmeet.app-main-6\:/anim/text_slide_in_left.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\text_slide_in_left.xml
com.spyro.vmeet.app-main-6\:/drawable-v26/ic_notification_vmeet.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable-v26\\ic_notification_vmeet.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_gradient_1.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_gradient_1.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_gradient_2.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_gradient_2.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_gradient_3.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_gradient_3.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_gradient_animation.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_gradient_animation.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_name_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_name_background.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_name_gradient.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_name_gradient.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_name_gradient_1.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_name_gradient_1.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_name_gradient_2.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_name_gradient_2.xml
com.spyro.vmeet.app-main-6\:/drawable/admin_name_gradient_3.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\admin_name_gradient_3.xml
com.spyro.vmeet.app-main-6\:/drawable/avatar_circle_border.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\avatar_circle_border.xml
com.spyro.vmeet.app-main-6\:/drawable/background_reply_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\background_reply_received.xml
com.spyro.vmeet.app-main-6\:/drawable/background_reply_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\background_reply_sent.xml
com.spyro.vmeet.app-main-6\:/drawable/badge_admin.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\badge_admin.xml
com.spyro.vmeet.app-main-6\:/drawable/badge_new.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\badge_new.xml
com.spyro.vmeet.app-main-6\:/drawable/badge_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\badge_user.xml
com.spyro.vmeet.app-main-6\:/drawable/bg_buzz_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_buzz_message_received.xml
com.spyro.vmeet.app-main-6\:/drawable/bg_buzz_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_buzz_message_sent.xml
com.spyro.vmeet.app-main-6\:/drawable/bg_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_message_received.xml
com.spyro.vmeet.app-main-6\:/drawable/bg_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bg_message_sent.xml
com.spyro.vmeet.app-main-6\:/drawable/bottom_sheet_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\bottom_sheet_background.xml
com.spyro.vmeet.app-main-6\:/drawable/button_cyberpunk.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_cyberpunk.xml
com.spyro.vmeet.app-main-6\:/drawable/button_outline_cyberpunk.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_outline_cyberpunk.xml
com.spyro.vmeet.app-main-6\:/drawable/button_rounded_accent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\button_rounded_accent.xml
com.spyro.vmeet.app-main-6\:/drawable/buzz_pulse_animation.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\buzz_pulse_animation.xml
com.spyro.vmeet.app-main-6\:/drawable/circle_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_background.xml
com.spyro.vmeet.app-main-6\:/drawable/circle_button_bg.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_button_bg.xml
com.spyro.vmeet.app-main-6\:/drawable/circle_dot.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_dot.xml
com.spyro.vmeet.app-main-6\:/drawable/circle_online_status.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circle_online_status.xml
com.spyro.vmeet.app-main-6\:/drawable/circular_button.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circular_button.xml
com.spyro.vmeet.app-main-6\:/drawable/circular_button_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\circular_button_background.xml
com.spyro.vmeet.app-main-6\:/drawable/color_preview_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\color_preview_background.xml
com.spyro.vmeet.app-main-6\:/drawable/color_square_shape.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\color_square_shape.xml
com.spyro.vmeet.app-main-6\:/drawable/color_swatch_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\color_swatch_background.xml
com.spyro.vmeet.app-main-6\:/drawable/comment_input_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\comment_input_background.xml
com.spyro.vmeet.app-main-6\:/drawable/cyberpunk_circle_button.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyberpunk_circle_button.xml
com.spyro.vmeet.app-main-6\:/drawable/cyberpunk_gradient.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\cyberpunk_gradient.xml
com.spyro.vmeet.app-main-6\:/drawable/default_avatar.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\default_avatar.xml
com.spyro.vmeet.app-main-6\:/drawable/default_room_icon.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\default_room_icon.xml
com.spyro.vmeet.app-main-6\:/drawable/distance_badge_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\distance_badge_background.xml
com.spyro.vmeet.app-main-6\:/drawable/glowing_border.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\glowing_border.xml
com.spyro.vmeet.app-main-6\:/drawable/glowing_neon_ring.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\glowing_neon_ring.xml
com.spyro.vmeet.app-main-6\:/drawable/gradient_bottom_overlay.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_bottom_overlay.xml
com.spyro.vmeet.app-main-6\:/drawable/gradient_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_message_received.xml
com.spyro.vmeet.app-main-6\:/drawable/gradient_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_message_sent.xml
com.spyro.vmeet.app-main-6\:/drawable/gradient_overlay.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_overlay.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_add.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_add_gif.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_gif.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_add_image.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_image.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_add_mic.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_mic.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_add_post.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_post.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_add_video.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_add_video.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_admin_crown.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_admin_crown.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_arrow_back.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_back.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_arrow_forward.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_forward.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_attach.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_attach.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_audiotrack.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_audiotrack.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_chat.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chat.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_chat_bubble.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chat_bubble.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_chats.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chats.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_check.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_check_outlined.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_check_outlined.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_close.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_close_circle.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close_circle.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_comment.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_comment.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_comment_outline.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_comment_outline.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_community.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_community.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_default_avatar.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_default_avatar.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_delete.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_delete.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_emoji.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_emoji.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_eye.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_eye.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_favorite_border.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_favorite_border.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_filter.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_filter.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_gif.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_gif.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_heart.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_heart.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_like_outline.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_like_outline.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_location.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_location_permission.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_location_permission.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_mark_read.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_mark_read.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_matches.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_matches.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_more_vert.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_more_vert.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_music_placeholder.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_music_placeholder.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_mute.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_mute.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_notification.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notification.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_notification_vmeet.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_notification_vmeet.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_pause.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_pause.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_placeholder_image.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_placeholder_image.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_play.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_play_arrow.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play_arrow.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_play_circle.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_play_circle.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_profile.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_profile.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_profile_placeholder.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_profile_placeholder.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_radar_empty.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_radar_empty.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_refresh.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_refresh.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_reply.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_reply.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_search.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_search.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_send.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_send.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_send_comment.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_send_comment.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_settings.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_settings.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_share.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_share.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_stop_mic.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_stop_mic.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_swipe.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_swipe.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_topic_placeholder.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_topic_placeholder.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_verified.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_verified.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_vibration.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_vibration.xml
com.spyro.vmeet.app-main-6\:/drawable/ic_visibility.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_visibility.xml
com.spyro.vmeet.app-main-6\:/drawable/image_error.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\image_error.xml
com.spyro.vmeet.app-main-6\:/drawable/image_placeholder.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\image_placeholder.xml
com.spyro.vmeet.app-main-6\:/drawable/indicator_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\indicator_background.xml
com.spyro.vmeet.app-main-6\:/drawable/login_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\login_background.xml
com.spyro.vmeet.app-main-6\:/drawable/message_input_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\message_input_background.xml
com.spyro.vmeet.app-main-6\:/drawable/message_received_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\message_received_background.xml
com.spyro.vmeet.app-main-6\:/drawable/message_sent_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\message_sent_background.xml
com.spyro.vmeet.app-main-6\:/drawable/mic_button_selector.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\mic_button_selector.xml
com.spyro.vmeet.app-main-6\:/drawable/modern_button.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\modern_button.xml
com.spyro.vmeet.app-main-6\:/drawable/modern_button_alt.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\modern_button_alt.xml
com.spyro.vmeet.app-main-6\:/drawable/modern_edit_text_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\modern_edit_text_background.xml
com.spyro.vmeet.app-main-6\:/drawable/neon_button.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button.xml
com.spyro.vmeet.app-main-6\:/drawable/neon_button_alt.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_alt.xml
com.spyro.vmeet.app-main-6\:/drawable/neon_button_blue.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_blue.xml
com.spyro.vmeet.app-main-6\:/drawable/neon_button_green.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_green.xml
com.spyro.vmeet.app-main-6\:/drawable/neon_button_red.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_button_red.xml
com.spyro.vmeet.app-main-6\:/drawable/neon_circle.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\neon_circle.xml
com.spyro.vmeet.app-main-6\:/drawable/online_status_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\online_status_background.xml
com.spyro.vmeet.app-main-6\:/drawable/online_status_indicator.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\online_status_indicator.xml
com.spyro.vmeet.app-main-6\:/drawable/post_edit_text_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\post_edit_text_background.xml
com.spyro.vmeet.app-main-6\:/drawable/preview_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\preview_background.xml
com.spyro.vmeet.app-main-6\:/drawable/progress_bar_cyberpunk.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\progress_bar_cyberpunk.xml
com.spyro.vmeet.app-main-6\:/drawable/radar_header_gradient.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\radar_header_gradient.xml
com.spyro.vmeet.app-main-6\:/drawable/reaction_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_background.xml
com.spyro.vmeet.app-main-6\:/drawable/reaction_background_own.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_background_own.xml
com.spyro.vmeet.app-main-6\:/drawable/reaction_container_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_container_background.xml
com.spyro.vmeet.app-main-6\:/drawable/reaction_selector_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reaction_selector_background.xml
com.spyro.vmeet.app-main-6\:/drawable/recording_button_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\recording_button_background.xml
com.spyro.vmeet.app-main-6\:/drawable/recording_cancel_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\recording_cancel_background.xml
com.spyro.vmeet.app-main-6\:/drawable/reply_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\reply_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_button_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_button_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_button_outline_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_button_outline_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_corner_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_corner_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_dark_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_dark_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_dialog_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_dialog_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_edittext_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_edittext_background.xml
com.spyro.vmeet.app-main-6\:/drawable/rounded_edittext_bg.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\rounded_edittext_bg.xml
com.spyro.vmeet.app-main-6\:/drawable/search_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\search_background.xml
com.spyro.vmeet.app-main-6\:/drawable/section_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\section_background.xml
com.spyro.vmeet.app-main-6\:/drawable/selected_color_border.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\selected_color_border.xml
com.spyro.vmeet.app-main-6\:/drawable/spinner_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\spinner_background.xml
com.spyro.vmeet.app-main-6\:/drawable/status_error.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_error.xml
com.spyro.vmeet.app-main-6\:/drawable/status_pending.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_pending.xml
com.spyro.vmeet.app-main-6\:/drawable/status_read.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_read.xml
com.spyro.vmeet.app-main-6\:/drawable/status_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\status_sent.xml
com.spyro.vmeet.app-main-6\:/drawable/story_ring_seen.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\story_ring_seen.xml
com.spyro.vmeet.app-main-6\:/drawable/story_ring_unseen.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\story_ring_unseen.xml
com.spyro.vmeet.app-main-6\:/drawable/story_ring_unseen_gradient.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\story_ring_unseen_gradient.xml
com.spyro.vmeet.app-main-6\:/drawable/swipe_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\swipe_background.xml
com.spyro.vmeet.app-main-6\:/drawable/tab_dot_default.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_dot_default.xml
com.spyro.vmeet.app-main-6\:/drawable/tab_dot_selected.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_dot_selected.xml
com.spyro.vmeet.app-main-6\:/drawable/tab_selected.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_selected.xml
com.spyro.vmeet.app-main-6\:/drawable/tab_selector.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_selector.xml
com.spyro.vmeet.app-main-6\:/drawable/tab_unselected.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\tab_unselected.xml
com.spyro.vmeet.app-main-6\:/drawable/trait_selected_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\trait_selected_background.xml
com.spyro.vmeet.app-main-6\:/drawable/trait_unselected_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\trait_unselected_background.xml
com.spyro.vmeet.app-main-6\:/drawable/unread_counter_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\unread_counter_background.xml
com.spyro.vmeet.app-main-6\:/drawable/unread_indicator.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\unread_indicator.xml
com.spyro.vmeet.app-main-6\:/drawable/video_progress_bar.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\video_progress_bar.xml
com.spyro.vmeet.app-main-6\:/drawable/voice_note_player_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\voice_note_player_background.xml
com.spyro.vmeet.app-main-6\:/drawable/youtube_play_button_background.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\youtube_play_button_background.xml
com.spyro.vmeet.app-main-6\:/font/app_font.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\app_font.xml
com.spyro.vmeet.app-main-6\:/font/combackhomeregularjemd9.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\combackhomeregularjemd9.ttf
com.spyro.vmeet.app-main-6\:/font/greatsracingfreeforpersonalitalicbll.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\greatsracingfreeforpersonalitalicbll.ttf
com.spyro.vmeet.app-main-6\:/font/inter.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\inter.ttf
com.spyro.vmeet.app-main-6\:/font/interitalic.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\interitalic.ttf
com.spyro.vmeet.app-main-6\:/font/knightwarriorw16n8.otf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\knightwarriorw16n8.otf
com.spyro.vmeet.app-main-6\:/font/orbitron.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\orbitron.ttf
com.spyro.vmeet.app-main-6\:/font/steelarj9vnj.otf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\steelarj9vnj.otf
com.spyro.vmeet.app-main-6\:/font/supremespikekvo8d.otf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\supremespikekvo8d.otf
com.spyro.vmeet.app-main-6\:/font/veniteadoremusrgrba.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\veniteadoremusrgrba.ttf
com.spyro.vmeet.app-main-6\:/font/veniteadoremusstraightyzo6v.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\veniteadoremusstraightyzo6v.ttf
com.spyro.vmeet.app-main-6\:/font/wowdinog33vp.ttf=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\font\\wowdinog33vp.ttf
com.spyro.vmeet.app-main-6\:/layout/activity_admin_panel.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_admin_panel.xml
com.spyro.vmeet.app-main-6\:/layout/activity_blind_date.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_blind_date.xml
com.spyro.vmeet.app-main-6\:/layout/activity_blocked_users.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_blocked_users.xml
com.spyro.vmeet.app-main-6\:/layout/activity_chat.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_chat.xml
com.spyro.vmeet.app-main-6\:/layout/activity_chat_list.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_chat_list.xml
com.spyro.vmeet.app-main-6\:/layout/activity_chat_room.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_chat_room.xml
com.spyro.vmeet.app-main-6\:/layout/activity_comments.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_comments.xml
com.spyro.vmeet.app-main-6\:/layout/activity_community_guidelines.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_community_guidelines.xml
com.spyro.vmeet.app-main-6\:/layout/activity_community_host.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_community_host.xml
com.spyro.vmeet.app-main-6\:/layout/activity_edit_personality_traits.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_edit_personality_traits.xml
com.spyro.vmeet.app-main-6\:/layout/activity_email_verification.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_email_verification.xml
com.spyro.vmeet.app-main-6\:/layout/activity_forgot_password.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_forgot_password.xml
com.spyro.vmeet.app-main-6\:/layout/activity_full_screen_image.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_full_screen_image.xml
com.spyro.vmeet.app-main-6\:/layout/activity_login.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_login.xml
com.spyro.vmeet.app-main-6\:/layout/activity_main_empty.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main_empty.xml
com.spyro.vmeet.app-main-6\:/layout/activity_matches.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_matches.xml
com.spyro.vmeet.app-main-6\:/layout/activity_my_videos.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_my_videos.xml
com.spyro.vmeet.app-main-6\:/layout/activity_password_reset.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_password_reset.xml
com.spyro.vmeet.app-main-6\:/layout/activity_profile.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_profile.xml
com.spyro.vmeet.app-main-6\:/layout/activity_reports.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_reports.xml
com.spyro.vmeet.app-main-6\:/layout/activity_room_loader.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_room_loader.xml
com.spyro.vmeet.app-main-6\:/layout/activity_settings.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_settings.xml
com.spyro.vmeet.app-main-6\:/layout/activity_settings_compat.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_settings_compat.xml
com.spyro.vmeet.app-main-6\:/layout/activity_story_editor.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_story_editor.xml
com.spyro.vmeet.app-main-6\:/layout/activity_story_viewer.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_story_viewer.xml
com.spyro.vmeet.app-main-6\:/layout/activity_swipe.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_swipe.xml
com.spyro.vmeet.app-main-6\:/layout/activity_tutorial.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_tutorial.xml
com.spyro.vmeet.app-main-6\:/layout/activity_upload_video.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_upload_video.xml
com.spyro.vmeet.app-main-6\:/layout/activity_user_management.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_user_management.xml
com.spyro.vmeet.app-main-6\:/layout/activity_user_profile.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_user_profile.xml
com.spyro.vmeet.app-main-6\:/layout/activity_verification.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_verification.xml
com.spyro.vmeet.app-main-6\:/layout/activity_video_feed.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_video_feed.xml
com.spyro.vmeet.app-main-6\:/layout/activity_video_uploader.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_video_uploader.xml
com.spyro.vmeet.app-main-6\:/layout/activity_videos.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_videos.xml
com.spyro.vmeet.app-main-6\:/layout/community_dropdown_menu.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\community_dropdown_menu.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_add_text_with_style.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_add_text_with_style.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_ban_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_ban_user.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_change_email.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_change_email.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_change_password.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_change_password.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_change_username.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_change_username.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_color_palette_picker.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_color_palette_picker.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_comments_bottom_sheet.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_comments_bottom_sheet.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_create_post.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create_post.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_create_room.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create_room.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_custom_color_picker.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_custom_color_picker.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_delete_room_confirmation.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_delete_room_confirmation.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_edit_room.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_edit_room.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_emoji_picker.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_emoji_picker.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_gif_picker.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_gif_picker.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_global_notification.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_global_notification.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_improved_emoji_picker.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_improved_emoji_picker.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_message_options.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_message_options.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_music_search.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_music_search.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_mute_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_mute_user.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_post_viewers.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_post_viewers.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_radar_filters.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_radar_filters.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_report_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_report_user.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_rules_acceptance.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_rules_acceptance.xml
com.spyro.vmeet.app-main-6\:/layout/dialog_story_viewers.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_story_viewers.xml
com.spyro.vmeet.app-main-6\:/layout/dropdown_item.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dropdown_item.xml
com.spyro.vmeet.app-main-6\:/layout/fragment_blind_date.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_blind_date.xml
com.spyro.vmeet.app-main-6\:/layout/fragment_chat_rooms.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_chat_rooms.xml
com.spyro.vmeet.app-main-6\:/layout/fragment_community.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_community.xml
com.spyro.vmeet.app-main-6\:/layout/fragment_private_chats.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_private_chats.xml
com.spyro.vmeet.app-main-6\:/layout/fragment_profiles.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_profiles.xml
com.spyro.vmeet.app-main-6\:/layout/fragment_radar.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_radar.xml
com.spyro.vmeet.app-main-6\:/layout/grid_emoji_category.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\grid_emoji_category.xml
com.spyro.vmeet.app-main-6\:/layout/item_admin_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_admin_user.xml
com.spyro.vmeet.app-main-6\:/layout/item_blind_date_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_blind_date_message_received.xml
com.spyro.vmeet.app-main-6\:/layout/item_blind_date_message_received_modern.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_blind_date_message_received_modern.xml
com.spyro.vmeet.app-main-6\:/layout/item_blind_date_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_blind_date_message_sent.xml
com.spyro.vmeet.app-main-6\:/layout/item_blind_date_message_sent_modern.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_blind_date_message_sent_modern.xml
com.spyro.vmeet.app-main-6\:/layout/item_blind_date_topic.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_blind_date_topic.xml
com.spyro.vmeet.app-main-6\:/layout/item_blocked_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_blocked_user.xml
com.spyro.vmeet.app-main-6\:/layout/item_chat_list.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_chat_list.xml
com.spyro.vmeet.app-main-6\:/layout/item_chat_room.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_chat_room.xml
com.spyro.vmeet.app-main-6\:/layout/item_comment.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_comment.xml
com.spyro.vmeet.app-main-6\:/layout/item_emoji.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_emoji.xml
com.spyro.vmeet.app-main-6\:/layout/item_gif.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_gif.xml
com.spyro.vmeet.app-main-6\:/layout/item_gif_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_gif_message_received.xml
com.spyro.vmeet.app-main-6\:/layout/item_gif_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_gif_message_sent.xml
com.spyro.vmeet.app-main-6\:/layout/item_image_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_image_message_received.xml
com.spyro.vmeet.app-main-6\:/layout/item_image_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_image_message_sent.xml
com.spyro.vmeet.app-main-6\:/layout/item_match.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_match.xml
com.spyro.vmeet.app-main-6\:/layout/item_mention_suggestion.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_mention_suggestion.xml
com.spyro.vmeet.app-main-6\:/layout/item_message_buzz_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_message_buzz_received.xml
com.spyro.vmeet.app-main-6\:/layout/item_message_buzz_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_message_buzz_sent.xml
com.spyro.vmeet.app-main-6\:/layout/item_message_reaction.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_message_reaction.xml
com.spyro.vmeet.app-main-6\:/layout/item_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_message_received.xml
com.spyro.vmeet.app-main-6\:/layout/item_message_sent.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_message_sent.xml
com.spyro.vmeet.app-main-6\:/layout/item_music_search.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_music_search.xml
com.spyro.vmeet.app-main-6\:/layout/item_my_video.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_my_video.xml
com.spyro.vmeet.app-main-6\:/layout/item_personality_trait.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_personality_trait.xml
com.spyro.vmeet.app-main-6\:/layout/item_personality_trait_category.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_personality_trait_category.xml
com.spyro.vmeet.app-main-6\:/layout/item_post.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_post.xml
com.spyro.vmeet.app-main-6\:/layout/item_post_viewer.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_post_viewer.xml
com.spyro.vmeet.app-main-6\:/layout/item_profile_image.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_profile_image.xml
com.spyro.vmeet.app-main-6\:/layout/item_radar_user.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_radar_user.xml
com.spyro.vmeet.app-main-6\:/layout/item_received_gif_message.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_received_gif_message.xml
com.spyro.vmeet.app-main-6\:/layout/item_report.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_report.xml
com.spyro.vmeet.app-main-6\:/layout/item_sent_gif_message.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_sent_gif_message.xml
com.spyro.vmeet.app-main-6\:/layout/item_story.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_story.xml
com.spyro.vmeet.app-main-6\:/layout/item_story_viewer.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_story_viewer.xml
com.spyro.vmeet.app-main-6\:/layout/item_user_card.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_user_card.xml
com.spyro.vmeet.app-main-6\:/layout/item_video.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_video.xml
com.spyro.vmeet.app-main-6\:/layout/item_video_vertical.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_video_vertical.xml
com.spyro.vmeet.app-main-6\:/layout/item_voice_message.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_voice_message.xml
com.spyro.vmeet.app-main-6\:/layout/item_voice_message_received.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_voice_message_received.xml
com.spyro.vmeet.app-main-6\:/layout/layout_blind_date_chat.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_blind_date_chat.xml
com.spyro.vmeet.app-main-6\:/layout/layout_blind_date_chat_modern.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_blind_date_chat_modern.xml
com.spyro.vmeet.app-main-6\:/layout/layout_blind_date_result.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_blind_date_result.xml
com.spyro.vmeet.app-main-6\:/layout/layout_blind_date_reveal.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_blind_date_reveal.xml
com.spyro.vmeet.app-main-6\:/layout/layout_blind_date_waiting.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_blind_date_waiting.xml
com.spyro.vmeet.app-main-6\:/layout/layout_join_room_prompt.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_join_room_prompt.xml
com.spyro.vmeet.app-main-6\:/layout/layout_profile_image_slider.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_profile_image_slider.xml
com.spyro.vmeet.app-main-6\:/layout/layout_reaction_selector.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_reaction_selector.xml
com.spyro.vmeet.app-main-6\:/layout/layout_user_muted.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_user_muted.xml
com.spyro.vmeet.app-main-6\:/layout/menu_community_dropdown.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\menu_community_dropdown.xml
com.spyro.vmeet.app-main-6\:/layout/profile_dropdown_menu.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\profile_dropdown_menu.xml
com.spyro.vmeet.app-main-6\:/layout/tutorial_page.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\tutorial_page.xml
com.spyro.vmeet.app-main-6\:/menu/bottom_nav_menu.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_nav_menu.xml
com.spyro.vmeet.app-main-6\:/menu/chat_menu.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\chat_menu.xml
com.spyro.vmeet.app-main-6\:/menu/menu_location_options.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_location_options.xml
com.spyro.vmeet.app-main-6\:/menu/menu_match_item.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_match_item.xml
com.spyro.vmeet.app-main-6\:/menu/menu_post_options.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_post_options.xml
com.spyro.vmeet.app-main-6\:/menu/menu_room_admin.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_room_admin.xml
com.spyro.vmeet.app-main-6\:/menu/menu_swipe.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\menu_swipe.xml
com.spyro.vmeet.app-main-6\:/menu/profile_menu.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\profile_menu.xml
com.spyro.vmeet.app-main-6\:/menu/room_chat_menu.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\room_chat_menu.xml
com.spyro.vmeet.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.spyro.vmeet.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.spyro.vmeet.app-main-6\:/mipmap-hdpi/ic_launcher.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.spyro.vmeet.app-main-6\:/mipmap-hdpi/ic_launcher_foreground.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.spyro.vmeet.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.spyro.vmeet.app-main-6\:/mipmap-mdpi/ic_launcher.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.spyro.vmeet.app-main-6\:/mipmap-mdpi/ic_launcher_foreground.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.spyro.vmeet.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.spyro.vmeet.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.spyro.vmeet.app-main-6\:/mipmap-xhdpi/ic_launcher_foreground.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.spyro.vmeet.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.spyro.vmeet.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.spyro.vmeet.app-main-6\:/mipmap-xxhdpi/ic_launcher_foreground.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.spyro.vmeet.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.spyro.vmeet.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.spyro.vmeet.app-main-6\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.spyro.vmeet.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.spyro.vmeet.app-main-6\:/raw/welcome.mp3=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\raw\\welcome.mp3
com.spyro.vmeet.app-main-6\:/xml/backup_rules.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.spyro.vmeet.app-main-6\:/xml/data_extraction_rules.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.spyro.vmeet.app-main-6\:/xml/file_paths.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\file_paths.xml
com.spyro.vmeet.app-main-6\:/xml/network_security_config.xml=C\:\\Users\\stark\\AndroidStudioProjects\\VMeet\\VMeet\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\network_security_config.xml
