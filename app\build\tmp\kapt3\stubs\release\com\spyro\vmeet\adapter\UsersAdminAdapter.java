package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\u0013\u0014B\u001b\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\t\u001a\u00020\nH\u0016J\u0018\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00022\u0006\u0010\u000e\u001a\u00020\nH\u0016J\u0018\u0010\u000f\u001a\u00020\u00022\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\nH\u0016R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/spyro/vmeet/adapter/UsersAdminAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/UsersAdminAdapter$UserViewHolder;", "users", "", "Lorg/json/JSONObject;", "listener", "Lcom/spyro/vmeet/adapter/UsersAdminAdapter$UserAdminListener;", "(Ljava/util/List;Lcom/spyro/vmeet/adapter/UsersAdminAdapter$UserAdminListener;)V", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "UserAdminListener", "UserViewHolder", "app_release"})
public final class UsersAdminAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.UsersAdminAdapter.UserViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<org.json.JSONObject> users = null;
    @org.jetbrains.annotations.NotNull()
    private final com.spyro.vmeet.adapter.UsersAdminAdapter.UserAdminListener listener = null;
    
    public UsersAdminAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends org.json.JSONObject> users, @org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.UsersAdminAdapter.UserAdminListener listener) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.UsersAdminAdapter.UserViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.UsersAdminAdapter.UserViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\t\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\n\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u000b"}, d2 = {"Lcom/spyro/vmeet/adapter/UsersAdminAdapter$UserAdminListener;", "", "onBanUser", "", "userObject", "Lorg/json/JSONObject;", "onMuteUser", "onUnbanUser", "onUnmuteUser", "onVerifyUser", "onViewHistory", "app_release"})
    public static abstract interface UserAdminListener {
        
        public abstract void onBanUser(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject);
        
        public abstract void onUnbanUser(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject);
        
        public abstract void onViewHistory(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject);
        
        public abstract void onMuteUser(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject);
        
        public abstract void onUnmuteUser(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject);
        
        public abstract void onVerifyUser(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u0006H\u0002J\u0016\u0010#\u001a\u00020!2\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\'J\u0010\u0010(\u001a\u00020!2\u0006\u0010$\u001a\u00020%H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0015X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/spyro/vmeet/adapter/UsersAdminAdapter$UserViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "API_URL", "", "banInfoLayout", "Landroid/widget/LinearLayout;", "btnBan", "Landroid/widget/Button;", "btnHistory", "btnMute", "btnUnban", "btnUnmute", "btnVerify", "deviceHistoryContainer", "imgAvatar", "Lde/hdodenhof/circleimageview/CircleImageView;", "muteInfoLayout", "tvBanExpiration", "Landroid/widget/TextView;", "tvBanReason", "tvDeviceHistoryTitle", "tvDeviceInfo", "tvEmail", "tvIpHistory", "tvLastIP", "tvMuteExpiration", "tvRole", "tvStatus", "tvUsername", "addDeviceToHistory", "", "deviceId", "bind", "userObject", "Lorg/json/JSONObject;", "listener", "Lcom/spyro/vmeet/adapter/UsersAdminAdapter$UserAdminListener;", "loadUserAvatar", "app_release"})
    public static final class UserViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imgAvatar = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvUsername = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvEmail = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvRole = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvLastIP = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDeviceInfo = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvIpHistory = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout banInfoLayout = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvBanReason = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvBanExpiration = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnBan = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnUnban = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnHistory = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnMute = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnUnmute = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnVerify = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout muteInfoLayout = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvMuteExpiration = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDeviceHistoryTitle = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout deviceHistoryContainer = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String API_URL = "http://77.110.116.89:3000";
        
        public UserViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        org.json.JSONObject userObject, @org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.adapter.UsersAdminAdapter.UserAdminListener listener) {
        }
        
        private final void addDeviceToHistory(java.lang.String deviceId) {
        }
        
        private final void loadUserAvatar(org.json.JSONObject userObject) {
        }
    }
}