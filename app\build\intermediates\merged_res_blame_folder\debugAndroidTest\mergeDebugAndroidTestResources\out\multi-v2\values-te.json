{"logs": [{"outputFile": "com.spyro.vmeet.test.app-mergeDebugAndroidTestResources-32:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\40bc0ded6ebc3196905947594b68c381\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,315,417,518,624,731,2078", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "202,310,412,513,619,726,850,2174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f80b51d56d568461b2fe773f95a970d9\\transformed\\ui-release\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,485,574,663,759,847,931,1004,1077,1161,1251,1328,1405,1474", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "196,283,380,480,569,658,754,842,926,999,1072,1156,1246,1323,1400,1469,1586"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "855,951,1038,1135,1235,1324,1413,1509,1597,1681,1754,1827,1911,2001,2179,2256,2325", "endColumns": "95,86,96,99,88,88,95,87,83,72,72,83,89,76,76,68,116", "endOffsets": "946,1033,1130,1230,1319,1408,1504,1592,1676,1749,1822,1906,1996,2073,2251,2320,2437"}}]}]}