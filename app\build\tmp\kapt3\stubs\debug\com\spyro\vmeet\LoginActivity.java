package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u000e\u001a\u00020\u000fH\u0002J@\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u0016\u001a\u00020\u00142\u0006\u0010\u0017\u001a\u00020\u00142\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0019H\u0002J\b\u0010\u001b\u001a\u00020\u000fH\u0002J\b\u0010\u001c\u001a\u00020\u000fH\u0002J\u0006\u0010\u001d\u001a\u00020\u000fJ\u0012\u0010\u001e\u001a\u00020\u000f2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0014J\u0018\u0010!\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020\u0004H\u0002J\u0010\u0010%\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020#H\u0002J\b\u0010&\u001a\u00020\u000fH\u0002J\b\u0010\'\u001a\u00020\u000fH\u0002J\u0010\u0010(\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020\u0004H\u0002J\u0010\u0010*\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020#H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006+"}, d2 = {"Lcom/spyro/vmeet/LoginActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "()V", "API_URL", "", "genderOptions", "", "isLoginMode", "", "requestPermissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "sexualityOptions", "tokenManager", "Lcom/spyro/vmeet/utils/TokenManager;", "animateDecorationLines", "", "animateLine", "line", "Landroid/view/View;", "minWidth", "", "maxWidth", "minMargin", "maxMargin", "duration", "", "delay", "handleLogin", "handleRegister", "logout", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onLoginSuccess", "userId", "", "username", "registerCurrentFCMToken", "requestNotificationPermission", "showDatePickerDialog", "showSpamWarningDialog", "message", "startNotificationService", "app_debug"})
public final class LoginActivity extends com.spyro.vmeet.activity.BaseActivity {
    private boolean isLoginMode = true;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    private com.spyro.vmeet.utils.TokenManager tokenManager;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> genderOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> sexualityOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String> requestPermissionLauncher = null;
    
    public LoginActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void handleLogin() {
    }
    
    private final void handleRegister() {
    }
    
    private final void startNotificationService(int userId) {
    }
    
    private final void registerCurrentFCMToken(int userId) {
    }
    
    private final void requestNotificationPermission() {
    }
    
    private final void animateDecorationLines() {
    }
    
    private final void animateLine(android.view.View line, float minWidth, float maxWidth, float minMargin, float maxMargin, long duration, long delay) {
    }
    
    private final void showDatePickerDialog() {
    }
    
    private final void onLoginSuccess(int userId, java.lang.String username) {
    }
    
    private final void showSpamWarningDialog(java.lang.String message) {
    }
    
    /**
     * Logout user and clear all tokens
     */
    public final void logout() {
    }
}