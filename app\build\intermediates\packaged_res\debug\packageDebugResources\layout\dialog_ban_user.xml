<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Duración del baneo"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <RadioGroup
        android:id="@+id/radioGroupBanDuration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/radio24h"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="24 horas"/>

        <RadioButton
            android:id="@+id/radio7d"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="7 días"/>

        <RadioButton
            android:id="@+id/radio30d"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="30 días"/>

        <RadioButton
            android:id="@+id/radioPermanent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Permanente"/>
    </RadioGroup>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Motivo del baneo *"
        android:textStyle="bold"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"/>    <EditText
        android:id="@+id/editTextBanReason"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="top"
        android:hint="Especifica el motivo del baneo"
        android:inputType="textMultiLine"
        android:lines="3"
        android:maxLines="5"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Duración en horas (opcional)"
        android:textStyle="bold"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="8dp"/>    <EditText
        android:id="@+id/editTextBanDuration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Dejar vacío para usar la opción seleccionada arriba"
        android:inputType="number"/>

</LinearLayout>