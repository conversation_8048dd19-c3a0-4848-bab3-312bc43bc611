package com.spyro.vmeet.audio;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0007\u0018\u0000 \u00132\u00020\u0001:\u0001\u0013B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\r\u001a\u0004\u0018\u00010\nJ\u0006\u0010\u0005\u001a\u00020\u0006J\u0006\u0010\u000e\u001a\u00020\u000fJ\b\u0010\u0010\u001a\u0004\u0018\u00010\nJ\u0006\u0010\u0011\u001a\u00020\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/spyro/vmeet/audio/AudioRecorder;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "isRecording", "", "mediaRecorder", "Landroid/media/MediaRecorder;", "outputFile", "", "startTime", "", "getOutputFile", "resetRecorder", "", "startRecording", "stopRecording", "", "Companion", "app_debug"})
public final class AudioRecorder {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaRecorder mediaRecorder;
    private boolean isRecording = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String outputFile;
    private long startTime = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AudioRecorder";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.audio.AudioRecorder.Companion Companion = null;
    
    public AudioRecorder(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Start recording audio
     * @return Path to the output file if recording started successfully, null otherwise
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String startRecording() {
        return null;
    }
    
    /**
     * Stop recording audio
     * @return Duration of the recording in seconds
     */
    public final int stopRecording() {
        return 0;
    }
    
    /**
     * Reset and release resources
     */
    public final void resetRecorder() {
    }
    
    public final boolean isRecording() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getOutputFile() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/audio/AudioRecorder$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}