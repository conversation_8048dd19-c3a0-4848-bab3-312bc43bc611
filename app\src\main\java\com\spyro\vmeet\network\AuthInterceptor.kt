package com.spyro.vmeet.network

import android.content.Context
import android.content.Intent
import android.util.Log
import com.spyro.vmeet.LoginActivity
import com.spyro.vmeet.utils.TokenManager
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * HTTP Interceptor that automatically adds JWT tokens to requests
 * and handles token refresh when needed
 */
class AuthInterceptor(private val context: Context) : Interceptor {
    
    companion object {
        private const val TAG = "AuthInterceptor"
        private const val AUTHORIZATION_HEADER = "Authorization"
        private const val BEARER_PREFIX = "Bearer "
    }
    
    private val tokenManager = TokenManager(context)
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Skip auth for login/register/refresh endpoints
        val url = originalRequest.url.toString()
        if (shouldSkipAuth(url)) {
            return chain.proceed(originalRequest)
        }
        
        // Get current access token
        val accessToken = tokenManager.getAccessToken()
        
        if (accessToken.isNullOrEmpty()) {
            Log.w(TAG, "No access token available, redirecting to login")
            redirectToLogin()
            throw IOException("No access token available")
        }
        
        // Add authorization header
        val authenticatedRequest = originalRequest.newBuilder()
            .header(AUTHORIZATION_HEADER, BEARER_PREFIX + accessToken)
            .build()
        
        // Execute request
        val response = chain.proceed(authenticatedRequest)
        
        // Handle token expiration
        if (response.code == 401) {
            Log.d(TAG, "Received 401, attempting token refresh")
            
            // Try to refresh token
            val refreshed = tokenManager.refreshTokenSync()
            
            if (refreshed) {
                Log.d(TAG, "Token refreshed successfully, retrying request")
                
                // Retry with new token
                val newAccessToken = tokenManager.getAccessToken()
                val retryRequest = originalRequest.newBuilder()
                    .header(AUTHORIZATION_HEADER, BEARER_PREFIX + newAccessToken)
                    .build()
                
                response.close() // Close original response
                return chain.proceed(retryRequest)
            } else {
                Log.w(TAG, "Token refresh failed, redirecting to login")
                redirectToLogin()
            }
        }
        
        return response
    }
    
    /**
     * Check if authentication should be skipped for this URL
     */
    private fun shouldSkipAuth(url: String): Boolean {
        val skipPaths = listOf(
            "/auth/login",
            "/auth/register", 
            "/auth/refresh",
            "/auth/forgot-password",
            "/auth/verify_reset_code",
            "/auth/reset_password"
        )
        
        return skipPaths.any { url.contains(it) }
    }
    
    /**
     * Redirect user to login screen
     */
    private fun redirectToLogin() {
        try {
            // Clear stored tokens
            tokenManager.clearTokens()
            
            // Create intent to login activity
            val intent = Intent(context, LoginActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                putExtra("session_expired", true)
            }
            
            context.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Error redirecting to login", e)
        }
    }
}
