package com.spyro.vmeet.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u00142\u0006\u0010\u0016\u001a\u00020\u0004J\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u0013J\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0006\u0010\u0019\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/spyro/vmeet/data/PersonalityTraits;", "", "()V", "CATEGORY_DATE_TYPE", "", "CATEGORY_DATING", "CATEGORY_EDUCATION", "CATEGORY_FACE_REVEALS", "CATEGORY_FAMILY", "CATEGORY_LIFESTYLE", "CATEGORY_MATCHING", "CATEGORY_PETS", "CATEGORY_PRONOUNS", "CATEGORY_RELATIONSHIP", "CATEGORY_RELIGION", "CATEGORY_SCHOOL", "CATEGORY_SEXUALITY", "CATEGORY_WORK", "getAllTraits", "", "Lcom/spyro/vmeet/data/PersonalityTrait;", "getTraitById", "id", "getTraitsByCategory", "Lcom/spyro/vmeet/adapter/TraitCategory;", "category", "app_release"})
public final class PersonalityTraits {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_PRONOUNS = "pronouns";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_SEXUALITY = "sexuality";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_RELATIONSHIP = "relationship";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_FAMILY = "family";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_DATING = "dating";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_MATCHING = "matching";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_DATE_TYPE = "date_type";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_FACE_REVEALS = "face_reveals";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_EDUCATION = "education";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_SCHOOL = "school";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_WORK = "work";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_RELIGION = "religion";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_PETS = "pets";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CATEGORY_LIFESTYLE = "lifestyle";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.data.PersonalityTraits INSTANCE = null;
    
    private PersonalityTraits() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.data.PersonalityTrait> getAllTraits() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.data.PersonalityTrait> getTraitsByCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String category) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.spyro.vmeet.data.PersonalityTrait getTraitById(@org.jetbrains.annotations.NotNull()
    java.lang.String id) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.adapter.TraitCategory> getTraitsByCategory() {
        return null;
    }
}