<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="12dp"
    android:paddingEnd="12dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:background="@color/cyberpunk_background">

    <ImageView
        android:id="@+id/imageViewCommentUserAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:background="@drawable/avatar_circle_border"
        android:padding="2dp"
        android:src="@drawable/ic_profile_placeholder"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewCommentUsername"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:text="Username"
        android:textColor="@color/cyberpunk_text_primary"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@id/imageViewCommentUserAvatar"
        app:layout_constraintTop_toTopOf="@id/imageViewCommentUserAvatar"
        app:layout_constraintEnd_toStartOf="@id/textViewCommentTimestamp" />

    <TextView
        android:id="@+id/textViewCommentTimestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="2h ago"
        android:textColor="@color/cyberpunk_text_secondary"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/textViewCommentUsername" />

    <TextView
        android:id="@+id/textViewCommentContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="This is a comment with cyberpunk style that might span multiple lines of text."
        android:textColor="@color/cyberpunk_text_primary"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="@id/textViewCommentUsername"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewCommentUsername" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="@id/textViewCommentContent"
        app:layout_constraintTop_toBottomOf="@id/textViewCommentContent">

        <ImageButton
            android:id="@+id/buttonCommentLike"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:src="@drawable/ic_like_outline"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:tint="@color/neon_pink" />

        <TextView
            android:id="@+id/textViewCommentLikes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0"
            android:textColor="@color/cyberpunk_text_secondary"
            android:textSize="12sp"
            android:layout_marginStart="4dp"
            android:layout_gravity="center_vertical" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 