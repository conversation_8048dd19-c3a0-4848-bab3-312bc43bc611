const jwt = require('jsonwebtoken');
const { getConnection } = require('../db');
const crypto = require('crypto');

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || crypto.randomBytes(64).toString('hex');
const JWT_EXPIRES_IN = '24h';
const REFRESH_TOKEN_EXPIRES_IN = '7d';

// Security logging
const securityLogger = {
    log: (level, message, details = {}) => {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`, details);
        // TODO: Implement proper logging to file/database
    },

    warn: function(message, details) { this.log('warn', message, details); },
    error: function(message, details) { this.log('error', message, details); },
    info: function(message, details) { this.log('info', message, details); }
};

/**
 * Generate JWT access token
 */
const generateAccessToken = (userId, userInfo = {}) => {
    const payload = {
        userId: parseInt(userId),
        username: userInfo.username,
        email: userInfo.email,
        role: userInfo.role || 'user',
        type: 'access',
        iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, JWT_SECRET, {
        expiresIn: JWT_EXPIRES_IN,
        issuer: 'vmeet-api',
        audience: 'vmeet-app'
    });
};

/**
 * Generate JWT refresh token
 */
const generateRefreshToken = (userId) => {
    const payload = {
        userId: parseInt(userId),
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, JWT_REFRESH_SECRET, {
        expiresIn: REFRESH_TOKEN_EXPIRES_IN,
        issuer: 'vmeet-api',
        audience: 'vmeet-app'
    });
};

/**
 * Store refresh token in database
 */
const storeRefreshToken = async (userId, refreshToken) => {
    let conn;
    try {
        conn = await getConnection();

        // Remove old refresh tokens for this user
        await conn.execute(
            'DELETE FROM user_refresh_tokens WHERE user_id = ?',
            [userId]
        );

        // Store new refresh token
        await conn.execute(
            'INSERT INTO user_refresh_tokens (user_id, token, expires_at, created_at) VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 7 DAY), NOW())',
            [userId, refreshToken]
        );

        await conn.end();
    } catch (error) {
        if (conn) await conn.end().catch(() => {});
        throw error;
    }
};

/**
 * Verify JWT access token middleware with backward compatibility
 */
const verifyAccessToken = async (req, res, next) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;

    // Check for legacy userId in query params, body, or URL path for backward compatibility
    const legacyUserId = req.query.userId || req.body.userId || req.params.userId || req.params.id;

    // Extract userId from URL path if present (e.g., /matches/1, /user/1)
    const pathUserId = req.path.match(/\/(\d+)$/)?.[1];
    const finalLegacyUserId = legacyUserId || pathUserId;

    // For backward compatibility, allow requests with userId but log them
    if (!token && finalLegacyUserId) {
        securityLogger.info('Legacy authentication attempt', {
            userId: finalLegacyUserId,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path,
            method: req.method
        });

        // Continue with legacy authentication
        try {
            const userId = parseInt(finalLegacyUserId);
            if (isNaN(userId) || userId <= 0) {
                securityLogger.warn('Invalid legacy userId', {
                    userId: finalLegacyUserId,
                    ip: req.ip,
                    path: req.path
                });
                return res.status(401).json({
                    error: 'ID de usuario inválido',
                    code: 'INVALID_USER_ID'
                });
            }

            // Set minimal user info for legacy requests
            req.user = {
                id: userId,
                userId: userId,
                isLegacy: true
            };

            // Continue to next middleware
            next();
            return;

        } catch (error) {
            securityLogger.error('Legacy authentication error', {
                error: error.message,
                userId: finalLegacyUserId,
                ip: req.ip
            });
            return res.status(500).json({
                error: 'Error de autenticación',
                code: 'AUTH_ERROR'
            });
        }
    }

    // If no token and no userId, reject
    if (!token && !finalLegacyUserId) {
        securityLogger.warn('Access attempt without token or userId', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path
        });
        return res.status(401).json({
            error: 'Acceso denegado',
            message: 'Token de autenticación requerido',
            code: 'NO_TOKEN'
        });
    }

    // If we have a JWT token, use the new system
    if (token) {
        try {
            // Verify token
            const decoded = jwt.verify(token, JWT_SECRET, {
                issuer: 'vmeet-api',
                audience: 'vmeet-app'
            });

            if (decoded.type !== 'access') {
                throw new Error('Invalid token type');
            }

            // Verify user still exists and is not banned
            let conn;
            try {
                conn = await getConnection();

                const [userRows] = await conn.execute(
                    'SELECT id, username, email, role, verified_status FROM users WHERE id = ?',
                    [decoded.userId]
                );

                if (userRows.length === 0) {
                    await conn.end();
                    securityLogger.warn('Token for non-existent user', {
                        userId: decoded.userId,
                        ip: req.ip
                    });
                    return res.status(401).json({
                        error: 'Usuario no encontrado',
                        code: 'USER_NOT_FOUND'
                    });
                }

                // Check if user is banned (skip for some routes)
                if (!req.path.includes('/auth/refresh') && !req.path.includes('/auth/logout')) {
                    const [banRows] = await conn.execute(
                        'SELECT * FROM user_bans WHERE user_id = ? AND (banned_until IS NULL OR banned_until > NOW()) ORDER BY id DESC LIMIT 1',
                        [decoded.userId]
                    );

                    if (banRows.length > 0) {
                        await conn.end();
                        const ban = banRows[0];
                        securityLogger.warn('Banned user access attempt', {
                            userId: decoded.userId,
                            reason: ban.reason,
                            ip: req.ip
                        });
                        return res.status(403).json({
                            error: 'Cuenta suspendida',
                            message: 'Tu cuenta ha sido suspendida',
                            reason: ban.reason,
                            bannedUntil: ban.banned_until,
                            code: 'USER_BANNED'
                        });
                    }
                }

                await conn.end();

                // Set user info in request
                req.user = {
                    id: userRows[0].id,
                    userId: userRows[0].id, // For backward compatibility
                    username: userRows[0].username,
                    email: userRows[0].email,
                    role: userRows[0].role,
                    isAdmin: userRows[0].role === 'admin',
                    verifiedStatus: userRows[0].verified_status
                };

                // Log successful authentication for admin users
                if (req.user.isAdmin) {
                    securityLogger.info('Admin access', {
                        userId: req.user.id,
                        username: req.user.username,
                        path: req.path,
                        ip: req.ip
                    });
                }

                next();

            } catch (dbError) {
                if (conn) await conn.end().catch(() => {});
                throw dbError;
            }

        } catch (error) {
            securityLogger.warn('Invalid token attempt', {
                error: error.message,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                path: req.path
            });

            if (error.name === 'TokenExpiredError') {
                return res.status(401).json({
                    error: 'Token expirado',
                    message: 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.',
                    code: 'TOKEN_EXPIRED'
                });
            } else if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({
                    error: 'Token inválido',
                    message: 'Token de autenticación inválido',
                    code: 'INVALID_TOKEN'
                });
            } else {
                return res.status(500).json({
                    error: 'Error de autenticación',
                    message: 'Error interno del servidor',
                    code: 'AUTH_ERROR'
                });
            }
        }
    }
};

/**
 * Verify admin role middleware with legacy support
 */
const verifyAdmin = async (req, res, next) => {
    // Check for legacy adminId parameter
    const legacyAdminId = req.query.adminId || req.body.adminId;

    if (!req.user && !legacyAdminId) {
        return res.status(401).json({
            error: 'Autenticación requerida',
            code: 'NO_AUTH'
        });
    }

    // If we have JWT user, use that
    if (req.user) {
        if (!req.user.isAdmin) {
            securityLogger.warn('Non-admin access attempt to admin route', {
                userId: req.user.id,
                username: req.user.username,
                path: req.path,
                ip: req.ip,
                isLegacy: req.user.isLegacy || false
            });
            return res.status(403).json({
                error: 'Acceso denegado',
                message: 'Se requieren privilegios de administrador',
                code: 'ADMIN_REQUIRED'
            });
        }

        next();
        return;
    }

    // Legacy admin verification
    if (legacyAdminId) {
        try {
            const adminId = parseInt(legacyAdminId);
            if (isNaN(adminId) || adminId <= 0) {
                return res.status(401).json({
                    error: 'ID de administrador inválido',
                    code: 'INVALID_ADMIN_ID'
                });
            }

            let conn;
            try {
                conn = await getConnection();

                const [adminRows] = await conn.execute(
                    'SELECT id, username, email, role FROM users WHERE id = ? AND role = "admin"',
                    [adminId]
                );

                await conn.end();

                if (adminRows.length === 0) {
                    securityLogger.warn('Legacy admin access attempt with invalid ID', {
                        adminId: adminId,
                        ip: req.ip,
                        path: req.path
                    });
                    return res.status(403).json({
                        error: 'Acceso denegado',
                        message: 'Se requieren privilegios de administrador',
                        code: 'ADMIN_REQUIRED'
                    });
                }

                // Set admin user info for legacy requests
                req.user = {
                    id: adminRows[0].id,
                    userId: adminRows[0].id,
                    username: adminRows[0].username,
                    email: adminRows[0].email,
                    role: adminRows[0].role,
                    isAdmin: true,
                    isLegacy: true
                };

                securityLogger.info('Legacy admin access', {
                    adminId: req.user.id,
                    username: req.user.username,
                    path: req.path,
                    ip: req.ip
                });

                next();

            } catch (dbError) {
                if (conn) await conn.end().catch(() => {});
                throw dbError;
            }

        } catch (error) {
            securityLogger.error('Legacy admin verification error', {
                error: error.message,
                adminId: legacyAdminId,
                ip: req.ip
            });
            return res.status(500).json({
                error: 'Error de verificación de administrador',
                code: 'ADMIN_VERIFICATION_ERROR'
            });
        }
    }
};

/**
 * Verify refresh token
 */
const verifyRefreshToken = async (refreshToken) => {
    try {
        const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET, {
            issuer: 'vmeet-api',
            audience: 'vmeet-app'
        });

        if (decoded.type !== 'refresh') {
            throw new Error('Invalid token type');
        }

        // Check if refresh token exists in database
        let conn;
        try {
            conn = await getConnection();

            const [tokenRows] = await conn.execute(
                'SELECT * FROM user_refresh_tokens WHERE user_id = ? AND token = ? AND expires_at > NOW()',
                [decoded.userId, refreshToken]
            );

            await conn.end();

            if (tokenRows.length === 0) {
                throw new Error('Refresh token not found or expired');
            }

            return decoded;

        } catch (dbError) {
            if (conn) await conn.end().catch(() => {});
            throw dbError;
        }

    } catch (error) {
        throw error;
    }
};

/**
 * Remove refresh token from database
 */
const removeRefreshToken = async (userId, refreshToken = null) => {
    let conn;
    try {
        conn = await getConnection();

        if (refreshToken) {
            await conn.execute(
                'DELETE FROM user_refresh_tokens WHERE user_id = ? AND token = ?',
                [userId, refreshToken]
            );
        } else {
            // Remove all refresh tokens for user
            await conn.execute(
                'DELETE FROM user_refresh_tokens WHERE user_id = ?',
                [userId]
            );
        }

        await conn.end();
    } catch (error) {
        if (conn) await conn.end().catch(() => {});
        throw error;
    }
};

/**
 * Permissive authentication middleware for transition period
 * Allows both JWT and legacy authentication without strict validation
 */
const permissiveAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.startsWith('Bearer ') ? authHeader.slice(7) : null;

        // Check for legacy userId in various places
        const legacyUserId = req.query.userId || req.body.userId || req.params.userId || req.params.id;
        const pathUserId = req.path.match(/\/(\d+)$/)?.[1];
        const finalUserId = legacyUserId || pathUserId;

        // Try JWT authentication first if token is present
        if (token) {
            try {
                const decoded = jwt.verify(token, JWT_SECRET, {
                    issuer: 'vmeet-api',
                    audience: 'vmeet-app'
                });

                // Validate decoded token has required properties
                if (decoded && typeof decoded === 'object' && decoded.userId) {
                    req.user = {
                        id: decoded.userId,
                        userId: decoded.userId,
                        username: decoded.username || '',
                        email: decoded.email || '',
                        role: decoded.role || 'user',
                        isAdmin: decoded.role === 'admin',
                        isLegacy: false
                    };

                    securityLogger.info('JWT authentication successful', {
                        userId: req.user.id,
                        path: req.path,
                        ip: req.ip
                    });

                    // JWT successful, continue
                    next();
                    return;
                }
            } catch (jwtError) {
                securityLogger.warn('JWT authentication failed, falling back to legacy', {
                    error: jwtError.message,
                    ip: req.ip,
                    path: req.path
                });
                // Continue to legacy authentication
            }
        }

        // Use legacy authentication if JWT failed or not present
        if (finalUserId) {
            const userId = parseInt(finalUserId);
            if (!isNaN(userId) && userId > 0) {
                req.user = {
                    id: userId,
                    userId: userId,
                    isLegacy: true
                };

                securityLogger.info('Legacy authentication used', {
                    userId: userId,
                    path: req.path,
                    ip: req.ip,
                    userAgent: req.get('User-Agent')
                });
            }
        }

        // Always continue, even without authentication
        next();

    } catch (error) {
        // If anything fails, log it but continue anyway
        securityLogger.error('Permissive auth middleware error', {
            error: error.message,
            stack: error.stack,
            ip: req.ip,
            path: req.path
        });

        // Continue without authentication
        next();
    }
};

module.exports = {
    generateAccessToken,
    generateRefreshToken,
    storeRefreshToken,
    verifyAccessToken,
    verifyAdmin,
    verifyRefreshToken,
    removeRefreshToken,
    permissiveAuth,
    securityLogger,
    JWT_SECRET,
    JWT_REFRESH_SECRET
};
