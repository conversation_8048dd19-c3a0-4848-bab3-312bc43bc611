package com.spyro.vmeet.utils;

/**
 * Utilidad para solicitar el envío de correos electrónicos a través del backend
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002JB\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u00042\u001a\u0010\r\u001a\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0004\u0012\u00020\u00070\u000eJB\u0010\u0010\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0011\u001a\u00020\u00042\u001a\u0010\r\u001a\u0016\u0012\u0004\u0012\u00020\u000f\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0004\u0012\u00020\u00070\u000eR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/utils/EmailUtils;", "", "()V", "API_URL", "", "TAG", "sendPasswordResetEmail", "", "context", "Landroid/content/Context;", "recipientEmail", "username", "resetCode", "callback", "Lkotlin/Function2;", "", "sendVerificationEmail", "verificationCode", "app_debug"})
public final class EmailUtils {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "EmailUtils";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://*************:3000";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.utils.EmailUtils INSTANCE = null;
    
    private EmailUtils() {
        super();
    }
    
    /**
     * Solicita al backend el envío de un correo electrónico de verificación al usuario
     *
     * @param context Contexto de la aplicación
     * @param recipientEmail Correo electrónico del destinatario
     * @param username Nombre de usuario
     * @param verificationCode Código de verificación
     * @param callback Callback para notificar el resultado del envío
     */
    public final void sendVerificationEmail(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String recipientEmail, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    java.lang.String verificationCode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
    
    /**
     * Envía un correo electrónico para restablecer la contraseña
     *
     * @param context Contexto de la aplicación
     * @param recipientEmail Correo electrónico del destinatario
     * @param username Nombre de usuario
     * @param resetCode Código de restablecimiento
     * @param callback Callback para notificar el resultado del envío
     */
    public final void sendPasswordResetEmail(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String recipientEmail, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    java.lang.String resetCode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> callback) {
    }
}