<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/cyberpunk_gradient">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="TENOR GIFS"
            android:textStyle="bold"
            android:textColor="@color/neon_blue"
            android:textSize="20sp"
            android:fontFamily="sans-serif-condensed"
            android:layout_marginBottom="8dp" />

        <ImageButton
            android:id="@+id/buttonCloseGifPicker"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/neon_purple"
            android:contentDescription="Close" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <EditText
            android:id="@+id/editTextGifSearch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Search Tenor"
            android:textColorHint="#80FFFFFF"
            android:textColor="@color/white"
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:padding="12dp"
            android:background="@drawable/message_input_background" />
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="400dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewGifs"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:visibility="gone" />

        <ProgressBar
            android:id="@+id/progressBarGifs"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminateTint="@color/neon_green"
            android:layout_gravity="center" />

        <TextView
            android:id="@+id/textViewNoResults"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="No GIFs found"
            android:textColor="@color/neon_pink"
            android:visibility="gone" />
    </FrameLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:padding="4dp"
        android:text="Powered by Tenor"
        android:textColor="@color/neon_blue"
        android:textSize="12sp"
        android:textStyle="italic" />

</LinearLayout> 