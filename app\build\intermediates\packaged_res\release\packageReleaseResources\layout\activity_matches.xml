<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#1A1A3A"
        android:elevation="4dp"
        android:paddingTop="0dp"
        android:minHeight="?attr/actionBarSize"
        app:titleTextColor="@color/comfy_blue"
        app:titleTextAppearance="@style/TextAppearance.VMeet.Headline2"
        android:layout_marginTop="@dimen/status_bar_height"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Tus Matches"
            android:textColor="@color/comfy_blue"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_gravity="center"/>
    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/tvMatchesHeader"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Personas que te han dado like"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewMatches"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/tvMatchesHeader"
        app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingVertical="4dp"
        android:clipToPadding="false"
        tools:listitem="@layout/item_match"
        tools:itemCount="5"/>

    <!-- Empty state card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/emptyStateCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardBackgroundColor="#1A1A3A"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/tvMatchesHeader"
        app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_max="400dp"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_matches"
                android:tint="@color/comfy_blue"
                android:alpha="0.8"
                android:layout_marginBottom="16dp"/>

            <TextView
                android:id="@+id/textViewEmpty"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No tienes matches todavía"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textAlignment="center"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Sigue dando like a perfiles para encontrar coincidencias"
                android:textColor="#AAAAAA"
                android:textSize="14sp"
                android:textAlignment="center"
                android:layout_marginTop="8dp"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_marginBottom="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:menu="@menu/bottom_nav_menu"
        app:elevation="8dp"
        style="@style/CustomBottomNavigation"/>

</androidx.constraintlayout.widget.ConstraintLayout>
