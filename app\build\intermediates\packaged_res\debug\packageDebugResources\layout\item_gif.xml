<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="3dp"
    app:cardBackgroundColor="@color/dark_blue">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imageViewGif"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:scaleType="centerCrop"
            android:contentDescription="GIF image" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/neon_purple"
            android:layout_marginHorizontal="4dp" />

        <TextView
            android:id="@+id/textViewGifTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:padding="8dp"
            android:textColor="@color/neon_green"
            android:textSize="12sp" />
    </LinearLayout>
</androidx.cardview.widget.CardView> 