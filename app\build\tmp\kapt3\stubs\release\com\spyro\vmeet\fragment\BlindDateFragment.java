package com.spyro.vmeet.fragment;

/**
 * Fragment for the Blind Date feature
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00d2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\r\b\u0007\u0018\u0000 n2\u00020\u0001:\u0002mnB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010F\u001a\u00020GH\u0002J\b\u0010H\u001a\u00020GH\u0002J\u000e\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00150JH\u0002J\b\u0010K\u001a\u00020LH\u0002J\u0010\u0010M\u001a\u00020G2\u0006\u0010N\u001a\u00020\u0015H\u0002J\b\u0010O\u001a\u00020GH\u0002J\b\u0010P\u001a\u00020GH\u0002J&\u0010Q\u001a\u0004\u0018\u00010\u001e2\u0006\u0010R\u001a\u00020S2\b\u0010T\u001a\u0004\u0018\u00010U2\b\u0010V\u001a\u0004\u0018\u00010WH\u0016J\b\u0010X\u001a\u00020GH\u0016J\b\u0010Y\u001a\u00020GH\u0016J\b\u0010Z\u001a\u00020GH\u0016J\b\u0010[\u001a\u00020GH\u0002J\u0010\u0010\\\u001a\u00020G2\u0006\u0010]\u001a\u00020LH\u0002J\b\u0010^\u001a\u00020GH\u0002J\b\u0010_\u001a\u00020GH\u0002J\u0010\u0010`\u001a\u00020G2\u0006\u0010a\u001a\u00020bH\u0002J\b\u0010c\u001a\u00020GH\u0002J\b\u0010d\u001a\u00020GH\u0002J\b\u0010e\u001a\u00020GH\u0002J\b\u0010f\u001a\u00020GH\u0002J\b\u0010g\u001a\u00020GH\u0002J\b\u0010h\u001a\u00020GH\u0002J\b\u0010i\u001a\u00020GH\u0002J\b\u0010j\u001a\u00020GH\u0002J\b\u0010k\u001a\u00020GH\u0002J\b\u0010l\u001a\u00020GH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010%\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010)\u001a\b\u0012\u0004\u0012\u00020+0*X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010,\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020.X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u000201X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u000201X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00104\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00105\u001a\u0004\u0018\u000106X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020(X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010<\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010@\u001a\u00020AX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010B\u001a\b\u0012\u0004\u0012\u00020\u00150*X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010C\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010D\u001a\u0004\u0018\u00010EX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006o"}, d2 = {"Lcom/spyro/vmeet/fragment/BlindDateFragment;", "Landroidx/fragment/app/Fragment;", "()V", "REVEAL_TIMEOUT_MS", "", "buttonCancelWaiting", "Landroid/widget/Button;", "buttonContinueToChat", "buttonLeaveChat", "buttonNewMatch", "buttonRevealNo", "buttonRevealYes", "buttonSendMessage", "Landroid/widget/ImageButton;", "chatMessageAdapter", "Lcom/spyro/vmeet/adapter/BlindDateMessageAdapter;", "countDownTimer", "Landroid/os/CountDownTimer;", "currentState", "Lcom/spyro/vmeet/fragment/BlindDateFragment$BlindDateState;", "currentTopic", "Lcom/spyro/vmeet/data/BlindDateTopic;", "editTextChatMessage", "Landroid/widget/EditText;", "imageViewOtherUserAvatar", "Landroid/widget/ImageView;", "imageViewUserAvatar", "lastMessageId", "", "layoutChat", "Landroid/view/View;", "layoutMatchResult", "Landroid/widget/LinearLayout;", "layoutResult", "layoutReveal", "layoutTopicSelection", "layoutWaiting", "messagePollingHandler", "Landroid/os/Handler;", "messagePollingRunnable", "Ljava/lang/Runnable;", "messages", "", "Lcom/spyro/vmeet/data/BlindDateMessage;", "pollingHandler", "progressBar", "Landroid/widget/ProgressBar;", "progressBarReveal", "recyclerViewChat", "Landroidx/recyclerview/widget/RecyclerView;", "recyclerViewTopics", "remainingSeconds", "revealTimeoutHandler", "session", "Lcom/spyro/vmeet/data/BlindDateSession;", "sessionPollingRunnable", "textViewChatTimer", "Landroid/widget/TextView;", "textViewChatTopic", "textViewEmpty", "textViewResultMessage", "textViewUserNames", "textViewWaitingForOther", "textViewWaitingTopic", "topicAdapter", "Lcom/spyro/vmeet/adapter/BlindDateTopicAdapter;", "topics", "userId", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "cancelWaiting", "", "continueToChat", "createDummyTopics", "", "getCurrentISOTimestamp", "", "joinQueue", "topic", "leaveChat", "loadTopics", "onCreateView", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onPause", "onResume", "pollForMessages", "processWebSocketMessage", "message", "resetToTopicSelection", "sendChatMessage", "sendRevealDecision", "reveal", "", "setupRecyclerView", "setupWebSocket", "showLeaveChatConfirmation", "startChatTimer", "startMessagePolling", "startSessionPolling", "stopMessagePolling", "stopSessionPolling", "updateTimerDisplay", "updateUIForCurrentState", "BlindDateState", "Companion", "app_release"})
public final class BlindDateFragment extends androidx.fragment.app.Fragment {
    private android.view.View layoutTopicSelection;
    private androidx.recyclerview.widget.RecyclerView recyclerViewTopics;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewEmpty;
    private com.spyro.vmeet.adapter.BlindDateTopicAdapter topicAdapter;
    private android.view.View layoutWaiting;
    private android.widget.TextView textViewWaitingTopic;
    private android.widget.Button buttonCancelWaiting;
    private android.view.View layoutChat;
    private android.widget.TextView textViewChatTopic;
    private android.widget.TextView textViewChatTimer;
    private androidx.recyclerview.widget.RecyclerView recyclerViewChat;
    private android.widget.EditText editTextChatMessage;
    private android.widget.ImageButton buttonSendMessage;
    private android.widget.Button buttonLeaveChat;
    private com.spyro.vmeet.adapter.BlindDateMessageAdapter chatMessageAdapter;
    private android.view.View layoutReveal;
    private android.widget.Button buttonRevealYes;
    private android.widget.Button buttonRevealNo;
    private android.widget.TextView textViewWaitingForOther;
    private android.widget.ProgressBar progressBarReveal;
    private android.view.View layoutResult;
    private android.widget.TextView textViewResultMessage;
    private android.widget.LinearLayout layoutMatchResult;
    private android.widget.ImageView imageViewUserAvatar;
    private android.widget.ImageView imageViewOtherUserAvatar;
    private android.widget.TextView textViewUserNames;
    private android.widget.Button buttonContinueToChat;
    private android.widget.Button buttonNewMatch;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.BlindDateTopic> topics = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.BlindDateMessage> messages = null;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.WebSocketClient webSocketClient;
    @org.jetbrains.annotations.NotNull()
    private com.spyro.vmeet.fragment.BlindDateFragment.BlindDateState currentState = com.spyro.vmeet.fragment.BlindDateFragment.BlindDateState.TOPIC_SELECTION;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.BlindDateTopic currentTopic;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.BlindDateSession session;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler messagePollingHandler;
    private int lastMessageId = 0;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "BlindDateFragment";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String WS_URL = "ws://77.110.116.89:3000";
    private static final int NORMAL_CLOSURE_STATUS = 1000;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler pollingHandler;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Runnable sessionPollingRunnable = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Runnable messagePollingRunnable = null;
    @org.jetbrains.annotations.Nullable()
    private android.os.CountDownTimer countDownTimer;
    private int remainingSeconds = 150;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler revealTimeoutHandler;
    private final long REVEAL_TIMEOUT_MS = 30000L;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.fragment.BlindDateFragment.Companion Companion = null;
    
    public BlindDateFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void loadTopics() {
    }
    
    private final void joinQueue(com.spyro.vmeet.data.BlindDateTopic topic) {
    }
    
    private final void startSessionPolling() {
    }
    
    private final void stopSessionPolling() {
    }
    
    private final void cancelWaiting() {
    }
    
    private final void setupWebSocket() {
    }
    
    private final void processWebSocketMessage(java.lang.String message) {
    }
    
    private final void updateUIForCurrentState() {
    }
    
    private final void startMessagePolling() {
    }
    
    private final void stopMessagePolling() {
    }
    
    private final void pollForMessages() {
    }
    
    /**
     * Create dummy topics for testing when server is not available
     */
    private final java.util.List<com.spyro.vmeet.data.BlindDateTopic> createDummyTopics() {
        return null;
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    private final void startChatTimer() {
    }
    
    private final void updateTimerDisplay() {
    }
    
    private final void sendChatMessage() {
    }
    
    private final java.lang.String getCurrentISOTimestamp() {
        return null;
    }
    
    private final void showLeaveChatConfirmation() {
    }
    
    private final void leaveChat() {
    }
    
    private final void sendRevealDecision(boolean reveal) {
    }
    
    private final void continueToChat() {
    }
    
    private final void resetToTopicSelection() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/spyro/vmeet/fragment/BlindDateFragment$BlindDateState;", "", "(Ljava/lang/String;I)V", "TOPIC_SELECTION", "WAITING", "CHAT", "REVEAL", "RESULT", "app_release"})
    static enum BlindDateState {
        /*public static final*/ TOPIC_SELECTION /* = new TOPIC_SELECTION() */,
        /*public static final*/ WAITING /* = new WAITING() */,
        /*public static final*/ CHAT /* = new CHAT() */,
        /*public static final*/ REVEAL /* = new REVEAL() */,
        /*public static final*/ RESULT /* = new RESULT() */;
        
        BlindDateState() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.spyro.vmeet.fragment.BlindDateFragment.BlindDateState> getEntries() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0006R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/spyro/vmeet/fragment/BlindDateFragment$Companion;", "", "()V", "API_URL", "", "NORMAL_CLOSURE_STATUS", "", "TAG", "WS_URL", "newInstance", "Lcom/spyro/vmeet/fragment/BlindDateFragment;", "userId", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.fragment.BlindDateFragment newInstance(int userId) {
            return null;
        }
    }
}