<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    android:layout_marginHorizontal="6dp"
    app:cardCornerRadius="12dp"
    app:cardBackgroundColor="#1A1A3A"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewAvatar"
            android:layout_width="50dp"
            android:layout_height="50dp"
            app:cardCornerRadius="25dp"
            app:cardElevation="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/imageViewAvatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                tools:src="@drawable/ic_profile_placeholder"/>
                
        </androidx.cardview.widget.CardView>
        
        <View
            android:id="@+id/avatarIndicator"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:background="@drawable/status_pending"
            android:backgroundTint="@color/comfy_blue"
            app:layout_constraintEnd_toEndOf="@id/cardViewAvatar"
            app:layout_constraintBottom_toBottomOf="@id/cardViewAvatar"/>

        <TextView
            android:id="@+id/textViewUsername"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Username"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:textSize="16sp"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toEndOf="@id/cardViewAvatar"
            app:layout_constraintTop_toTopOf="@id/cardViewAvatar"
            app:layout_constraintEnd_toStartOf="@id/buttonMenuMatch"
            app:layout_constraintHorizontal_bias="0.0"
            tools:text="Maria Gonzalez"/>

        <TextView
            android:id="@+id/textViewLastMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Último mensaje..."
            android:textColor="#CCCCCC"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            app:layout_constraintStart_toEndOf="@id/cardViewAvatar"
            app:layout_constraintTop_toBottomOf="@id/textViewUsername"
            app:layout_constraintEnd_toStartOf="@id/buttonMenuMatch"
            tools:text="Hola! ¿Cómo estás? Me gustaría conocerte mejor..."/>

        <TextView
            android:id="@+id/textViewTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12:30"
            android:textColor="#AAAAAA"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@id/buttonMenuMatch"
            app:layout_constraintTop_toTopOf="@id/textViewUsername"
            android:layout_marginEnd="8dp"
            tools:text="12:30"/>

        <!-- Menu button for options like unmatch -->
        <ImageButton
            android:id="@+id/buttonMenuMatch"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Menu options"
            android:src="@drawable/ic_more_vert"
            android:tint="#FFFFFF"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView> 