<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.spyro.vmeet"
    android:versionCode="29"
    android:versionName="0.90" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- Location permissions for Radar feature - ONLY foreground access -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <queries>

        <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />

    <permission
        android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.spyro.vmeet.VMeetApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.VMeet" >
        <activity
            android:name="com.spyro.vmeet.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.VMeet" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="com.spyro.vmeet.LoginActivity" />
        <activity android:name="com.spyro.vmeet.TutorialActivity" />
        <activity
            android:name="com.spyro.vmeet.ProfileActivity"
            android:exported="false" />
        <activity
            android:name="com.spyro.vmeet.BlockedUsersActivity"
            android:exported="false" />
        <activity android:name="com.spyro.vmeet.SwipeActivity" />
        <activity android:name="com.spyro.vmeet.MatchesActivity" />
        <activity android:name="com.spyro.vmeet.CommunityHostActivity" />
        <activity android:name="com.spyro.vmeet.ui.community.CommentsActivity" />
        <activity android:name="com.spyro.vmeet.ui.community.StoryEditorActivity" />
        <activity
            android:name="com.spyro.vmeet.activity.BlindDateActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity
            android:name="com.spyro.vmeet.activity.ChatActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
        <activity android:name="com.spyro.vmeet.activity.ChatListActivity" />
        <activity android:name="com.spyro.vmeet.activity.SettingsActivity" />

        <!-- Email verification and password reset activities -->
        <activity android:name="com.spyro.vmeet.activity.EmailVerificationActivity" />
        <activity android:name="com.spyro.vmeet.activity.PasswordResetActivity" />
        <activity android:name="com.spyro.vmeet.activity.ForgotPasswordActivity" />

        <!-- Chat Room Loader - handles safe transition to chat room -->
        <activity
            android:name="com.spyro.vmeet.activity.RoomLoaderActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:theme="@style/Theme.VMeet.NoActionBar"
            android:windowSoftInputMode="adjustResize" />

        <!-- Chat Room Activity for group chat functionality -->
        <activity
            android:name="com.spyro.vmeet.activity.ChatRoomActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
            android:excludeFromRecents="false"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />

        <!-- Video feature activities -->
        <activity
            android:name="com.spyro.vmeet.VideoFeedActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.VMeet.NoActionBar" />
        <activity
            android:name="com.spyro.vmeet.UploadVideoActivity"
            android:theme="@style/Theme.VMeet.NoActionBar" />
        <activity
            android:name="com.spyro.vmeet.MyVideosActivity"
            android:theme="@style/Theme.VMeet.NoActionBar" />

        <!-- Admin panel activities -->
        <activity
            android:name="com.spyro.vmeet.AdminPanelActivity"
            android:label="Panel de Administración" />
        <activity
            android:name="com.spyro.vmeet.UserManagementActivity"
            android:label="Gestión de Usuarios" />
        <activity
            android:name="com.spyro.vmeet.ReportsActivity"
            android:label="Reportes de Usuarios" />

        <!-- Full-screen image viewer activity with a special theme for immersive viewing -->
        <activity
            android:name="com.spyro.vmeet.FullScreenImageActivity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />

        <!-- Story Viewer Activity -->
        <activity
            android:name="com.spyro.vmeet.ui.community.StoryViewerActivity"
            android:theme="@style/Theme.AppCompat.NoActionBar" />

        <!-- User Profile Activity -->
        <activity
            android:name="com.spyro.vmeet.activity.UserProfileActivity"
            android:label="Perfil de Usuario" />

        <!-- Edit Personality Traits Activity -->
        <activity
            android:name="com.spyro.vmeet.activity.EditPersonalityTraitsActivity"
            android:label="Editar Rasgos de Personalidad" />

        <!-- Community Guidelines Activity -->
        <activity
            android:name="com.spyro.vmeet.activity.CommunityGuidelinesActivity"
            android:label="Normas de la comunidad" />

        <!-- User Verification Activity -->
        <activity
            android:name="com.spyro.vmeet.VerificationActivity"
            android:label="Verificación de Usuario"
            android:theme="@style/Theme.VMeet.NoActionBar" />
        <activity
            android:name="com.spyro.vmeet.VerificationManagementActivity"
            android:label="Gestión de Verificaciones"
            android:theme="@style/Theme.VMeet.NoActionBar" />

        <!-- Firebase Cloud Messaging Service -->
        <service
            android:name="com.spyro.vmeet.notifications.VMeetFirebaseMessagingService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- BroadcastReceiver for notification actions -->
        <receiver
            android:name="com.spyro.vmeet.notifications.MessageActionReceiver"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.spyro.vmeet.ACTION_MARK_AS_READ" />
                <action android:name="com.spyro.vmeet.ACTION_REPLY" />
            </intent-filter>
        </receiver>

        <!-- Sample AdMob App ID. Replace with your real App ID before production. -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~**********" />

        <!-- FileProvider for sharing images from camera -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.spyro.vmeet.provider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.spyro.vmeet.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider> <!-- Needs to be explicitly declared on P+ -->
        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/ga_ad_services_config" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.spyro.vmeet.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
    </application>

</manifest>