<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:shareInterpolator="false"
    android:fillEnabled="true"
    android:fillAfter="true">
    
    <!-- Entrada con fade in -->
    <alpha
        android:fromAlpha="0.0"
        android:toAlpha="1.0"
        android:duration="500" />
    
    <!-- Rotación continua y leve -->
    <rotate
        android:startOffset="500"
        android:fromDegrees="-3"
        android:toDegrees="3"
        android:pivotX="50%"
        android:pivotY="50%"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:interpolator="@android:anim/linear_interpolator"
        android:duration="1200" />
</set> 