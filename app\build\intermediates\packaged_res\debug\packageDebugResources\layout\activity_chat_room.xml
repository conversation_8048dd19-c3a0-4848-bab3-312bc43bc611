<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient"
    android:fitsSystemWindows="true"
    tools:context=".activity.ChatRoomActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbarRoom"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="#1A1A3A"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingEnd="16dp">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/imageViewRoomIcon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/default_room_icon"
                app:civ_border_color="@color/neon_blue"
                app:civ_border_width="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/imageViewRoomIcon"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/textViewRoomName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="Sala de Gamers VMeet" />

                <TextView
                    android:id="@+id/textViewRoomDescription"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="#e0e0e0"
                    android:textSize="12sp"
                    tools:text="Sala para conversar sobre videojuegos" />

                <TextView
                    android:id="@+id/textViewRoomMembers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#c0c0c0"
                    android:textSize="11sp"
                    tools:text="24 miembros" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <FrameLayout
        android:id="@+id/containerRoomMessages"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layoutSendMessage"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbarRoom">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewRoomMessages"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:listitem="@layout/item_message" />

    </FrameLayout>

    <!-- Reply preview layout -->
    <LinearLayout
        android:id="@+id/layoutReplyPreview"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#55000000"
        android:padding="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/layoutSendMessage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <View
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="@color/neon_blue" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Respondiendo a"
                android:textSize="12sp"
                android:textStyle="italic"
                android:textColor="@color/neon_blue" />

            <androidx.emoji2.widget.EmojiTextView
                android:id="@+id/textViewReplyPreview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:maxLines="2"
                android:ellipsize="end" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/buttonCancelReply"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:contentDescription="Cancel reply" />
    </LinearLayout>

    <!-- Mention Suggestions List -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMentionSuggestions"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="4dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/layoutSendMessage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewMentionSuggestions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxHeight="200dp" 
            android:padding="4dp" />

    </androidx.cardview.widget.CardView>

    <!-- Modified input layout with mic button -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutSendMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:background="#1A1A3A"
        android:clipToPadding="false"
        android:clipChildren="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Mic button for voice messages -->
        <ImageButton
            android:id="@+id/buttonMic"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/mic_button_selector"
            android:src="@android:drawable/ic_btn_speak_now"
            android:scaleType="fitCenter"
            android:padding="10dp"
            android:tint="#FFFFFF"
            android:contentDescription="Record voice message"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cardInputMessage"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            app:cardBackgroundColor="#121212"
            app:cardCornerRadius="24dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/buttonRoomSend"
            app:layout_constraintStart_toEndOf="@id/buttonMic"
            app:layout_constraintTop_toTopOf="parent">

            <EditText
                android:id="@+id/editTextRoomMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@android:color/transparent"
                android:enabled="false"
                android:hint="Escribe un mensaje..."
                android:inputType="textMultiLine"
                android:maxLines="4"
                android:minHeight="48dp"
                android:paddingStart="16dp"
                android:paddingTop="8dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp"
                android:textColor="@color/white"
                android:textColorHint="#80FFFFFF"
                android:textSize="16sp" />
        </androidx.cardview.widget.CardView>

        <ImageButton
            android:id="@+id/buttonRoomSend"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/circle_button_bg"
            android:contentDescription="Enviar mensaje"
            android:enabled="false"
            android:padding="12dp"
            android:scaleType="fitCenter"
            android:src="@android:drawable/ic_menu_send"
            android:tint="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- User Muted Layout -->
    <LinearLayout
        android:id="@+id/layoutMuted"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#212138"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:src="@drawable/ic_mute"
            android:tint="#FF5252"
            android:layout_marginBottom="12dp"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Estás silenciado"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF"
            android:gravity="center"
            android:layout_marginBottom="8dp"/>

        <TextView
            android:id="@+id/textViewMuteRemainingTime"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Te quedan X minutos de castigo antes de volver a la normalidad"
            android:textSize="14sp"
            android:textColor="#EEEEEE"
            android:gravity="center"/>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBarRoom"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 