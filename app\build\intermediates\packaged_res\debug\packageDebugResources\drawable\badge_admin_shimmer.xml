<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Sombra exterior para efecto de brillo -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#00000000"
                android:centerColor="#40FFD700"
                android:endColor="#00000000"
                android:type="radial"
                android:gradientRadius="80dp" />
            <corners android:radius="18dp" />
            <padding
                android:left="4dp"
                android:top="4dp"
                android:right="4dp"
                android:bottom="4dp" />
        </shape>
    </item>
    
    <!-- Borde dorado brillante -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFD700"
                android:endColor="#FFA500"
                android:angle="45" />
            <corners android:radius="16dp" />
            <padding
                android:left="2dp"
                android:top="2dp"
                android:right="2dp"
                android:bottom="2dp" />
        </shape>
    </item>
    
    <!-- Fondo principal con gradiente dorado -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#FFD700"
                android:centerColor="#FFED4E"
                android:endColor="#FFA500"
                android:angle="315"
                android:type="linear" />
            <corners android:radius="14dp" />
        </shape>
    </item>
    
    <!-- Efecto de brillo superior -->
    <item android:top="2dp" android:bottom="8dp" android:left="4dp" android:right="4dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#80FFFFFF"
                android:centerColor="#40FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="270" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</layer-list>
