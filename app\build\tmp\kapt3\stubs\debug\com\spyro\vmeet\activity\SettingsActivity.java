package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J \u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J \u0010\u001d\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\u00042\u0006\u0010\u001f\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J \u0010 \u001a\u00020\u00182\u0006\u0010!\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u001cH\u0002J\b\u0010\"\u001a\u00020#H\u0002J\b\u0010$\u001a\u00020\u0018H\u0002J\b\u0010%\u001a\u00020\u0018H\u0002J\b\u0010&\u001a\u00020\u0018H\u0002J\u0012\u0010\'\u001a\u00020\u00182\b\u0010(\u001a\u0004\u0018\u00010)H\u0014J\b\u0010*\u001a\u00020\u0018H\u0002J\b\u0010+\u001a\u00020\u0018H\u0002J\b\u0010,\u001a\u00020\u0018H\u0002J\b\u0010-\u001a\u00020\u0018H\u0002J\b\u0010.\u001a\u00020\u0018H\u0002J\b\u0010/\u001a\u00020\u0018H\u0002J\b\u00100\u001a\u00020\u0018H\u0002J\b\u00101\u001a\u00020\u0018H\u0002J\u0010\u00102\u001a\u00020\u00182\u0006\u00103\u001a\u000204H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00065"}, d2 = {"Lcom/spyro/vmeet/activity/SettingsActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "TAG", "bottomNavView", "Lcom/google/android/material/bottomnavigation/BottomNavigationView;", "currentAccuracyLevel", "currentEmail", "currentMaxDistance", "", "currentUsername", "progressBar", "Landroid/widget/ProgressBar;", "switchLocationSharing", "Landroidx/appcompat/widget/SwitchCompat;", "switchMatchesOnly", "switchReceiveMessages", "textAccuracyLevel", "Landroid/widget/TextView;", "textMaxDistance", "userId", "changeEmail", "", "newEmail", "password", "dialog", "Landroidx/appcompat/app/AlertDialog;", "changePassword", "currentPassword", "newPassword", "changeUsername", "newUsername", "createCompatibilityLayout", "Landroid/view/View;", "initializeViews", "loadLocationSettings", "loadUserSettings", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "setupBottomNavigation", "setupListeners", "showAccuracyLevelDialog", "showChangeEmailDialog", "showChangePasswordDialog", "showChangeUsernameDialog", "showMaxDistanceDialog", "updateLocationSettings", "updateMessageSettings", "receiveMessages", "", "app_debug"})
public final class SettingsActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "SettingsActivity";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    private int userId = 0;
    private com.google.android.material.bottomnavigation.BottomNavigationView bottomNavView;
    private androidx.appcompat.widget.SwitchCompat switchReceiveMessages;
    private androidx.appcompat.widget.SwitchCompat switchLocationSharing;
    private androidx.appcompat.widget.SwitchCompat switchMatchesOnly;
    private android.widget.TextView textAccuracyLevel;
    private android.widget.TextView textMaxDistance;
    private android.widget.ProgressBar progressBar;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentUsername = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentEmail = "";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentAccuracyLevel = "approximate";
    private int currentMaxDistance = 50000;
    
    public SettingsActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final android.view.View createCompatibilityLayout() {
        return null;
    }
    
    private final void initializeViews() {
    }
    
    private final void setupListeners() {
    }
    
    private final void setupBottomNavigation() {
    }
    
    private final void loadUserSettings() {
    }
    
    private final void updateMessageSettings(boolean receiveMessages) {
    }
    
    private final void showChangeUsernameDialog() {
    }
    
    private final void showChangePasswordDialog() {
    }
    
    private final void showChangeEmailDialog() {
    }
    
    private final void changeUsername(java.lang.String newUsername, java.lang.String password, androidx.appcompat.app.AlertDialog dialog) {
    }
    
    private final void changePassword(java.lang.String currentPassword, java.lang.String newPassword, androidx.appcompat.app.AlertDialog dialog) {
    }
    
    private final void changeEmail(java.lang.String newEmail, java.lang.String password, androidx.appcompat.app.AlertDialog dialog) {
    }
    
    private final void loadLocationSettings() {
    }
    
    private final void updateLocationSettings() {
    }
    
    private final void showAccuracyLevelDialog() {
    }
    
    private final void showMaxDistanceDialog() {
    }
}