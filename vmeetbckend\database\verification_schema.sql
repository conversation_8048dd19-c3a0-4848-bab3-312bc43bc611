-- V<PERSON><PERSON>t User Verification System Database Schema

-- Add verification status to users table
ALTER TABLE users ADD COLUMN verified_status ENUM('not_verified', 'pending', 'approved', 'rejected') DEFAULT 'not_verified';
ALTER TABLE users ADD COLUMN verified_at TIMESTAMP NULL;

-- Create user_verifications table
CREATE TABLE user_verifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    dni_photo_url VARCHAR(500) NOT NULL,
    selfie_photo_url VARCHAR(500) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP NULL,
    admin_id INT NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIG<PERSON> KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_submitted_at (submitted_at)
);

-- Create verification audit trail table
CREATE TABLE verification_audit (
    id INT PRIMARY KEY AUTO_INCREMENT,
    verification_id INT NOT NULL,
    admin_id INT NOT NULL,
    action ENUM('approved', 'rejected', 'reviewed') NOT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (verification_id) REFERENCES user_verifications(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_verification_id (verification_id),
    INDEX idx_admin_id (admin_id)
);

-- Update existing users to have not_verified status
UPDATE users SET verified_status = 'not_verified' WHERE verified_status IS NULL;
