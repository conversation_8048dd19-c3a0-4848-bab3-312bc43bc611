<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    android:background="@color/cyberpunk_card_background">

    <View
        android:id="@+id/colorPreviewInDialog"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginBottom="16dp"
        android:background="@android:color/white" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Rojo" 
        android:textColor="@color/neon_pink"/>
    <SeekBar
        android:id="@+id/seekBarRed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="255"
        android:progressBackgroundTint="@color/neon_blue_dark"
        android:progressTint="@color/neon_pink"
        android:thumbTint="@color/neon_pink" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Verde"
        android:layout_marginTop="8dp"
        android:textColor="@color/neon_blue"/>
    <SeekBar
        android:id="@+id/seekBarGreen"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="255"
        android:progressBackgroundTint="@color/neon_blue_dark"
        android:progressTint="@color/neon_blue"
        android:thumbTint="@color/neon_blue" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Azul"
        android:layout_marginTop="8dp"
        android:textColor="@color/cyberpunk_yellow"/>
    <SeekBar
        android:id="@+id/seekBarBlue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="255"
        android:progressBackgroundTint="@color/neon_blue_dark"
        android:progressTint="@color/cyberpunk_yellow"
        android:thumbTint="@color/cyberpunk_yellow" />

</LinearLayout> 