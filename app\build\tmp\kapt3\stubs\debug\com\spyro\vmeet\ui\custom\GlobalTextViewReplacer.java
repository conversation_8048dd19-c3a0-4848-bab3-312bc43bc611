package com.spyro.vmeet.ui.custom;

/**
 * Global utility to apply pixel-perfect rendering to TextViews
 * to eliminate gradient issues throughout the entire application
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bJ\u000e\u0010\f\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\r\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u0016\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0013\u001a\u00020\u0014J\u0016\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0016\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u0014J\u0010\u0010\u0017\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0002J \u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0019\u001a\u00020\b2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001dH\u0002J\u0010\u0010\u001e\u001a\u00020\u00062\u0006\u0010\u001f\u001a\u00020 H\u0002J\u0010\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020\u001dH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/spyro/vmeet/ui/custom/GlobalTextViewReplacer;", "", "()V", "TAG", "", "applyPixelPerfectToViewGroup", "", "viewGroup", "Landroid/view/ViewGroup;", "applyToActivity", "activity", "Landroid/app/Activity;", "applyToAllRecyclerViews", "applyToFragment", "fragment", "Landroidx/fragment/app/Fragment;", "rootView", "Landroid/view/View;", "applyToViewGroup", "context", "Landroid/content/Context;", "applyToViewHolder", "itemView", "findAndProcessRecyclerViews", "replaceWithNumberTextView", "parent", "index", "", "originalTextView", "Landroid/widget/TextView;", "setupRecyclerViewMonitoring", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "shouldApplyPixelPerfect", "", "textView", "app_debug"})
public final class GlobalTextViewReplacer {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "GlobalTextViewReplacer";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.custom.GlobalTextViewReplacer INSTANCE = null;
    
    private GlobalTextViewReplacer() {
        super();
    }
    
    /**
     * Apply pixel-perfect rendering to an entire Activity
     */
    public final void applyToActivity(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    /**
     * Apply pixel-perfect rendering to a Fragment
     */
    public final void applyToFragment(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment fragment, @org.jetbrains.annotations.NotNull()
    android.view.View rootView) {
    }
    
    /**
     * Apply pixel-perfect rendering to a specific ViewGroup
     */
    public final void applyToViewGroup(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup viewGroup, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    private final void applyPixelPerfectToViewGroup(android.view.ViewGroup viewGroup) {
    }
    
    private final boolean shouldApplyPixelPerfect(android.widget.TextView textView) {
        return false;
    }
    
    private final void replaceWithNumberTextView(android.view.ViewGroup parent, int index, android.widget.TextView originalTextView) {
    }
    
    /**
     * Apply pixel-perfect rendering to a RecyclerView adapter's ViewHolder
     */
    public final void applyToViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.View itemView, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Apply pixel-perfect rendering to all RecyclerViews in an activity
     */
    public final void applyToAllRecyclerViews(@org.jetbrains.annotations.NotNull()
    android.app.Activity activity) {
    }
    
    private final void findAndProcessRecyclerViews(android.view.ViewGroup viewGroup) {
    }
    
    private final void setupRecyclerViewMonitoring(androidx.recyclerview.widget.RecyclerView recyclerView) {
    }
}