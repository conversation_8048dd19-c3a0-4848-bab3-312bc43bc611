<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="24dp">

    <!-- Category Title -->
    <TextView
        android:id="@+id/textViewCategoryTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Categoría"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/cyberpunk_accent"
        android:layout_marginBottom="12dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:fontFamily="@font/inter" />

    <!-- Traits RecyclerView for this category with optimizations -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewCategoryTraits"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="false"
        android:overScrollMode="never"
        android:padding="4dp" />

</LinearLayout>
