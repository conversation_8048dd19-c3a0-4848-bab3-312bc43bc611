package com.spyro.vmeet.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\b\u0007\u0018\u0000 \'2\u00020\u0001:\u0001\'B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0002J\u0010\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u0006\u0010\u0014\u001a\u00020\u000eJ \u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0019H\u0002J\b\u0010\u001b\u001a\u00020\u000eH\u0002J\b\u0010\u001c\u001a\u00020\u000eH\u0002J\b\u0010\u001d\u001a\u00020\u000eH\u0002J\b\u0010\u001e\u001a\u00020\u000eH\u0002J\b\u0010\u001f\u001a\u00020\u000eH\u0002J\b\u0010 \u001a\u00020\u000eH\u0002J\b\u0010!\u001a\u00020\u000eH\u0002J\u0010\u0010\"\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u0010\u0010#\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u000e\u0010$\u001a\u00020\u000e2\u0006\u0010%\u001a\u00020\u0013J\b\u0010&\u001a\u00020\u000eH\u0002R\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006("}, d2 = {"Lcom/spyro/vmeet/ui/ProfileDropdownMenu;", "", "context", "Landroid/content/Context;", "userId", "", "(Landroid/content/Context;I)V", "API_URL", "", "client", "Lokhttp3/OkHttpClient;", "popupWindow", "Landroid/widget/PopupWindow;", "checkAdminStatus", "", "adminLayout", "Landroid/widget/LinearLayout;", "checkVerificationStatus", "popupView", "Landroid/view/View;", "dismiss", "loadUserInfo", "imageView", "Landroid/widget/ImageView;", "nameTextView", "Landroid/widget/TextView;", "handleTextView", "navigateToAdminPanel", "navigateToCommunityGuidelines", "navigateToLogin", "navigateToProfile", "navigateToSettings", "navigateToVerification", "performLogout", "setupMenuItems", "setupUserInfo", "show", "anchorView", "showLogoutConfirmation", "Companion", "app_debug"})
public final class ProfileDropdownMenu {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final int userId = 0;
    @org.jetbrains.annotations.Nullable()
    private android.widget.PopupWindow popupWindow;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ProfileDropdownMenu";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.ProfileDropdownMenu.Companion Companion = null;
    
    public ProfileDropdownMenu(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int userId) {
        super();
    }
    
    public final void show(@org.jetbrains.annotations.NotNull()
    android.view.View anchorView) {
    }
    
    private final void setupUserInfo(android.view.View popupView) {
    }
    
    private final void setupMenuItems(android.view.View popupView) {
    }
    
    private final void loadUserInfo(android.widget.ImageView imageView, android.widget.TextView nameTextView, android.widget.TextView handleTextView) {
    }
    
    private final void checkAdminStatus(android.widget.LinearLayout adminLayout) {
    }
    
    private final void navigateToProfile() {
    }
    
    private final void navigateToSettings() {
    }
    
    private final void navigateToCommunityGuidelines() {
    }
    
    private final void navigateToAdminPanel() {
    }
    
    private final void navigateToVerification() {
    }
    
    private final void showLogoutConfirmation() {
    }
    
    private final void performLogout() {
    }
    
    private final void navigateToLogin() {
    }
    
    private final void checkVerificationStatus(android.view.View popupView) {
    }
    
    public final void dismiss() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/ui/ProfileDropdownMenu$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}