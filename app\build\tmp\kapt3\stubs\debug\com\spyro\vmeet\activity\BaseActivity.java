package com.spyro.vmeet.activity;

/**
 * Base activity that all activities in the app should extend.
 * Handles common functionality like updating user online status.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\b\'\u0018\u0000 \u001f2\u00020\u0001:\u0001\u001fB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u000b\u001a\u00020\fH\u0004J\u0010\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\u000fH\u0004J\u0012\u0010\u0010\u001a\u00020\f2\b\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u0014J\b\u0010\u0013\u001a\u00020\fH\u0014J\b\u0010\u0014\u001a\u00020\fH\u0014J\u0012\u0010\u0015\u001a\u00020\f2\b\u0010\u0016\u001a\u0004\u0018\u00010\u000fH\u0016J\u001c\u0010\u0015\u001a\u00020\f2\b\u0010\u0016\u001a\u0004\u0018\u00010\u000f2\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018H\u0016J\u0010\u0010\u0015\u001a\u00020\f2\u0006\u0010\u0019\u001a\u00020\nH\u0016J\b\u0010\u001a\u001a\u00020\fH\u0002J\b\u0010\u001b\u001a\u00020\fH\u0002J\b\u0010\u001c\u001a\u00020\fH\u0002J\b\u0010\u001d\u001a\u00020\fH\u0002J\b\u0010\u001e\u001a\u00020\fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/spyro/vmeet/activity/BaseActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "handler", "Landroid/os/Handler;", "isUpdatingOnlineStatus", "", "updateRunnable", "Ljava/lang/Runnable;", "userId", "", "applyPixelPerfectTextViews", "", "applyWindowInsets", "rootView", "Landroid/view/View;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onPause", "onResume", "setContentView", "view", "params", "Landroid/view/ViewGroup$LayoutParams;", "layoutResID", "setupEdgeToEdgeDisplay", "startContinuousMonitoring", "startOnlineStatusUpdates", "stopOnlineStatusUpdates", "updateOnlineStatus", "Companion", "app_debug"})
public abstract class BaseActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "BaseActivity";
    private static final long ONLINE_UPDATE_INTERVAL = 120000L;
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.Lazy<?> client$delegate = null;
    private static long lastStatusUpdateTime = 0L;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler handler = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.Runnable updateRunnable = null;
    private int userId = -1;
    private boolean isUpdatingOnlineStatus = false;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.BaseActivity.Companion Companion = null;
    
    public BaseActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public void setContentView(int layoutResID) {
    }
    
    @java.lang.Override()
    public void setContentView(@org.jetbrains.annotations.Nullable()
    android.view.View view) {
    }
    
    @java.lang.Override()
    public void setContentView(@org.jetbrains.annotations.Nullable()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup.LayoutParams params) {
    }
    
    /**
     * Apply NumberTextView replacement to eliminate number gradients
     * Call this method after inflating new views or updating content
     */
    protected final void applyPixelPerfectTextViews() {
    }
    
    private final void startContinuousMonitoring() {
    }
    
    /**
     * Sets up edge-to-edge display by making the app draw under system bars
     * and handling insets properly
     */
    private final void setupEdgeToEdgeDisplay() {
    }
    
    /**
     * Apply window insets to a specific view
     * Call this method in your activity's onCreate after setContentView
     *
     * @param rootView The root view of your layout
     */
    protected final void applyWindowInsets(@org.jetbrains.annotations.NotNull()
    android.view.View rootView) {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    private final void startOnlineStatusUpdates() {
    }
    
    private final void stopOnlineStatusUpdates() {
    }
    
    private final void updateOnlineStatus() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0007\u001a\u00020\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000b\u0010\f\u001a\u0004\b\t\u0010\nR\u000e\u0010\r\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/activity/BaseActivity$Companion;", "", "()V", "ONLINE_UPDATE_INTERVAL", "", "TAG", "", "client", "Lokhttp3/OkHttpClient;", "getClient", "()Lokhttp3/OkHttpClient;", "client$delegate", "Lkotlin/Lazy;", "lastStatusUpdateTime", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        private final okhttp3.OkHttpClient getClient() {
            return null;
        }
    }
}