<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPagerUsers"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/linearLayoutButtons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/linearLayoutButtons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageButton
            android:id="@+id/buttonDislike"
            android:layout_width="wrap_content"
            android:layout_height="70dp"
            android:layout_marginEnd="40dp"
            android:background="@drawable/circular_button_background"
            android:contentDescription="Dislike"
            android:padding="15dp"
            android:scaleType="centerInside"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            app:tint="?attr/colorError" />

        <ImageButton
            android:id="@+id/buttonLike"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:background="@drawable/circular_button_background" 
            android:src="@android:drawable/star_on" 
            app:tint="@color/comfy_blue" 
            android:contentDescription="Like"
            android:scaleType="centerInside"
            android:padding="15dp"
            android:layout_marginStart="40dp"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
