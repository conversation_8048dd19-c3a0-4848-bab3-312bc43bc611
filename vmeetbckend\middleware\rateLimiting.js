const rateLimit = require('express-rate-limit');
const { securityLogger } = require('./jwtAuth');

/**
 * General API rate limiting
 */
const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // Limit each IP to 1000 requests per windowMs
    message: {
        error: 'Demasiadas solicitudes',
        message: 'Has excedido el límite de solicitudes. Intenta nuevamente en 15 minutos.',
        code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - General', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path
        });
        res.status(429).json({
            error: 'Demasiadas solicitudes',
            message: 'Has excedido el límite de solicitudes. Intenta nuevamente en 15 minutos.',
            code: 'RATE_LIMIT_EXCEEDED'
        });
    }
});

/**
 * Strict rate limiting for authentication endpoints
 */
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 login attempts per windowMs
    message: {
        error: 'Demasiados intentos de autenticación',
        message: 'Has excedido el límite de intentos de inicio de sesión. Intenta nuevamente en 15 minutos.',
        code: 'AUTH_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true, // Don't count successful requests
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - Auth', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path,
            body: { username: req.body?.username } // Log username attempt
        });
        res.status(429).json({
            error: 'Demasiados intentos de autenticación',
            message: 'Has excedido el límite de intentos de inicio de sesión. Intenta nuevamente en 15 minutos.',
            code: 'AUTH_RATE_LIMIT_EXCEEDED'
        });
    }
});

/**
 * Moderate rate limiting for admin endpoints
 */
const adminLimiter = rateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 100, // Limit each IP to 100 admin requests per windowMs
    message: {
        error: 'Demasiadas solicitudes administrativas',
        message: 'Has excedido el límite de solicitudes administrativas. Intenta nuevamente en 5 minutos.',
        code: 'ADMIN_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - Admin', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            path: req.path,
            userId: req.user?.id
        });
        res.status(429).json({
            error: 'Demasiadas solicitudes administrativas',
            message: 'Has excedido el límite de solicitudes administrativas. Intenta nuevamente en 5 minutos.',
            code: 'ADMIN_RATE_LIMIT_EXCEEDED'
        });
    }
});

/**
 * Strict rate limiting for password reset
 */
const passwordResetLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // Limit each IP to 3 password reset attempts per hour
    message: {
        error: 'Demasiados intentos de restablecimiento',
        message: 'Has excedido el límite de intentos de restablecimiento de contraseña. Intenta nuevamente en 1 hora.',
        code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - Password Reset', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            email: req.body?.email
        });
        res.status(429).json({
            error: 'Demasiados intentos de restablecimiento',
            message: 'Has excedido el límite de intentos de restablecimiento de contraseña. Intenta nuevamente en 1 hora.',
            code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED'
        });
    }
});

/**
 * Rate limiting for registration
 */
const registerLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // Limit each IP to 5 registration attempts per hour
    message: {
        error: 'Demasiados intentos de registro',
        message: 'Has excedido el límite de intentos de registro. Intenta nuevamente en 1 hora.',
        code: 'REGISTER_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - Registration', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            username: req.body?.username,
            email: req.body?.email
        });
        res.status(429).json({
            error: 'Demasiados intentos de registro',
            message: 'Has excedido el límite de intentos de registro. Intenta nuevamente en 1 hora.',
            code: 'REGISTER_RATE_LIMIT_EXCEEDED'
        });
    }
});

/**
 * Rate limiting for messaging
 */
const messageLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 30, // Limit each IP to 30 messages per minute
    message: {
        error: 'Demasiados mensajes',
        message: 'Has excedido el límite de mensajes por minuto. Intenta nuevamente en un momento.',
        code: 'MESSAGE_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - Messaging', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id
        });
        res.status(429).json({
            error: 'Demasiados mensajes',
            message: 'Has excedido el límite de mensajes por minuto. Intenta nuevamente en un momento.',
            code: 'MESSAGE_RATE_LIMIT_EXCEEDED'
        });
    }
});

/**
 * Rate limiting for swipe actions
 */
const swipeLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 minute
    max: 60, // Limit each IP to 60 swipes per minute
    message: {
        error: 'Demasiados swipes',
        message: 'Has excedido el límite de swipes por minuto. Intenta nuevamente en un momento.',
        code: 'SWIPE_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
        securityLogger.warn('Rate limit exceeded - Swipe', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id
        });
        res.status(429).json({
            error: 'Demasiados swipes',
            message: 'Has excedido el límite de swipes por minuto. Intenta nuevamente en un momento.',
            code: 'SWIPE_RATE_LIMIT_EXCEEDED'
        });
    }
});

module.exports = {
    generalLimiter,
    authLimiter,
    adminLimiter,
    passwordResetLimiter,
    registerLimiter,
    messageLimiter,
    swipeLimiter
};
