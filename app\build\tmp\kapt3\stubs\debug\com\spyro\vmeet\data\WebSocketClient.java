package com.spyro.vmeet.data;

/**
 * WebSocket client for real-time communication with the VMeet server.
 * Handles connection, messaging, and events for the chat system.
 * Includes improved reconnection logic and heartbeat mechanism.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0017\b\u0007\u0018\u00002\u00020\u0001B\u00a4\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\u0014\b\u0002\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\n\u0012M\b\u0002\u0010\u000b\u001aG\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u000f\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0010\u0012\u0013\u0012\u00110\u0011\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0012\u0012\u0004\u0012\u00020\b0\f\u0012\u0018\b\u0002\u0010\u0013\u001a\u0012\u0012\b\u0012\u00060\u0014j\u0002`\u0015\u0012\u0004\u0012\u00020\b0\n\u00a2\u0006\u0002\u0010\u0016J\u0006\u0010=\u001a\u00020\bJ\u0006\u0010>\u001a\u00020\bJ\b\u0010?\u001a\u0004\u0018\u00010<J\u0006\u0010@\u001a\u00020\bJ\b\u0010A\u001a\u00020\bH\u0002J\u000e\u0010B\u001a\u00020\u00112\u0006\u0010C\u001a\u00020\u0003J\b\u0010D\u001a\u00020\bH\u0002J>\u0010E\u001a\u00020\b26\u0010F\u001a2\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(G\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(H\u0012\u0004\u0012\u00020\b02J\u001a\u0010I\u001a\u00020\b2\u0012\u0010F\u001a\u000e\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\b0\nJ\u001a\u0010J\u001a\u00020\b2\u0012\u0010F\u001a\u000e\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\b0\nJ>\u0010K\u001a\u00020\b26\u0010F\u001a2\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(L\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(M\u0012\u0004\u0012\u00020\b02J\b\u0010N\u001a\u00020\bH\u0002J\b\u0010O\u001a\u00020\bH\u0002J\u001d\u0010P\u001a\u00020\b2\b\u0010G\u001a\u0004\u0018\u00010\u00052\u0006\u0010Q\u001a\u00020\u0011\u00a2\u0006\u0002\u0010RR\u000e\u0010\u0017\u001a\u00020\u0018X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0003X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u001d\u001a\u0004\u0018\u00010\u0005X\u0086\u000e\u00a2\u0006\u0010\n\u0002\u0010\"\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!R\u000e\u0010#\u001a\u00020$X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010%\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\'\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R\u000e\u0010+\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0011\u0010,\u001a\u00020\u00118F\u00a2\u0006\u0006\u001a\u0004\b,\u0010(R\u000e\u0010-\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u000200X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u00101\u001a\u0016\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b\u0018\u000102X\u0082\u000e\u00a2\u0006\u0002\n\u0000RS\u0010\u000b\u001aG\u0012\u0013\u0012\u00110\u0005\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u000f\u0012\u0013\u0012\u00110\u0003\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0010\u0012\u0013\u0012\u00110\u0011\u00a2\u0006\f\b\r\u0012\b\b\u000e\u0012\u0004\b\b(\u0012\u0012\u0004\u0012\u00020\b0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001e\u0010\u0013\u001a\u0012\u0012\b\u0012\u00060\u0014j\u0002`\u0015\u0012\u0004\u0012\u00020\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u00103\u001a\u0010\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\b\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u00105\u001a\u0010\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\b\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u00106\u001a\u0010\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\b\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u00107\u001a\u0010\u0012\u0004\u0012\u000204\u0012\u0004\u0012\u00020\b\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u00108\u001a\u0016\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b\u0018\u000102X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010:\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010;\u001a\u0004\u0018\u00010<X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006S"}, d2 = {"Lcom/spyro/vmeet/data/WebSocketClient;", "", "serverUrl", "", "userId", "", "onOpen", "Lkotlin/Function0;", "", "onMessage", "Lkotlin/Function1;", "onClose", "Lkotlin/Function3;", "Lkotlin/ParameterName;", "name", "code", "reason", "", "remote", "onError", "Ljava/lang/Exception;", "Lkotlin/Exception;", "(Ljava/lang/String;ILkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function3;Lkotlin/jvm/functions/Function1;)V", "BASE_RECONNECT_DELAY_MS", "", "MAX_RECONNECT_ATTEMPTS", "TAG", "client", "Lokhttp3/OkHttpClient;", "currentMatchId", "getCurrentMatchId", "()Ljava/lang/Integer;", "setCurrentMatchId", "(Ljava/lang/Integer;)V", "Ljava/lang/Integer;", "gson", "Lcom/google/gson/Gson;", "heartbeatTimer", "Ljava/util/Timer;", "isChatVisible", "()Z", "setChatVisible", "(Z)V", "isConnecting", "isOpen", "lastPingTime", "lastPongTime", "mainHandler", "Landroid/os/Handler;", "onChatDeletedListener", "Lkotlin/Function2;", "onMessageReactionAdded", "Lcom/spyro/vmeet/data/MessageReaction;", "onMessageReactionAddedListener", "onMessageReactionRemoved", "onMessageReactionRemovedListener", "onUnreadCountUpdated", "reconnectAttempts", "reconnectTimer", "webSocket", "Lokhttp3/WebSocket;", "connect", "disconnect", "getWebSocket", "reconnect", "scheduleReconnect", "send", "message", "sendHeartbeat", "setOnChatDeletedListener", "listener", "matchId", "deletedBy", "setOnMessageReactionAddedListener", "setOnMessageReactionRemovedListener", "setOnUnreadCountUpdatedListener", "roomId", "unreadCount", "startHeartbeatTimer", "stopHeartbeatTimer", "updateChatState", "isVisible", "(Ljava/lang/Integer;Z)V", "app_debug"})
public final class WebSocketClient {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String serverUrl = null;
    private final int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onOpen = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function3<java.lang.Integer, java.lang.String, java.lang.Boolean, kotlin.Unit> onClose = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.Exception, kotlin.Unit> onError = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "WebSocketClient";
    @org.jetbrains.annotations.Nullable()
    private okhttp3.WebSocket webSocket;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.MessageReaction, kotlin.Unit> onMessageReactionAdded;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.MessageReaction, kotlin.Unit> onMessageReactionRemoved;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onUnreadCountUpdated;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.MessageReaction, kotlin.Unit> onMessageReactionAddedListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.MessageReaction, kotlin.Unit> onMessageReactionRemovedListener;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> onChatDeletedListener;
    private boolean isConnecting = false;
    private int reconnectAttempts = 0;
    private final int MAX_RECONNECT_ATTEMPTS = 10;
    private final long BASE_RECONNECT_DELAY_MS = 1000L;
    private long lastPingTime = 0L;
    private long lastPongTime = 0L;
    @org.jetbrains.annotations.Nullable()
    private java.util.Timer heartbeatTimer;
    @org.jetbrains.annotations.Nullable()
    private java.util.Timer reconnectTimer;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler mainHandler = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Integer currentMatchId;
    private boolean isChatVisible = false;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    
    public WebSocketClient(@org.jetbrains.annotations.NotNull()
    java.lang.String serverUrl, int userId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onOpen, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onMessage, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function3<? super java.lang.Integer, ? super java.lang.String, ? super java.lang.Boolean, kotlin.Unit> onClose, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Exception, kotlin.Unit> onError) {
        super();
    }
    
    public final boolean isOpen() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getCurrentMatchId() {
        return null;
    }
    
    public final void setCurrentMatchId(@org.jetbrains.annotations.Nullable()
    java.lang.Integer p0) {
    }
    
    public final boolean isChatVisible() {
        return false;
    }
    
    public final void setChatVisible(boolean p0) {
    }
    
    /**
     * Connect to the WebSocket server with improved error handling and reconnection logic
     */
    public final void connect() {
    }
    
    /**
     * Schedule a reconnection attempt with exponential backoff
     */
    private final void scheduleReconnect() {
    }
    
    /**
     * Start the heartbeat timer to keep the connection alive
     */
    private final void startHeartbeatTimer() {
    }
    
    /**
     * Stop the heartbeat timer
     */
    private final void stopHeartbeatTimer() {
    }
    
    /**
     * Send a heartbeat ping to keep the connection alive
     */
    private final void sendHeartbeat() {
    }
    
    /**
     * Disconnect from the WebSocket server
     */
    public final void disconnect() {
    }
    
    /**
     * Send a message through the WebSocket connection with retry logic
     */
    public final boolean send(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
        return false;
    }
    
    /**
     * Force reconnection
     */
    public final void reconnect() {
    }
    
    /**
     * Update current match ID and chat visibility state
     */
    public final void updateChatState(@org.jetbrains.annotations.Nullable()
    java.lang.Integer matchId, boolean isVisible) {
    }
    
    /**
     * Set callback for message reaction added events
     */
    public final void setOnMessageReactionAddedListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.MessageReaction, kotlin.Unit> listener) {
    }
    
    /**
     * Set callback for message reaction removed events
     */
    public final void setOnMessageReactionRemovedListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.MessageReaction, kotlin.Unit> listener) {
    }
    
    /**
     * Set callback for chat deleted events
     */
    public final void setOnChatDeletedListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * Set callback for unread count update events
     */
    public final void setOnUnreadCountUpdatedListener(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super java.lang.Integer, kotlin.Unit> listener) {
    }
    
    /**
     * Get the underlying WebSocket instance
     */
    @org.jetbrains.annotations.Nullable()
    public final okhttp3.WebSocket getWebSocket() {
        return null;
    }
}