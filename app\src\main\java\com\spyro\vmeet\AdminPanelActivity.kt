package com.spyro.vmeet

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import com.spyro.vmeet.activity.BaseActivity
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit

class AdminPanelActivity : BaseActivity() {
    companion object {
        private const val TAG = "AdminPanelActivity"

        // Intent factory method to create an intent to launch this activity
        fun createIntent(context: Context, userId: Int): Intent {
            val intent = Intent(context, AdminPanelActivity::class.java)
            intent.putExtra("USER_ID", userId)
            return intent
        }
    }

    private lateinit var tvTotalUsers: TextView
    private lateinit var tvNewUsers: TextView
    private lateinit var tvActiveToday: TextView
    private lateinit var tvActiveWeek: TextView
    private lateinit var tvBannedUsers: TextView
    private lateinit var tvTotalPosts: TextView
    private lateinit var btnViewUsers: Button
    private lateinit var btnViewReports: Button
    private lateinit var btnViewVerifications: Button
    private lateinit var btnSendNotification: Button
    private var userId: Int = 0

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_admin_panel)

        // Get user ID from intent or shared preferences
        userId = intent.getIntExtra("USER_ID", -1)
        if (userId <= 0) {
            // If not in intent, try shared preferences
            val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", -1)
        }

        if (userId <= 0) {
            // No valid user ID, redirect to login
            Toast.makeText(this, "Error de autenticación", Toast.LENGTH_SHORT).show()
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
            return
        }

        // Set up toolbar
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Panel de Administración"

        // Initialize views
        tvTotalUsers = findViewById(R.id.tvTotalUsers)
        tvNewUsers = findViewById(R.id.tvNewUsers)
        tvActiveToday = findViewById(R.id.tvActiveToday)
        tvActiveWeek = findViewById(R.id.tvActiveWeek)
        tvBannedUsers = findViewById(R.id.tvBannedUsers)
        tvTotalPosts = findViewById(R.id.tvTotalPosts)
        btnViewUsers = findViewById(R.id.btnViewUsers)
        btnViewReports = findViewById(R.id.btnViewReports)
        btnViewVerifications = findViewById(R.id.btnViewVerifications)
        btnSendNotification = findViewById(R.id.btnSendNotification)

        // Set up click listeners
        btnViewUsers.setOnClickListener {
            openUserManagementScreen()
        }

        btnViewReports.setOnClickListener {
            openReportsScreen()
        }

        btnViewVerifications.setOnClickListener {
            openVerificationsScreen()
        }

        btnSendNotification.setOnClickListener {
            showSendNotificationDialog()
        }

        // Verify admin status
        checkAdminStatus()
    }

    override fun onResume() {
        super.onResume()
        // Load statistics
        loadAdminStatistics()
    }

    private fun checkAdminStatus() {
        val url = "http://77.110.116.89:3000/user/check-admin/$userId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Log.e(TAG, "Error checking admin status", e)
                    Toast.makeText(
                        this@AdminPanelActivity,
                        "Error al verificar permisos de administrador",
                        Toast.LENGTH_SHORT
                    ).show()
                    finish()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val isAdmin = jsonObject.getBoolean("isAdmin")

                    if (!success || !isAdmin) {
                        runOnUiThread {
                            Toast.makeText(
                                this@AdminPanelActivity,
                                "No tienes permisos de administrador",
                                Toast.LENGTH_SHORT
                            ).show()
                            finish()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        Log.e(TAG, "Error parsing admin status response", e)
                        Toast.makeText(
                            this@AdminPanelActivity,
                            "Error al verificar permisos de administrador",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                }
            }
        })
    }

    private fun loadAdminStatistics() {
        val url = "http://77.110.116.89:3000/admin/statistics?adminId=$userId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Log.e(TAG, "Error loading admin statistics", e)
                    Toast.makeText(
                        this@AdminPanelActivity,
                        "Error al cargar estadísticas",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")

                    if (success) {
                        val statistics = jsonObject.getJSONObject("statistics")

                        runOnUiThread {
                            tvTotalUsers.text = statistics.getInt("totalUsers").toString()
                            tvNewUsers.text = statistics.getInt("newUsers").toString()
                            tvActiveToday.text = statistics.getInt("activeToday").toString()
                            tvActiveWeek.text = statistics.getInt("activeWeek").toString()
                            tvBannedUsers.text = statistics.getInt("bannedUsers").toString()
                            tvTotalPosts.text = statistics.getInt("totalPosts").toString()
                        }
                    } else {
                        runOnUiThread {
                            Toast.makeText(
                                this@AdminPanelActivity,
                                "Error al cargar estadísticas",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        Log.e(TAG, "Error parsing statistics response", e)
                        Toast.makeText(
                            this@AdminPanelActivity,
                            "Error al procesar estadísticas",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun openUserManagementScreen() {
        val intent = Intent(this, UserManagementActivity::class.java)
        intent.putExtra("USER_ID", userId)
        startActivity(intent)
    }

    private fun openReportsScreen() {
        val intent = Intent(this, ReportsActivity::class.java)
        intent.putExtra("USER_ID", userId)
        startActivity(intent)
    }

    private fun openVerificationsScreen() {
        val intent = Intent(this, VerificationManagementActivity::class.java)
        intent.putExtra("USER_ID", userId)
        startActivity(intent)
    }

    private fun showSendNotificationDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("Enviar Notificación Global")

        // Set up the input
        val dialogView = layoutInflater.inflate(R.layout.dialog_global_notification, null)
        builder.setView(dialogView)

        // Add buttons
        builder.setPositiveButton("Enviar") { dialog, _ ->
            // Extract values from dialog
            val titleEditText = dialogView.findViewById<android.widget.EditText>(R.id.etNotificationTitle)
            val messageEditText = dialogView.findViewById<android.widget.EditText>(R.id.etNotificationMessage)
            val imageUrlEditText = dialogView.findViewById<android.widget.EditText>(R.id.etImageUrl)
            val buttonUrlEditText = dialogView.findViewById<android.widget.EditText>(R.id.etButtonUrl)
            val buttonTextEditText = dialogView.findViewById<android.widget.EditText>(R.id.etButtonText)

            val title = titleEditText.text.toString().trim()
            val message = messageEditText.text.toString().trim()
            val imageUrl = imageUrlEditText.text.toString().trim()
            val buttonUrl = buttonUrlEditText.text.toString().trim()
            val buttonText = buttonTextEditText.text.toString().trim()

            if (title.isEmpty() || message.isEmpty()) {
                Toast.makeText(this, "Título y mensaje son obligatorios", Toast.LENGTH_SHORT).show()
                return@setPositiveButton
            }

            // Send notification
            sendGlobalNotification(title, message, imageUrl, buttonUrl, buttonText)
            dialog.dismiss()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ -> dialog.cancel() }

        builder.show()
    }

    private fun sendGlobalNotification(
        title: String,
        message: String,
        imageUrl: String,
        buttonUrl: String,
        buttonText: String
    ) {
        val url = "http://77.110.116.89:3000/admin/send-notification"

        val jsonBody = JSONObject().apply {
            put("title", title)
            put("message", message)
            put("adminId", userId)

            if (imageUrl.isNotEmpty()) {
                put("imageUrl", imageUrl)
            }

            if (buttonUrl.isNotEmpty() && buttonText.isNotEmpty()) {
                put("buttonUrl", buttonUrl)
                put("buttonText", buttonText)
            }
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Log.e(TAG, "Error sending global notification", e)
                    Toast.makeText(
                        this@AdminPanelActivity,
                        "Error al enviar notificación",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val message = jsonObject.getString("message")

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(
                                this@AdminPanelActivity,
                                "Notificación enviada: $message",
                                Toast.LENGTH_SHORT
                            ).show()
                        } else {
                            Toast.makeText(
                                this@AdminPanelActivity,
                                "Error: $message",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        Log.e(TAG, "Error parsing notification response", e)
                        Toast.makeText(
                            this@AdminPanelActivity,
                            "Error al procesar respuesta del servidor",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }
}