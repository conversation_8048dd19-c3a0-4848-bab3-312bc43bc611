package com.spyro.vmeet.util;

/**
 * Clase para capturar y guardar los logs de ADB (logcat) en un archivo
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\b\u0007\u0018\u0000 \u00132\u00020\u0001:\u0001\u0013B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\r\u001a\u00020\u000eH\u0002J\u0006\u0010\u000f\u001a\u00020\u000eJ\b\u0010\u0010\u001a\u00020\u000eH\u0002J\u0006\u0010\u0011\u001a\u00020\u000eJ\b\u0010\u0012\u001a\u00020\u000eH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/spyro/vmeet/util/LogcatManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "isRunning", "", "logFile", "Ljava/io/File;", "logThread", "Ljava/lang/Thread;", "logcatProcess", "Ljava/lang/Process;", "logDeviceInfo", "", "startCapture", "startLogcatCapture", "stopCapture", "stopLogcatProcess", "Companion", "app_debug"})
public final class LogcatManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Process logcatProcess;
    @org.jetbrains.annotations.Nullable()
    private java.io.File logFile;
    private boolean isRunning = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Thread logThread;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "LogcatManager";
    @org.jetbrains.annotations.Nullable()
    private static com.spyro.vmeet.util.LogcatManager instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.util.LogcatManager.Companion Companion = null;
    
    private LogcatManager(android.content.Context context) {
        super();
    }
    
    /**
     * Inicia la captura de logs de ADB
     */
    public final void startCapture() {
    }
    
    /**
     * Registra información del dispositivo al inicio del archivo de log
     */
    private final void logDeviceInfo() {
    }
    
    /**
     * Inicia la captura de logs de ADB en segundo plano
     */
    private final void startLogcatCapture() {
    }
    
    /**
     * Detiene la captura de logs de ADB
     */
    public final void stopCapture() {
    }
    
    /**
     * Detiene el proceso de logcat
     */
    private final void stopLogcatProcess() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\tR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/spyro/vmeet/util/LogcatManager$Companion;", "", "()V", "TAG", "", "instance", "Lcom/spyro/vmeet/util/LogcatManager;", "getInstance", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.util.LogcatManager getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}