package com.spyro.vmeet.network;

/**
 * Centralized HTTP client configuration with authentication
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J\u000e\u0010\b\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007J\u0006\u0010\t\u001a\u00020\nR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/spyro/vmeet/network/HttpClient;", "", "()V", "client", "Lokhttp3/OkHttpClient;", "createClient", "context", "Landroid/content/Context;", "getInstance", "reset", "", "app_debug"})
public final class HttpClient {
    @org.jetbrains.annotations.Nullable()
    private static okhttp3.OkHttpClient client;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.network.HttpClient INSTANCE = null;
    
    private HttpClient() {
        super();
    }
    
    /**
     * Get configured OkHttpClient instance
     */
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient getInstance(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Create and configure OkHttpClient
     */
    private final okhttp3.OkHttpClient createClient(android.content.Context context) {
        return null;
    }
    
    /**
     * Reset client instance (useful for testing or configuration changes)
     */
    public final void reset() {
    }
}