package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u0000 C2\u00020\u0001:\u0001CB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u0004H\u0002J\b\u0010)\u001a\u00020\'H\u0002J\u0010\u0010*\u001a\u00020\'2\u0006\u0010+\u001a\u00020,H\u0002J\b\u0010-\u001a\u00020\'H\u0002J\u0012\u0010.\u001a\u00020\'2\b\u0010/\u001a\u0004\u0018\u000100H\u0014J\b\u00101\u001a\u00020\'H\u0014J\b\u00102\u001a\u00020\'H\u0014J\u0018\u00103\u001a\b\u0012\u0004\u0012\u000205042\b\u00106\u001a\u0004\u0018\u000107H\u0002J\u0012\u00108\u001a\u00020\u00062\b\u00109\u001a\u0004\u0018\u00010\u0004H\u0002J\b\u0010:\u001a\u00020\'H\u0002J\b\u0010;\u001a\u00020\'H\u0002J\b\u0010<\u001a\u00020\'H\u0002J\b\u0010=\u001a\u00020\'H\u0002J\b\u0010>\u001a\u00020\'H\u0002J\b\u0010?\u001a\u00020\'H\u0002J\b\u0010@\u001a\u00020\'H\u0002J\u0010\u0010A\u001a\u00020\'2\u0006\u0010B\u001a\u00020\rH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020!X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006D"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommentsActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "POLLING_INTERVAL", "", "TAG", "buttonSendComment", "Landroid/widget/ImageButton;", "commentsAdapter", "Lcom/spyro/vmeet/ui/community/CommentsAdapter;", "currentPost", "Lcom/spyro/vmeet/data/community/Post;", "currentUserId", "", "currentUsername", "editTextComment", "Landroid/widget/EditText;", "imageViewPostUserAvatar", "Landroid/widget/ImageView;", "isPollingActive", "", "pollingHandler", "Landroid/os/Handler;", "pollingRunnable", "Ljava/lang/Runnable;", "postId", "progressBar", "Landroid/widget/ProgressBar;", "recyclerViewComments", "Landroidx/recyclerview/widget/RecyclerView;", "textViewPostContent", "Landroid/widget/TextView;", "textViewPostTimestamp", "textViewPostUsername", "toolbar", "Landroidx/appcompat/widget/Toolbar;", "addComment", "", "commentText", "initializeViews", "likeComment", "comment", "Lcom/spyro/vmeet/data/community/PostComment;", "loadPostAndComments", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onPause", "onResume", "parseReactions", "", "Lcom/spyro/vmeet/data/MessageReaction;", "jsonArray", "Lorg/json/JSONArray;", "parseTimestamp", "dateString", "pollForReactionUpdates", "setupListeners", "setupRecyclerView", "setupToolbar", "setupWindowInsets", "startReactionPolling", "stopReactionPolling", "updatePostUI", "postToDisplay", "Companion", "app_debug"})
public final class CommentsActivity extends androidx.appcompat.app.AppCompatActivity {
    private androidx.appcompat.widget.Toolbar toolbar;
    private androidx.recyclerview.widget.RecyclerView recyclerViewComments;
    private android.widget.EditText editTextComment;
    private android.widget.ImageButton buttonSendComment;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewPostUsername;
    private android.widget.TextView textViewPostTimestamp;
    private android.widget.TextView textViewPostContent;
    private android.widget.ImageView imageViewPostUserAvatar;
    private com.spyro.vmeet.ui.community.CommentsAdapter commentsAdapter;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String postId = "";
    private int currentUserId = -1;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentUsername = "";
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.community.Post currentPost;
    private final long POLLING_INTERVAL = 1000L;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler pollingHandler = null;
    private boolean isPollingActive = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable pollingRunnable;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "CommentsActivity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String EXTRA_POST_ID = "extra_post_id";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.community.CommentsActivity.Companion Companion = null;
    
    public CommentsActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeViews() {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupListeners() {
    }
    
    private final void setupWindowInsets() {
    }
    
    private final void loadPostAndComments() {
    }
    
    private final void updatePostUI(com.spyro.vmeet.data.community.Post postToDisplay) {
    }
    
    private final void startReactionPolling() {
    }
    
    private final void stopReactionPolling() {
    }
    
    private final void pollForReactionUpdates() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    private final void addComment(java.lang.String commentText) {
    }
    
    private final void likeComment(com.spyro.vmeet.data.community.PostComment comment) {
    }
    
    private final long parseTimestamp(java.lang.String dateString) {
        return 0L;
    }
    
    private final java.util.List<com.spyro.vmeet.data.MessageReaction> parseReactions(org.json.JSONArray jsonArray) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommentsActivity$Companion;", "", "()V", "EXTRA_POST_ID", "", "newIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "postId", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent newIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        java.lang.String postId) {
            return null;
        }
    }
}