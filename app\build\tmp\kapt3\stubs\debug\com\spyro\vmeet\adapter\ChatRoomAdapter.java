package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010#\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u001fBa\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\u0016\b\u0002\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\u0016\b\u0002\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\rJ\b\u0010\u0013\u001a\u00020\u0012H\u0016J\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00050\u0015J\u001c\u0010\u0016\u001a\u00020\b2\n\u0010\u0017\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0018\u001a\u00020\u0012H\u0016J\u001c\u0010\u0019\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u0012H\u0016J\u0014\u0010\u001d\u001a\u00020\b2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00050\u0015R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082D\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/spyro/vmeet/adapter/ChatRoomAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/ChatRoomAdapter$ChatRoomViewHolder;", "rooms", "", "Lcom/spyro/vmeet/data/ChatRoom;", "onRoomClick", "Lkotlin/Function1;", "", "isAdmin", "", "onEditRoom", "onDeleteRoom", "(Ljava/util/List;Lkotlin/jvm/functions/Function1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "TAG", "", "animatedDescriptions", "", "", "getItemCount", "getRooms", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "updateRooms", "newRooms", "ChatRoomViewHolder", "app_debug"})
public final class ChatRoomAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.ChatRoomAdapter.ChatRoomViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.ChatRoom> rooms = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.ChatRoom, kotlin.Unit> onRoomClick = null;
    private final boolean isAdmin = false;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.ChatRoom, kotlin.Unit> onEditRoom = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.ChatRoom, kotlin.Unit> onDeleteRoom = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "ChatRoomAdapter";
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.Integer> animatedDescriptions = null;
    
    public ChatRoomAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.ChatRoom> rooms, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.ChatRoom, kotlin.Unit> onRoomClick, boolean isAdmin, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.ChatRoom, kotlin.Unit> onEditRoom, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.ChatRoom, kotlin.Unit> onDeleteRoom) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.ChatRoomAdapter.ChatRoomViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.ChatRoomAdapter.ChatRoomViewHolder holder, int position) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.spyro.vmeet.data.ChatRoom> getRooms() {
        return null;
    }
    
    public final void updateRooms(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.ChatRoom> newRooms) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u0014J\u0018\u0010\u0015\u001a\u00020\u00102\u0006\u0010\u0016\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u0012H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0017"}, d2 = {"Lcom/spyro/vmeet/adapter/ChatRoomAdapter$ChatRoomViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/ChatRoomAdapter;Landroid/view/View;)V", "imageViewRoomIcon", "Lde/hdodenhof/circleimageview/CircleImageView;", "textViewLastMessage", "Landroid/widget/TextView;", "textViewLastMessageTime", "textViewMemberCount", "textViewRoomDescription", "textViewRoomName", "textViewUnreadCount", "viewUnreadIndicator", "bind", "", "room", "Lcom/spyro/vmeet/data/ChatRoom;", "wasAnimated", "", "showAdminOptions", "view", "app_debug"})
    public final class ChatRoomViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewRoomIcon = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewRoomName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewRoomDescription = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewLastMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewLastMessageTime = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewMemberCount = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View viewUnreadIndicator = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewUnreadCount = null;
        
        public ChatRoomViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.ChatRoom room, boolean wasAnimated) {
        }
        
        private final void showAdminOptions(android.view.View view, com.spyro.vmeet.data.ChatRoom room) {
        }
    }
}