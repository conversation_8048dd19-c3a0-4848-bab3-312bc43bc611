package com.spyro.vmeet.adapter;

/**
 * Adapter for displaying nearby users in the Radar feature
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0001\u001bB\u0015\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0014\u0010\r\u001a\u00020\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u0010J\u0006\u0010\u0011\u001a\u00020\u000eJ\b\u0010\u0012\u001a\u00020\u0006H\u0016J\u001c\u0010\u0013\u001a\u00020\u000e2\n\u0010\u0014\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0015\u001a\u00020\u0006H\u0016J\u001c\u0010\u0016\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0006H\u0016J\u0014\u0010\u001a\u001a\u00020\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\n0\u0010R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/spyro/vmeet/adapter/RadarUserAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/RadarUserAdapter$RadarUserViewHolder;", "context", "Landroid/content/Context;", "currentUserId", "", "(Landroid/content/Context;I)V", "userIdMap", "", "Lcom/spyro/vmeet/data/RadarUser;", "users", "", "addUsers", "", "newUsers", "", "clearUsers", "getItemCount", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "updateUsers", "RadarUserViewHolder", "app_release"})
public final class RadarUserAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.RadarUserAdapter.RadarUserViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final int currentUserId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.RadarUser> users = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, com.spyro.vmeet.data.RadarUser> userIdMap = null;
    
    public RadarUserAdapter(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int currentUserId) {
        super();
    }
    
    public final void updateUsers(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.RadarUser> newUsers) {
    }
    
    public final void addUsers(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.RadarUser> newUsers) {
    }
    
    public final void clearUsers() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.RadarUserAdapter.RadarUserViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.RadarUserAdapter.RadarUserViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014J\u0012\u0010\u0015\u001a\u00020\u00122\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0002J\u0010\u0010\u0018\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\u0010\u0010\u0019\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/spyro/vmeet/adapter/RadarUserAdapter$RadarUserViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/RadarUserAdapter;Landroid/view/View;)V", "buttonViewProfile", "Landroid/widget/ImageButton;", "imageViewAvatar", "Lde/hdodenhof/circleimageview/CircleImageView;", "textViewAge", "Landroid/widget/TextView;", "textViewBio", "textViewDistance", "textViewLocation", "textViewOnlineStatus", "textViewUsername", "viewOnlineStatus", "bind", "", "user", "Lcom/spyro/vmeet/data/RadarUser;", "loadAvatar", "avatarUrl", "", "openUserProfile", "setupClickListeners", "app_release"})
    public final class RadarUserViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewAvatar = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View viewOnlineStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewUsername = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewDistance = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewLocation = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewAge = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewOnlineStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewBio = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageButton buttonViewProfile = null;
        
        public RadarUserViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.RadarUser user) {
        }
        
        private final void loadAvatar(java.lang.String avatarUrl) {
        }
        
        private final void setupClickListeners(com.spyro.vmeet.data.RadarUser user) {
        }
        
        private final void openUserProfile(com.spyro.vmeet.data.RadarUser user) {
        }
    }
}