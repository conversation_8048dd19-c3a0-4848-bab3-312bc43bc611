package com.spyro.vmeet.fragment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000~\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0012\b\u0007\u0018\u0000 A2\u00020\u0001:\u0001AB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001b\u001a\u00020\u001cH\u0002J,\u0010\u001d\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001f2\b\u0010!\u001a\u0004\u0018\u00010\u001f2\b\u0010\"\u001a\u0004\u0018\u00010\u001fH\u0002J\u0010\u0010#\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020\u0018H\u0002J4\u0010%\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001f2\b\u0010!\u001a\u0004\u0018\u00010\u001f2\b\u0010\"\u001a\u0004\u0018\u00010\u001fH\u0002J\u0010\u0010&\u001a\u00020\u001f2\u0006\u0010\'\u001a\u00020\u001fH\u0002J\b\u0010(\u001a\u00020\u001cH\u0002J&\u0010)\u001a\u0004\u0018\u00010*2\u0006\u0010+\u001a\u00020,2\b\u0010-\u001a\u0004\u0018\u00010.2\b\u0010/\u001a\u0004\u0018\u000100H\u0016J\b\u00101\u001a\u00020\u001cH\u0016J\b\u00102\u001a\u00020\u001cH\u0016J\b\u00103\u001a\u00020\u001cH\u0016J\u0010\u00104\u001a\u00020\u001c2\u0006\u00105\u001a\u00020\u0014H\u0002J\b\u00106\u001a\u00020\u001cH\u0002J\b\u00107\u001a\u00020\u001cH\u0002J\b\u00108\u001a\u00020\u001cH\u0002J\u0010\u00109\u001a\u00020\u001c2\u0006\u00105\u001a\u00020\u0014H\u0002J\u0010\u0010:\u001a\u00020\u001c2\u0006\u00105\u001a\u00020\u0014H\u0002J\u0010\u0010;\u001a\u00020\u001c2\u0006\u0010<\u001a\u00020\u001fH\u0002J\b\u0010=\u001a\u00020\u001cH\u0002J\b\u0010>\u001a\u00020\u001cH\u0002J\u0018\u0010?\u001a\u00020\u001c2\u0006\u0010$\u001a\u00020\u00182\u0006\u0010@\u001a\u00020\u0018H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u0013X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006B"}, d2 = {"Lcom/spyro/vmeet/fragment/ChatRoomsFragment;", "Landroidx/fragment/app/Fragment;", "()V", "POLLING_INTERVAL", "", "fabCreateRoom", "Lcom/google/android/material/floatingactionbutton/FloatingActionButton;", "isAdmin", "", "isPollingActive", "pollingHandler", "Landroid/os/Handler;", "progressBarRooms", "Landroid/widget/ProgressBar;", "recyclerViewChatRooms", "Landroidx/recyclerview/widget/RecyclerView;", "roomAdapter", "Lcom/spyro/vmeet/adapter/ChatRoomAdapter;", "roomsList", "", "Lcom/spyro/vmeet/data/ChatRoom;", "textViewEmptyRooms", "Landroid/widget/TextView;", "userId", "", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "checkAdminStatus", "", "createChatRoom", "name", "", "description", "iconUrl", "rules", "deleteChatRoom", "roomId", "editChatRoom", "formatTimestamp", "timestamp", "loadChatRooms", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onPause", "onResume", "openChatRoomActivity", "room", "setupRecyclerView", "setupWebSocket", "showCreateRoomDialog", "showDeleteRoomConfirmationDialog", "showEditRoomDialog", "showError", "message", "startPolling", "stopPolling", "updateRoomUnreadCount", "unreadCount", "Companion", "app_debug"})
public final class ChatRoomsFragment extends androidx.fragment.app.Fragment {
    private androidx.recyclerview.widget.RecyclerView recyclerViewChatRooms;
    private android.widget.TextView textViewEmptyRooms;
    private android.widget.ProgressBar progressBarRooms;
    private com.google.android.material.floatingactionbutton.FloatingActionButton fabCreateRoom;
    private com.spyro.vmeet.adapter.ChatRoomAdapter roomAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.ChatRoom> roomsList = null;
    private int userId = 0;
    private boolean isAdmin = false;
    private boolean isPollingActive = false;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler pollingHandler = null;
    private final long POLLING_INTERVAL = 1000L;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.WebSocketClient webSocketClient;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ChatRoomsFragment";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String WS_URL = "ws://77.110.116.89:3000/ws";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.fragment.ChatRoomsFragment.Companion Companion = null;
    
    public ChatRoomsFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupWebSocket() {
    }
    
    private final void updateRoomUnreadCount(int roomId, int unreadCount) {
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    private final void checkAdminStatus() {
    }
    
    private final void loadChatRooms() {
    }
    
    private final java.lang.String formatTimestamp(java.lang.String timestamp) {
        return null;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void startPolling() {
    }
    
    private final void stopPolling() {
    }
    
    private final void openChatRoomActivity(com.spyro.vmeet.data.ChatRoom room) {
    }
    
    private final void showCreateRoomDialog() {
    }
    
    private final void showEditRoomDialog(com.spyro.vmeet.data.ChatRoom room) {
    }
    
    private final void showDeleteRoomConfirmationDialog(com.spyro.vmeet.data.ChatRoom room) {
    }
    
    private final void createChatRoom(java.lang.String name, java.lang.String description, java.lang.String iconUrl, java.lang.String rules) {
    }
    
    private final void editChatRoom(int roomId, java.lang.String name, java.lang.String description, java.lang.String iconUrl, java.lang.String rules) {
    }
    
    private final void deleteChatRoom(int roomId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/spyro/vmeet/fragment/ChatRoomsFragment$Companion;", "", "()V", "API_URL", "", "TAG", "WS_URL", "newInstance", "Lcom/spyro/vmeet/fragment/ChatRoomsFragment;", "userId", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.fragment.ChatRoomsFragment newInstance(int userId) {
            return null;
        }
    }
}