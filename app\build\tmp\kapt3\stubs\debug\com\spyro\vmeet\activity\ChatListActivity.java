package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u0000 /2\u00020\u0001:\u0002./B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010\u0016\u001a\u00020\u0015H\u0002J\u0010\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\tH\u0002J\u0010\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001aH\u0002J\u001c\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u001a0\u001d2\u0006\u0010\u001e\u001a\u00020\u0011H\u0002J\u001c\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u001a0\u001d2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\b\u0010 \u001a\u00020\u0015H\u0002J\u0012\u0010!\u001a\u00020\u00152\b\u0010\"\u001a\u0004\u0018\u00010#H\u0014J\b\u0010$\u001a\u00020\u0015H\u0014J\b\u0010%\u001a\u00020\u0015H\u0014J\u0010\u0010&\u001a\u00020\u00152\u0006\u0010\'\u001a\u00020\u0011H\u0002J\b\u0010(\u001a\u00020\u0015H\u0002J\u0010\u0010)\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\tH\u0002J\u0010\u0010*\u001a\u00020\u00152\u0006\u0010+\u001a\u00020\u001aH\u0002J\b\u0010,\u001a\u00020\u0015H\u0002J\b\u0010-\u001a\u00020\u0015H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00060"}, d2 = {"Lcom/spyro/vmeet/activity/ChatListActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "()V", "POLLING_INTERVAL", "", "bottomNavView", "Lcom/google/android/material/bottomnavigation/BottomNavigationView;", "chatSummaryList", "", "Lcom/spyro/vmeet/data/ChatSummary;", "isPollingActive", "", "pollingHandler", "Landroid/os/Handler;", "tabLayout", "Lcom/google/android/material/tabs/TabLayout;", "userId", "", "viewPager", "Landroidx/viewpager2/widget/ViewPager2;", "clearAllNotifications", "", "clearAppBadge", "deleteChat", "chatSummary", "formatTimestamp", "", "timestamp", "getLastMessage", "Lkotlin/Pair;", "matchId", "getUserInfo", "loadChatList", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onPause", "onResume", "setupBottomNavigation", "currentUserId", "setupViewPager", "showDeleteChatDialog", "showError", "message", "startPolling", "stopPolling", "ChatPagerAdapter", "Companion", "app_debug"})
public final class ChatListActivity extends com.spyro.vmeet.activity.BaseActivity {
    private com.google.android.material.tabs.TabLayout tabLayout;
    private androidx.viewpager2.widget.ViewPager2 viewPager;
    private com.google.android.material.bottomnavigation.BottomNavigationView bottomNavView;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.ChatSummary> chatSummaryList = null;
    private boolean isPollingActive = false;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler pollingHandler = null;
    private final long POLLING_INTERVAL = 1000L;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ChatListActivity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    private static final int PRIVATE_CHATS_TAB = 0;
    private static final int CHAT_ROOMS_TAB = 1;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.ChatListActivity.Companion Companion = null;
    
    public ChatListActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupViewPager() {
    }
    
    private final void setupBottomNavigation(int currentUserId) {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    private final void startPolling() {
    }
    
    private final void stopPolling() {
    }
    
    private final void showDeleteChatDialog(com.spyro.vmeet.data.ChatSummary chatSummary) {
    }
    
    private final void deleteChat(com.spyro.vmeet.data.ChatSummary chatSummary) {
    }
    
    private final void loadChatList() {
    }
    
    private final kotlin.Pair<java.lang.String, java.lang.String> getUserInfo(int userId) {
        return null;
    }
    
    private final kotlin.Pair<java.lang.String, java.lang.String> getLastMessage(int matchId) {
        return null;
    }
    
    private final java.lang.String formatTimestamp(java.lang.String timestamp) {
        return null;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void clearAllNotifications() {
    }
    
    private final void clearAppBadge() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0002\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\b\u0010\t\u001a\u00020\bH\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/spyro/vmeet/activity/ChatListActivity$ChatPagerAdapter;", "Landroidx/viewpager2/adapter/FragmentStateAdapter;", "currentActivity", "Landroidx/fragment/app/FragmentActivity;", "(Landroidx/fragment/app/FragmentActivity;)V", "createFragment", "Landroidx/fragment/app/Fragment;", "position", "", "getItemCount", "app_debug"})
    static final class ChatPagerAdapter extends androidx.viewpager2.adapter.FragmentStateAdapter {
        @org.jetbrains.annotations.NotNull()
        private final androidx.fragment.app.FragmentActivity currentActivity = null;
        
        public ChatPagerAdapter(@org.jetbrains.annotations.NotNull()
        androidx.fragment.app.FragmentActivity currentActivity) {
            super(null);
        }
        
        @java.lang.Override()
        public int getItemCount() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public androidx.fragment.app.Fragment createFragment(int position) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/spyro/vmeet/activity/ChatListActivity$Companion;", "", "()V", "API_URL", "", "CHAT_ROOMS_TAB", "", "PRIVATE_CHATS_TAB", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}