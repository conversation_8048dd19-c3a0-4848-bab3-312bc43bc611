<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="60dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/imageViewUserProfile"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_profile_placeholder" 
        app:civ_border_width="1dp"
        app:civ_border_color="#CCCCCC"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cardViewMessage"
        android:contentDescription="User profile picture" />

    <!-- Add sender name text view for admin badge -->
    <TextView
        android:id="@+id/textViewSenderName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="2dp"
        android:textSize="12sp"
        android:textStyle="bold"
        android:textColor="#FFFFFF"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingTop="2dp"
        android:paddingBottom="2dp"
        android:visibility="gone"
        android:gravity="center"
        app:layout_constraintEnd_toStartOf="@+id/imageViewUserProfile"
        app:layout_constraintBottom_toTopOf="@+id/cardViewMessage"
        tools:text="Admin"
        tools:visibility="visible" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardElevation="0dp"
        app:cardCornerRadius="12dp"
        app:layout_constraintEnd_toStartOf="@+id/imageViewUserProfile"
        app:layout_constraintWidth_max="300dp"
        app:layout_constraintWidth_percent="0.8"
        android:layout_marginEnd="8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/message_sent_background">

            <!-- Reply section -->
            <LinearLayout
                android:id="@+id/layoutReply"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="#55000000"
                android:padding="8dp"
                android:layout_marginBottom="4dp"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">
                    
                    <View
                        android:layout_width="3dp"
                        android:layout_height="match_parent"
                        android:background="#64B5F6"
                        android:layout_marginEnd="8dp"/>
                        
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                            
                        <TextView
                            android:id="@+id/textViewReplyLabel"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Respondiendo a:"
                            android:textColor="#64B5F6"
                            android:textStyle="italic"
                            android:textSize="12sp"/>
    
                        <androidx.emoji2.widget.EmojiTextView
                            android:id="@+id/textViewReplyText"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="#FFFFFF"
                            android:textSize="14sp"
                            android:maxLines="4"
                            android:ellipsize="end"
                            tools:text="Original message being replied to"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- Actual message -->
            <androidx.emoji2.widget.EmojiTextView
                android:id="@+id/textViewMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="12dp"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:breakStrategy="simple"
                android:hyphenationFrequency="none"
                android:lineBreakStyle="strict"
                tools:text="This is the message text with a very long content that should wrap properly to the next line without being cut off or displaying improperly in the user interface"/>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
    
    <!-- Reactions container -->
    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/reactionsContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        app:flexWrap="wrap"
        app:alignItems="flex_start"
        app:justifyContent="flex_end"
        app:layout_constraintEnd_toEndOf="@id/cardViewMessage"
        app:layout_constraintTop_toBottomOf="@id/cardViewMessage"
        app:layout_constraintBottom_toTopOf="@id/textViewTime"/>

    <TextView
        android:id="@+id/textViewTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="#AAAAAA"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="4dp"
        app:layout_constraintEnd_toStartOf="@id/viewMessageStatus"
        app:layout_constraintTop_toBottomOf="@id/reactionsContainer"/>

    <View
        android:id="@+id/viewMessageStatus"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="2dp"
        android:background="@drawable/status_pending"
        app:layout_constraintEnd_toEndOf="@id/cardViewMessage"
        app:layout_constraintTop_toBottomOf="@id/reactionsContainer"/>

</androidx.constraintlayout.widget.ConstraintLayout>