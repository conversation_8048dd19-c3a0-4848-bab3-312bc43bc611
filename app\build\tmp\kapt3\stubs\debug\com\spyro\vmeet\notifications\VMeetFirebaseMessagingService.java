package com.spyro.vmeet.notifications;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\b\n\u0002\b\u000b\b\u0007\u0018\u0000 \"2\u00020\u0001:\u0001\"B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0082@\u00a2\u0006\u0002\u0010\u0007J\u0018\u0010\b\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0082@\u00a2\u0006\u0002\u0010\u0007J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016J\u0010\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u0006H\u0016J\u001c\u0010\u000f\u001a\u00020\n2\b\u0010\u0010\u001a\u0004\u0018\u00010\u00062\b\u0010\u0011\u001a\u0004\u0018\u00010\u0006H\u0002J>\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00062\b\u0010\u0014\u001a\u0004\u0018\u00010\u00062\b\u0010\u0015\u001a\u0004\u0018\u00010\u00062\b\u0010\u0016\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J*\u0010\u0019\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u00182\u0006\u0010\u001c\u001a\u00020\u00062\b\u0010\u001d\u001a\u0004\u0018\u00010\u0006H\u0002J2\u0010\u001e\u001a\u00020\n2\u0006\u0010\u001f\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u00182\u0006\u0010 \u001a\u00020\u00062\u0006\u0010!\u001a\u00020\u00062\b\u0010\u001d\u001a\u0004\u0018\u00010\u0006H\u0002\u00a8\u0006#"}, d2 = {"Lcom/spyro/vmeet/notifications/VMeetFirebaseMessagingService;", "Lcom/google/firebase/messaging/FirebaseMessagingService;", "()V", "getBitmapFromUrl", "Landroid/graphics/Bitmap;", "urlString", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getBitmapFromUrlWithoutRotation", "onMessageReceived", "", "remoteMessage", "Lcom/google/firebase/messaging/RemoteMessage;", "onNewToken", "token", "sendDefaultNotification", "title", "body", "sendGlobalNotification", "message", "imageUrl", "buttonUrl", "buttonText", "notificationId", "", "sendNewMatchNotification", "matchId", "otherUserId", "otherUserName", "avatarUrl", "sendNewMessageNotification", "senderId", "senderName", "messageText", "Companion", "app_debug"})
public final class VMeetFirebaseMessagingService extends com.google.firebase.messaging.FirebaseMessagingService {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "VMeetFCMService";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String NEW_MESSAGE_CHANNEL_ID = "new_message_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String NEW_MATCH_CHANNEL_ID = "new_match_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GLOBAL_NOTIFICATION_CHANNEL_ID = "vmeet_notifications";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_MARK_AS_READ = "com.spyro.vmeet.ACTION_MARK_AS_READ";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_REPLY = "com.spyro.vmeet.ACTION_REPLY";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String KEY_TEXT_REPLY = "key_text_reply";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_MATCH_ID = "match_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_SENDER_ID = "sender_id";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_NOTIFICATION_ID = "notification_id";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.notifications.VMeetFirebaseMessagingService.Companion Companion = null;
    
    public VMeetFirebaseMessagingService() {
        super();
    }
    
    @java.lang.Override()
    public void onMessageReceived(@org.jetbrains.annotations.NotNull()
    com.google.firebase.messaging.RemoteMessage remoteMessage) {
    }
    
    @java.lang.Override()
    public void onNewToken(@org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
    
    private final void sendNewMessageNotification(int senderId, int matchId, java.lang.String senderName, java.lang.String messageText, java.lang.String avatarUrl) {
    }
    
    private final void sendNewMatchNotification(int matchId, int otherUserId, java.lang.String otherUserName, java.lang.String avatarUrl) {
    }
    
    private final void sendDefaultNotification(java.lang.String title, java.lang.String body) {
    }
    
    private final java.lang.Object getBitmapFromUrl(java.lang.String urlString, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    private final void sendGlobalNotification(java.lang.String title, java.lang.String message, java.lang.String imageUrl, java.lang.String buttonUrl, java.lang.String buttonText, int notificationId) {
    }
    
    private final java.lang.Object getBitmapFromUrlWithoutRotation(java.lang.String urlString, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/notifications/VMeetFirebaseMessagingService$Companion;", "", "()V", "ACTION_MARK_AS_READ", "", "ACTION_REPLY", "EXTRA_MATCH_ID", "EXTRA_NOTIFICATION_ID", "EXTRA_SENDER_ID", "GLOBAL_NOTIFICATION_CHANNEL_ID", "KEY_TEXT_REPLY", "NEW_MATCH_CHANNEL_ID", "NEW_MESSAGE_CHANNEL_ID", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}