package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0017\u001a\u00020\u0018H\u0002J\b\u0010\u0019\u001a\u00020\u001aH\u0002J\b\u0010\u001b\u001a\u00020\u001aH\u0002J\u0012\u0010\u001c\u001a\u00020\u001a2\b\u0010\u001d\u001a\u0004\u0018\u00010\u001eH\u0014J\b\u0010\u001f\u001a\u00020\u001aH\u0002J\b\u0010 \u001a\u00020\u001aH\u0002J\b\u0010!\u001a\u00020\u001aH\u0002J\b\u0010\"\u001a\u00020\u001aH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006#"}, d2 = {"Lcom/spyro/vmeet/activity/EditPersonalityTraitsActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "TAG", "buttonSave", "Landroid/widget/Button;", "categories", "", "Lcom/spyro/vmeet/adapter/TraitCategory;", "categoryAdapter", "Lcom/spyro/vmeet/adapter/PersonalityTraitCategoryAdapter;", "progressBar", "Landroid/widget/ProgressBar;", "recyclerViewTraits", "Landroidx/recyclerview/widget/RecyclerView;", "textViewTitle", "Landroid/widget/TextView;", "toolbar", "Landroidx/appcompat/widget/Toolbar;", "userId", "", "createCompatibilityLayout", "Landroid/view/View;", "initializeViews", "", "loadPersonalityTraits", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "savePersonalityTraits", "setupRecyclerView", "setupSaveButton", "setupToolbar", "app_release"})
public final class EditPersonalityTraitsActivity extends androidx.appcompat.app.AppCompatActivity {
    private androidx.appcompat.widget.Toolbar toolbar;
    private androidx.recyclerview.widget.RecyclerView recyclerViewTraits;
    private android.widget.Button buttonSave;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewTitle;
    private com.spyro.vmeet.adapter.PersonalityTraitCategoryAdapter categoryAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.adapter.TraitCategory> categories = null;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "EditPersonalityTraits";
    
    public EditPersonalityTraitsActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final android.view.View createCompatibilityLayout() {
        return null;
    }
    
    private final void initializeViews() {
    }
    
    private final void setupToolbar() {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void loadPersonalityTraits() {
    }
    
    private final void setupSaveButton() {
    }
    
    private final void savePersonalityTraits() {
    }
}