package com.spyro.vmeet.ui.community

import android.app.Application
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.spyro.vmeet.data.MessageReaction
import com.spyro.vmeet.data.community.Post
import com.spyro.vmeet.data.community.Story
import com.spyro.vmeet.data.community.StoryViewer
import org.json.JSONObject
import org.json.JSONArray
import com.spyro.vmeet.ui.community.TextOverlayData
import java.io.File
import java.io.OutputStream
import java.net.HttpURLConnection
import java.net.URL
import kotlin.concurrent.thread
import java.util.regex.Pattern
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone

class CommunityViewModel(application: Application) : AndroidViewModel(application) {
    
    private val API_URL = "http://77.110.116.89:3000"
    private val TAG = "CommunityViewModel"
    
    internal val _posts = MutableLiveData<List<Post>>()
    val posts: LiveData<List<Post>> = _posts
    
    // LiveData for Stories
    internal val _stories = MutableLiveData<List<Story>>()
    val stories: LiveData<List<Story>> = _stories
    
    internal val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    internal val _errorMessage = MutableLiveData<String?>()
    val errorMessage: LiveData<String?> = _errorMessage
    
    // LiveData for Story Upload Success
    internal val _storyUploadSuccess = MutableLiveData<Boolean>()
    val storyUploadSuccess: LiveData<Boolean> = _storyUploadSuccess
    
    // For real-time polling
    private val POLLING_INTERVAL = 1000L // 1 second
    private val handler = Handler(Looper.getMainLooper())
    private var isPollingActive = false
    private var latestPostId: String = ""
    private var latestPostTimestamp: Long = 0
    private var pollingRunnable: Runnable? = null
    
    init {
        loadPosts()
        startPolling()
        // loadStories() // Will be called from Fragment with userId
    }
    
    // Start real-time polling
    fun startPolling() {
        if (isPollingActive) return
        
        isPollingActive = true
        Log.d(TAG, "Starting real-time post polling at ${POLLING_INTERVAL}ms intervals")
        
        pollingRunnable = object : Runnable {
            override fun run() {
                if (!isPollingActive) return
                
                checkForNewPosts()
                handler.postDelayed(this, POLLING_INTERVAL)
            }
        }
        
        // Start immediately
        pollingRunnable?.let {
            handler.post(it)
        }
    }
    
    // Stop polling to prevent memory leaks
    fun stopPolling() {
        isPollingActive = false
        pollingRunnable?.let {
            handler.removeCallbacks(it)
        }
        pollingRunnable = null
        Log.d(TAG, "Stopped real-time post polling")
    }
      // Check for new posts
    private fun checkForNewPosts() {
        // Skip if we don't have any posts loaded yet or we're currently loading
        if (_posts.value.isNullOrEmpty() || _isLoading.value == true) return
        
        thread {
            try {
                val url = URL("$API_URL/community/posts?since=${latestPostTimestamp}")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 5000 // Shorter timeout for frequent polling
                connection.readTimeout = 5000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val jsonPosts = jsonResponse.getJSONArray("posts")
                        
                        if (jsonPosts.length() > 0) {
                            Log.d(TAG, "Found ${jsonPosts.length()} new posts")
                            
                            val newPosts = mutableListOf<Post>()
                            var hasNewerPosts = false
                            
                            for (i in 0 until jsonPosts.length()) {
                                val jsonPost = jsonPosts.getJSONObject(i)
                                val postId = jsonPost.optString("id", "")
                                val timestamp = parseTimestamp(jsonPost.optString("created_at", ""))
                                val textContent = jsonPost.optString("text_content", "")
                                
                                // Check if this is actually a new post
                                if (postId != latestPostId && timestamp > latestPostTimestamp) {
                                    hasNewerPosts = true
                                    
                                    // Update latest post tracking
                                    if (timestamp > latestPostTimestamp) {
                                        latestPostTimestamp = timestamp
                                        latestPostId = postId
                                    }
                                    
                                    // Extract YouTube ID if present
                                    val youtubeId = extractYoutubeId(textContent)
                                    
                                    // Parse reactions
                                    val reactionsArray = jsonPost.optJSONArray("reactions")
                                    val reactions = mutableListOf<MessageReaction>()
                                    
                                    if (reactionsArray != null) {
                                        for (j in 0 until reactionsArray.length()) {
                                            val jsonReaction = reactionsArray.getJSONObject(j)
                                            reactions.add(
                                                MessageReaction(
                                                    id = jsonReaction.optInt("id", 0),
                                                    messageId = jsonReaction.optInt("post_id", 0),
                                                    userId = jsonReaction.optInt("user_id", 0),
                                                    reaction = jsonReaction.optString("reaction", "👍"),
                                                    createdAt = jsonReaction.optString("created_at", "")
                                                )
                                            )
                                        }
                                    }
                                    
                                    newPosts.add(
                                        Post(
                                            id = postId,
                                            userId = jsonPost.optInt("user_id", 0),
                                            username = jsonPost.optString("username", "Usuario"),
                                            userAvatar = jsonPost.optString("user_avatar", "").takeIf { it.isNotEmpty() },
                                            timestamp = timestamp,
                                            textContent = textContent,
                                            imageUrl = jsonPost.optString("image_url", "").takeIf { it.isNotEmpty() },
                                            gifUrl = jsonPost.optString("gif_url", "").takeIf { it.isNotEmpty() },
                                            gifPreviewUrl = jsonPost.optString("gif_preview_url", "").takeIf { it.isNotEmpty() },
                                            voiceNoteUrl = jsonPost.optString("voice_note_url", "").takeIf { it.isNotEmpty() },
                                            voiceNoteDuration = jsonPost.optInt("voice_note_duration", 0).takeIf { it > 0 },
                                            commentCount = jsonPost.optInt("comment_count", 0),
                                            reactions = reactions,
                                            youtubeVideoId = youtubeId
                                        )
                                    )
                                }
                            }
                            
                            // Update posts list if we found new ones
                            if (hasNewerPosts) {
                                val currentPosts = _posts.value?.toMutableList() ?: mutableListOf()
                                // Add new posts to the beginning of the list
                                currentPosts.addAll(0, newPosts)
                                // Sort posts by timestamp (newest first)
                                currentPosts.sortByDescending { it.timestamp }
                                _posts.postValue(currentPosts)
                            }
                        }
                    }
                }
                  connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error polling for new posts", e)
                // Don't show error message to user for polling failures
            }
        }
        
        // Also check for reaction updates on existing posts
        checkForReactionUpdates()
    }
    
    // Check for reaction updates on existing posts
    private fun checkForReactionUpdates() {
        val currentPosts = _posts.value
        if (currentPosts.isNullOrEmpty()) return
        
        thread {
            try {
                // Take the first 10 posts to check for reaction updates (most recent ones)
                val postsToCheck = currentPosts.take(10)
                var hasUpdates = false
                val updatedPosts = currentPosts.toMutableList()
                
                for (i in postsToCheck.indices) {
                    val post = postsToCheck[i]
                    
                    try {
                        val url = URL("$API_URL/community/posts/${post.id}")
                        val connection = url.openConnection() as HttpURLConnection
                        connection.requestMethod = "GET"
                        connection.connectTimeout = 3000 // Even shorter timeout for reaction checks
                        connection.readTimeout = 3000
                        
                        val responseCode = connection.responseCode
                        if (responseCode == HttpURLConnection.HTTP_OK) {
                            val response = connection.inputStream.bufferedReader().readText()
                            
                            val jsonResponse = JSONObject(response)
                            if (jsonResponse.optBoolean("success", false)) {
                                val jsonPost = jsonResponse.getJSONObject("post")
                                
                                // Parse reactions
                                val reactionsArray = jsonPost.optJSONArray("reactions")
                                val newReactions = mutableListOf<MessageReaction>()
                                
                                if (reactionsArray != null) {
                                    for (j in 0 until reactionsArray.length()) {
                                        val jsonReaction = reactionsArray.getJSONObject(j)
                                        newReactions.add(
                                            MessageReaction(
                                                id = jsonReaction.optInt("id", 0),
                                                messageId = jsonReaction.optInt("post_id", 0),
                                                userId = jsonReaction.optInt("user_id", 0),
                                                reaction = jsonReaction.optString("reaction", "👍"),
                                                createdAt = jsonReaction.optString("created_at", "")
                                            )
                                        )
                                    }
                                }
                                
                                // Check if reactions have changed
                                val currentReactions = post.reactions ?: emptyList()
                                val changed = currentReactions.size != newReactions.size ||
                                              !currentReactions.containsAll(newReactions) ||
                                              !newReactions.containsAll(currentReactions)
                                
                                if (changed) {
                                    // Find the post in the full list and update it
                                    val fullListIndex = updatedPosts.indexOfFirst { it.id == post.id }
                                    if (fullListIndex != -1) {
                                        val updatedPost = updatedPosts[fullListIndex].copy(reactions = newReactions)
                                        updatedPosts[fullListIndex] = updatedPost
                                        hasUpdates = true
                                        Log.d(TAG, "Updated reactions for post ${post.id}: ${newReactions.size} reactions")
                                    }
                                }
                            }
                        }
                        
                        connection.disconnect()
                        
                        // Small delay between requests to avoid overwhelming the server
                        Thread.sleep(50)
                        
                    } catch (e: Exception) {
                        // Log but don't fail the whole process
                        Log.d(TAG, "Error checking reactions for post ${post.id}: ${e.message}")
                    }
                }
                
                // Update UI if we found changes
                if (hasUpdates) {
                    handler.post {
                        _posts.postValue(updatedPosts)
                    }
                }
                
            } catch (e: Exception) {
                Log.d(TAG, "Error checking reaction updates: ${e.message}")
            }
        }
    }
    
    private fun extractYoutubeId(text: String?): String? {
        if (text.isNullOrEmpty()) return null
        
        // Match standard YouTube URLs
        val standardPattern = Pattern.compile(
            "https?://(?:www\\.)?youtube\\.com/watch\\?v=([\\w-]+)(?:&\\S*)?",
            Pattern.CASE_INSENSITIVE
        )
        val standardMatcher = standardPattern.matcher(text)
        if (standardMatcher.find()) {
            return standardMatcher.group(1)
        }
        
        // Match YouTube short URLs
        val shortPattern = Pattern.compile(
            "https?://(?:www\\.)?youtu\\.be/([\\w-]+)(?:\\?\\S*)?",
            Pattern.CASE_INSENSITIVE
        )
        val shortMatcher = shortPattern.matcher(text)
        if (shortMatcher.find()) {
            return shortMatcher.group(1)
        }
        
        // Match YouTube mobile app URLs
        val mobilePattern = Pattern.compile(
            "https?://(?:www\\.)?youtube\\.com/embed/([\\w-]+)(?:\\?\\S*)?",
            Pattern.CASE_INSENSITIVE
        )
        val mobileMatcher = mobilePattern.matcher(text)
        if (mobileMatcher.find()) {
            return mobileMatcher.group(1)
        }
        
        return null
    }
    
    fun loadPosts() {
        _isLoading.postValue(true)
        
        thread {
            try {
                val url = URL("$API_URL/community/posts")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Posts response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val jsonPosts = jsonResponse.getJSONArray("posts")
                        val loadedPosts = mutableListOf<Post>()
                        
                        for (i in 0 until jsonPosts.length()) {
                            val jsonPost = jsonPosts.getJSONObject(i)
                            val postId = jsonPost.optString("id", "")
                            val timestamp = parseTimestamp(jsonPost.optString("created_at", ""))
                            val textContent = jsonPost.optString("text_content", "")
                            
                            // Update latest post tracking for polling
                            if (i == 0 || timestamp > latestPostTimestamp) {
                                latestPostTimestamp = timestamp
                                latestPostId = postId
                            }
                            
                            // Extract YouTube ID if present
                            val youtubeId = extractYoutubeId(textContent)
                            
                            // Parse reactions if available
                            val reactionsArray = jsonPost.optJSONArray("reactions")
                            val reactions = mutableListOf<MessageReaction>()
                            
                            if (reactionsArray != null) {
                                for (j in 0 until reactionsArray.length()) {
                                    val jsonReaction = reactionsArray.getJSONObject(j)
                                    reactions.add(
                                        MessageReaction(
                                            id = jsonReaction.optInt("id", 0),
                                            messageId = jsonReaction.optInt("post_id", 0),
                                            userId = jsonReaction.optInt("user_id", 0),
                                            reaction = jsonReaction.optString("reaction", "👍"),
                                            createdAt = jsonReaction.optString("created_at", "")
                                        )
                                    )
                                }
                            }
                            
                            loadedPosts.add(
                                Post(
                                    id = postId,
                                    userId = jsonPost.optInt("user_id", 0),
                                    username = jsonPost.optString("username", "Usuario"),
                                    userAvatar = jsonPost.optString("user_avatar", "").takeIf { it.isNotEmpty() },
                                    timestamp = timestamp,
                                    textContent = textContent,
                                    imageUrl = jsonPost.optString("image_url", "").takeIf { it.isNotEmpty() },
                                    gifUrl = jsonPost.optString("gif_url", "").takeIf { it.isNotEmpty() },
                                    gifPreviewUrl = jsonPost.optString("gif_preview_url", "").takeIf { it.isNotEmpty() },
                                    voiceNoteUrl = jsonPost.optString("voice_note_url", "").takeIf { it.isNotEmpty() },
                                    voiceNoteDuration = jsonPost.optInt("voice_note_duration", 0).takeIf { it > 0 },
                                    commentCount = jsonPost.optInt("comment_count", 0),
                                    reactions = reactions,
                                    youtubeVideoId = youtubeId
                                )
                            )
                        }
                        
                        // Sort posts by timestamp (newest first)
                        loadedPosts.sortByDescending { it.timestamp }
                        
                        _posts.postValue(loadedPosts)
                    } else {
                        val message = jsonResponse.optString("message", "Failed to load posts")
                        _errorMessage.postValue(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error loading posts: $responseCode, Body: $errorBody")
                    _errorMessage.postValue("Error del servidor: $responseCode")
                }
                
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error loading posts", e)
                _errorMessage.postValue("Error de red: ${e.message}")
            } finally {
                _isLoading.postValue(false)
            }
        }
    }
    
    fun createPost(userId: Int, username: String, content: String, imageUrl: String? = null, voiceNoteUrl: String? = null, voiceNoteDuration: Int? = null, gifUrl: String? = null, gifPreviewUrl: String? = null) {
        Log.d(TAG, "createPost called with userId: $userId, username: $username, content: '$content', imageUrl: $imageUrl, voiceNoteUrl: $voiceNoteUrl, voiceNoteDuration: $voiceNoteDuration, gifUrl: $gifUrl, gifPreviewUrl: $gifPreviewUrl")
        _isLoading.postValue(true)
        
        thread {
            try {
                val url = URL("$API_URL/community/posts")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val jsonBody = JSONObject().apply {
                    put("userId", userId)
                    put("textContent", content)
                    imageUrl?.let { if (it.isNotEmpty()) put("image_url", it) }
                    voiceNoteUrl?.let { if (it.isNotEmpty()) put("voice_note_url", it) }
                    voiceNoteDuration?.let { if (it > 0) put("voice_note_duration", it) }
                    
                    // Ensure valid GIF URLs before adding them to the request
                    if (!gifUrl.isNullOrEmpty() && gifUrl != "null") {
                        put("gif_url", gifUrl)
                        Log.d(TAG, "Added gif_url to request: $gifUrl")
                    }
                    
                    if (!gifPreviewUrl.isNullOrEmpty() && gifPreviewUrl != "null") {
                        put("gif_preview_url", gifPreviewUrl)
                        Log.d(TAG, "Added gif_preview_url to request: $gifPreviewUrl")
                    }
                }
                Log.d(TAG, "Creating post with JSON body: ${jsonBody.toString()}")
                
                val outputStream = connection.outputStream
                outputStream.write(jsonBody.toString().toByteArray())
                outputStream.flush()
                outputStream.close()
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Create post response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        // Instead of reloading all posts, get the newly created post from the response
                        val jsonPost = jsonResponse.optJSONObject("post")
                        if (jsonPost != null) {
                            // Extract text content from response
                            val textContent = jsonPost.optString("text_content", "")
                            
                            // Extract YouTube ID if present
                            val youtubeId = extractYoutubeId(textContent)
                            
                            // Create a Post object from the response JSON
                            val newPost = Post(
                                id = jsonPost.optString("id", ""),
                                userId = jsonPost.optInt("user_id", 0),
                                username = jsonPost.optString("username", username),
                                userAvatar = jsonPost.optString("user_avatar", "").takeIf { it.isNotEmpty() },
                                timestamp = parseTimestamp(jsonPost.optString("created_at", "")),
                                textContent = textContent,
                                imageUrl = jsonPost.optString("image_url", "").takeIf { it.isNotEmpty() },
                                gifUrl = jsonPost.optString("gif_url", "").takeIf { it.isNotEmpty() },
                                gifPreviewUrl = jsonPost.optString("gif_preview_url", "").takeIf { it.isNotEmpty() },
                                voiceNoteUrl = jsonPost.optString("voice_note_url", "").takeIf { it.isNotEmpty() },
                                voiceNoteDuration = jsonPost.optInt("voice_note_duration", 0).takeIf { it > 0 },
                                commentCount = 0, // New post has 0 comments
                                reactions = mutableListOf(), // New post has no reactions
                                youtubeVideoId = youtubeId
                            )
                            
                            // Add the new post to the current list and update the LiveData
                            val currentPosts = _posts.value?.toMutableList() ?: mutableListOf()
                            currentPosts.add(0, newPost) // Add at index 0 (top of the list)
                            _posts.postValue(currentPosts)
                            
                            Log.d(TAG, "New post added to the top of the list")
                        } else {
                            // Fallback to reloading all posts if post details not found in response
                            Log.w(TAG, "Post created but details not in response, reloading all posts")
                            loadPosts()
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Failed to create post")
                        _errorMessage.postValue(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error creating post: $responseCode, Body: $errorBody")
                    _errorMessage.postValue("Error del servidor: $responseCode")
                }
                
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error creating post", e)
                _errorMessage.postValue("Error de red: ${e.message}")
            } finally {
                _isLoading.postValue(false)
            }
        }
    }
    
    fun likePost(postId: String, userId: Int) {
        thread {
            try {
                val url = URL("$API_URL/community/posts/$postId/reactions")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.doOutput = true
                
                val jsonBody = JSONObject().apply {
                    put("userId", userId)
                    put("reaction", "👍")
                }
                
                val outputStream = connection.outputStream
                outputStream.write(jsonBody.toString().toByteArray())
                outputStream.flush()
                outputStream.close()
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Like post response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    val isRemoved = jsonResponse.optBoolean("removed", false)
                    val isAdded = jsonResponse.optBoolean("added", false)
                    val isUpdated = jsonResponse.optBoolean("updated", false)
                    
                    // Update the post in our local list
                    val currentPosts = _posts.value?.toMutableList() ?: mutableListOf()
                    val postIndex = currentPosts.indexOfFirst { it.id == postId }
                    
                    if (postIndex != -1) {
                        val post = currentPosts[postIndex]
                        val reactions = post.reactions?.toMutableList() ?: mutableListOf()
                        
                        // Handle reaction update based on response
                        if (isRemoved) {
                            // Remove user's reaction
                            reactions.removeIf { it.userId == userId }
                        } else if (isAdded) {
                            // Add new reaction
                            reactions.add(
                                MessageReaction(
                                    id = System.currentTimeMillis().toInt(), // Temp ID
                                    messageId = postId.toIntOrNull() ?: 0,
                                    userId = userId,
                                    reaction = "👍",
                                    createdAt = "" // Not important for UI display
                                )
                            )
                        } else if (isUpdated) {
                            // Update existing reaction (remove old one, add new one)
                            reactions.removeIf { it.userId == userId }
                            reactions.add(
                                MessageReaction(
                                    id = System.currentTimeMillis().toInt(),
                                    messageId = postId.toIntOrNull() ?: 0,
                                    userId = userId,
                                    reaction = "👍",
                                    createdAt = ""
                                )
                            )
                        }
                        
                        // Create updated post with new reactions
                        val updatedPost = post.copy(reactions = reactions)
                        currentPosts[postIndex] = updatedPost
                        
                        // Update LiveData with updated list (maintaining original order)
                        _posts.postValue(currentPosts)
                    } else {
                        // Fallback: If post not found in our list, reload all posts
                        loadPosts()
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error liking post: $responseCode, Body: $errorBody")
                }
                
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error liking post", e)
            }
        }
    }
    
    fun uploadImage(imageUri: Uri, onSuccess: (String) -> Unit, onError: (String) -> Unit) {
        _isLoading.postValue(true)
        
        thread {
            var connection: HttpURLConnection? = null
            try {
                val url = URL("$API_URL/uploads/community-image") // Still using the separate upload for now
                connection = url.openConnection() as HttpURLConnection
                val boundary = "----${System.currentTimeMillis()}"
                val lineEnd = "\r\n"
                val twoHyphens = "--"
                
                connection.requestMethod = "POST"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                connection.doOutput = true
                connection.useCaches = false
                connection.setRequestProperty("Connection", "Keep-Alive")
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")

                val outputStream: OutputStream = connection.outputStream
                val writer = java.io.DataOutputStream(outputStream) // Use DataOutputStream for binary data

                // File part
                val fileName = "image_${System.currentTimeMillis()}.jpg" // Generate a more unique name or get from URI
                writer.writeBytes(twoHyphens + boundary + lineEnd)
                writer.writeBytes("Content-Disposition: form-data; name=\"image\"; filename=\"${fileName}\"" + lineEnd)
                writer.writeBytes("Content-Type: image/jpeg" + lineEnd) // Assume JPEG, adjust if needed
                writer.writeBytes(lineEnd)

                // Read image data from Uri
                getApplication<Application>().contentResolver.openInputStream(imageUri)?.use { inputStream ->
                    val buffer = ByteArray(4096)
                    var bytesRead: Int
                    while (true) {
                        bytesRead = inputStream.read(buffer)
                        if (bytesRead == -1) break
                        writer.write(buffer, 0, bytesRead)
                    }
                } ?: run {
                    throw java.io.IOException("Could not open InputStream for Uri: $imageUri")
                }
                writer.writeBytes(lineEnd)

                // End of multipart data
                writer.writeBytes(twoHyphens + boundary + twoHyphens + lineEnd)
                writer.flush()
                writer.close()
                outputStream.close()
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Upload image response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val imageUrl = jsonResponse.optString("url", "")
                        if (imageUrl.isNotEmpty()) {
                            onSuccess(imageUrl)
                        } else {
                            onError("Image URL not found in response.")
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Failed to upload image")
                        onError(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error uploading image: $responseCode, Body: $errorBody")
                    onError("Error del servidor (subiendo imagen): $responseCode")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error uploading image", e)
                onError("Error de red (subiendo imagen): ${e.message}")
            } finally {
                connection?.disconnect()
                _isLoading.postValue(false) // Ensure loading state is reset
            }
        }
    }
    
    fun uploadVoiceNote(audioFileUri: Uri, duration: Int, onSuccess: (voiceUrl: String, duration: Int) -> Unit, onError: (String) -> Unit) {
        _isLoading.postValue(true)
        Log.d(TAG, "uploadVoiceNote called with URI: $audioFileUri, duration: $duration")

        thread {
            var connection: HttpURLConnection? = null
            try {
                val url = URL("$API_URL/uploads/community-voice-note")
                connection = url.openConnection() as HttpURLConnection
                val boundary = "----${System.currentTimeMillis()}"
                val lineEnd = "\r\n"
                val twoHyphens = "--"

                connection.requestMethod = "POST"
                connection.connectTimeout = 30000 // Increased timeout for potentially larger files
                connection.readTimeout = 30000
                connection.doOutput = true
                connection.useCaches = false
                connection.setRequestProperty("Connection", "Keep-Alive")
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
                connection.setRequestProperty("Accept", "application/json")


                val outputStream: OutputStream = connection.outputStream
                val writer = java.io.DataOutputStream(outputStream)

                // File part
                val fileName = "voicenote_${System.currentTimeMillis()}.m4a"
                writer.writeBytes(twoHyphens + boundary + lineEnd)
                writer.writeBytes("Content-Disposition: form-data; name=\"audio\"; filename=\"$fileName\"" + lineEnd)
                writer.writeBytes("Content-Type: audio/mp4" + lineEnd) // .m4a is MPEG-4 Part 14 (MP4) container with AAC audio
                writer.writeBytes(lineEnd)

                // Read audio data from Uri
                getApplication<Application>().contentResolver.openInputStream(audioFileUri)?.use { inputStream ->
                    val buffer = ByteArray(4096)
                    var bytesRead: Int
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        writer.write(buffer, 0, bytesRead)
                    }
                } ?: run {
                    Log.e(TAG, "Failed to open input stream for audio URI: $audioFileUri")
                    Handler(Looper.getMainLooper()).post {
                        onError("Error al leer el archivo de audio.")
                    }
                    _isLoading.postValue(false)
                    return@thread
                }

                writer.writeBytes(lineEnd)
                writer.writeBytes(twoHyphens + boundary + twoHyphens + lineEnd)
                writer.flush()
                writer.close()

                val responseCode = connection.responseCode
                Log.d(TAG, "Upload voice note response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Upload voice note success response: $response")
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val voiceUrl = jsonResponse.optString("url")
                        if (voiceUrl.isNotEmpty()) {
                            Handler(Looper.getMainLooper()).post {
                                onSuccess(voiceUrl, duration)
                            }
                        } else {
                            Log.e(TAG, "Voice note URL is empty in response")
                            Handler(Looper.getMainLooper()).post {
                                onError("No se pudo obtener la URL de la nota de voz del servidor.")
                            }
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Error al subir la nota de voz.")
                        Log.e(TAG, "Upload voice note failed: $message")
                        Handler(Looper.getMainLooper()).post {
                            onError(message)
                        }
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error uploading voice note: $responseCode, Body: $errorBody")
                    Handler(Looper.getMainLooper()).post {
                        onError("Error del servidor al subir nota de voz: $responseCode")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error uploading voice note", e)
                Handler(Looper.getMainLooper()).post {
                    onError("Error de red al subir nota de voz: ${e.message}")
                }
            } finally {
                connection?.disconnect()
                // Post to main thread to ensure LiveData is updated correctly
                Handler(Looper.getMainLooper()).post {
                    _isLoading.postValue(false)
                }
            }
        }
    }
    
    fun deletePost(postId: String, userId: Int) {
        Log.d(TAG, "Deleting post with ID: $postId by user: $userId")
        _isLoading.postValue(true)
        
        thread {
            try {
                val url = URL("$API_URL/community/posts/$postId?userId=$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "DELETE"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Delete post response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        // Remove the post from the current list
                        val currentPosts = _posts.value?.toMutableList() ?: mutableListOf()
                        val postIndex = currentPosts.indexOfFirst { it.id == postId }
                        
                        if (postIndex != -1) {
                            currentPosts.removeAt(postIndex)
                            _posts.postValue(currentPosts)
                        }
                        
                        // Show success message
                        _errorMessage.postValue("Publicación eliminada")
                    } else {
                        val message = jsonResponse.optString("message", "Failed to delete post")
                        _errorMessage.postValue(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error deleting post: $responseCode, Body: $errorBody")
                    _errorMessage.postValue("Error del servidor al eliminar: $responseCode")
                }
                
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting post", e)
                _errorMessage.postValue("Error de red: ${e.message}")
            } finally {
                _isLoading.postValue(false)
            }
        }
    }
    
    // Clean up when ViewModel is destroyed
    override fun onCleared() {
        super.onCleared()
        stopPolling()
    }

    // Function to load stories
    fun loadStories(currentUserId: Int) {
        _isLoading.postValue(true) 
        thread {
            try {
                val url = URL("$API_URL/community/stories?userId=$currentUserId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Stories response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val jsonStories = jsonResponse.getJSONArray("stories")
                        val loadedStories = mutableListOf<Story>()
                        
                        if (jsonStories.length() == 0) {
                            Log.d(TAG, "No stories returned from API.")
                        } else {
                            Log.d(TAG, "Received ${jsonStories.length()} stories from API.")
                        }

                        for (i in 0 until jsonStories.length()) {
                            val jsonStory = jsonStories.getJSONObject(i)

                            val textOverlaysJsonArray = jsonStory.optJSONArray("text_overlays")
                            val storyTextOverlays = mutableListOf<TextOverlayData>()
                            if (textOverlaysJsonArray != null) {
                                Log.d(TAG, "Processing text_overlays JSON for story ID ${jsonStory.getString("id")}: ${textOverlaysJsonArray}")
                                for (j in 0 until textOverlaysJsonArray.length()) {
                                    val overlayJson = textOverlaysJsonArray.getJSONObject(j)
                                    Log.d(TAG, "  Overlay $j: ${overlayJson}")
                                    
                                    // Validar que los datos estén presentes
                                    val text = overlayJson.optString("text", "")
                                    val colorHex = overlayJson.optString("colorHex", "#FFFFFF")
                                    val fontResName = overlayJson.optString("fontResourceName", "default")
                                    val isBold = overlayJson.optBoolean("isBold", false)
                                    val isItalic = overlayJson.optBoolean("isItalic", false)
                                    val animName = overlayJson.optString("animationName", "none")
                                    val sizeSp = overlayJson.optDouble("sizeSp", 24.0).toFloat()
                                    val xPercent = overlayJson.optDouble("xPercent", 0.5).toFloat()
                                    val yPercent = overlayJson.optDouble("yPercent", 0.5).toFloat()
                                    
                                    Log.d(TAG, "  Parsed values: text='$text', colorHex='$colorHex', font='$fontResName', " +
                                              "isBold=$isBold, isItalic=$isItalic, anim='$animName', " +
                                              "sizeSp=$sizeSp, x=$xPercent, y=$yPercent")
                                    
                                    storyTextOverlays.add(
                                        TextOverlayData(
                                            text = text,
                                            colorHex = colorHex,
                                            fontResourceName = fontResName,
                                            isBold = isBold,
                                            isItalic = isItalic,
                                            animationName = animName,
                                            sizeSp = sizeSp,
                                            xPercent = xPercent,
                                            yPercent = yPercent
                                        )
                                    )
                                }
                            }

                            loadedStories.add(
                                Story(
                                    id = jsonStory.getString("id"),
                                    userId = jsonStory.getInt("user_id"),
                                    username = jsonStory.getString("username"),
                                    userAvatar = jsonStory.optString("user_avatar").takeIf { it.isNotEmpty() },
                                    mediaUrl = jsonStory.getString("media_url"),
                                    mediaType = jsonStory.getString("media_type"),
                                    timestamp = jsonStory.getLong("created_at"),
                                    expiresAt = jsonStory.getLong("expires_at"),
                                    views = jsonStory.getInt("views"),
                                    seenByCurrentUser = jsonStory.optBoolean("seen_by_current_user", false),
                                    textOverlays = if (storyTextOverlays.isNotEmpty()) storyTextOverlays else null,
                                    musicTitle = jsonStory.optString("music_title").takeIf { it.isNotEmpty() },
                                    musicArtist = jsonStory.optString("music_artist").takeIf { it.isNotEmpty() },
                                    musicPreviewUrl = jsonStory.optString("music_preview_url").takeIf { it.isNotEmpty() }?.let {
                                        if (it.startsWith("/uploads/")) API_URL + it else it
                                    },

                                    musicCoverArt = jsonStory.optString("music_cover_art").takeIf { it.isNotEmpty() },
                                    musicDuration = jsonStory.optInt("music_duration").takeIf { it > 0 }
                                )
                            )
                        }
                        _stories.postValue(loadedStories.filter { it.expiresAt > System.currentTimeMillis() })
                        Log.d(TAG, "Successfully loaded and parsed stories. Active stories: ${loadedStories.filter { it.expiresAt > System.currentTimeMillis() }.size}")
                    } else {
                        val message = jsonResponse.optString("message", "Failed to load stories from API (success: false)")
                        Log.e(TAG, "Error loading stories: $message. Full response: $response")
                        _errorMessage.postValue(message)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error loading stories: $responseCode, Body: $errorBody")
                    _errorMessage.postValue("Error del servidor al cargar historias: $responseCode")
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Exception while loading stories", e)
                _errorMessage.postValue("Error de red al cargar historias: ${e.message}")
            } finally {
                _isLoading.postValue(false)
            }
        }
    }

    fun uploadStoryMedia(mediaUri: Uri, mediaType: String, onSuccess: (String) -> Unit, onError: (String) -> Unit) {
        _isLoading.postValue(true)
        Log.d(TAG, "uploadStoryMedia called with URI: $mediaUri, Type: $mediaType")

        thread {
            var connection: HttpURLConnection? = null
            try {
                val url = URL("$API_URL/uploads/community-story-media") // New endpoint
                connection = url.openConnection() as HttpURLConnection
                val boundary = "----${System.currentTimeMillis()}"
                val lineEnd = "\r\n"
                val twoHyphens = "--"

                connection.requestMethod = "POST"
                connection.connectTimeout = 30000 
                connection.readTimeout = 30000
                connection.doOutput = true
                connection.useCaches = false
                connection.setRequestProperty("Connection", "Keep-Alive")
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=$boundary")
                connection.setRequestProperty("Accept", "application/json")

                val outputStream: OutputStream = connection.outputStream
                val writer = java.io.DataOutputStream(outputStream)

                // Media file part
                val extension = if (mediaType == "image") "jpg" else "mp4" // Basic extension handling
                val fileName = "story_${System.currentTimeMillis()}.$extension"
                writer.writeBytes(twoHyphens + boundary + lineEnd)
                writer.writeBytes("Content-Disposition: form-data; name=\"media\"; filename=\"$fileName\"" + lineEnd)
                val contentType = if (mediaType == "image") "image/jpeg" else "video/mp4"
                writer.writeBytes("Content-Type: $contentType" + lineEnd)
                writer.writeBytes(lineEnd)

                getApplication<Application>().contentResolver.openInputStream(mediaUri)?.use { inputStream ->
                    val buffer = ByteArray(4096)
                    var bytesRead: Int
                    while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                        writer.write(buffer, 0, bytesRead)
                    }
                } ?: run {
                    Log.e(TAG, "Failed to open input stream for story media URI: $mediaUri")
                    Handler(Looper.getMainLooper()).post {
                        onError("Error al leer el archivo multimedia.")
                    }
                    _isLoading.postValue(false)
                    return@thread
                }

                writer.writeBytes(lineEnd)
                
                // MediaType part (as a simple form field)
                writer.writeBytes(twoHyphens + boundary + lineEnd)
                writer.writeBytes("Content-Disposition: form-data; name=\"mediaType\"" + lineEnd)
                writer.writeBytes(lineEnd)
                writer.writeBytes(mediaType + lineEnd)

                writer.writeBytes(twoHyphens + boundary + twoHyphens + lineEnd)
                writer.flush()
                writer.close()

                val responseCode = connection.responseCode
                Log.d(TAG, "Upload story media response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Upload story media success response: $response")
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val mediaUrl = jsonResponse.optString("url")
                        if (mediaUrl.isNotEmpty()) {
                            Handler(Looper.getMainLooper()).post {
                                onSuccess(mediaUrl)
                            }
                        } else {
                            Log.e(TAG, "Media URL is empty in story upload response")
                            Handler(Looper.getMainLooper()).post {
                                onError("No se pudo obtener la URL del servidor.")
                            }
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Error al subir el archivo.")
                        Log.e(TAG, "Upload story media failed: $message")
                        Handler(Looper.getMainLooper()).post {
                            onError(message)
                        }
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error uploading story media: $responseCode, Body: $errorBody")
                    Handler(Looper.getMainLooper()).post {
                        onError("Error del servidor al subir archivo: $responseCode")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error uploading story media", e)
                Handler(Looper.getMainLooper()).post {
                    onError("Error de red al subir archivo: ${e.message}")
                }
            } finally {
                connection?.disconnect()
                Handler(Looper.getMainLooper()).post {
                    _isLoading.postValue(false)
                }
            }
        }
    }

    // Update createStory to use uploadStoryMedia
    fun createStory(userId: Int, username: String, mediaUri: Uri, mediaType: String, textOverlays: List<TextOverlayData>?) {
        Log.d(TAG, "createStory called with userId: $userId, mediaType: $mediaType, mediaUri: $mediaUri, textOverlays: ${textOverlays?.size ?: 0}")
        _isLoading.postValue(true)

        uploadStoryMedia(mediaUri, mediaType,
            onSuccess = { mediaUrl ->
                Log.d(TAG, "Story media uploaded successfully: $mediaUrl. Now creating story entry.")
                thread {
                    try {
                        val url = URL("$API_URL/community/stories")
                        val connection = url.openConnection() as HttpURLConnection
                        connection.requestMethod = "POST"
                        connection.setRequestProperty("Content-Type", "application/json")
                        connection.doOutput = true
                        connection.connectTimeout = 15000
                        connection.readTimeout = 15000

                        val jsonBody = JSONObject().apply {
                            put("userId", userId)
                            put("mediaUrl", mediaUrl)
                            put("mediaType", mediaType)
                            // Backend will set username, userAvatar, timestamp, expiresAt via join or lookup

                            if (textOverlays != null && textOverlays.isNotEmpty()) {
                                val overlaysArray = JSONArray()
                                textOverlays.forEach { overlay ->
                                    val overlayJson = JSONObject().apply {
                                        put("text", overlay.text)
                                        put("colorHex", overlay.colorHex)
                                        put("fontResourceName", overlay.fontResourceName)
                                        put("isBold", overlay.isBold)
                                        put("isItalic", overlay.isItalic)
                                        put("animationName", overlay.animationName)
                                        put("sizeSp", overlay.sizeSp)
                                        put("xPercent", overlay.xPercent)
                                        put("yPercent", overlay.yPercent)
                                    }
                                    overlaysArray.put(overlayJson)
                                }
                                put("text_overlays", overlaysArray.toString()) // Send as JSON string
                                Log.d(TAG, "Adding text_overlays: ${overlaysArray.toString()}")
                            } else {
                                Log.d(TAG, "No text overlays to add.")
                            }
                        }
                        Log.d(TAG, "Creating story with JSON body: ${jsonBody.toString()}")

                        val outputStream = connection.outputStream
                        outputStream.write(jsonBody.toString().toByteArray())
                        outputStream.flush()
                        outputStream.close()

                        val responseCode = connection.responseCode
                        if (responseCode == HttpURLConnection.HTTP_CREATED || responseCode == HttpURLConnection.HTTP_OK) {
                            val response = connection.inputStream.bufferedReader().readText()
                            Log.d(TAG, "Create story entry response: $response")
                            val jsonResponse = JSONObject(response)
                            if (jsonResponse.optBoolean("success", false)) {
                                Log.d(TAG, "Story entry created successfully. Refreshing stories.")
                                loadStories(userId) // Refresh stories list, pass current user ID
                                _storyUploadSuccess.postValue(true)
                            } else {
                                val message = jsonResponse.optString("message", "Failed to create story entry")
                                Log.e(TAG, "Error creating story entry: $message")
                                _errorMessage.postValue(message)
                            }
                        } else {
                            val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                            Log.e(TAG, "HTTP Error creating story entry: $responseCode, Body: $errorBody")
                            _errorMessage.postValue("Error del servidor al crear historia: $responseCode")
                        }
                        connection.disconnect()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error creating story entry", e)
                        _errorMessage.postValue("Error de red al crear historia: ${e.message}")
                    } finally {
                        _isLoading.postValue(false)
                    }
                }
            },
            onError = { errorMessage ->
                Log.e(TAG, "Failed to upload story media: $errorMessage")
                _errorMessage.postValue("Error al subir multimedia para historia: $errorMessage")
                _isLoading.postValue(false)
                _storyUploadSuccess.postValue(false)
            }
        )
    }

    // Placeholder for marking a story as seen
    fun markStoryAsSeen(storyId: String, userId: Int /* Current user ID */) {
        Log.d(TAG, "markStoryAsSeen called for storyId: $storyId by userId: $userId")
        thread {
            try {
                val url = URL("$API_URL/community/stories/$storyId/seen")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val jsonBody = JSONObject().apply {
                    put("userId", userId)
                }

                val outputStream = connection.outputStream
                outputStream.write(jsonBody.toString().toByteArray())
                outputStream.flush()
                outputStream.close()

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Log.d(TAG, "Successfully marked story $storyId as seen by user $userId")
                    // Update local state - find the story and mark it as seen
                    val currentStories = _stories.value?.toMutableList() ?: mutableListOf()
                    currentStories.find { it.id == storyId }?.let { story ->
                        story.seenByCurrentUser = true
                        _stories.postValue(currentStories)
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error marking story as seen: $responseCode, Body: $errorBody")
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error marking story as seen", e)
            }
        }
    }

    // Helper method to format URLs (images, avatars, media, etc.)
    fun formatUrl(path: String?): String? {
        return when {
            path == null -> null
            path.isEmpty() -> null
            path == "null" -> null
            path.startsWith("http") -> path // Already a complete URL
            else -> "$API_URL$path" // Prepend API_URL to relative paths
        }
    }

    // Function to load stories for a specific user
    fun loadStoriesForUser(targetUserId: Int, currentUserId: Int, callback: (List<Story>) -> Unit) {
        _isLoading.postValue(true) 
        thread {
            try {
                val url = URL("$API_URL/community/stories/user/$targetUserId?userId=$currentUserId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "User stories response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val jsonStories = jsonResponse.getJSONArray("stories")
                        val loadedStories = mutableListOf<Story>()
                        
                        if (jsonStories.length() == 0) {
                            Log.d(TAG, "No stories returned for user $targetUserId")
                            // Return empty list through callback
                            Handler(Looper.getMainLooper()).post {
                                callback(emptyList())
                            }
                        } else {
                            Log.d(TAG, "Received ${jsonStories.length()} stories for user $targetUserId")
                        }

                        for (i in 0 until jsonStories.length()) {
                            val jsonStory = jsonStories.getJSONObject(i)

                            val textOverlaysJsonArray = jsonStory.optJSONArray("text_overlays")
                            val storyTextOverlays = mutableListOf<TextOverlayData>()
                            if (textOverlaysJsonArray != null) {
                                Log.d(TAG, "loadStoriesForUser: Processing text_overlays JSON for story ID ${jsonStory.getString("id")}: ${textOverlaysJsonArray}")
                                for (j in 0 until textOverlaysJsonArray.length()) {
                                    val overlayJson = textOverlaysJsonArray.getJSONObject(j)
                                    Log.d(TAG, "  Overlay $j: ${overlayJson}")
                                    
                                    // Validar que los datos estén presentes
                                    val text = overlayJson.optString("text", "")
                                    val colorHex = overlayJson.optString("colorHex", "#FFFFFF")
                                    val fontResName = overlayJson.optString("fontResourceName", "default")
                                    val isBold = overlayJson.optBoolean("isBold", false)
                                    val isItalic = overlayJson.optBoolean("isItalic", false)
                                    val animName = overlayJson.optString("animationName", "none")
                                    val sizeSp = overlayJson.optDouble("sizeSp", 24.0).toFloat()
                                    val xPercent = overlayJson.optDouble("xPercent", 0.5).toFloat()
                                    val yPercent = overlayJson.optDouble("yPercent", 0.5).toFloat()
                                    
                                    Log.d(TAG, "  Parsed values: text='$text', colorHex='$colorHex', font='$fontResName', " +
                                              "isBold=$isBold, isItalic=$isItalic, anim='$animName', " +
                                              "sizeSp=$sizeSp, x=$xPercent, y=$yPercent")
                                    
                                    storyTextOverlays.add(
                                        TextOverlayData(
                                            text = text,
                                            colorHex = colorHex,
                                            fontResourceName = fontResName,
                                            isBold = isBold,
                                            isItalic = isItalic,
                                            animationName = animName,
                                            sizeSp = sizeSp,
                                            xPercent = xPercent,
                                            yPercent = yPercent
                                        )
                                    )
                                }
                            }

                            loadedStories.add(
                                Story(
                                    id = jsonStory.getString("id"),
                                    userId = jsonStory.getInt("user_id"),
                                    username = jsonStory.getString("username"),
                                    userAvatar = jsonStory.optString("user_avatar").takeIf { it.isNotEmpty() },
                                    mediaUrl = jsonStory.getString("media_url"),
                                    mediaType = jsonStory.getString("media_type"),
                                    timestamp = jsonStory.getLong("created_at"),
                                    expiresAt = jsonStory.getLong("expires_at"),
                                    views = jsonStory.getInt("views"),
                                    seenByCurrentUser = jsonStory.optBoolean("seen_by_current_user", false),
                                    textOverlays = if (storyTextOverlays.isNotEmpty()) storyTextOverlays else null,
                                    musicTitle = jsonStory.optString("music_title").takeIf { it.isNotEmpty() },
                                    musicArtist = jsonStory.optString("music_artist").takeIf { it.isNotEmpty() },
                                    musicPreviewUrl = jsonStory.optString("music_preview_url").takeIf { it.isNotEmpty() }?.let {
                                        if (it.startsWith("/uploads/")) API_URL + it else it
                                    },

                                    musicCoverArt = jsonStory.optString("music_cover_art").takeIf { it.isNotEmpty() },
                                    musicDuration = jsonStory.optInt("music_duration").takeIf { it > 0 }
                                )
                            )
                        }
                        val activeStories = loadedStories.filter { it.expiresAt > System.currentTimeMillis() }
                        Handler(Looper.getMainLooper()).post {
                            callback(activeStories)
                        }
                        Log.d(TAG, "Successfully loaded and parsed stories for user $targetUserId. Active stories: ${activeStories.size}")
                    } else {
                        val message = jsonResponse.optString("message", "Failed to load stories for user from API")
                        Log.e(TAG, "Error loading stories for user $targetUserId: $message")
                        Handler(Looper.getMainLooper()).post {
                            callback(emptyList())
                        }
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error loading stories for user $targetUserId: $responseCode, Body: $errorBody")
                    Handler(Looper.getMainLooper()).post {
                        callback(emptyList())
                    }
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Exception while loading stories for user $targetUserId", e)
                Handler(Looper.getMainLooper()).post {
                    callback(emptyList())
                }
            } finally {
                _isLoading.postValue(false)
            }
        }
    }

    // Add this new method to fetch story viewers
    fun getStoryViewers(storyId: String, callback: (List<StoryViewer>) -> Unit) {
        _isLoading.postValue(true)
        
        thread {
            try {
                val url = URL("$API_URL/community/stories/$storyId/viewers")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Story viewers response: $response")
                    
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val jsonViewers = jsonResponse.getJSONArray("viewers")
                        val loadedViewers = mutableListOf<StoryViewer>()
                        
                        for (i in 0 until jsonViewers.length()) {
                            val jsonViewer = jsonViewers.getJSONObject(i)
                            loadedViewers.add(
                                StoryViewer(
                                    id = jsonViewer.optInt("user_id", 0),
                                    username = jsonViewer.optString("username", "Usuario"),
                                    avatarUrl = jsonViewer.optString("avatar_url", "").takeIf { it.isNotEmpty() },
                                    viewedAt = jsonViewer.optLong("viewed_at", System.currentTimeMillis()),
                                    order = i + 1 // Assign order based on array position
                                )
                            )
                        }
                        
                        // Sort viewers by time (most recent first)
                        loadedViewers.sortByDescending { it.viewedAt }
                        
                        Handler(Looper.getMainLooper()).post {
                            callback(loadedViewers)
                        }
                    } else {
                        val message = jsonResponse.optString("message", "Failed to load story viewers")
                        Log.e(TAG, "Error loading story viewers: $message")
                        Handler(Looper.getMainLooper()).post {
                            callback(emptyList())
                        }
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error loading story viewers: $responseCode, Body: $errorBody")
                    Handler(Looper.getMainLooper()).post {
                        callback(emptyList())
                    }
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Exception while loading story viewers", e)
                Handler(Looper.getMainLooper()).post {
                    callback(emptyList())
                }
            } finally {
                _isLoading.postValue(false)
            }
        }
    }

    fun clearErrorMessage() {
        _errorMessage.postValue(null)
    }

    fun clearStoryUploadSuccess() {
        _storyUploadSuccess.postValue(false) // Reset to false
    }

    fun deleteStory(storyId: String, userId: Int) {
        _isLoading.postValue(true)
        thread {
            try {
                val url = URL("$API_URL/community/stories/$storyId?userId=$userId") // Pass userId as query param
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "DELETE"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Delete story response: $response")
                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        // Remove the story from the local list
                        val currentStories = _stories.value?.toMutableList() ?: mutableListOf()
                        val storyIndex = currentStories.indexOfFirst { it.id == storyId }
                        if (storyIndex != -1) {
                            currentStories.removeAt(storyIndex)
                            _stories.postValue(currentStories)
                        }
                        // Potentially post a success message to another LiveData if needed for UI feedback beyond list update
                        _errorMessage.postValue("Historia eliminada correctamente") // Using errorMessage for now for simplicity
                    } else {
                        _errorMessage.postValue(jsonResponse.optString("message", "Error al eliminar la historia"))
                    }
                } else {
                    val errorBody = connection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error deleting story: $responseCode, Body: $errorBody")
                    _errorMessage.postValue("Error del servidor al eliminar: $responseCode")
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting story", e)
                _errorMessage.postValue("Error de red al eliminar: ${e.message}")
            } finally {
                _isLoading.postValue(false)
            }
        }
    }

    private fun parseTimestamp(dateString: String): Long {
        if (dateString.isNullOrEmpty()) return 0L
        return try {
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
            sdf.timeZone = TimeZone.getTimeZone("UTC")
            sdf.parse(dateString)?.time ?: 0L
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing timestamp: $dateString", e)
            0L
        }
    }
}