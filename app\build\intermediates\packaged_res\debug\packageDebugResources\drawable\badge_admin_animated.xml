<?xml version="1.0" encoding="utf-8"?>
<animated-selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Estado normal -->
    <item android:id="@+id/normal" android:state_pressed="false">
        <layer-list>
            <!-- Capa de brillo exterior -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#00FFD700"
                        android:centerColor="#80FFD700"
                        android:endColor="#00FFD700"
                        android:type="radial"
                        android:gradientRadius="100dp" />
                    <corners android:radius="16dp" />
                    <padding
                        android:left="2dp"
                        android:top="2dp"
                        android:right="2dp"
                        android:bottom="2dp" />
                </shape>
            </item>
            
            <!-- Capa principal -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#FFD700"
                        android:centerColor="#FFA500"
                        android:endColor="#FF8C00"
                        android:angle="45" />
                    <corners android:radius="14dp" />
                    <stroke
                        android:width="2dp"
                        android:color="#FFFFFF" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- Estado animado -->
    <item android:id="@+id/animated">
        <animation-list android:oneshot="false">
            <item android:duration="300">
                <layer-list>
                    <!-- Capa de brillo exterior expandido -->
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:startColor="#40FFD700"
                                android:centerColor="#AAFFD700"
                                android:endColor="#40FFD700"
                                android:type="radial"
                                android:gradientRadius="120dp" />
                            <corners android:radius="16dp" />
                            <padding
                                android:left="3dp"
                                android:top="3dp"
                                android:right="3dp"
                                android:bottom="3dp" />
                        </shape>
                    </item>
                    
                    <!-- Capa principal brillante -->
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:startColor="#FFEB3B"
                                android:centerColor="#FFD700"
                                android:endColor="#FFB347"
                                android:angle="90" />
                            <corners android:radius="14dp" />
                            <stroke
                                android:width="2dp"
                                android:color="#FFFFFF" />
                        </shape>
                    </item>
                </layer-list>
            </item>
            
            <item android:duration="300">
                <layer-list>
                    <!-- Capa de brillo exterior -->
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:startColor="#00FFD700"
                                android:centerColor="#80FFD700"
                                android:endColor="#00FFD700"
                                android:type="radial"
                                android:gradientRadius="100dp" />
                            <corners android:radius="16dp" />
                            <padding
                                android:left="2dp"
                                android:top="2dp"
                                android:right="2dp"
                                android:bottom="2dp" />
                        </shape>
                    </item>
                    
                    <!-- Capa principal -->
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:startColor="#FFD700"
                                android:centerColor="#FFA500"
                                android:endColor="#FF8C00"
                                android:angle="45" />
                            <corners android:radius="14dp" />
                            <stroke
                                android:width="2dp"
                                android:color="#FFFFFF" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </animation-list>
    </item>

    <!-- Transición de normal a animado -->
    <transition android:fromId="@id/normal" android:toId="@id/animated">
        <animation-list>
            <item android:duration="300">
                <layer-list>
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:startColor="#20FFD700"
                                android:centerColor="#90FFD700"
                                android:endColor="#20FFD700"
                                android:type="radial"
                                android:gradientRadius="110dp" />
                            <corners android:radius="16dp" />
                            <padding
                                android:left="2.5dp"
                                android:top="2.5dp"
                                android:right="2.5dp"
                                android:bottom="2.5dp" />
                        </shape>
                    </item>
                    
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:startColor="#FFE14D"
                                android:centerColor="#FFBF00"
                                android:endColor="#FF9E18"
                                android:angle="67.5" />
                            <corners android:radius="14dp" />
                            <stroke
                                android:width="2dp"
                                android:color="#FFFFFF" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </animation-list>
    </transition>
</animated-selector>
