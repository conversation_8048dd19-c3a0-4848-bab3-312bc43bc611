#!/bin/bash

# Install JWT and security dependencies for VMeet backend
echo "Installing JWT and security dependencies..."

# JWT dependencies
npm install jsonwebtoken
npm install express-rate-limit
npm install helmet

# Additional security dependencies
npm install bcryptjs
npm install validator
npm install express-validator

echo "Dependencies installed successfully!"
echo "Please run the following SQL script to create the necessary tables:"
echo "mysql -u vmeet -p vmeet < database/jwt_schema.sql"
