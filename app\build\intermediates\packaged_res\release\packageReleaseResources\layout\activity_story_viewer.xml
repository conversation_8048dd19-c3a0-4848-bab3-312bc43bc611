<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/storyContainer"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    android:fitsSystemWindows="true"
    tools:context=".ui.community.StoryViewerActivity">

    <!-- Story Image (for image stories) -->
    <ImageView
        android:id="@+id/storyImage"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:visibility="gone"
        tools:visibility="visible"
        tools:src="@tools:sample/backgrounds/scenic"
        android:contentDescription="Story Image" />

    <!-- Video View (for video stories) -->
    <VideoView
        android:id="@+id/videoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="visible" />

    <!-- Container for Text Overlays -->
    <FrameLayout
        android:id="@+id/textOverlayContainer"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Left Tap Area (previous story) -->
    <View
        android:id="@+id/leftTapArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rightTapArea"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="1" />

    <!-- Right Tap Area (next story) -->
    <View
        android:id="@+id/rightTapArea"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toEndOf="@+id/leftTapArea"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_weight="2" />

    <!-- Loading Progress -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Top Bar with User Info -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:background="#33000000"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Progress Indicator Container -->
        <LinearLayout
            android:id="@+id/progressIndicatorContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="2dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- User Avatar -->
        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/userAvatar"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/progressIndicatorContainer"
            app:layout_constraintStart_toStartOf="parent"
            tools:src="@tools:sample/avatars"
            android:contentDescription="User Avatar" />

        <!-- Username -->
        <TextView
            android:id="@+id/usernameText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintTop_toBottomOf="@id/progressIndicatorContainer"
            app:layout_constraintStart_toEndOf="@id/userAvatar"
            app:layout_constraintBottom_toBottomOf="@id/userAvatar"
            tools:text="Username" />

        <!-- Story Timestamp (Time Ago) -->
        <TextView
            android:id="@+id/storyTimestampText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@id/usernameText"
            app:layout_constraintStart_toStartOf="@id/usernameText"
            tools:text="2 hours ago" />

        <!-- Delete Story Button (visible only to owner) -->
        <ImageView
            android:id="@+id/deleteStoryButton"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@android:drawable/ic_menu_delete"
            android:tint="@android:color/white"
            android:layout_marginEnd="8dp"
            app:layout_constraintTop_toTopOf="@id/closeButton"
            app:layout_constraintBottom_toBottomOf="@id/closeButton"
            app:layout_constraintEnd_toStartOf="@id/closeButton"
            android:contentDescription="Delete Story"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- Close Button -->
        <ImageView
            android:id="@+id/closeButton"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@android:color/white"
            app:layout_constraintTop_toBottomOf="@id/progressIndicatorContainer"
            app:layout_constraintEnd_toEndOf="parent"
            android:contentDescription="Close" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Music Info Container -->
    <LinearLayout
        android:id="@+id/musicInfoContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#66000000"
        android:padding="12dp"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="72dp">

        <!-- Music Cover Art -->
        <ImageView
            android:id="@+id/musicCoverArt"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:scaleType="centerCrop"
            tools:src="@tools:sample/backgrounds/scenic"
            android:contentDescription="Music Cover Art" />

        <!-- Music Info (Title and Artist) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="12dp">

            <!-- Music Title -->
            <TextView
                android:id="@+id/musicTitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Song Title" />

            <!-- Music Artist -->
            <TextView
                android:id="@+id/musicArtistText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="Artist Name" />
        </LinearLayout>

        <!-- Music Icon -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_media_play"
            android:tint="@android:color/white"
            android:contentDescription="Music Playing" />
    </LinearLayout>

    <!-- Story Views Icon and Count -->
    <LinearLayout
        android:id="@+id/storyViewsContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/rounded_dark_background"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6dp"
        android:layout_marginBottom="24dp"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageView
            android:id="@+id/viewIcon"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_eye"
            android:tint="@android:color/white"
            android:contentDescription="Story views" />

        <TextView
            android:id="@+id/viewCountText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            tools:text="5" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>