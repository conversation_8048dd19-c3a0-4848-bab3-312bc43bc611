-- VMeet User Verification System - Add Document Type Column
-- Execute this script to add document_type support to existing verification system

-- Add document_type column to existing user_verifications table
ALTER TABLE user_verifications 
ADD COLUMN document_type ENUM('dni', 'cedula', 'passport', 'driving_license', 'residence_card', 'voter_id') 
DEFAULT 'dni' 
AFTER user_id;

-- Update existing records to have 'dni' as document type (for backward compatibility)
UPDATE user_verifications 
SET document_type = 'dni' 
WHERE document_type IS NULL;

-- Verify the changes
SELECT 
    id,
    user_id,
    document_type,
    status,
    submitted_at
FROM user_verifications 
ORDER BY submitted_at DESC 
LIMIT 10;

-- Show table structure to confirm changes
DESCRIBE user_verifications;
