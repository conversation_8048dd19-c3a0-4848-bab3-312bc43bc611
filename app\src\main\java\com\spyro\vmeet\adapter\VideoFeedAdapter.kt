package com.spyro.vmeet.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.ui.PlayerView
import com.spyro.vmeet.R
import com.spyro.vmeet.VideoFeedActivity
import com.spyro.vmeet.dialog.CommentsBottomSheetDialog
import com.spyro.vmeet.model.VideoPost
import java.util.concurrent.ConcurrentHashMap
import okhttp3.*
import org.json.JSONObject
import java.io.IOException
import android.util.Log

class VideoFeedAdapter(
    private val context: Context,
    private val userId: Int
) : RecyclerView.Adapter<VideoFeedAdapter.VideoViewHolder>() {

    private val videos = mutableListOf<VideoPost>()
    private val players = ConcurrentHashMap<Int, ExoPlayer>()
    private var currentPlayingPosition = -1

    // Enhanced cache for complete user info
    data class UserInfo(val username: String, val avatarUrl: String?)
    private val userInfoCache = ConcurrentHashMap<Int, UserInfo>()

    private val API_URL = "http://*************:3000"

    inner class VideoViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val playerView: PlayerView = view.findViewById(R.id.playerView)
        val textUsername: TextView = view.findViewById(R.id.textUsername)
        val textDescription: TextView = view.findViewById(R.id.textDescription)
        val textLikes: TextView = view.findViewById(R.id.textLikes)
        val textComments: TextView = view.findViewById(R.id.textComments)
        val textViews: TextView = view.findViewById(R.id.textViews)
        val imageLike: ImageView = view.findViewById(R.id.imageLike)
        val imageComment: ImageView = view.findViewById(R.id.imageComment)
        val imageUserAvatar: de.hdodenhof.circleimageview.CircleImageView = view.findViewById(R.id.imageUserAvatar)

        init {
            // Set up click listeners
            imageLike.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    handleLikeClick(position)
                }
            }

            imageComment.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    handleCommentClick(position)
                }
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VideoViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_video, parent, false)
        return VideoViewHolder(view)
    }

    override fun onBindViewHolder(holder: VideoViewHolder, position: Int) {
        onBindViewHolder(holder, position, mutableListOf())
    }

    override fun onBindViewHolder(holder: VideoViewHolder, position: Int, payloads: MutableList<Any>) {
        val video = videos[position]

        // Handle partial updates
        if (payloads.isNotEmpty()) {
            for (payload in payloads) {
                when (payload) {
                    "likes_only" -> {
                        holder.textLikes.text = formatCount(video.likes)
                        return
                    }
                    "comments_only" -> {
                        holder.textComments.text = formatCount(video.comments)
                        return
                    }
                    "user_info_only" -> {
                        // Only update user info without affecting video playback
                        loadUserInfo(video.userId, holder.textUsername, holder.imageUserAvatar)
                        return
                    }
                }
            }
            return
        }

        // Full bind - only when creating new view or no payloads
        // Set up ExoPlayer only if not already set
        if (players[position] == null) {
            val player = ExoPlayer.Builder(context).build()
            player.repeatMode = Player.REPEAT_MODE_ONE
            player.setMediaItem(MediaItem.fromUri(video.videoUrl))
            player.prepare()

            // Record view when video starts playing
            player.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    if (playbackState == Player.STATE_READY && player.isPlaying) {
                        recordVideoView(video.id)
                    }
                }
            })

            holder.playerView.player = player
            players[position] = player
        } else {
            // Reuse existing player
            holder.playerView.player = players[position]
        }

        // Set video metadata
        loadUserInfo(video.userId, holder.textUsername, holder.imageUserAvatar)
        holder.textDescription.text = video.description
        holder.textLikes.text = formatCount(video.likes)
        holder.textComments.text = formatCount(video.comments)
        holder.textViews.text = formatCount(video.views)

        // Set up click listeners for profile navigation
        holder.imageUserAvatar.setOnClickListener {
            openUserProfile(video.userId)
        }

        holder.textUsername.setOnClickListener {
            openUserProfile(video.userId)
        }

        // Start playing if this is the current position
        if (position == currentPlayingPosition) {
            players[position]?.playWhenReady = true
        }
    }

    override fun getItemCount() = videos.size

    fun updateVideos(newVideos: List<VideoPost>) {
        videos.clear()
        videos.addAll(newVideos)
        // Set the first video as current playing position when loading videos
        if (newVideos.isNotEmpty() && currentPlayingPosition == -1) {
            currentPlayingPosition = 0
        }
        notifyDataSetChanged()
    }

    fun pauseAllVideos() {
        players.values.forEach { it.pause() }
    }

    fun pauseAllVideosExcept(position: Int) {
        currentPlayingPosition = position
        players.forEach { (pos, player) ->
            if (pos != position) {
                player.pause()
            } else {
                player.play()
            }
        }
    }

    fun resumeCurrentVideo() {
        if (currentPlayingPosition != -1 && currentPlayingPosition < videos.size) {
            val player = players[currentPlayingPosition]
            if (player != null) {
                player.play()
                Log.d("VideoFeedAdapter", "Resumed video at position $currentPlayingPosition")
            } else {
                Log.d("VideoFeedAdapter", "No player found for position $currentPlayingPosition, will recreate")
            }
        }
    }

    private fun handleLikeClick(position: Int) {
        if (position >= 0 && position < videos.size) {
            val video = videos[position]
            Log.d("VideoFeedAdapter", "Like clicked for video ${video.id}")

            // Make API call to like/unlike video
            val client = OkHttpClient()
            val requestBody = okhttp3.FormBody.Builder()
                .add("userId", "1") // TODO: Get real user ID
                .build()

            val request = Request.Builder()
                .url("$API_URL/videos/${video.id}/like")
                .post(requestBody)
                .build()

            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e("VideoFeedAdapter", "Error liking video", e)
                }

                override fun onResponse(call: Call, response: Response) {
                    try {
                        val responseBody = response.body?.string()
                        if (response.isSuccessful && responseBody != null) {
                            val jsonObject = JSONObject(responseBody)
                            val liked = jsonObject.getBoolean("liked")
                            val likeCount = jsonObject.getInt("likeCount")

                            // Update video data
                            video.likes = likeCount

                            // Update UI on main thread without recreating the view
                            (context as android.app.Activity).runOnUiThread {
                                // Update only the specific item without recreating the entire view
                                notifyItemChanged(position, "likes_only")
                                Log.d("VideoFeedAdapter", "Video ${video.id} ${if (liked) "liked" else "unliked"}, new count: $likeCount")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("VideoFeedAdapter", "Error parsing like response", e)
                    }
                }
            })
        }
    }

    private fun handleCommentClick(position: Int) {
        if (position >= 0 && position < videos.size) {
            val video = videos[position]
            Log.d("VideoFeedAdapter", "Comment clicked for video ${video.id}")

            // Open comments bottom sheet dialog
            val activity = context as VideoFeedActivity
            val commentsDialog = CommentsBottomSheetDialog.newInstance(video.id, video.comments)
            commentsDialog.setOnCommentAddedListener { newCommentCount ->
                // Update the video comment count
                video.comments = newCommentCount
                // Update UI without recreating the view
                activity.runOnUiThread {
                    notifyItemChanged(position, "comments_only")
                }
            }
            commentsDialog.show(activity.supportFragmentManager, "CommentsDialog")
        }
    }

    private fun recordVideoView(videoId: Int) {
        Log.d("VideoFeedAdapter", "Recording view for video $videoId")

        val client = OkHttpClient()
        val requestBody = okhttp3.FormBody.Builder()
            .add("userId", "1") // TODO: Get real user ID
            .build()

        val request = Request.Builder()
            .url("$API_URL/videos/$videoId/view")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("VideoFeedAdapter", "Error recording view", e)
            }

            override fun onResponse(call: Call, response: Response) {
                if (response.isSuccessful) {
                    Log.d("VideoFeedAdapter", "View recorded for video $videoId")
                } else {
                    Log.e("VideoFeedAdapter", "Failed to record view: ${response.code}")
                }
            }
        })
    }

    private fun loadUserInfo(userId: Int, usernameTextView: TextView, avatarImageView: de.hdodenhof.circleimageview.CircleImageView) {
        Log.d("VideoFeedAdapter", "Loading user info for userId: $userId")

        // Check cache first
        userInfoCache[userId]?.let { cachedUserInfo ->
            Log.d("VideoFeedAdapter", "Found cached info for userId $userId: ${cachedUserInfo.username}, avatar: ${cachedUserInfo.avatarUrl}")
            usernameTextView.text = "@${cachedUserInfo.username}"
            // Load cached avatar if available
            if (!cachedUserInfo.avatarUrl.isNullOrEmpty()) {
                loadAvatarImage(cachedUserInfo.avatarUrl, avatarImageView)
            } else {
                // Only set default if no avatar URL in cache
                avatarImageView.setImageResource(R.drawable.default_avatar)
            }
            return
        }

        // Set default username while loading from API, but delay setting default avatar
        usernameTextView.text = "@usuario"
        Log.d("VideoFeedAdapter", "No cache found for userId $userId, loading from API...")

        // Load from API
        val client = OkHttpClient()
        val request = Request.Builder()
            .url("$API_URL/profile/profile/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("VideoFeedAdapter", "Error loading user info for userId $userId", e)
                // Set default avatar on failure
                (context as android.app.Activity).runOnUiThread {
                    avatarImageView.setImageResource(R.drawable.default_avatar)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    Log.d("VideoFeedAdapter", "API response for userId $userId: $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonObject = JSONObject(responseBody)
                        val userObject = jsonObject.optJSONObject("user")
                        val username = userObject?.optString("username", "usuario") ?: "usuario"
                        val avatarUrl = userObject?.optString("avatar_url", null)

                        Log.d("VideoFeedAdapter", "Parsed user info - username: $username, avatarUrl: $avatarUrl")

                        // Cache the complete user info
                        userInfoCache[userId] = UserInfo(username, avatarUrl)

                        // Update UI on main thread
                        (context as android.app.Activity).runOnUiThread {
                            usernameTextView.text = "@$username"

                            // Load avatar if available, otherwise set default
                            if (!avatarUrl.isNullOrEmpty()) {
                                Log.d("VideoFeedAdapter", "Loading avatar image for userId $userId: $avatarUrl")
                                loadAvatarImage(avatarUrl, avatarImageView)
                            } else {
                                Log.d("VideoFeedAdapter", "No avatar URL for userId $userId, setting default")
                                avatarImageView.setImageResource(R.drawable.default_avatar)
                            }
                        }
                    } else {
                        Log.e("VideoFeedAdapter", "API error for userId $userId: ${response.code}")
                        (context as android.app.Activity).runOnUiThread {
                            avatarImageView.setImageResource(R.drawable.default_avatar)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("VideoFeedAdapter", "Error parsing user info for userId $userId", e)
                    (context as android.app.Activity).runOnUiThread {
                        avatarImageView.setImageResource(R.drawable.default_avatar)
                    }
                }
            }
        })
    }

    private fun loadAvatarImage(avatarUrl: String, imageView: de.hdodenhof.circleimageview.CircleImageView) {
        // Use Glide to load the avatar image with enhanced caching and smooth transitions
        try {
            val fullUrl = if (avatarUrl.startsWith("http")) {
                avatarUrl
            } else {
                "$API_URL$avatarUrl"
            }

            Log.d("VideoFeedAdapter", "Loading avatar image with Glide: $fullUrl")

            com.bumptech.glide.Glide.with(context)
                .load(fullUrl)
                .placeholder(R.drawable.default_avatar)
                .error(R.drawable.default_avatar)
                .fallback(R.drawable.default_avatar)
                .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.ALL)
                .skipMemoryCache(false) // Enable memory cache for faster loading
                .transition(com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions.withCrossFade(200))
                .into(imageView)

            Log.d("VideoFeedAdapter", "Glide request initiated for avatar: $fullUrl")
        } catch (e: Exception) {
            Log.e("VideoFeedAdapter", "Error loading avatar image", e)
            imageView.setImageResource(R.drawable.default_avatar)
        }
    }

    private fun formatCount(count: Int): String {
        return when {
            count < 1000 -> count.toString()
            count < 1000000 -> String.format("%.1fK", count / 1000.0)
            else -> String.format("%.1fM", count / 1000000.0)
        }
    }

    override fun onViewRecycled(holder: VideoViewHolder) {
        super.onViewRecycled(holder)
        val position = holder.adapterPosition

        // Clear Glide requests to prevent loading into recycled views
        try {
            com.bumptech.glide.Glide.with(context).clear(holder.imageUserAvatar)
        } catch (e: Exception) {
            Log.e("VideoFeedAdapter", "Error clearing Glide request", e)
        }

        // Release video player
        players[position]?.let { player ->
            player.stop()
            player.release()
            players.remove(position)
        }
    }



    fun stopAllVideos() {
        players.values.forEach { player ->
            player.stop()
        }
    }

    fun releaseAllPlayers() {
        players.values.forEach { player ->
            player.release()
        }
        players.clear()
        currentPlayingPosition = -1
    }

    fun clearUserInfoCache() {
        userInfoCache.clear()
    }

    fun getUserInfoCacheSize(): Int {
        return userInfoCache.size
    }

    fun startFirstVideo() {
        if (videos.isNotEmpty() && currentPlayingPosition == 0) {
            players[0]?.playWhenReady = true
        }
    }

    fun refreshFirstVideoUserInfo() {
        if (videos.isNotEmpty()) {
            Log.d("VideoFeedAdapter", "Refreshing first video user info")
            // Use a specific payload to only update user info, not the entire view
            notifyItemChanged(0, "user_info_only")
        }
    }

    fun recreatePlayersIfNeeded() {
        // If we have videos but no players, recreate them
        if (videos.isNotEmpty() && players.isEmpty()) {
            Log.d("VideoFeedAdapter", "Recreating players after returning from another activity")
            // Recreate the player for the current position
            if (currentPlayingPosition >= 0 && currentPlayingPosition < videos.size) {
                val video = videos[currentPlayingPosition]
                val player = ExoPlayer.Builder(context).build()
                player.repeatMode = Player.REPEAT_MODE_ONE
                player.setMediaItem(MediaItem.fromUri(video.videoUrl))
                player.prepare()

                // Record view when video starts playing
                player.addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        if (playbackState == Player.STATE_READY && player.isPlaying) {
                            recordVideoView(video.id)
                        }
                    }
                })

                players[currentPlayingPosition] = player

                // Notify the adapter to rebind the current item
                notifyItemChanged(currentPlayingPosition)
            }
        }
    }

    private fun openUserProfile(userId: Int) {
        Log.d("VideoFeedAdapter", "Opening profile for userId: $userId")
        val intent = android.content.Intent(context, com.spyro.vmeet.ProfileActivity::class.java)
        intent.putExtra("USER_ID", userId)
        intent.putExtra("VIEW_ONLY_MODE", true)
        context.startActivity(intent)
    }
}
