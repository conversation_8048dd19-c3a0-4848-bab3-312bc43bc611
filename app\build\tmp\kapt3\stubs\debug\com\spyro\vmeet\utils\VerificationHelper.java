package com.spyro.vmeet.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001:\u0001\u001bB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\rJ\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0004J\u000e\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u000f\u001a\u00020\u0004J\u000e\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u000f\u001a\u00020\u0004J\u001e\u0010\u0013\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017J\u001e\u0010\u0018\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0016\u001a\u00020\u0017R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/spyro/vmeet/utils/VerificationHelper;", "", "()V", "API_URL", "", "TAG", "client", "Lokhttp3/OkHttpClient;", "checkVerificationStatus", "", "userId", "", "callback", "Lcom/spyro/vmeet/utils/VerificationHelper$VerificationStatusCallback;", "getVerificationStatusColor", "status", "getVerificationStatusText", "isUserVerified", "", "updateVerificationBadge", "badge", "Lcom/spyro/vmeet/ui/VerificationBadge;", "context", "Landroid/content/Context;", "updateVerificationImageView", "imageView", "Landroid/widget/ImageView;", "VerificationStatusCallback", "app_debug"})
public final class VerificationHelper {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "VerificationHelper";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.utils.VerificationHelper INSTANCE = null;
    
    private VerificationHelper() {
        super();
    }
    
    /**
     * Check verification status for a user and update UI accordingly
     */
    public final void checkVerificationStatus(int userId, @org.jetbrains.annotations.Nullable()
    com.spyro.vmeet.utils.VerificationHelper.VerificationStatusCallback callback) {
    }
    
    /**
     * Update a VerificationBadge with user's verification status
     */
    public final void updateVerificationBadge(int userId, @org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.ui.VerificationBadge badge, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Update a regular ImageView with verification badge
     */
    public final void updateVerificationImageView(int userId, @org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    /**
     * Get verification status text for display
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getVerificationStatusText(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
        return null;
    }
    
    /**
     * Get verification status color resource
     */
    public final int getVerificationStatusColor(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
        return 0;
    }
    
    /**
     * Check if user is verified (approved status)
     */
    public final boolean isUserVerified(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0018\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH&\u00a8\u0006\n"}, d2 = {"Lcom/spyro/vmeet/utils/VerificationHelper$VerificationStatusCallback;", "", "onError", "", "error", "", "onStatusReceived", "status", "isVerified", "", "app_debug"})
    public static abstract interface VerificationStatusCallback {
        
        public abstract void onStatusReceived(@org.jetbrains.annotations.NotNull()
        java.lang.String status, boolean isVerified);
        
        public abstract void onError(@org.jetbrains.annotations.NotNull()
        java.lang.String error);
    }
}