package com.spyro.vmeet.dialog;

/**
 * Confirmation dialog for deleting a chat room
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B#\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u00a2\u0006\u0002\u0010\tJ\u0012\u0010\u000f\u001a\u00020\b2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0014R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/dialog/DeleteRoomConfirmationDialog;", "Landroid/app/Dialog;", "context", "Landroid/content/Context;", "room", "Lcom/spyro/vmeet/data/ChatRoom;", "onConfirm", "Lkotlin/Function0;", "", "(Landroid/content/Context;Lcom/spyro/vmeet/data/ChatRoom;Lkotlin/jvm/functions/Function0;)V", "buttonCancel", "Landroid/widget/Button;", "buttonDelete", "textViewRoomName", "Landroid/widget/TextView;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "app_debug"})
public final class DeleteRoomConfirmationDialog extends android.app.Dialog {
    @org.jetbrains.annotations.NotNull()
    private final com.spyro.vmeet.data.ChatRoom room = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm = null;
    private android.widget.TextView textViewRoomName;
    private android.widget.Button buttonDelete;
    private android.widget.Button buttonCancel;
    
    public DeleteRoomConfirmationDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.data.ChatRoom room, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm) {
        super(null);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
}