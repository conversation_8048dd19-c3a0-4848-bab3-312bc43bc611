package com.spyro.vmeet

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.view.menu.MenuBuilder
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.widget.NestedScrollView
import com.bumptech.glide.Glide
import org.json.JSONObject
import java.io.DataOutputStream
import java.io.File
import java.io.FileInputStream
import java.net.HttpURLConnection
import java.net.URL
import com.google.android.material.textfield.TextInputLayout
import com.google.android.material.textfield.TextInputEditText
import kotlin.concurrent.thread
import com.google.android.material.bottomnavigation.BottomNavigationView
import android.content.SharedPreferences
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.appbar.CollapsingToolbarLayout
import com.google.android.material.floatingactionbutton.FloatingActionButton
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone
import android.app.DatePickerDialog
import java.util.concurrent.TimeUnit
import com.spyro.vmeet.activity.ChatListActivity
import java.time.Instant
import java.time.temporal.ChronoUnit
import android.os.Handler
import android.os.Looper
import com.spyro.vmeet.data.UserStatus
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.Call
import okhttp3.Callback
import java.io.IOException
import okhttp3.ResponseBody
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import android.app.AlertDialog
import okhttp3.FormBody
import com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton
import com.spyro.vmeet.activity.BaseActivity
import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import com.google.android.material.button.MaterialButton
import com.google.android.material.radiobutton.MaterialRadioButton
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.spyro.vmeet.adapter.PersonalityTraitAdapter
import com.spyro.vmeet.data.PersonalityTrait
import com.spyro.vmeet.data.PersonalityTraits
import com.spyro.vmeet.activity.EditPersonalityTraitsActivity
import com.spyro.vmeet.adapter.PersonalityTraitCategoryAdapter
import com.spyro.vmeet.adapter.TraitCategory
import androidx.recyclerview.widget.LinearLayoutManager
import org.json.JSONArray
import java.util.concurrent.ConcurrentHashMap
import com.spyro.vmeet.ui.ProfileDropdownMenu
import com.spyro.vmeet.ui.CommunityDropdownMenu

class ProfileActivity : BaseActivity() {
    private val PICK_IMAGE_REQUEST = 1
    private val PERMISSION_REQUEST_CODE = 100
    private var avatarUrl: String? = null
    private var userId: Int = 0
    private val API_URL = "http://*************:3000"
    private var isEditMode = false

    // Listas de opciones para género y sexualidad
    private val genderOptions = listOf(
        "Hombre", "Mujer", "No binario", "Género fluido", "Agénero",
        "Bigénero", "Transgénero", "Genderqueer", "Demigénero", "Otro"
    )

    private val sexualityOptions = listOf(
        "Heterosexual", "Homosexual", "Bisexual", "Pansexual", "Asexual",
        "Demisexual", "Queer", "Polisexual", "Androsexual", "Ginosexual", "Otro"
    )

    // Admin button
    private lateinit var buttonAdminPanel: Button

    // App Bar & Image
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var collapsingToolbarLayout: CollapsingToolbarLayout
    private lateinit var fabChangeAvatar: ExtendedFloatingActionButton

    // Scrollable Content Area
    private lateinit var nestedScrollView: NestedScrollView    // Top Info (View Mode)
    private lateinit var textViewUsername: TextView
    private lateinit var textViewAge: TextView
    private lateinit var textViewPronounsValue: TextView
    private lateinit var textViewAdminBadge: TextView
    private lateinit var textViewVerifiedBadge: TextView

    // Experience and Level UI elements
    private lateinit var textViewUserLevel: TextView
    private lateinit var progressBarUserLevel: ProgressBar
    private lateinit var textViewUserXP: TextView

    // Top Info (Edit Mode)
    private lateinit var inputLayoutPronouns: TextInputLayout
    private lateinit var editTextPronouns: TextInputEditText

    // About Me (View Mode)
    private lateinit var textViewBioLabel: TextView
    private lateinit var textViewBioValue: TextView

    // About Me (Edit Mode)
    private lateinit var inputLayoutBio: TextInputLayout
    private lateinit var editTextBio: TextInputEditText

    // Other Details Container (View Mode)
    private lateinit var viewModeDetailsLayout: LinearLayout
    private lateinit var textViewJobValue: TextView
    private lateinit var textViewSchoolValue: TextView
    private lateinit var textViewLocationValue: TextView
    private lateinit var textViewFavGameValue: TextView
    private lateinit var textViewFavPlatformValue: TextView
    private lateinit var textViewLookingForValue: TextView
    private lateinit var textViewBirthdateValue: TextView
    private lateinit var textViewGenderValue: TextView
    private lateinit var textViewSexualityValue: TextView
    private lateinit var textViewCityCountryValue: TextView

    // Other Details Container (Edit Mode)
    private lateinit var editModeDetailsLayout: LinearLayout
    private lateinit var inputLayoutJob: TextInputLayout
    private lateinit var editTextJob: TextInputEditText
    private lateinit var inputLayoutSchool: TextInputLayout
    private lateinit var editTextSchool: TextInputEditText
    private lateinit var inputLayoutLocation: TextInputLayout
    private lateinit var editTextLocation: TextInputEditText
    private lateinit var inputLayoutFavoriteGame: TextInputLayout
    private lateinit var editTextFavoriteGame: TextInputEditText
    private lateinit var inputLayoutFavoritePlatform: TextInputLayout
    private lateinit var editTextFavoritePlatform: TextInputEditText
    private lateinit var inputLayoutLookingFor: TextInputLayout
    private lateinit var editTextLookingFor: TextInputEditText
    private lateinit var inputLayoutBirthdate: TextInputLayout
    private lateinit var editTextBirthdate: TextInputEditText
    private lateinit var inputLayoutGender: TextInputLayout
    private lateinit var editTextGender: AutoCompleteTextView
    private lateinit var inputLayoutSexuality: TextInputLayout
    private lateinit var editTextSexuality: AutoCompleteTextView

    // Buttons & Navigation
    private lateinit var buttonEditSave: Button
    private lateinit var buttonLogout: Button
    private lateinit var buttonBlockedUsers: Button
    private lateinit var buttonBlockUser: Button  // New block user button
    private lateinit var buttonReportUser: Button // Report button
    private lateinit var bottomNavView: BottomNavigationView

    // Variables for status checking
    private val client = OkHttpClient()
    private val statusHandler = Handler(Looper.getMainLooper())
    private var statusRunnable: Runnable? = null
    private var lastStatusCheck: Long = 0
    private var statusCheckInterval = 30000L // 30 seconds

    // New variable for online status
    private lateinit var textViewOnlineStatus: TextView
    private lateinit var toolbar: androidx.appcompat.widget.Toolbar

    // New properties
    private lateinit var viewPagerImages: ViewPager2
    private lateinit var tabLayoutDots: TabLayout
    private lateinit var buttonSetAsMain: Button
    private lateinit var fabAddImage: FloatingActionButton
    private lateinit var profileImageAdapter: ProfileImageAdapter
    private val profileImages = mutableListOf<String>()
    private var mainImageUrl: String? = null
    private val MAX_PROFILE_IMAGES = 5

    // Add to class-level variables
    private var isUserBlocked = false // Track if the current profile user is blocked

    // Send message button
    private lateinit var buttonSendMessage: MaterialButton

    // Personality Traits
    private lateinit var recyclerViewPersonalityTraits: RecyclerView
    private lateinit var buttonEditTraits: MaterialButton
    // Note: We now use PersonalityTraitCategoryAdapter directly in updatePersonalityTraitsDisplay
    private val personalityTraits = mutableListOf<PersonalityTrait>()

    companion object {
        private const val TAG = "ProfileActivity" // Define TAG for logging
        private const val EDIT_TRAITS_REQUEST_CODE = 1001

        // Cache de ubicaciones de usuarios
        private val locationCache = ConcurrentHashMap<Int, Pair<String?, String?>>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate started")

        // Try to set the content view with error handling
        try {
            setContentView(R.layout.activity_profile)

            try {
                // Initialize views
                initializeViews()

                // Get user ID from intent
                userId = intent.getIntExtra("USER_ID", 0)
                Log.d(TAG, "User ID from intent: $userId")

                // Check if we're in view-only mode
                val viewOnlyMode = intent.getBooleanExtra("VIEW_ONLY_MODE", false)
                Log.d(TAG, "View only mode: $viewOnlyMode")

                // Obtener la información de ciudad y país desde el intent (si viene del radar)
                val cityFromIntent = intent.getStringExtra("CITY")
                val countryFromIntent = intent.getStringExtra("COUNTRY")

                Log.d(TAG, "Ciudad desde intent: $cityFromIntent, País desde intent: $countryFromIntent")

                // Si tenemos información de ciudad y país desde el radar, la mostramos directamente
                // y la guardamos en el cache para futuros accesos
                if (!cityFromIntent.isNullOrEmpty() || !countryFromIntent.isNullOrEmpty()) {
                    val cityCountryText = when {
                        !cityFromIntent.isNullOrEmpty() && !countryFromIntent.isNullOrEmpty() -> "$cityFromIntent, $countryFromIntent"
                        !cityFromIntent.isNullOrEmpty() -> cityFromIntent
                        !countryFromIntent.isNullOrEmpty() -> countryFromIntent
                        else -> ""
                    }

                    if (cityCountryText.isNotEmpty()) {
                        // Actualizar la vista de ciudad y país directamente
                        // Esto lo hacemos después de initializeViews, así que textViewCityCountryValue ya está inicializado
                        textViewCityCountryValue.post {
                            textViewCityCountryValue.text = cityCountryText
                            Log.d(TAG, "Texto de ciudad/país actualizado directamente desde el intent: $cityCountryText")
                        }

                        // Guardar en el cache
                        saveUserLocation(userId, cityFromIntent, countryFromIntent)
                    }
                } else {
                    // Intentar cargar del cache
                    val cachedLocation = loadUserLocation(userId)
                    if (cachedLocation != null) {
                        val (city, country) = cachedLocation
                        if (!city.isNullOrEmpty() || !country.isNullOrEmpty()) {
                            val cityCountryText = when {
                                !city.isNullOrEmpty() && !country.isNullOrEmpty() -> "$city, $country"
                                !city.isNullOrEmpty() -> city
                                !country.isNullOrEmpty() -> country
                                else -> ""
                            }

                            textViewCityCountryValue.post {
                                textViewCityCountryValue.text = cityCountryText
                                Log.d(TAG, "Texto de ciudad/país actualizado desde cache: $cityCountryText")
                            }
                        }
                    }
                }

                // Set up listeners and load profile
                setupListeners()

                // Get logged in user ID for bottom navigation
                val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
                val loggedInUserId = prefs.getInt("USER_ID", 0)
                setupBottomNavigation(loggedInUserId)

                loadProfile(userId)

                // Check if we should show profile tips for new users
                checkAndShowProfileTips()
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up views from layout", e)
                // If we can't set up the views, create a fallback layout
                setContentView(createCompatibilityLayout())
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error inflating activity_profile layout", e)
            // Create a compatibility layout programmatically as a fallback
            setContentView(createCompatibilityLayout())
        }
    }

    private fun createCompatibilityLayout(): View {
        Log.d(TAG, "Creating compatibility layout for profile activity")

        // Create root layout
        val rootLayout = androidx.constraintlayout.widget.ConstraintLayout(this)
        rootLayout.layoutParams = android.view.ViewGroup.LayoutParams(
            android.view.ViewGroup.LayoutParams.MATCH_PARENT,
            android.view.ViewGroup.LayoutParams.MATCH_PARENT
        )
        // Use the cyberpunk gradient background color
        rootLayout.setBackgroundColor(resources.getColor(R.color.cyberpunk_background, theme))

        // Create a toolbar
        val toolbar = androidx.appcompat.widget.Toolbar(this)
        toolbar.id = View.generateViewId()
        val toolbarParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            resources.getDimensionPixelSize(android.R.dimen.app_icon_size)
        )
        toolbarParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        toolbarParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        toolbarParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        toolbarParams.topMargin = resources.getDimensionPixelSize(R.dimen.status_bar_height)
        toolbar.layoutParams = toolbarParams
        toolbar.setBackgroundColor(resources.getColor(R.color.cyberpunk_card_background, theme))

        // Add a title to the toolbar
        val titleTextView = TextView(this)
        titleTextView.text = "Perfil"
        titleTextView.setTextColor(resources.getColor(R.color.comfy_blue, theme))
        titleTextView.textSize = 20f
        val titleParams = androidx.appcompat.widget.Toolbar.LayoutParams(
            androidx.appcompat.widget.Toolbar.LayoutParams.WRAP_CONTENT,
            androidx.appcompat.widget.Toolbar.LayoutParams.WRAP_CONTENT
        )
        titleParams.gravity = android.view.Gravity.CENTER
        titleTextView.layoutParams = titleParams
        toolbar.addView(titleTextView)

        rootLayout.addView(toolbar)

        // Create a ScrollView for profile content
        val scrollView = androidx.core.widget.NestedScrollView(this)
        scrollView.id = View.generateViewId()
        val scrollParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            0
        )
        scrollParams.topToBottom = toolbar.id
        scrollParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        scrollParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        scrollParams.bottomToTop = R.id.bottom_navigation
        scrollView.layoutParams = scrollParams

        // Create a LinearLayout for profile content
        val contentLayout = LinearLayout(this)
        contentLayout.orientation = LinearLayout.VERTICAL
        contentLayout.layoutParams = android.view.ViewGroup.LayoutParams(
            android.view.ViewGroup.LayoutParams.MATCH_PARENT,
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
        contentLayout.setPadding(32, 32, 32, 32)

        // Add a message about fallback mode
        val fallbackMessage = TextView(this)
        fallbackMessage.text = "Modo de compatibilidad activado. Algunas funciones pueden no estar disponibles."
        fallbackMessage.setTextColor(resources.getColor(android.R.color.white, theme))
        fallbackMessage.textSize = 16f
        fallbackMessage.setPadding(0, 0, 0, 32)
        contentLayout.addView(fallbackMessage)

        // Create a profile image view
        val profileImageView = ImageView(this)
        profileImageView.id = View.generateViewId()
        val imageParams = LinearLayout.LayoutParams(
            200, 200
        )
        imageParams.gravity = android.view.Gravity.CENTER
        imageParams.bottomMargin = 16
        profileImageView.layoutParams = imageParams
        profileImageView.scaleType = ImageView.ScaleType.CENTER_CROP
        profileImageView.setImageResource(R.drawable.ic_default_avatar)
        contentLayout.addView(profileImageView)

        // Add username TextView
        val usernameTextView = TextView(this)
        usernameTextView.id = R.id.textViewUsername
        usernameTextView.text = "Cargando perfil..."
        usernameTextView.setTextColor(resources.getColor(android.R.color.white, theme))
        usernameTextView.textSize = 24f
        usernameTextView.gravity = android.view.Gravity.CENTER
        usernameTextView.setPadding(0, 16, 0, 16)
        contentLayout.addView(usernameTextView)

        // Add age TextView
        val ageTextView = TextView(this)
        ageTextView.id = R.id.textViewAge
        ageTextView.text = ""
        ageTextView.setTextColor(resources.getColor(android.R.color.white, theme))
        ageTextView.textSize = 18f
        ageTextView.gravity = android.view.Gravity.CENTER
        ageTextView.setPadding(0, 0, 0, 16)
        contentLayout.addView(ageTextView)

        // Add a divider
        val divider = View(this)
        divider.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            2
        )
        divider.setBackgroundColor(resources.getColor(R.color.cyberpunk_divider, theme))
        divider.setPadding(0, 8, 0, 8)
        contentLayout.addView(divider)

        // Add bio section
        val bioLabel = TextView(this)
        bioLabel.text = "Biografía"
        bioLabel.setTextColor(resources.getColor(R.color.neon_blue, theme))
        bioLabel.textSize = 18f
        bioLabel.setPadding(0, 16, 0, 8)
        contentLayout.addView(bioLabel)

        val bioValue = TextView(this)
        bioValue.id = R.id.textViewBioValue
        bioValue.text = "Cargando..."
        bioValue.setTextColor(resources.getColor(android.R.color.white, theme))
        bioValue.textSize = 16f
        bioValue.setPadding(0, 0, 0, 16)
        contentLayout.addView(bioValue)

        // Add edit/save button
        val editSaveButton = Button(this)
        editSaveButton.id = R.id.buttonEditSave
        editSaveButton.text = "Editar"
        editSaveButton.setBackgroundColor(resources.getColor(R.color.neon_blue, theme))
        editSaveButton.setTextColor(resources.getColor(android.R.color.white, theme))
        val buttonParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        buttonParams.topMargin = 32
        editSaveButton.layoutParams = buttonParams
        contentLayout.addView(editSaveButton)

        // Add logout button
        val logoutButton = Button(this)
        logoutButton.id = R.id.buttonLogout
        logoutButton.text = "Cerrar Sesión"
        logoutButton.setBackgroundColor(resources.getColor(R.color.dark_red, theme))
        logoutButton.setTextColor(resources.getColor(android.R.color.white, theme))
        val logoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        logoutParams.topMargin = 16
        logoutButton.layoutParams = logoutParams
        contentLayout.addView(logoutButton)

        // Add the content layout to the scroll view
        scrollView.addView(contentLayout)
        rootLayout.addView(scrollView)

        // Create BottomNavigationView
        val bottomNav = com.google.android.material.bottomnavigation.BottomNavigationView(this)
        bottomNav.id = R.id.bottom_navigation
        val bottomNavParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        bottomNavParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        bottomNavParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        bottomNavParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        bottomNav.layoutParams = bottomNavParams
        bottomNav.inflateMenu(R.menu.bottom_nav_menu)
        bottomNav.setBackgroundColor(resources.getColor(R.color.cyberpunk_card_background, theme))
        bottomNav.selectedItemId = R.id.navigation_profile

        // Set up bottom navigation listener
        bottomNav.setOnItemSelectedListener { item ->
            try {
                when (item.itemId) {
                    R.id.navigation_swipe -> {
                        try {
                            val intent = Intent(this, SwipeActivity::class.java)
                            intent.putExtra("USER_ID", userId)
                            intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                            startActivity(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error navigating to SwipeActivity", e)
                            Toast.makeText(this, "Error al navegar a Descubrir", Toast.LENGTH_SHORT).show()
                        }
                        true
                    }
                    R.id.navigation_matches -> {
                        try {
                            val intent = Intent(this, MatchesActivity::class.java)
                            intent.putExtra("USER_ID", userId)
                            intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                            startActivity(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error navigating to MatchesActivity", e)
                            Toast.makeText(this, "Error al navegar a Matches", Toast.LENGTH_SHORT).show()
                        }
                        true
                    }
                    R.id.navigation_community -> {
                        try {
                            val intent = Intent(this, CommunityHostActivity::class.java)
                            intent.putExtra("USER_ID", userId)
                            intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                            startActivity(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error navigating to CommunityHostActivity", e)
                            Toast.makeText(this, "Error al navegar a Comunidad", Toast.LENGTH_SHORT).show()
                        }
                        true
                    }
                    R.id.navigation_chats -> {
                        try {
                            val intent = Intent(this, ChatListActivity::class.java)
                            intent.putExtra("USER_ID", userId)
                            intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                            startActivity(intent)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error navigating to ChatListActivity", e)
                            Toast.makeText(this, "Error al navegar a Chats", Toast.LENGTH_SHORT).show()
                        }
                        true
                    }
                    R.id.navigation_profile -> {
                        // Already in ProfileActivity
                        true
                    }
                    else -> false
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in bottom navigation", e)
                Toast.makeText(this, "Error en la navegación", Toast.LENGTH_SHORT).show()
                false
            }
        }

        rootLayout.addView(bottomNav)

        // Initialize minimal UI elements
        this.bottomNavView = bottomNav
        this.textViewUsername = usernameTextView
        this.textViewAge = ageTextView
        this.textViewBioValue = bioValue
        this.buttonEditSave = editSaveButton
        this.buttonLogout = logoutButton

        // Get user ID from intent or preferences
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val intentUserId = intent.getIntExtra("USER_ID", 0)
        val loggedInUserId = prefs.getInt("USER_ID", 0)

        // If no user ID was provided in the intent, use the logged-in user's ID
        userId = if (intentUserId > 0) intentUserId else loggedInUserId

        // Set up button listeners
        editSaveButton.setOnClickListener {
            if (isEditMode) {
                saveProfileData()
            } else {
                switchToEditMode()
            }
        }

        logoutButton.setOnClickListener {
            logoutUser()
        }

        // Load profile data
        if (userId > 0) {
            loadProfile(userId)
        } else {
            Log.e(TAG, "Invalid user ID in compatibility mode")
            Toast.makeText(this, "Error: ID de usuario no encontrado", Toast.LENGTH_SHORT).show()
        }

        // Log that we're using the compatibility layout
        Log.d(TAG, "Using compatibility layout for profile activity")
        Toast.makeText(this, "Modo de compatibilidad activado", Toast.LENGTH_SHORT).show()

        return rootLayout
    }

    private fun initializeViews() {
        // Set up toolbar
        toolbar = findViewById(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayShowTitleEnabled(false)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        // Add additional click listener for more options icon
        toolbar.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.menu_logout -> {
                    Log.d(TAG, "Logout menu item clicked directly from toolbar")
                    logoutUser()
                    return@setOnMenuItemClickListener true
                }
                R.id.menu_settings -> {
                    Log.d(TAG, "Settings menu item clicked directly from toolbar")
                    val intent = Intent(this, com.spyro.vmeet.activity.SettingsActivity::class.java)
                    intent.putExtra("USER_ID", getSharedPreferences("VMeetPrefs", MODE_PRIVATE).getInt("USER_ID", 0))
                    startActivity(intent)
                    return@setOnMenuItemClickListener true
                }
                R.id.menu_block_user -> {
                    if (isUserBlocked) {
                        showUnblockUserConfirmation()
                    } else {
                        showBlockUserConfirmation()
                    }
                    return@setOnMenuItemClickListener true
                }
                else -> false
            }
        }

        // Initialize UI References
        appBarLayout = findViewById(R.id.app_bar_layout)
        collapsingToolbarLayout = findViewById(R.id.collapsing_toolbar_layout)

        // Get view references from included layout
        val imageSlider = findViewById<View>(R.id.imageSlider)
        viewPagerImages = imageSlider.findViewById(R.id.viewPagerImages)
        tabLayoutDots = imageSlider.findViewById(R.id.tabLayoutDots)
        buttonSetAsMain = imageSlider.findViewById(R.id.buttonSetAsMain)

        fabChangeAvatar = findViewById(R.id.fabChangeAvatar)
        fabAddImage = findViewById(R.id.fabAddImage)

        // Initialize the image adapter
        profileImageAdapter = ProfileImageAdapter(
            this,
            profileImages,
            API_URL,
            onImageClickListener = { position ->
                // Show options when clicking an image in edit mode
                if (isEditMode) {
                    showImageOptions(position)
                } else {
                    // Open image in full screen view when in view mode
                    openFullScreenImage(position)
                }
            },
            onDeleteClickListener = { position ->
                // Handle direct delete button click
                if (isEditMode && profileImages.size > 1) {
                    deleteProfileImage(position)
                } else if (profileImages.size <= 1) {
                    Toast.makeText(this, "Debes mantener al menos una imagen", Toast.LENGTH_SHORT).show()
                }
            }
        )

        // Set up ViewPager with adapter
        viewPagerImages.adapter = profileImageAdapter

        // Connect TabLayout with ViewPager2
        TabLayoutMediator(tabLayoutDots, viewPagerImages) { tab, _ ->
            // No special configuration needed for dots
        }.attach()

        // Set up button to mark an image as main profile picture
        buttonSetAsMain.setOnClickListener {
            val currentPosition = viewPagerImages.currentItem
            setMainProfileImage(currentPosition)
        }        // The rest of the initialization stays the same
        nestedScrollView = findViewById(R.id.nestedScrollView)
        textViewUsername = findViewById(R.id.textViewUsername)
        textViewAge = findViewById(R.id.textViewAge)
        textViewPronounsValue = findViewById(R.id.textViewPronounsValue)
        textViewAdminBadge = findViewById(R.id.textViewAdminBadge)
        textViewVerifiedBadge = findViewById(R.id.textViewVerifiedBadge)

        // Initialize Experience and Level UI elements
        textViewUserLevel = findViewById(R.id.textViewUserLevel)
        progressBarUserLevel = findViewById(R.id.progressBarUserLevel)
        textViewUserXP = findViewById(R.id.textViewUserXP)
        inputLayoutPronouns = findViewById(R.id.inputLayoutPronouns)
        editTextPronouns = findViewById(R.id.editTextPronouns)
        textViewBioLabel = findViewById(R.id.textViewBioLabel)
        textViewBioValue = findViewById(R.id.textViewBioValue)
        inputLayoutBio = findViewById(R.id.inputLayoutBio)
        editTextBio = findViewById(R.id.editTextBio)
        viewModeDetailsLayout = findViewById(R.id.viewModeDetailsLayout)
        textViewJobValue = findViewById(R.id.textViewJobValue)
        textViewSchoolValue = findViewById(R.id.textViewSchoolValue)
        textViewLocationValue = findViewById(R.id.textViewLocationValue)
        textViewFavGameValue = findViewById(R.id.textViewFavGameValue)
        textViewFavPlatformValue = findViewById(R.id.textViewFavPlatformValue)
        textViewLookingForValue = findViewById(R.id.textViewLookingForValue)
        textViewBirthdateValue = findViewById(R.id.textViewBirthdateValue)
        textViewGenderValue = findViewById(R.id.textViewGenderValue)
        textViewSexualityValue = findViewById(R.id.textViewSexualityValue)
        textViewCityCountryValue = findViewById(R.id.textViewCityCountryValue)
        editModeDetailsLayout = findViewById(R.id.editModeDetailsLayout)
        inputLayoutJob = findViewById(R.id.inputLayoutJob)
        editTextJob = findViewById(R.id.editTextJob)
        inputLayoutSchool = findViewById(R.id.inputLayoutSchool)
        editTextSchool = findViewById(R.id.editTextSchool)
        inputLayoutLocation = findViewById(R.id.inputLayoutLocation)
        editTextLocation = findViewById(R.id.editTextLocation)
        inputLayoutFavoriteGame = findViewById(R.id.inputLayoutFavGame)
        editTextFavoriteGame = findViewById(R.id.editTextFavoriteGame)
        inputLayoutFavoritePlatform = findViewById(R.id.inputLayoutFavPlatform)
        editTextFavoritePlatform = findViewById(R.id.editTextFavoritePlatform)
        inputLayoutLookingFor = findViewById(R.id.inputLayoutLookingFor)
        editTextLookingFor = findViewById(R.id.editTextLookingFor)
        inputLayoutBirthdate = findViewById(R.id.inputLayoutBirthdate)
        editTextBirthdate = findViewById(R.id.editTextBirthdate)
        inputLayoutGender = findViewById(R.id.inputLayoutGender)
        editTextGender = findViewById(R.id.editTextGender)
        inputLayoutSexuality = findViewById(R.id.inputLayoutSexuality)
        editTextSexuality = findViewById(R.id.editTextSexuality)

        // Configurar los adaptadores para los dropdowns de género y sexualidad
        val genderAdapter = ArrayAdapter(this, R.layout.dropdown_item, genderOptions)
        val sexualityAdapter = ArrayAdapter(this, R.layout.dropdown_item, sexualityOptions)

        editTextGender.setAdapter(genderAdapter)
        editTextSexuality.setAdapter(sexualityAdapter)
        buttonEditSave = findViewById(R.id.buttonEditSave)
        buttonLogout = findViewById(R.id.buttonLogout)
        buttonBlockedUsers = findViewById(R.id.buttonBlockedUsers)
        buttonBlockUser = findViewById(R.id.buttonBlockUser)  // Initialize the block user button
        buttonReportUser = findViewById(R.id.buttonReportUser) // Initialize the report user button
        bottomNavView = findViewById(R.id.bottom_navigation)
        buttonAdminPanel = findViewById(R.id.buttonAdminPanel)
        buttonSendMessage = findViewById(R.id.buttonSendMessage) // Initialize the send message button

        // Initialize Personality Traits
        recyclerViewPersonalityTraits = findViewById(R.id.recyclerViewPersonalityTraits)
        buttonEditTraits = findViewById(R.id.buttonEditTraits)



        // Setup personality traits RecyclerView
        setupPersonalityTraitsRecyclerView()

        // Initialize online status TextView
        textViewOnlineStatus = findViewById(R.id.textViewOnlineStatus)

        // Determine user ID and if we are viewing our own profile or someone else's
        var viewingUserId = intent.getIntExtra("USER_ID", 0)
        val isViewOnly = intent.getBooleanExtra("VIEW_ONLY_MODE", false)
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val loggedInUserId = prefs.getInt("USER_ID", 0)

        // If no user ID was provided in the intent, use the logged-in user's ID
        if (viewingUserId == 0) {
            Log.d(TAG, "No USER_ID provided in intent, using logged-in user ID: $loggedInUserId")
            viewingUserId = loggedInUserId

            // If we still don't have a valid user ID, show an error and finish
            if (viewingUserId == 0) {
                Log.e(TAG, "Both viewing User ID and logged-in User ID are 0, cannot load profile.")
                Toast.makeText(this, "Error: ID de usuario no encontrado.", Toast.LENGTH_LONG).show()
                finish()
                return
            }
        }

        userId = viewingUserId // The profile we are actually showing
        val isOwnProfile = userId == loggedInUserId && !isViewOnly

        Log.d(TAG, "Final User ID to load: $userId (isOwnProfile: $isOwnProfile)")

        Log.d(TAG, "Viewing Profile for User ID: $userId, Logged in User ID: $loggedInUserId, Is Own Profile (Editable): $isOwnProfile, Is View Only: $isViewOnly")

        // Initialize and setup BottomNavigationView
        // Highlight profile tab if viewing own profile, otherwise maybe swipe tab?
        bottomNavView.selectedItemId = if (isOwnProfile) R.id.navigation_profile else R.id.navigation_swipe // Tentative: highlight swipe if viewing others
        setupBottomNavigation(loggedInUserId) // Set up navigation with correct user ID

        // Set initial mode based on whether it's own profile or view-only
        if (isOwnProfile) {
            isEditMode = false // Start own profile in view mode by default
            buttonEditSave.visibility = View.VISIBLE
            buttonLogout.visibility = View.VISIBLE
            buttonBlockedUsers.visibility = View.VISIBLE // Show blocked users button for own profile
            buttonBlockUser.visibility = View.GONE // Hide block button on own profile
            buttonReportUser.visibility = View.GONE // Hide report button on own profile
            buttonSendMessage.visibility = View.GONE // Hide send message button on own profile
            buttonAdminPanel.visibility = View.GONE // Initially hide admin button, will check admin status
            checkAdminStatus(loggedInUserId) // Check if user is admin
            switchToViewMode() // Apply view mode styling initially
        } else {
            isEditMode = false // Force view mode when viewing others
            buttonEditSave.visibility = View.GONE // Hide edit/save button
            buttonLogout.visibility = View.GONE  // Hide logout button
            buttonBlockedUsers.visibility = View.GONE // Hide blocked users button when viewing others
            buttonBlockUser.visibility = View.VISIBLE // Show block button when viewing others
            buttonReportUser.visibility = View.VISIBLE // Show report button when viewing others
            buttonSendMessage.visibility = View.VISIBLE // Show send message button when viewing others
            buttonAdminPanel.visibility = View.GONE // Hide admin button
            fabChangeAvatar.visibility = View.GONE // Ensure FAB is hidden
            switchToViewMode() // Apply view mode styling
        }

        loadProfile(userId)

        // Only allow edit/save functionality if it's the user's own profile
        buttonEditSave.setOnClickListener {
            if (isOwnProfile) {
                if (isEditMode) {
                    saveProfileData()
                } else {
                    switchToEditMode()
                }
            } // Else: Button is hidden or does nothing
        }

        // Set up logout button
        buttonLogout.setOnClickListener {
            logoutUser()
        }

        // Set up blocked users button
        buttonBlockedUsers.setOnClickListener {
            val intent = Intent(this, BlockedUsersActivity::class.java)
            startActivity(intent)
        }

        // Set up block user button
        buttonBlockUser.setOnClickListener {
            showBlockUserConfirmation()
        }

        // Set up report user button
        buttonReportUser.setOnClickListener {
            showReportUserDialog()
        }

        // Set up send message button
        buttonSendMessage.setOnClickListener {
            openChatWithUser()
        }

        editTextBirthdate.setOnClickListener {
            if (isEditMode && isOwnProfile) { // Only allow date picking if editing own profile
                showDatePickerDialog()
            }
        }

        fabChangeAvatar.setOnClickListener {
             if (isEditMode && isOwnProfile) { // Only allow avatar change if editing own profile
                if (checkStoragePermission()) {
                    openImagePicker()
                } else {
                    requestStoragePermission()
                }
            } else {
                // This FAB is hidden in view mode, but good practice to check
                Toast.makeText(this, "Pulsa ${getString(R.string.profile_button_edit)} para cambiar el avatar", Toast.LENGTH_SHORT).show()
            }
        }

        // If viewing someone else's profile, start status updates
        if (viewingUserId != loggedInUserId) {
            startStatusUpdates(viewingUserId)
        } else {
            // Hide status for own profile
            textViewOnlineStatus.visibility = View.GONE
        }

        // Set up fabAddImage click listener
        fabAddImage.setOnClickListener {
            if (isEditMode && profileImages.size < MAX_PROFILE_IMAGES) {
                if (checkStoragePermission()) {
                    openImagePicker()
                } else {
                    requestStoragePermission()
                }
            } else if (profileImages.size >= MAX_PROFILE_IMAGES) {
                Toast.makeText(this, "Máximo de $MAX_PROFILE_IMAGES imágenes alcanzado", Toast.LENGTH_SHORT).show()
            }
        }

        // Check if we should show profile tips for new users
        val showProfileTips = intent.getBooleanExtra("SHOW_PROFILE_TIPS", false)
        val prefsForTips = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val isFirstLogin = prefsForTips.getBoolean("IS_FIRST_LOGIN", false)

        if (showProfileTips || isFirstLogin) {
            // Clear the first login flag since we're showing the tips
            prefsForTips.edit().putBoolean("IS_FIRST_LOGIN", false).apply()

            // Show profile completion tips
            showProfileCompletionTips()
        }

        // Set up Admin Panel button click listener
        buttonAdminPanel.setOnClickListener {
            val intent = AdminPanelActivity.createIntent(this, userId)
            startActivity(intent)
        }
    }

    private fun switchToViewMode() {
        Log.d("ProfileActivity", "Switching to View Mode")
        isEditMode = false

        // Update adapter edit mode state
        profileImageAdapter.isEditMode = false

        // Toggle Visibility: Show View elements, hide Edit elements
        textViewPronounsValue.visibility = View.VISIBLE
        inputLayoutPronouns.visibility = View.GONE

        textViewBioValue.visibility = View.VISIBLE
        textViewBioLabel.visibility = View.VISIBLE // Ensure label is visible
        inputLayoutBio.visibility = View.GONE

        viewModeDetailsLayout.visibility = View.VISIBLE
        editModeDetailsLayout.visibility = View.GONE

        // Update Button Text & FAB Visibility
        buttonEditSave.text = getString(R.string.profile_button_edit)
        fabChangeAvatar.visibility = View.GONE
        fabAddImage.visibility = View.GONE

        // Hide the Set as Main button in view mode
        buttonSetAsMain.visibility = View.GONE

        // Disable click listener for birthdate in view mode
        editTextBirthdate.isClickable = false
        editTextBirthdate.isFocusable = false
        editTextBirthdate.isFocusableInTouchMode = false // Ensure keyboard doesn't pop up

        // Ensure logout button is visible if this is the user's own profile
        val prefsForViewMode = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val loggedInUserId = prefsForViewMode.getInt("USER_ID", 0)
        if (userId == loggedInUserId && !intent.getBooleanExtra("VIEW_ONLY_MODE", false)) {
            buttonLogout.visibility = View.VISIBLE
            buttonBlockedUsers.visibility = View.VISIBLE // Show blocked users button
            buttonBlockUser.visibility = View.GONE // Hide block button on own profile
            buttonReportUser.visibility = View.GONE // Hide report button on own profile
            buttonSendMessage.visibility = View.GONE // Hide send message button on own profile
            // Admin button visibility is controlled separately by checkAdminStatus()
        } else {
            buttonBlockedUsers.visibility = View.GONE // Hide for other profiles
            buttonBlockUser.visibility = View.VISIBLE // Show block button for other profiles
            buttonReportUser.visibility = View.VISIBLE // Show report button for other profiles
        }
    }

    private fun switchToEditMode() {
        Log.d("ProfileActivity", "Switching to Edit Mode")
        isEditMode = true

        // Update adapter edit mode state
        profileImageAdapter.isEditMode = true

        // Toggle Visibility: Hide View elements, show Edit elements
        textViewPronounsValue.visibility = View.GONE
        inputLayoutPronouns.visibility = View.VISIBLE

        textViewBioValue.visibility = View.GONE
        textViewBioLabel.visibility = View.GONE // Hide label when input is shown
        inputLayoutBio.visibility = View.VISIBLE

        viewModeDetailsLayout.visibility = View.GONE
        editModeDetailsLayout.visibility = View.VISIBLE

        // Update Button Text & FAB Visibility
        buttonEditSave.text = getString(R.string.profile_button_save)
        fabChangeAvatar.visibility = View.VISIBLE

        // Show the Set as Main button in edit mode
        buttonSetAsMain.visibility = View.VISIBLE

        // Show the Add Image FAB in edit mode if we have less than max images
        fabAddImage.visibility = if (profileImages.size < MAX_PROFILE_IMAGES) View.VISIBLE else View.GONE

        // Enable click listener for birthdate in edit mode
        editTextBirthdate.isClickable = true
        editTextBirthdate.isFocusable = true // Keep focusable for a11y but not in touch mode initially
        editTextBirthdate.isFocusableInTouchMode = false // Don't pop keyboard, rely on click listener
        editTextBirthdate.error = null // Clear any previous errors

        // Configurar los adaptadores para los dropdowns de género y sexualidad
        val genderAdapter = ArrayAdapter(this, R.layout.dropdown_item, genderOptions)
        val sexualityAdapter = ArrayAdapter(this, R.layout.dropdown_item, sexualityOptions)

        editTextGender.setAdapter(genderAdapter)
        editTextSexuality.setAdapter(sexualityAdapter)

        // Ensure logout button is visible if this is the user's own profile
        val prefsForEditMode = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val loggedInUserId = prefsForEditMode.getInt("USER_ID", 0)
        if (userId == loggedInUserId && !intent.getBooleanExtra("VIEW_ONLY_MODE", false)) {
            buttonLogout.visibility = View.VISIBLE
            buttonBlockedUsers.visibility = View.VISIBLE // Show blocked users button
            buttonBlockUser.visibility = View.GONE // Hide block button on own profile
            buttonReportUser.visibility = View.GONE // Hide report button on own profile
            buttonSendMessage.visibility = View.GONE // Hide send message button on own profile
            // Admin button visibility is controlled separately by checkAdminStatus()
        } else {
            buttonBlockedUsers.visibility = View.GONE // Hide for other profiles
            buttonBlockUser.visibility = View.VISIBLE // Show block button for other profiles
            buttonReportUser.visibility = View.VISIBLE // Show report button for other profiles
        }

        // Scroll to the top when entering edit mode so users can see all editable fields
        nestedScrollView.post {
            nestedScrollView.smoothScrollTo(0, 0)
        }
    }

    private fun saveProfileData() {
        // Collect data from EditTexts
        val pronouns = editTextPronouns.text.toString()
        val bio = editTextBio.text.toString()
        val jobTitle = editTextJob.text.toString()
        val school = editTextSchool.text.toString()
        val location = editTextLocation.text.toString()
        val favoriteGame = editTextFavoriteGame.text.toString()
        val favoritePlatform = editTextFavoritePlatform.text.toString()
        val lookingFor = editTextLookingFor.text.toString()
        val birthdate = editTextBirthdate.text.toString()
        val gender = editTextGender.text.toString()
        val sexuality = editTextSexuality.text.toString()

        // Show loading state
        buttonEditSave.isEnabled = false
        // Consider adding a ProgressBar

        val profileData = mutableMapOf<String, Any?>()
        profileData["pronouns"] = pronouns
        profileData["bio"] = bio
        profileData["job_title"] = jobTitle
        profileData["school"] = school
        profileData["location"] = location
        profileData["favorite_game"] = favoriteGame
        profileData["favorite_platform"] = favoritePlatform
        profileData["looking_for"] = lookingFor
        profileData["birthdate"] = birthdate.ifEmpty { null }
        profileData["gender"] = gender
        profileData["sexuality"] = sexuality

        // Add profile images
        if (profileImages.isNotEmpty()) {
            val imagesArray = profileImages.mapIndexed { index, imageUrl ->
                mapOf(
                    "url" to imageUrl,
                    "isMain" to (imageUrl == mainImageUrl)
                )
            }
            profileData["profile_images"] = imagesArray
        }

        // Backward compatibility - still include avatar_url for older versions
        if (mainImageUrl != null) {
            profileData["avatar_url"] = mainImageUrl
        }

        // Convert to JSON
        val jsonObj = JSONObject(profileData as Map<*, *>)
        val jsonStr = jsonObj.toString()

        // Log the JSON data being sent
        Log.d(TAG, "Sending profile update: $jsonStr")

        thread {
            try {
                // Fix the URL to match the backend endpoint
                val url = URL("$API_URL/profile/profile/$userId")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "PUT"
                conn.setRequestProperty("Content-Type", "application/json")
                conn.doOutput = true

                // Write data
                val os = conn.outputStream
                os.write(jsonStr.toByteArray())
                os.flush()
                os.close()

                // Check response
                val responseCode = conn.responseCode
                Log.d(TAG, "Profile update response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Log response for debugging
                    val response = conn.inputStream.bufferedReader().use { it.readText() }
                    Log.d(TAG, "Profile update success response: $response")

                    runOnUiThread {
                        Toast.makeText(this, "Perfil actualizado", Toast.LENGTH_SHORT).show()

                        // Switch back to view mode, showing the updated data
                        switchToViewMode()
                        buttonEditSave.isEnabled = true

                        // Reload profile to show updated data
                        loadProfile(userId)
                    }
                } else {
                    // Log error details for debugging
                    val errorResponse = conn.errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e(TAG, "Profile update error: $responseCode, $errorResponse")

                    runOnUiThread {
                        Toast.makeText(this, "Error al actualizar perfil: $responseCode", Toast.LENGTH_SHORT).show()
                        buttonEditSave.isEnabled = true
                    }
                }
            } catch (e: Exception) {
                Log.e("ProfileActivity", "Error saving profile", e)
                runOnUiThread {
                    Toast.makeText(this, "Error: ${e.message}", Toast.LENGTH_SHORT).show()
                    buttonEditSave.isEnabled = true
                }
            }
        }
    }

    private fun updateTextViewsFromEditTexts() {
        Log.d(TAG, "Updating TextViews from EditTexts")
        val pronouns = editTextPronouns.text.toString()
        val bio = editTextBio.text.toString()
        val job = editTextJob.text.toString()
        val school = editTextSchool.text.toString()
        val location = editTextLocation.text.toString()
        val favGame = editTextFavoriteGame.text.toString()
        val favPlatform = editTextFavoritePlatform.text.toString()
        val lookingFor = editTextLookingFor.text.toString()
        val gender = editTextGender.text.toString()
        val sexuality = editTextSexuality.text.toString()
        val birthdateInput = editTextBirthdate.text.toString()

        // Note: When updating from edit texts, we don't have the city and country data
        // These will be properly set when the profile is reloaded from the server

        textViewPronounsValue.text = if (pronouns.isEmpty() || pronouns == "null") "Por completar" else pronouns
        textViewBioValue.text = if (bio.isEmpty() || bio == "null") "Por completar" else bio
        textViewJobValue.text = if (job.isEmpty() || job == "null") "Por completar" else job
        textViewSchoolValue.text = if (school.isEmpty() || school == "null") "Por completar" else school

        // For location, we'll use what the user entered directly until the profile is reloaded
        textViewLocationValue.text = if (location.isEmpty() || location == "null") "Por completar" else location

        // La ciudad y país se mantendrán como estaban, ya que no se pueden editar directamente
        // Solo se actualizarán cuando se recargue el perfil desde el servidor

        textViewFavGameValue.text = if (favGame.isEmpty() || favGame == "null") "Por completar" else favGame
        textViewFavPlatformValue.text = if (favPlatform.isEmpty() || favPlatform == "null") "Por completar" else favPlatform
        textViewLookingForValue.text = if (lookingFor.isEmpty() || lookingFor == "null") "Por completar" else lookingFor
        textViewGenderValue.text = if (gender.isEmpty() || gender == "null") "Por completar" else gender
        textViewSexualityValue.text = if (sexuality.isEmpty() || sexuality == "null") "Por completar" else sexuality
        textViewBirthdateValue.text = if (birthdateInput.isEmpty() || birthdateInput == "null") "Por completar" else birthdateInput

        Log.d(TAG, "updateTextViewsFromEditTexts - Sexuality: '$sexuality', textViewSexualityValue.text: '${textViewSexualityValue.text}'")
        Log.d(TAG, "updateTextViewsFromEditTexts - Gender: '$gender', textViewGenderValue.text: '${textViewGenderValue.text}'")

        // Update the combined username/age view
        val age = calculateAge(birthdateInput)
        val ageText = if (age != null) age.toString() else ""
        val currentUsername = textViewUsername.text.toString().substringBefore(',') // Get original username
        if (ageText.isNotEmpty()) {
            textViewUsername.text = "$currentUsername, $ageText"
        } else {
            textViewUsername.text = currentUsername
        }
        // textViewAge is already cleared/unused
    }

    private fun checkStoragePermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ uses READ_MEDIA_IMAGES
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES) == PackageManager.PERMISSION_GRANTED
        } else {
            // Older versions use READ_EXTERNAL_STORAGE
            ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestStoragePermission() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
        ActivityCompat.requestPermissions(this, arrayOf(permission), PERMISSION_REQUEST_CODE)
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                openImagePicker()
            } else {
                Toast.makeText(this, "Permiso denegado. No se puede seleccionar imagen.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun openImagePicker() {
        if (profileImages.size >= MAX_PROFILE_IMAGES) {
            Toast.makeText(this, "Máximo de $MAX_PROFILE_IMAGES imágenes alcanzado", Toast.LENGTH_SHORT).show()
            return
        }

        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, PICK_IMAGE_REQUEST)
    }



    private fun uploadAvatar(imageUri: Uri) {
        val filePath = getPathFromUri(imageUri)
        if (filePath == null) {
            Log.e("ProfileActivity", "Could not get file path from Uri: $imageUri")
            Toast.makeText(this, "Error al obtener ruta del archivo.", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d("ProfileActivity", "Uploading avatar from path: $filePath")
        // Show progress indicator maybe

        thread {
            var connection: HttpURLConnection? = null
            val boundary = "----${System.currentTimeMillis()}----"
            val lineEnd = "\r\n" // CORRECTED: Use actual CRLF
            val twoHyphens = "--"

            try {
                val file = File(filePath)
                if (!file.exists()) {
                    Log.e("ProfileActivity", "File does not exist at path: $filePath")
                    runOnUiThread { Toast.makeText(this, "El archivo de imagen no existe.", Toast.LENGTH_SHORT).show() }
                    return@thread
                }
                val fileInputStream = FileInputStream(file)
                val url = URL("$API_URL/uploads/profileImage") // Updated endpoint for multiple images
                connection = url.openConnection() as HttpURLConnection

                // Connection properties
                connection.doInput = true
                connection.doOutput = true
                connection.useCaches = false
                connection.requestMethod = "POST"
                connection.setRequestProperty("Connection", "Keep-Alive")
                connection.setRequestProperty("ENCTYPE", "multipart/form-data")
                connection.setRequestProperty("Content-Type", "multipart/form-data;boundary=$boundary")
                // Crucially, add the user ID header
                connection.setRequestProperty("X-User-ID", userId.toString())
                // Indicate if this should be the main image
                connection.setRequestProperty("X-Set-As-Main", (profileImages.isEmpty()).toString())

                val dos = DataOutputStream(connection.outputStream)

                // File part
                dos.writeBytes(twoHyphens + boundary + lineEnd)
                // Use 'image' as the field name, matching Multer config on backend
                dos.writeBytes("Content-Disposition: form-data; name=\"image\";filename=\"${file.name}\"" + lineEnd) // Use simple \" for quotes
                dos.writeBytes("Content-Type: ${contentResolver.getType(imageUri)}" + lineEnd) // Dynamically get content type
                dos.writeBytes(lineEnd)

                // Read file data
                var bytesAvailable = fileInputStream.available()
                val bufferSize = Math.min(bytesAvailable, 1 * 1024 * 1024) // 1MB buffer
                val buffer = ByteArray(bufferSize)
                var bytesRead = fileInputStream.read(buffer, 0, bufferSize)

                while (bytesRead > 0) {
                    dos.write(buffer, 0, bytesRead)
                    bytesAvailable = fileInputStream.available()
                    bytesRead = fileInputStream.read(buffer, 0, Math.min(bytesAvailable, bufferSize))
                }

                // End of file data requires CRLF before the final boundary
                dos.writeBytes(lineEnd)

                // End of multipart
                dos.writeBytes(twoHyphens + boundary + twoHyphens + lineEnd)

                fileInputStream.close()
                dos.flush()
                dos.close()

                // Get response
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream = connection.inputStream
                    val response = inputStream.bufferedReader().use { it.readText() }
                    Log.i("ProfileActivity", "Image uploaded successfully. Response: $response")
                    val jsonResponse = JSONObject(response)

                    if (jsonResponse.optBoolean("success", false)) {
                        // Get the relative path from response
                        val newImagePath = jsonResponse.optString("url", null)
                        val isMain = jsonResponse.optBoolean("isMain", false)

                        if (newImagePath != null) {
                            runOnUiThread {
                                // Add the new image to our list
                                profileImageAdapter.addImage(newImagePath)

                                // If this is the first image or set as main, update mainImageUrl
                                if (isMain || profileImages.isEmpty()) {
                                    mainImageUrl = newImagePath
                                    profileImageAdapter.setMainImagePosition(profileImages.size - 1)
                                }

                                // Show feedback
                                Toast.makeText(this, "Imagen subida correctamente", Toast.LENGTH_SHORT).show()

                                // Update the Add Image FAB visibility
                                if (isEditMode && profileImages.size >= MAX_PROFILE_IMAGES) {
                                    fabAddImage.visibility = View.GONE
                                }
                            }
                        } else {
                            Log.e("ProfileActivity", "Upload response missing 'url'. Response: $response")
                            runOnUiThread { Toast.makeText(this, "Error: Respuesta de subida inválida.", Toast.LENGTH_SHORT).show() }
                        }
                    } else {
                        val errorMessage = jsonResponse.optString("message", "Error desconocido")
                        Log.e("ProfileActivity", "Upload failed: $errorMessage")
                        runOnUiThread { Toast.makeText(this, errorMessage, Toast.LENGTH_SHORT).show() }
                    }
                } else {
                    val errorStream = connection.errorStream
                    val errorResponse = errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e("ProfileActivity", "Image upload failed. Code: $responseCode - $errorResponse")
                    runOnUiThread { Toast.makeText(this, "Error al subir imagen: $responseCode", Toast.LENGTH_SHORT).show() }
                }
            } catch (e: Exception) {
                Log.e("ProfileActivity", "Exception during image upload", e)
                runOnUiThread { Toast.makeText(this, "Error subiendo imagen: ${e.message}", Toast.LENGTH_SHORT).show() }
            } finally {
                connection?.disconnect()
                // Hide progress indicator
            }
        }
    }

    private fun revertAvatarImage() {
        runOnUiThread {
            // Remove the old avatar loading code that uses imageViewAvatar
            // and instead update the viewPager if needed
            if (!avatarUrl.isNullOrEmpty() && avatarUrl != "null") {
                // For now we'll just reload the profile to keep it simple
                loadProfile(userId)
            }
        }
    }

    private fun getPathFromUri(uri: Uri): String? {
        var path: String? = null
        val projection = arrayOf(MediaStore.Images.Media.DATA)
        try {
            val cursor = contentResolver.query(uri, projection, null, null, null)
            cursor?.use { // Use 'use' for automatic closing
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                    path = it.getString(columnIndex)
                } else {
                    Log.w("ProfileActivity", "Cursor is empty for URI: $uri")
                }
            } ?: Log.e("ProfileActivity", "ContentResolver query returned null cursor for URI: $uri")
        } catch (e: Exception) {
            Log.e("ProfileActivity", "Error getting path from URI: $uri", e)
        }
        if (path == null) {
            Log.w("ProfileActivity", "Path resolved to null. Trying alternative for some URIs...")
            // Fallback for some cases like content://com.android.providers.downloads.documents/document/msf:XXXX
            path = FileUtils.getPath(this, uri) // Needs a FileUtils helper class
            if(path != null) {
                Log.d("ProfileActivity", "Path obtained via FileUtils: $path")
            } else {
                Log.e("ProfileActivity", "Path still null after FileUtils fallback.")
            }
        }
        return path
    }

    private fun setupBottomNavigation(loggedInUserId: Int) {
        try {
            bottomNavView.setOnItemSelectedListener { item ->
                try {
                    val currentUserId = loggedInUserId
                    Log.d(TAG, "Bottom nav clicked, item ID: ${item.itemId}, title: ${item.title}, currentUserId: $currentUserId")

                    when (item.itemId) {
                        R.id.navigation_swipe -> {
                            try {
                                Log.d(TAG, "Navigating to SwipeActivity")
                                val intent = Intent(this, SwipeActivity::class.java)
                                intent.putExtra("USER_ID", currentUserId)
                                intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                                startActivity(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error navigating to SwipeActivity", e)
                                Toast.makeText(this, "Error al navegar a Descubrir", Toast.LENGTH_SHORT).show()
                            }
                            true
                        }
                        R.id.navigation_matches -> {
                            try {
                                Log.d(TAG, "Navigating to MatchesActivity for item ID: ${item.itemId}")
                                val intent = Intent(this, MatchesActivity::class.java)
                                intent.putExtra("USER_ID", currentUserId)
                                intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                                startActivity(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error navigating to MatchesActivity", e)
                                Toast.makeText(this, "Error al navegar a Matches", Toast.LENGTH_SHORT).show()
                            }
                            true
                        }
                        R.id.navigation_community -> {
                            try {
                                Log.d(TAG, "Showing community dropdown menu")
                                // Show community dropdown menu
                                val communityMenu = CommunityDropdownMenu(this, currentUserId)
                                communityMenu.show(bottomNavView)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error showing community dropdown menu", e)
                                Toast.makeText(this, "Error al mostrar el menú de comunidad", Toast.LENGTH_SHORT).show()
                            }
                            false // Return false to prevent the tab from being selected
                        }
                        R.id.navigation_chats -> { // Navigate to ChatListActivity
                            try {
                                Log.d(TAG, "Navigating to ChatListActivity for item ID: ${item.itemId}")
                                val intent = Intent(this, ChatListActivity::class.java)
                                intent.putExtra("USER_ID", currentUserId)
                                intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
                                startActivity(intent)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error navigating to ChatListActivity", e)
                                Toast.makeText(this, "Error al navegar a Chats", Toast.LENGTH_SHORT).show()
                            }
                            true
                        }
                        R.id.navigation_profile -> {
                            try {
                                if (userId != currentUserId || intent.getBooleanExtra("VIEW_ONLY_MODE", false)) {
                                    Log.d(TAG, "Navigating to own ProfileActivity")
                                    val intent = Intent(this, ProfileActivity::class.java)
                                    intent.putExtra("USER_ID", currentUserId)
                                    intent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT or Intent.FLAG_ACTIVITY_CLEAR_TOP
                                    startActivity(intent)
                                } else {
                                    Log.d(TAG, "Already viewing own profile, doing nothing.")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Error navigating to ProfileActivity", e)
                                Toast.makeText(this, "Error al navegar a Perfil", Toast.LENGTH_SHORT).show()
                            }
                            true
                        }
                        else -> false
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in bottom navigation", e)
                    Toast.makeText(this, "Error en la navegación", Toast.LENGTH_SHORT).show()
                    false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up bottom navigation", e)
            Toast.makeText(this, "Error al configurar la navegación", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onResume() {
        super.onResume()
        // Ensure the correct item is selected based on whether viewing own profile or not
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val loggedInUserId = prefs.getInt("USER_ID", 0)
        val isOwnProfile = userId == loggedInUserId && !intent.getBooleanExtra("VIEW_ONLY_MODE", false)
        val targetItemId = if (isOwnProfile) R.id.navigation_profile else R.id.navigation_swipe // Keep highlighting swipe if viewing others

        if (bottomNavView.selectedItemId != targetItemId) {
             bottomNavView.selectedItemId = targetItemId
        }

        // Always reload the profile when resuming to ensure we have the latest data
        Log.d(TAG, "onResume: Reloading profile for user ID: $userId")
        loadProfile(userId)

        // If viewing someone else's profile
        if (userId != loggedInUserId && userId > 0) {
            // Check status immediately when activity comes to foreground
            checkUserStatus(userId)
            startStatusUpdates(userId)

            // Check if the viewed user is blocked every time we resume the activity
            checkIfUserIsBlocked()
        }
    }

    private fun loadProfile(userId: Int) {
        // Show loading indicator or progress bar
        val loadingView = findViewById<ProgressBar>(R.id.progressBar)
        loadingView?.visibility = View.VISIBLE

        // Log the user ID we're loading
        Log.d(TAG, "Loading profile for user ID: $userId")

        thread {
            var connection: HttpURLConnection? = null
            try {
                val url = URL("$API_URL/profile/profile/$userId") // Updated with correct path
                connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000 // 10 seconds
                connection.readTimeout = 15000 // 15 seconds

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream = connection.inputStream
                    val response = inputStream.bufferedReader().use { it.readText() }
                    val responseObject = JSONObject(response) // Parse the entire response
                    Log.i("ProfileActivity", "Profile data received: ${responseObject.toString(2)}")
                    if (responseObject.optBoolean("success", false)) {
                        val user = responseObject.optJSONObject("user") // Get the nested "user" object
                        if (user != null) {
                            // Ensure we load the "role" field to check admin status
                            if (!user.has("role")) {
                                // Make an additional request to fetch admin status if needed
                                checkUserRole(userId, user)
                            } else {
                                runOnUiThread {
                                    populateUI(user)
                                    loadingView?.visibility = View.GONE

                                    // Check if viewing someone else's profile
                                    val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
                                    val loggedInUserId = prefs.getInt("USER_ID", 0)

                                    if (userId != loggedInUserId) {
                                        // Check if the user is blocked
                                        checkIfUserIsBlocked()
                                    }

                                    // Siempre intentamos obtener la información más reciente del radar
                                    // después de cargar el perfil, independientemente de cómo se accedió
                                    // Hacemos esto con un pequeño retraso para que primero se muestre el perfil
                                    Handler(Looper.getMainLooper()).postDelayed({
                                        fetchLocationFromRadar(userId)
                                    }, 500)
                                }
                            }
                        } else {
                            Log.e("ProfileActivity", "User object is null in response")
                            runOnUiThread {
                                Toast.makeText(this, "Error: Datos de usuario no encontrados en la respuesta.", Toast.LENGTH_SHORT).show()
                                loadingView?.visibility = View.GONE
                            }
                        }
                    } else {
                        Log.e("ProfileActivity", "Response success is false or missing")
                        val message = responseObject.optString("message", "Error al cargar perfil.")
                        runOnUiThread {
                            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
                            loadingView?.visibility = View.GONE
                        }
                    }
                } else if (responseCode == HttpURLConnection.HTTP_NOT_FOUND) {
                    Log.w("ProfileActivity", "Profile not found for user $userId. Backend might need POST /profile.")
                    // Handle case where profile doesn't exist (e.g., show defaults, maybe prompt creation)
                    runOnUiThread {
                        Toast.makeText(this, "Perfil no encontrado.", Toast.LENGTH_SHORT).show()
                        // Populate with defaults or placeholders
                        populateUI(JSONObject()) // Pass empty object for defaults
                        loadingView?.visibility = View.GONE
                    }
                } else {
                    Log.e("ProfileActivity", "Failed to load profile. HTTP Code: $responseCode - ${connection.responseMessage}")
                    val errorStream = connection.errorStream
                    val errorResponse = errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e("ProfileActivity", "Error details: $errorResponse")
                    runOnUiThread {
                        Toast.makeText(this, "Error al cargar el perfil: ${connection.responseMessage}", Toast.LENGTH_SHORT).show()
                        loadingView?.visibility = View.GONE
                    }
                }
            } catch (e: Exception) {
                Log.e("ProfileActivity", "Error loading profile", e)
                runOnUiThread {
                    Toast.makeText(this, "Error de red al cargar perfil: ${e.message}", Toast.LENGTH_SHORT).show()
                    loadingView?.visibility = View.GONE
                }
            } finally {
                connection?.disconnect()
            }
        }
    }

    private fun populateUI(user: JSONObject) {
        val username = user.optString("username", "Usuario")
        val birthdateStr = user.optString("birthdate", null)
        val pronouns = user.optString("pronouns", "")
        val bio = user.optString("bio", "")
        val jobTitle = user.optString("job_title", "")
        val school = user.optString("school", "")
        val location = user.optString("location", "")
        val favoriteGame = user.optString("favorite_game", "")
        val favoritePlatform = user.optString("favorite_platform", "")
        val lookingFor = user.optString("looking_for", "")
        val gender = user.optString("gender", "")
        val sexuality = user.optString("sexuality", "")
        val role = user.optString("role", "")

        // Extract city and country fields
        val city = user.optString("city", "")
        val country = user.optString("country", "")

        // Log all fields for debugging
        Log.d(TAG, "JSON user data: ${user.toString(2)}")
        Log.d(TAG, "Sexuality from server: '$sexuality'")
        Log.d(TAG, "Gender from server: '$gender'")
        Log.d(TAG, "City from server: '$city', Country: '$country'")
        Log.d(TAG, "Location from server: '$location'")

        Log.d(TAG, "Sexuality from server: '$sexuality'")
        Log.d(TAG, "Gender from server: '$gender'")
        Log.d(TAG, "City from server: '$city', Country: '$country'")
        val isAdmin = role == "admin"

        // Process avatar and profile images
        mainImageUrl = null
        profileImages.clear()

        // Get profile images array if available
        val profileImagesArray = user.optJSONArray("profile_images")
        var mainImageIndex = 0

        if (profileImagesArray != null && profileImagesArray.length() > 0) {
            // Process all profile images
            for (i in 0 until profileImagesArray.length()) {
                try {
                    val imageObj = profileImagesArray.optJSONObject(i)
                    val imagePath = imageObj?.optString("url")?.replace("\\/", "/")
                    val isMain = imageObj?.optBoolean("isMain", false) ?: false

                    if (!imagePath.isNullOrEmpty() && imagePath != "null") {
                        profileImages.add(imagePath)

                        // Keep track of main image
                        if (isMain) {
                            mainImageUrl = imagePath
                            mainImageIndex = i
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing profile image at index $i", e)
                }
            }
        } else {
            // Backward compatibility: handle single avatar_url
            val rawAvatarUrl = user.optString("avatar_url", null)
            val avatarUrl = rawAvatarUrl?.replace("\\/", "/")?.takeUnless { it == "null" || it.isNullOrEmpty() }

            if (!avatarUrl.isNullOrEmpty()) {
                profileImages.add(avatarUrl)
                mainImageUrl = avatarUrl
            }
        }

        // If we found any images, update the adapter
        if (profileImages.isNotEmpty()) {
            profileImageAdapter.notifyDataSetChanged()

            if (mainImageIndex < profileImages.size) {
                viewPagerImages.currentItem = mainImageIndex
            }

            // Show/hide buttons based on mode
            buttonSetAsMain.visibility = if (isEditMode) View.VISIBLE else View.GONE
        }

        // Apply admin styling - moved to a single implementation and enhanced for consistency
        if (isAdmin) {
            textViewAdminBadge.visibility = View.VISIBLE

            // Apply red color to admin username with gradient effect
            val textColor = getColor(android.R.color.holo_red_light)
            textViewUsername.setTextColor(textColor)

            // Remove any background that might interfere with text color
            textViewUsername.background = null

            // Set a tag to mark this user as admin for reference elsewhere
            textViewUsername.tag = "admin"
        } else {
            textViewAdminBadge.visibility = View.GONE
            textViewUsername.setTextColor(getColor(R.color.comfy_blue))
            textViewUsername.background = null
            textViewUsername.tag = null
        }

        // Check verification status and show badge
        checkVerificationStatus(userId)

        // Handle visibility of block, report, and send message buttons
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val loggedInUserId = prefs.getInt("USER_ID", 0)

        // Check if viewing own profile
        if (userId == loggedInUserId) {
            // Hide block, report, and send message buttons when viewing own profile
            buttonBlockUser.visibility = View.GONE
            buttonReportUser.visibility = View.GONE
            buttonSendMessage.visibility = View.GONE
        } else if (isAdmin) {
            // Hide block and report buttons for admin profiles
            buttonBlockUser.visibility = View.GONE
            buttonReportUser.visibility = View.GONE
            buttonSendMessage.visibility = View.VISIBLE
        } else {
            // Show block and report buttons for non-admin profiles
            buttonBlockUser.visibility = View.VISIBLE
            buttonReportUser.visibility = View.VISIBLE
            buttonSendMessage.visibility = View.VISIBLE
        }

        // Fill edit mode fields for profile editing
        if (isEditMode) {
            // ... existing code ...
        }

        // Update the adapter with the new images
        profileImageAdapter.notifyDataSetChanged()

        // Set the main image position
        if (mainImageUrl != null) {
            profileImageAdapter.setMainImagePosition(mainImageIndex)
        }

        // Show feedback if no images found
        if (profileImages.isEmpty()) {
            // Add a placeholder if needed
            val placeholder = "/images/default_profile.jpg" // Assuming backend has this
            profileImages.add(placeholder)
            mainImageUrl = placeholder
            profileImageAdapter.notifyDataSetChanged()
        }

        // Calculate Age
        val age = calculateAge(birthdateStr)
        val ageText = if (age != null) age.toString() else ""

        // Populate View Mode Elements - Revert to original behavior for username and age
        if (ageText.isNotEmpty()) {
            textViewUsername.text = "$username, $ageText"
        } else {
            textViewUsername.text = username
        }
        textViewAge.text = "" // Clear textViewAge as it was before

        // Set textViewBirthdateValue to show the actual birthdate in Spanish format
        textViewBirthdateValue.text = formatBirthdateForDisplay(birthdateStr)

        // Continue with existing population logic but add check for "null" string
        textViewPronounsValue.text = if (pronouns.isEmpty() || pronouns == "null") "Por completar" else pronouns
        textViewBioValue.text = if (bio.isEmpty() || bio == "null") "Por completar" else bio
        textViewJobValue.text = if (jobTitle.isEmpty() || jobTitle == "null") "Por completar" else jobTitle
        textViewSchoolValue.text = if (school.isEmpty() || school == "null") "Por completar" else school

        // Format and display location with city and country if available
        val formattedLocation = formatLocation(location, city, country)
        textViewLocationValue.text = if (formattedLocation.isEmpty()) "Por completar" else formattedLocation

        textViewFavGameValue.text = if (favoriteGame.isEmpty() || favoriteGame == "null") "Por completar" else favoriteGame
        textViewFavPlatformValue.text = if (favoritePlatform.isEmpty() || favoritePlatform == "null") "Por completar" else favoritePlatform
        textViewLookingForValue.text = if (lookingFor.isEmpty() || lookingFor == "null") "Por completar" else lookingFor
        textViewGenderValue.text = if (gender.isEmpty() || gender == "null") "Por completar" else gender
        textViewSexualityValue.text = if (sexuality.isEmpty() || sexuality == "null") "Por completar" else sexuality

        // Mostrar la información de ciudad y país cuando están disponibles
        val cityCountryText = when {
            !city.isNullOrEmpty() && !country.isNullOrEmpty() -> "$city, $country"
            !city.isNullOrEmpty() -> city
            !country.isNullOrEmpty() -> country
            else -> ""
        }
        Log.d(TAG, "City: '$city', Country: '$country', Formatted as: '$cityCountryText'")

        // Verificar si ya tenemos un valor configurado (que podría venir del intent)
        val currentCityCountryText = textViewCityCountryValue.text.toString()
        if (currentCityCountryText.isNotEmpty() && currentCityCountryText != "Por completar") {
            // Ya tenemos un valor configurado desde el intent, no lo sobreescribimos
            Log.d(TAG, "Manteniendo valor existente de ciudad/país: '$currentCityCountryText'")
        } else {
            // No hay un valor configurado previamente

            // 1. Intentar usar los datos del perfil si están disponibles
            if (!cityCountryText.isNullOrEmpty()) {
                textViewCityCountryValue.text = cityCountryText

                // Guardar estos datos en el cache
                saveUserLocation(userId, city, country)
            } else {
                // 2. Intentar cargar del cache si no hay datos en el perfil
                val cachedLocation = loadUserLocation(userId)
                if (cachedLocation != null) {
                    val (cachedCity, cachedCountry) = cachedLocation
                    if (!cachedCity.isNullOrEmpty() || !cachedCountry.isNullOrEmpty()) {
                        val cachedText = when {
                            !cachedCity.isNullOrEmpty() && !cachedCountry.isNullOrEmpty() -> "$cachedCity, $cachedCountry"
                            !cachedCity.isNullOrEmpty() -> cachedCity
                            !cachedCountry.isNullOrEmpty() -> cachedCountry
                            else -> ""
                        }

                        textViewCityCountryValue.text = cachedText
                        Log.d(TAG, "Ubicación cargada del cache: '$cachedText'")
                    } else {
                        // No hay datos en el cache, mostrar "Por completar"
                        textViewCityCountryValue.text = "Por completar"
                    }
                } else {
                    // No hay datos en el cache, mostrar "Por completar"
                    textViewCityCountryValue.text = "Por completar"
                }

                // 3. Siempre intentar obtener los datos más recientes del radar
                fetchLocationFromRadar(userId)
            }
        }
          // Siempre intentar actualizar los datos desde el radar, pero con un pequeño retraso
        // para que no afecte a la carga inicial de la interfaz
        Handler(Looper.getMainLooper()).postDelayed({
            fetchLocationFromRadar(userId)
        }, 500)

        // Process and display level/experience information
        val levelInfo = user.optJSONObject("level_info")
        if (levelInfo != null) {
            val level = levelInfo.optInt("level", 1)
            val totalXP = levelInfo.optInt("total_xp", 0)
            val currentLevelXP = levelInfo.optInt("current_level_xp", 0)
            val xpForNextLevel = levelInfo.optInt("xp_for_next_level", 100)
            val progressPercent = levelInfo.optInt("progress_percentage", 0)

            Log.d(TAG, "Level info: level=$level, currentXP=$currentLevelXP, nextLevelXP=$xpForNextLevel, totalXP=$totalXP")

            // Update UI elements
            textViewUserLevel.text = "Nivel $level"
            textViewUserXP.text = "XP: $currentLevelXP/$xpForNextLevel"
            progressBarUserLevel.max = xpForNextLevel
            progressBarUserLevel.progress = currentLevelXP
        } else {
            Log.w(TAG, "No level_info found in user data")
            // Set default values
            textViewUserLevel.text = "Nivel 1"
            textViewUserXP.text = "XP: 0/300"
            progressBarUserLevel.max = 300
            progressBarUserLevel.progress = 0
        }

        // Populate Edit Mode Elements - also check for "null" string
        editTextPronouns.setText(if (pronouns == "null") "" else pronouns)
        editTextBio.setText(if (bio == "null") "" else bio)
        editTextJob.setText(if (jobTitle == "null") "" else jobTitle)
        editTextSchool.setText(if (school == "null") "" else school)
        editTextLocation.setText(if (location == "null") "" else location)
        editTextFavoriteGame.setText(if (favoriteGame == "null") "" else favoriteGame)
        editTextFavoritePlatform.setText(if (favoritePlatform == "null") "" else favoritePlatform)
        editTextLookingFor.setText(if (lookingFor == "null") "" else lookingFor)
        editTextGender.setText(if (gender == "null") "" else gender)
        editTextSexuality.setText(if (sexuality == "null") "" else sexuality)
        // Format birthdate for EditText (assuming YYYY-MM-DD from DB)
        val displayBirthdate = formatBirthdateForInput(birthdateStr)
        editTextBirthdate.setText(displayBirthdate)

        // Ensure UI starts in the correct mode state visually
        if (isEditMode) {
            switchToEditMode()
        } else {
            switchToViewMode()
        }

        // Load personality traits
        loadPersonalityTraits()
    }

    private fun calculateAge(birthdateString: String?): Int? {
        if (birthdateString.isNullOrEmpty() || birthdateString == "null") {
            return null
        }
        return try {
            // Try parsing common formats, prioritize ISO YYYY-MM-DD
            val formatter = DateTimeFormatter.ISO_LOCAL_DATE // Handles YYYY-MM-DD
            val birthDate = LocalDate.parse(birthdateString.substringBefore("T"), formatter) // Handle potential timestamp
            Period.between(birthDate, LocalDate.now()).years
        } catch (e: DateTimeParseException) {
            Log.w("ProfileActivity", "Could not parse birthdate: $birthdateString", e)
            null // Return null if parsing fails
        } catch (e: Exception) {
            Log.e("ProfileActivity", "Error calculating age from: $birthdateString", e)
            null
        }
    }

    private fun formatBirthdateForInput(birthdateString: String?): String {
        if (birthdateString.isNullOrEmpty() || birthdateString == "null") return ""
        return try {
            // Just return the date part if it has a timestamp
            val datePart = birthdateString.substringBefore("T")

            // Log the date part for debugging
            Log.d(TAG, "formatBirthdateForInput - Date part: $datePart")

            datePart
        } catch (e: Exception) {
            Log.w("ProfileActivity", "Could not format birthdate string: $birthdateString", e)
            "" // Return empty if formatting fails
        }
    }

    private fun formatBirthdateForDisplay(birthdateString: String?): String {
        if (birthdateString.isNullOrEmpty() || birthdateString == "null") return "Por completar"

        return try {
            // Parse the ISO date (YYYY-MM-DD)
            val datePart = birthdateString.substringBefore("T") // Remove time part if exists

            // Log the date part for debugging
            Log.d(TAG, "formatBirthdateForDisplay - Date part: $datePart")

            // Parse with LocalDate but add a timezone adjustment to prevent one-day shifts
            val parts = datePart.split("-")
            if (parts.size != 3) {
                return datePart // Return as-is if not in expected format
            }

            // Extract year, month, day directly from the string parts to avoid timezone issues
            val year = parts[0].toInt()
            val month = parts[1].toInt()
            val day = parts[2].toInt()

            // Log the extracted parts for debugging
            Log.d(TAG, "formatBirthdateForDisplay - Year: $year, Month: $month, Day: $day")

            // Create a formatter for "dd de MMMM, yyyy" in Spanish
            val spanishMonths = mapOf(
                1 to "Enero", 2 to "Febrero", 3 to "Marzo", 4 to "Abril",
                5 to "Mayo", 6 to "Junio", 7 to "Julio", 8 to "Agosto",
                9 to "Septiembre", 10 to "Octubre", 11 to "Noviembre", 12 to "Diciembre"
            )

            // Format as "21 de Enero, 1996"
            val monthName = spanishMonths[month] ?: ""
            val formattedDate = "$day de $monthName, $year"

            // Log the formatted date for debugging
            Log.d(TAG, "formatBirthdateForDisplay - Formatted date: $formattedDate")

            formattedDate
        } catch (e: Exception) {
            Log.w("ProfileActivity", "Could not format birthdate for display: $birthdateString", e)
            birthdateString.substringBefore("T") // Fallback to original date without time
        }
    }

    private fun logoutUser() {
        Log.d(TAG, "logoutUser() method called")

        // Clear SharedPreferences
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE).edit()
        prefs.remove("USER_ID")
        prefs.remove("fcm_token") // Remove FCM token to ensure a clean logout
        // prefs.remove("AUTH_TOKEN") // If you use tokens
        prefs.apply()

        Log.d(TAG, "User preferences cleared")

        // Navigate to Login screen (Assuming LoginActivity exists)
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish() // Close ProfileActivity
    }

    private fun showDatePickerDialog() {
        val calendar = Calendar.getInstance()
        // Try parsing the current date text to pre-fill the picker
        val currentText = editTextBirthdate.text.toString()
        if (currentText.isNotEmpty()) {
            try {
                // Use the same format you expect to display/save
                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                sdf.timeZone = TimeZone.getTimeZone("UTC") // Use UTC to avoid timezone issues
                val date = sdf.parse(currentText)
                if (date != null) {
                    calendar.time = date
                }
            } catch (e: Exception) {
                Log.e(TAG, "Could not parse date from editTextBirthdate: $currentText", e)
                // Keep calendar as default (today) if parsing fails
            }
        } else {
             // Default to 18 years ago if empty
            calendar.add(Calendar.YEAR, -18)
        }


        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = DatePickerDialog(
            this,
            { _, selectedYear, selectedMonth, selectedDayOfMonth ->
                // Create a date with the selected values
                // Use UTC calendar to avoid timezone issues
                val selectedDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"))
                selectedDate.set(Calendar.YEAR, selectedYear)
                selectedDate.set(Calendar.MONTH, selectedMonth)
                selectedDate.set(Calendar.DAY_OF_MONTH, selectedDayOfMonth)
                selectedDate.set(Calendar.HOUR_OF_DAY, 12) // Set to noon to avoid timezone issues
                selectedDate.set(Calendar.MINUTE, 0)
                selectedDate.set(Calendar.SECOND, 0)
                selectedDate.set(Calendar.MILLISECOND, 0)

                // Format the date as yyyy-MM-dd
                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                sdf.timeZone = TimeZone.getTimeZone("UTC") // Use UTC to avoid timezone issues
                val formattedDate = sdf.format(selectedDate.time)

                // Log the selected date for debugging
                Log.d(TAG, "Selected date: $selectedYear-${selectedMonth+1}-$selectedDayOfMonth, Formatted: $formattedDate")

                editTextBirthdate.setText(formattedDate)
                editTextBirthdate.error = null // Clear error after selection
            },
            year,
            month,
            day
        )

        // Optional: Set max date (e.g., today) or min date
        datePickerDialog.datePicker.maxDate = System.currentTimeMillis() // Cannot select future date

        datePickerDialog.show()
    }

    object FileUtils {
        fun getPath(context: android.content.Context, uri: Uri): String? {
            // Basic implementation - might need more robust handling from online examples
            // DocumentProvider
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && android.provider.DocumentsContract.isDocumentUri(context, uri)) {
                // ExternalStorageProvider
                if (isExternalStorageDocument(uri)) {
                    val docId = android.provider.DocumentsContract.getDocumentId(uri)
                    val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    val type = split[0]

                    if ("primary".equals(type, ignoreCase = true)) {
                        return "${android.os.Environment.getExternalStorageDirectory()}/${split[1]}"
                    }
                }
                // DownloadsProvider
                else if (isDownloadsDocument(uri)) {
                    try {
                        val id = android.provider.DocumentsContract.getDocumentId(uri)
                        if (id != null && id.startsWith("raw:")) {
                            return id.substring(4)
                        }

                        val contentUriPrefixesToTry = arrayOf(
                            "content://downloads/public_downloads",
                            "content://downloads/my_downloads",
                            "content://downloads/all_downloads"
                        )

                        for (contentUriPrefix in contentUriPrefixesToTry) {
                            val contentUri = android.content.ContentUris.withAppendedId(Uri.parse(contentUriPrefix), id.toLong())
                            try {
                                val path = getDataColumn(context, contentUri, null, null)
                                if (path != null) {
                                    return path
                                }
                            } catch (e: Exception) {
                                // Ignore exception and try the next prefix
                            }
                        }
                        // Path could not be retrieved using ContentResolver, trying alternative method...
                        // return getPathFromSavingTempFile(context, uri) // Last resort if needed
                    } catch (e: NumberFormatException) {
                        Log.e("FileUtils", "Downloads provider ID is not a long: ${android.provider.DocumentsContract.getDocumentId(uri)}")
                        return null
                    } catch(e: Exception) {
                        Log.e("FileUtils", "Error getting path from DownloadsProvider", e)
                        return null
                    }
                }
                // MediaProvider
                else if (isMediaDocument(uri)) {
                    val docId = android.provider.DocumentsContract.getDocumentId(uri)
                    val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    val type = split[0]

                    var contentUri: Uri? = null
                    when (type) {
                        "image" -> contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                        "video" -> contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                        "audio" -> contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                    }

                    val selection = "_id=?"
                    val selectionArgs = arrayOf(split[1])

                    return getDataColumn(context, contentUri, selection, selectionArgs)
                }
            }
            // MediaStore (and general)
            else if ("content".equals(uri.scheme, ignoreCase = true)) {
                // Return the remote address
                return if (isGooglePhotosUri(uri)) uri.lastPathSegment else getDataColumn(context, uri, null, null)
            }
            // File
            else if ("file".equals(uri.scheme, ignoreCase = true)) {
                return uri.path
            }

            return null
        }

        private fun getDataColumn(context: android.content.Context, uri: Uri?, selection: String?, selectionArgs: Array<String>?): String? {
            var cursor: android.database.Cursor? = null
            val column = "_data"
            val projection = arrayOf(column)

            try {
                cursor = context.contentResolver.query(uri!!, projection, selection, selectionArgs, null)
                if (cursor != null && cursor.moveToFirst()) {
                    val index = cursor.getColumnIndexOrThrow(column)
                    return cursor.getString(index)
                }
            } finally {
                cursor?.close()
            }
            return null
        }

        private fun isExternalStorageDocument(uri: Uri): Boolean {
            return "com.android.externalstorage.documents" == uri.authority
        }

        private fun isDownloadsDocument(uri: Uri): Boolean {
            return "com.android.providers.downloads.documents" == uri.authority
        }

        private fun isMediaDocument(uri: Uri): Boolean {
            return "com.android.providers.media.documents" == uri.authority
        }

        private fun isGooglePhotosUri(uri: Uri): Boolean {
            return "com.google.android.apps.photos.content" == uri.authority
        }
    }

    // New method to start periodic status updates
    private fun startStatusUpdates(userId: Int) {
        if (userId <= 0) {
            return
        }

        statusRunnable = object : Runnable {
            override fun run() {
                checkUserStatus(userId)
                statusHandler.postDelayed(this, statusCheckInterval)
            }
        }

        // Start immediately
        statusRunnable?.let {
            statusHandler.post(it)
        }
    }

    // Stop status updates to prevent memory leaks
    private fun stopStatusUpdates() {
        statusRunnable?.let {
            statusHandler.removeCallbacks(it)
        }
    }

    // Check user online status
    private fun checkUserStatus(userId: Int) {
        val now = System.currentTimeMillis()
        // Avoid too frequent calls
        if (now - lastStatusCheck < 5000) return

        lastStatusCheck = now

        val statusUrl = "$API_URL/user/status/$userId"
        Log.d(TAG, "Checking user status at: $statusUrl")

        val request = Request.Builder()
            .url(statusUrl)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error checking user status", e)
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    if (response.isSuccessful) {
                        val responseBody = response.body?.string()

                        if (responseBody != null) {
                            // Log the raw response
                            Log.d(TAG, "Status response: $responseBody")

                            // Parse the response
                            val jsonResponse = JSONObject(responseBody)
                            val success = jsonResponse.optBoolean("success", false)

                            if (success) {
                                val online = jsonResponse.optBoolean("online", false)
                                val lastSeen = if (jsonResponse.has("lastSeen") && !jsonResponse.isNull("lastSeen"))
                                    jsonResponse.getString("lastSeen") else null

                                Log.d(TAG, "Parsed status: online=$online, lastSeen=$lastSeen")

                                // Update UI on main thread
                                runOnUiThread {
                                    updateStatusDisplay(online, lastSeen)
                                }
                            } else {
                                Log.e(TAG, "Status response indicated failure")
                            }
                        } else {
                            Log.e(TAG, "Empty response body from status endpoint")
                        }
                    } else {
                        Log.e(TAG, "Failed to get user status: ${response.code} - ${response.message}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing status response", e)
                }
            }
        })
    }

    // Format and display the status
    private fun updateStatusDisplay(online: Boolean, lastSeen: String?) {
        if (online) {
            // User is online
            textViewOnlineStatus.text = "En línea"
            textViewOnlineStatus.setTextColor(ContextCompat.getColor(this, android.R.color.holo_green_light))
        } else {
            // User is offline, format last seen time
            try {
                val lastSeenTime = if (!lastSeen.isNullOrEmpty()) {
                    Instant.parse(lastSeen)
                } else {
                    null
                }

                if (lastSeenTime != null) {
                    val now = Instant.now()
                    val diffMinutes = ChronoUnit.MINUTES.between(lastSeenTime, now)
                    val diffHours = ChronoUnit.HOURS.between(lastSeenTime, now)
                    val diffDays = ChronoUnit.DAYS.between(lastSeenTime, now)

                    val formattedTime = when {
                        diffMinutes < 1 -> "Último acceso: hace un momento"
                        diffMinutes == 1L -> "Último acceso: hace 1 minuto"
                        diffMinutes < 60 -> "Último acceso: hace $diffMinutes minutos"
                        diffHours == 1L -> "Último acceso: hace 1 hora"
                        diffHours < 24 -> "Último acceso: hace $diffHours horas"
                        diffDays == 1L -> "Último acceso: hace 1 día"
                        else -> "Último acceso: hace $diffDays días"
                    }

                    textViewOnlineStatus.text = formattedTime
                } else {
                    textViewOnlineStatus.text = "No disponible"
                }

                textViewOnlineStatus.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
            } catch (e: Exception) {
                Log.e(TAG, "Error formatting last seen time", e)
                textViewOnlineStatus.text = "No disponible"
                textViewOnlineStatus.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
            }
        }
    }

    // Clean up resources in onPause and onResume
    override fun onPause() {
        super.onPause()
        stopStatusUpdates()
    }

    // Menu handling
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.profile_menu, menu)

        // Get the logout menu item and check for admin
        val blockMenuItem = menu.findItem(R.id.menu_block_user)

        // Get current user ID from shared preferences
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val currentUserId = sharedPreferences.getInt("USER_ID", -1)

        // Check if the viewed user is an admin
        val isViewedUserAdmin = textViewUsername.tag == "admin"

        // Only show block menu item when viewing someone else's profile who is not an admin
        if (userId == currentUserId || userId <= 0 || currentUserId <= 0 || isViewedUserAdmin) {
            blockMenuItem?.isVisible = false
        } else {
            blockMenuItem?.isVisible = true

            // Update block menu item text based on blocked status
            if (isUserBlocked) {
                blockMenuItem?.title = "Desbloquear usuario"
            } else {
                blockMenuItem?.title = "Bloquear usuario"
            }
        }

        return true
    }

    // Update the onPrepareOptionsMenu method to update menu items dynamically
    override fun onPrepareOptionsMenu(menu: Menu): Boolean {
        val blockMenuItem = menu.findItem(R.id.menu_block_user)
        val settingsMenuItem = menu.findItem(R.id.menu_settings)

        // Get current user ID from shared preferences
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val currentUserId = sharedPreferences.getInt("USER_ID", -1)

        // Check if the viewed user is an admin
        val isViewedUserAdmin = textViewUsername.tag == "admin"

        // Only show block menu item when viewing someone else's profile who is not an admin
        if (userId == currentUserId || userId <= 0 || currentUserId <= 0 || isViewedUserAdmin) {
            blockMenuItem?.isVisible = false
        } else {
            blockMenuItem?.isVisible = true

            // Update block menu item text based on blocked status
            if (isUserBlocked) {
                blockMenuItem?.title = "Desbloquear usuario"
            } else {
                blockMenuItem?.title = "Bloquear usuario"
            }
        }

        // Only show settings menu item when viewing own profile
        settingsMenuItem?.isVisible = (userId == currentUserId)

        return super.onPrepareOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menu_block_user -> {
                showBlockUserConfirmation()
                true
            }
            R.id.menu_settings -> {
                val intent = Intent(this, com.spyro.vmeet.activity.SettingsActivity::class.java)
                intent.putExtra("USER_ID", getSharedPreferences("VMeetPrefs", MODE_PRIVATE).getInt("USER_ID", 0))
                startActivity(intent)
                true
            }
            R.id.menu_logout -> {
                logoutUser()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showBlockUserConfirmation() {
        Log.d(TAG, "Showing block user confirmation dialog")

        val builder = AlertDialog.Builder(this)
        builder.setTitle("Bloquear usuario")
        builder.setMessage("¿Estás seguro de que quieres bloquear a este usuario? No podrán enviarte mensajes ni ver tu perfil.")

        builder.setPositiveButton("Bloquear") { _, _ ->
            Log.d(TAG, "Block user confirmed")
            blockUser()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            Log.d(TAG, "Block user cancelled")
            dialog.dismiss()
        }

        val dialog = builder.create()
        dialog.show()
    }

    private fun blockUser() {
        val userIdToBlock = userId
        if (userIdToBlock <= 0) {
            Toast.makeText(this, "Error: No se pudo identificar al usuario", Toast.LENGTH_SHORT).show()
            return
        }

        // Get the current user ID from preferences
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val currentUserId = sharedPreferences.getInt("USER_ID", -1)

        if (currentUserId <= 0) {
            Toast.makeText(this, "Error: No se pudo identificar tu cuenta", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d(TAG, "Blocking user with ID: $userIdToBlock, Current user ID: $currentUserId")

        // Show loading and disable button
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        progressBar.visibility = View.VISIBLE
        buttonBlockUser.isEnabled = false
        buttonBlockUser.text = "Bloqueando..."

        thread {
            try {
                // Create URL for the block user endpoint
                val url = URL("$API_URL/user/block")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                connection.setRequestProperty("Content-Type", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("blockedUserId", userIdToBlock)

                val jsonPayloadString = jsonPayload.toString()
                Log.d(TAG, "Sending block request with payload: $jsonPayloadString")

                // Send the request
                val outputStream = connection.outputStream
                outputStream.write(jsonPayloadString.toByteArray())
                outputStream.flush()
                outputStream.close()

                val responseCode = connection.responseCode
                Log.d(TAG, "Block user response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Block user response: $response")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonBlockUser.isEnabled = true
                        Toast.makeText(this, "Usuario bloqueado con éxito", Toast.LENGTH_SHORT).show()

                        // Update button state immediately
                        isUserBlocked = true
                        updateBlockButtonState()
                    }
                } else {
                    val errorStream = connection.errorStream
                    val errorBody = errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error blocking user: $responseCode, Body: $errorBody")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonBlockUser.isEnabled = true
                        Toast.makeText(this, "Error al bloquear usuario: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error blocking user", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonBlockUser.isEnabled = true
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun setMainProfileImage(position: Int) {
        if (position in 0 until profileImages.size) {
            profileImageAdapter.setMainImagePosition(position)
            mainImageUrl = profileImages[position]
            Toast.makeText(this, "Imagen establecida como principal", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showImageOptions(position: Int) {
        val options = arrayOf("Establecer como principal", "Eliminar imagen")
        AlertDialog.Builder(this)
            .setTitle("Opciones de imagen")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> setMainProfileImage(position)
                    1 -> {
                        if (profileImages.size > 1) {
                            deleteProfileImage(position)
                        } else {
                            Toast.makeText(this, "Debes mantener al menos una imagen", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }
            .show()
    }

    private fun deleteProfileImage(position: Int) {
        if (position !in 0 until profileImages.size) {
            return
        }

        val imageUrl = profileImages[position]

        // Show loading indicator
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        progressBar.visibility = View.VISIBLE

        // Get the current user ID from preferences
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val currentUserId = sharedPreferences.getInt("USER_ID", -1)

        // First try to extract the ID from the URL
        val imageId = extractImageIdFromUrl(imageUrl)

        if (imageId != null) {
            // If we have an ID, use the direct delete endpoint
            deleteImageById(currentUserId, imageId, position, progressBar)
        } else {
            // If we can't get the ID, we need to query the server to find the image ID by URL
            findAndDeleteImageByUrl(currentUserId, imageUrl, position, progressBar)
        }
    }

    private fun deleteImageById(userId: Int, imageId: Int, position: Int, progressBar: ProgressBar) {
        thread {
            try {
                // Create URL for the delete image endpoint
                val url = URL("$API_URL/profile/image/$userId/$imageId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "DELETE"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                Log.d(TAG, "Delete image response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Delete image response: $response")

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        // Remove the image from the adapter
                        profileImageAdapter.removeImage(position)

                        // Ensure we still have a valid main image
                        if (profileImageAdapter.mainImagePosition < profileImages.size) {
                            mainImageUrl = profileImages[profileImageAdapter.mainImagePosition]
                        }

                        // Show the Add Image FAB if we now have less than max images
                        if (isEditMode && profileImages.size < MAX_PROFILE_IMAGES) {
                            fabAddImage.visibility = View.VISIBLE
                        }

                        Toast.makeText(this, "Imagen eliminada correctamente", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    val errorStream = connection.errorStream
                    val errorBody = errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error deleting image: $responseCode, Body: $errorBody")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(this, "Error al eliminar imagen: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting image", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun findAndDeleteImageByUrl(userId: Int, imageUrlToDelete: String, position: Int, progressBar: ProgressBar) {
        thread {
            try {
                // First try to extract the filename from the URL
                val filename = imageUrlToDelete.substringAfterLast("/")
                Log.d(TAG, "Extracted filename: $filename")

                // Query the database to get the image ID
                val url = URL("$API_URL/profile/find-image-id/$userId/$filename")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                Log.d(TAG, "Find image ID response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Find image ID response: $response")

                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val imageId = jsonResponse.optInt("imageId", -1)

                        if (imageId > 0) {
                            // We found the image ID, now delete it
                            Log.d(TAG, "Found image ID from database: $imageId")
                            connection.disconnect()
                            deleteImageById(userId, imageId, position, progressBar)
                            return@thread
                        }
                    }

                    connection.disconnect()

                    // If we couldn't find the ID, try to delete by URL
                    Log.d(TAG, "Image ID not found, trying to delete by URL")
                    deleteImageByUrl(userId, imageUrlToDelete, position, progressBar)
                } else {
                    val errorStream = connection.errorStream
                    val errorBody = errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error finding image ID: $responseCode, Body: $errorBody")
                    connection.disconnect()

                    // If we couldn't find the ID, try to delete by URL
                    Log.d(TAG, "Error finding image ID, trying to delete by URL")
                    deleteImageByUrl(userId, imageUrlToDelete, position, progressBar)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error finding image ID", e)

                // If there was an error, try to delete by URL
                Log.d(TAG, "Exception finding image ID, trying to delete by URL")
                deleteImageByUrl(userId, imageUrlToDelete, position, progressBar)
            }
        }
    }

    private fun deleteImageByUrl(userId: Int, imageUrlToDelete: String, position: Int, progressBar: ProgressBar) {
        thread {
            try {
                // Create URL for the delete image by URL endpoint
                val url = URL("$API_URL/profile/delete-image-by-url/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.setRequestProperty("Content-Type", "application/json")
                connection.doOutput = true
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                // Create JSON payload with the image URL
                val jsonPayload = JSONObject()
                jsonPayload.put("imageUrl", imageUrlToDelete)

                // Log the request for debugging
                Log.d(TAG, "Delete image by URL request: $jsonPayload")

                // Write data
                val os = connection.outputStream
                os.write(jsonPayload.toString().toByteArray())
                os.flush()
                os.close()

                val responseCode = connection.responseCode
                Log.d(TAG, "Delete image by URL response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Delete image by URL response: $response")

                    // Reload the profile to get the latest data
                    loadProfile(userId)

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        // Remove the image from the adapter and local list
                        if (position < profileImages.size) {
                            profileImages.removeAt(position)
                            profileImageAdapter.notifyDataSetChanged()
                        }

                        // Show the Add Image FAB if we now have less than max images
                        if (isEditMode && profileImages.size < MAX_PROFILE_IMAGES) {
                            fabAddImage.visibility = View.VISIBLE
                        }

                        Toast.makeText(this, "Imagen eliminada correctamente", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    val errorStream = connection.errorStream
                    val errorBody = errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error deleting image by URL: $responseCode, Body: $errorBody")

                    // If we couldn't delete by URL, use the fallback method
                    connection.disconnect()
                    updateProfileToRemoveImage(userId, imageUrlToDelete, position, progressBar)
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting image by URL", e)

                // If we couldn't delete by URL, use the fallback method
                updateProfileToRemoveImage(userId, imageUrlToDelete, position, progressBar)
            }
        }
    }

    private fun updateProfileToRemoveImage(userId: Int, imageUrlToRemove: String, position: Int, progressBar: ProgressBar) {
        // Log the current state for debugging
        Log.d(TAG, "Removing image at position $position: $imageUrlToRemove")
        Log.d(TAG, "Current profileImages: $profileImages")

        // First, make a local copy of the current profile images
        val currentImages = ArrayList<String>(profileImages)

        // Create a new profile_images array without the image to remove
        // We need to be more flexible with the comparison to handle different URL formats
        val updatedImages = currentImages.filter { currentUrl ->
            // Compare full URLs
            if (currentUrl == imageUrlToRemove) {
                return@filter false
            }

            // Compare filenames
            val currentFilename = currentUrl.substringAfterLast("/")
            val removeFilename = imageUrlToRemove.substringAfterLast("/")

            if (currentFilename == removeFilename) {
                return@filter false
            }

            // Keep this image
            return@filter true
        }

        Log.d(TAG, "Filtered profileImages: $updatedImages")

        // If we have no images left, we need to add a default one
        if (updatedImages.isEmpty()) {
            runOnUiThread {
                progressBar.visibility = View.GONE
                Toast.makeText(this, "Debes mantener al menos una imagen", Toast.LENGTH_SHORT).show()
            }
            return
        }

        // Update the main image if needed
        var newMainImageUrl = mainImageUrl
        if (mainImageUrl == imageUrlToRemove ||
            mainImageUrl?.substringAfterLast("/") == imageUrlToRemove.substringAfterLast("/")) {
            newMainImageUrl = updatedImages[0]
            Log.d(TAG, "Updated mainImageUrl to: $newMainImageUrl")
        }

        // Create JSON payload for profile update
        val profileData = mutableMapOf<String, Any?>()

        // Add all other profile fields to ensure we don't lose data
        profileData["pronouns"] = ""
        profileData["bio"] = ""
        profileData["job_title"] = ""
        profileData["school"] = ""
        profileData["location"] = ""
        profileData["favorite_game"] = ""
        profileData["favorite_platform"] = ""
        profileData["looking_for"] = ""
        profileData["birthdate"] = null
        profileData["gender"] = ""
        profileData["sexuality"] = ""

        // Add profile images
        val imagesArray = updatedImages.map { imageUrl ->
            mapOf(
                "url" to imageUrl,
                "isMain" to (imageUrl == newMainImageUrl)
            )
        }
        profileData["profile_images"] = imagesArray

        // Backward compatibility - still include avatar_url for older versions
        if (newMainImageUrl != null) {
            profileData["avatar_url"] = newMainImageUrl
        }

        // Convert to JSON
        val jsonObj = JSONObject(profileData as Map<*, *>)
        val jsonStr = jsonObj.toString()

        // Log the request body for debugging
        Log.d(TAG, "[REQUEST BODY] $jsonStr")

        thread {
            try {
                // Update profile to remove the image
                val url = URL("$API_URL/profile/profile/$userId")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "PUT"
                conn.setRequestProperty("Content-Type", "application/json")
                conn.doOutput = true

                // Write data
                val os = conn.outputStream
                os.write(jsonStr.toByteArray())
                os.flush()
                os.close()

                // Check response
                val responseCode = conn.responseCode
                Log.d(TAG, "Profile update response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Log response for debugging
                    val response = conn.inputStream.bufferedReader().use { it.readText() }
                    Log.d(TAG, "Profile update success response: $response")

                    // After successful update, reload the profile to get the latest data
                    loadProfile(userId)

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        // Update the local profileImages list
                        profileImages.clear()
                        profileImages.addAll(updatedImages)

                        // Update main image URL
                        mainImageUrl = newMainImageUrl

                        // Update the adapter
                        profileImageAdapter.notifyDataSetChanged()

                        // Show the Add Image FAB if we now have less than max images
                        if (isEditMode && profileImages.size < MAX_PROFILE_IMAGES) {
                            fabAddImage.visibility = View.VISIBLE
                        }

                        Toast.makeText(this, "Imagen eliminada correctamente", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    // Log error details for debugging
                    val errorResponse = conn.errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e(TAG, "Profile update error: $responseCode, $errorResponse")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(this, "Error al actualizar perfil: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                conn.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error updating profile", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun extractImageIdFromUrl(imageUrl: String): Int? {
        // Try to extract the image ID from the URL
        // Example URL format: /uploads/profile_images/123.jpg where 123 is the ID
        // Or from database ID in the URL path: /profile/image/userId/imageId

        try {
            // First check if it's a direct database ID reference
            val pathSegments = imageUrl.split("/")
            if (pathSegments.size >= 2) {
                val lastSegment = pathSegments.last()
                val secondLastSegment = pathSegments[pathSegments.size - 2]

                // If the last segment is a number, it might be the ID
                if (lastSegment.all { it.isDigit() }) {
                    return lastSegment.toInt()
                }

                // If the second last segment is a number, it might be the ID
                if (secondLastSegment.all { it.isDigit() }) {
                    return secondLastSegment.toInt()
                }
            }

            // If we can't find a direct ID, we'll try to extract it from the filename
            val filename = imageUrl.substringAfterLast("/")
            val idPart = filename.substringBefore(".")

            if (idPart.all { it.isDigit() }) {
                return idPart.toInt()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error extracting image ID from URL: $imageUrl", e)
        }

        return null
    }

    private fun openFullScreenImage(position: Int) {
        // Get the image URL for the selected position
        val imageUrl = profileImageAdapter.getImageUrl(position) ?: return

        // Start FullScreenImageActivity
        val intent = Intent(this, FullScreenImageActivity::class.java).apply {
            putExtra(FullScreenImageActivity.EXTRA_IMAGE_URL, imageUrl)
            putExtra(FullScreenImageActivity.EXTRA_API_BASE_URL, API_URL)
        }
        startActivity(intent)
    }

    private fun showProfileCompletionTips() {
        // Create and show a dialog with tips for completing the profile
        val dialogBuilder = AlertDialog.Builder(this)
            .setTitle("¡Completa tu perfil!")
            .setMessage("Completar tu perfil aumentará tus chances de conseguir matches:\n\n" +
                    "1. Agrega al menos una foto (no necesariamente real)\n" +
                    "2. Añade una descripción corta sobre ti\n" +
                    "3. Completa tus juegos y plataformas favoritas\n\n" +
                    "¿Deseas completar tu perfil ahora?")
            .setPositiveButton("¡Vamos a ello!") { _, _ ->
                // Switch to edit mode
                if (!isEditMode) {
                    switchToEditMode()
                }

                // Scroll to the top to show the add photo button
                nestedScrollView.smoothScrollTo(0, 0)

                // Make the FAB pulse
                fabChangeAvatar.apply {
                    visibility = View.VISIBLE
                    // Add pulse animation if desired
                }
            }
            .setNegativeButton("Más tarde") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(true)

        dialogBuilder.create().show()
    }

    private fun checkAdminStatus(userId: Int) {
        val url = "http://*************:3000/user/check-admin/$userId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error checking admin status", e)
                runOnUiThread {
                    buttonAdminPanel.visibility = View.GONE
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val isAdmin = jsonObject.getBoolean("isAdmin")

                    runOnUiThread {
                        buttonAdminPanel.visibility = if (success && isAdmin) View.VISIBLE else View.GONE
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing admin status response", e)
                    runOnUiThread {
                        buttonAdminPanel.visibility = View.GONE
                    }
                }
            }
        })
    }

    // New method to fetch user role/admin status and merge it with existing user data
    private fun checkUserRole(userId: Int, userObject: JSONObject) {
        thread {
            try {
                val url = URL("$API_URL/user/check-admin/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)
                    val success = jsonResponse.optBoolean("success", false)
                    val isAdmin = jsonResponse.optBoolean("isAdmin", false)

                    // Add role information to the user object
                    userObject.put("role", if (isAdmin) "admin" else "user")

                    Log.d(TAG, "User role updated: ${if (isAdmin) "admin" else "user"}")

                    runOnUiThread {
                        // Update UI with the enhanced user object
                        populateUI(userObject)
                        findViewById<ProgressBar>(R.id.progressBar)?.visibility = View.GONE
                    }
                } else {
                    // Failed to get admin status, but we can still show the profile
                    runOnUiThread {
                        populateUI(userObject)
                        findViewById<ProgressBar>(R.id.progressBar)?.visibility = View.GONE
                    }
                }
                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking admin status", e)
                runOnUiThread {
                    // Still show profile with available data on error
                    populateUI(userObject)
                    findViewById<ProgressBar>(R.id.progressBar)?.visibility = View.GONE
                }
            }
        }
    }

    // Add this method to check if a user is blocked
    private fun checkIfUserIsBlocked() {
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val currentUserId = sharedPreferences.getInt("USER_ID", -1)

        if (currentUserId <= 0 || userId <= 0) {
            Log.e(TAG, "Invalid user IDs for block check: currentUserId=$currentUserId, viewedUserId=$userId")
            return
        }

        Log.d(TAG, "Checking if user $userId is blocked by current user $currentUserId")

        // Show loading
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        progressBar.visibility = View.VISIBLE

        // Force the button to show "Loading..." state
        buttonBlockUser.isEnabled = false
        buttonBlockUser.text = "Verificando..."

        thread {
            try {
                val url = URL("$API_URL/user/check-blocked-status")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                connection.setRequestProperty("Content-Type", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("blockedUserId", userId)

                val jsonPayloadString = jsonPayload.toString()
                Log.d(TAG, "Checking block status with payload: $jsonPayloadString")

                // Send the request
                val outputStream = connection.outputStream
                outputStream.write(jsonPayloadString.toByteArray())
                outputStream.flush()
                outputStream.close()

                val responseCode = connection.responseCode

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Check block status response: $response")

                    val jsonResponse = JSONObject(response)
                    val wasBlocked = isUserBlocked // Keep track of previous state
                    isUserBlocked = jsonResponse.optBoolean("isBlocked", false)

                    Log.d(TAG, "Block status result: isBlocked=$isUserBlocked (was $wasBlocked)")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonBlockUser.isEnabled = true
                        updateBlockButtonState()

                        if (wasBlocked != isUserBlocked) {
                            Log.i(TAG, "Block status changed from $wasBlocked to $isUserBlocked")
                        }
                    }
                } else {
                    // Enhanced error logging
                    val errorStream = connection.errorStream
                    val errorResponse = errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e(TAG, "HTTP Error checking block status: $responseCode - Error details: $errorResponse")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonBlockUser.isEnabled = true
                        Toast.makeText(this, "Error al verificar estado de bloqueo: $responseCode", Toast.LENGTH_SHORT).show()
                        // Default to assuming not blocked if there's an error
                        isUserBlocked = false
                        updateBlockButtonState()
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking block status", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonBlockUser.isEnabled = true
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                    isUserBlocked = false
                    updateBlockButtonState()
                }
            }
        }
    }

    // Add this method to update button state based on blocked status
    private fun updateBlockButtonState() {
        Log.d(TAG, "Updating block button state. User is blocked: $isUserBlocked")

        // Update the button
        if (isUserBlocked) {
            buttonBlockUser.text = "DESBLOQUEAR USUARIO"
            buttonBlockUser.setOnClickListener {
                showUnblockUserConfirmation()
            }
        } else {
            buttonBlockUser.text = "BLOQUEAR USUARIO"
            buttonBlockUser.setOnClickListener {
                showBlockUserConfirmation()
            }
        }

        // Update the toolbar menu
        invalidateOptionsMenu()
    }

    // Add unblock confirmation dialog
    private fun showUnblockUserConfirmation() {
        Log.d(TAG, "Showing unblock user confirmation dialog")

        val builder = AlertDialog.Builder(this)
        builder.setTitle("Desbloquear usuario")
        builder.setMessage("¿Estás seguro de que quieres desbloquear a este usuario? Podrá enviarte mensajes y ver tu perfil nuevamente.")

        builder.setPositiveButton("Desbloquear") { _, _ ->
            Log.d(TAG, "Unblock user confirmed")
            unblockUser()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            Log.d(TAG, "Unblock user cancelled")
            dialog.dismiss()
        }

        val dialog = builder.create()
        dialog.show()
    }

    // Add unblock user function
    private fun unblockUser() {
        val userIdToUnblock = userId
        if (userIdToUnblock <= 0) {
            Toast.makeText(this, "Error: No se pudo identificar al usuario", Toast.LENGTH_SHORT).show()
            return
        }

        // Get the current user ID from preferences
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val currentUserId = sharedPreferences.getInt("USER_ID", -1)

        if (currentUserId <= 0) {
            Toast.makeText(this, "Error: No se pudo identificar tu cuenta", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d(TAG, "Unblocking user with ID: $userIdToUnblock, Current user ID: $currentUserId")

        // Show loading and disable button
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        progressBar.visibility = View.VISIBLE
        buttonBlockUser.isEnabled = false
        buttonBlockUser.text = "Desbloqueando..."

        thread {
            try {
                // Create URL for the unblock user endpoint
                val url = URL("$API_URL/user/unblock")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                connection.setRequestProperty("Content-Type", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("blockedUserId", userIdToUnblock)

                val jsonPayloadString = jsonPayload.toString()
                Log.d(TAG, "Sending unblock request with payload: $jsonPayloadString")

                // Send the request
                val outputStream = connection.outputStream
                outputStream.write(jsonPayloadString.toByteArray())
                outputStream.flush()
                outputStream.close()

                val responseCode = connection.responseCode
                Log.d(TAG, "Unblock user response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Unblock user response: $response")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonBlockUser.isEnabled = true
                        Toast.makeText(this, "Usuario desbloqueado con éxito", Toast.LENGTH_SHORT).show()

                        // Update button state immediately
                        isUserBlocked = false
                        updateBlockButtonState()
                    }
                } else {
                    val errorStream = connection.errorStream
                    val errorBody = errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error unblocking user: $responseCode, Body: $errorBody")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonBlockUser.isEnabled = true
                        Toast.makeText(this, "Error al desbloquear usuario: $responseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error unblocking user", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonBlockUser.isEnabled = true
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }    private fun showReportUserDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_report_user, null)
        val dialog = androidx.appcompat.app.AlertDialog.Builder(this, R.style.Theme_VMeet_Dialog_Alert)
            .setView(dialogView)
            .create()

        val radioGroup = dialogView.findViewById<RadioGroup>(R.id.radioGroupReportReason)
        val editTextDetails = dialogView.findViewById<TextInputEditText>(R.id.editTextDetails)
        val buttonCancel = dialogView.findViewById<MaterialButton>(R.id.buttonCancel)
        val buttonSendReport = dialogView.findViewById<MaterialButton>(R.id.buttonSendReport)

        // Configurar los botones
        buttonCancel.setOnClickListener {
            dialog.dismiss()
        }

        buttonSendReport.setOnClickListener {
            val selectedId = radioGroup.checkedRadioButtonId
            if (selectedId != -1) {
                val selectedRadioButton = dialogView.findViewById<MaterialRadioButton>(selectedId)
                val reason = selectedRadioButton.text.toString()
                val details = editTextDetails.text.toString()
                submitReport(reason, details)
                dialog.dismiss()
            } else {
                Toast.makeText(this, "Por favor selecciona un motivo", Toast.LENGTH_SHORT).show()
            }
        }

        // Configurar el diálogo para ocupar casi todo el ancho de la pantalla
        dialog.window?.apply {
            attributes?.width = android.view.ViewGroup.LayoutParams.MATCH_PARENT
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }

        dialog.show()
    }

    private fun submitReport(reason: String, details: String) {
        val sharedPreferences = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val reporterId = sharedPreferences.getInt("USER_ID", -1)

        if (reporterId <= 0) {
            Toast.makeText(this, "Error: No se pudo identificar tu cuenta", Toast.LENGTH_SHORT).show()
            return
        }

        // Show loading indicator
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        progressBar.visibility = View.VISIBLE
        buttonReportUser.isEnabled = false

        thread {
            try {
                val url = URL("$API_URL/user/report")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "POST"
                connection.doOutput = true
                connection.setRequestProperty("Content-Type", "application/json")
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                // Create JSON payload
                val jsonPayload = JSONObject().apply {
                    put("reporterId", reporterId)
                    put("reportedUserId", userId)
                    put("reason", reason)
                    put("details", details)
                }

                val jsonPayloadString = jsonPayload.toString()
                Log.d(TAG, "Sending report with payload: $jsonPayloadString")

                // Send the request
                val outputStream = connection.outputStream
                outputStream.write(jsonPayloadString.toByteArray())
                outputStream.flush()
                outputStream.close()

                val responseCode = connection.responseCode

                if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonReportUser.isEnabled = true
                        Toast.makeText(this, "Reporte enviado correctamente", Toast.LENGTH_SHORT).show()
                    }                } else {
                    val errorStream = connection.errorStream
                    val errorResponse = errorStream?.bufferedReader()?.use { it.readText() } ?: "No error details"
                    Log.e(TAG, "HTTP Error submitting report: $responseCode - Error details: $errorResponse")

                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        buttonReportUser.isEnabled = true
                        when (responseCode) {
                            409 -> Toast.makeText(this, "Ya existe un reporte pendiente para este usuario", Toast.LENGTH_LONG).show()
                            else -> Toast.makeText(this, "Error al enviar reporte: $responseCode", Toast.LENGTH_SHORT).show()
                        }
                    }
                }

                connection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error submitting report", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonReportUser.isEnabled = true
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun openChatWithUser() {
        // Don't allow sending messages to yourself
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        val loggedInUserId = prefs.getInt("USER_ID", 0)

        if (userId == loggedInUserId) {
            Toast.makeText(this, "No puedes enviarte mensajes a ti mismo", Toast.LENGTH_SHORT).show()
            return
        }

        // Show loading indicator
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)
        progressBar.visibility = View.VISIBLE

        // Get the user's avatar URL
        var avatarUrl: String? = null

        // Check if we have profile images
        if (profileImages.isNotEmpty()) {
            // Use the main image if available, otherwise use the first image
            avatarUrl = mainImageUrl ?: profileImages[0]

            // Make sure the URL is absolute
            if (avatarUrl != null && !avatarUrl.startsWith("http")) {
                avatarUrl = "$API_URL$avatarUrl"
            }
        }

        Log.d(TAG, "Using avatar URL: $avatarUrl for chat")

        // First check if there's a match between the users
        thread {
            try {
                // Primero, obtener todos los matches entre los usuarios
                val matchesUrl = URL("$API_URL/matches/all?userId=$loggedInUserId&otherUserId=$userId")
                val matchesConnection = matchesUrl.openConnection() as HttpURLConnection
                matchesConnection.requestMethod = "GET"
                matchesConnection.connectTimeout = 10000
                matchesConnection.readTimeout = 10000

                val matchesResponseCode = matchesConnection.responseCode
                if (matchesResponseCode == HttpURLConnection.HTTP_OK) {
                    val matchesResponse = matchesConnection.inputStream.bufferedReader().readText()
                    val matchesJsonResponse = JSONObject(matchesResponse)
                    val isMatch = matchesJsonResponse.optBoolean("isMatch", false)

                    if (isMatch) {
                        // Hay un match, obtener todos los IDs de match
                        val matchesArray = matchesJsonResponse.optJSONArray("matches")
                        val matchIds = mutableListOf<Int>()

                        if (matchesArray != null) {
                            for (i in 0 until matchesArray.length()) {
                                val matchId = matchesArray.optInt(i, -1)
                                if (matchId != -1) {
                                    matchIds.add(matchId)
                                }
                            }
                        }

                        // Usar el primer ID de match si hay alguno
                        val matchId = if (matchIds.isNotEmpty()) matchIds[0] else -1

                        Log.d(TAG, "Found ${matchIds.size} matches between users $loggedInUserId and $userId: $matchIds")

                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            if (matchId != -1) {
                                val intent = Intent(this, com.spyro.vmeet.activity.ChatActivity::class.java).apply {
                                    putExtra("MATCH_ID", matchId)
                                    putExtra("OTHER_USER_ID", userId)
                                    putExtra("OTHER_USER_NAME", textViewUsername.text.toString())
                                    putExtra("OTHER_USER_AVATAR", avatarUrl)
                                    // Añadir flag para indicar que es un nuevo chat desde perfil
                                    putExtra("FROM_PROFILE", true)
                                    // Añadir todos los IDs de match para que la actividad de chat pueda usarlos
                                    putExtra("ALL_MATCH_IDS", matchIds.toIntArray())
                                }
                                startActivity(intent)

                                // Enviar broadcast para actualizar la lista de chats
                                val updateIntent = Intent("com.spyro.vmeet.UPDATE_CHAT_LIST")
                                // Asegurarse de que el broadcast sea explícito para Android 12+
                                updateIntent.setPackage(packageName)
                                sendBroadcast(updateIntent)
                            } else {
                                Toast.makeText(this, "Error: No se pudo obtener el ID de la coincidencia", Toast.LENGTH_SHORT).show()
                            }
                        }
                        return@thread
                    } else {
                        // No match exists, check if user is admin or if we can send messages without match
                        val isAdmin = textViewUsername.tag == "admin"
                        val isViewerAdmin = checkIfViewerIsAdmin()

                        // Verificar si el otro usuario permite recibir mensajes sin coincidencia
                        checkIfUserAcceptsMessages(userId) { acceptsMessages ->
                            if (isAdmin || isViewerAdmin || acceptsMessages) {
                                // Create a direct match for admins or if user accepts messages without match
                                createDirectChat(loggedInUserId, userId, avatarUrl)
                            } else {
                                // Regular users need to match first if the other user doesn't accept messages
                                runOnUiThread {
                                    progressBar.visibility = View.GONE
                                    Toast.makeText(this, "Este usuario solo acepta mensajes de perfiles con los que ha hecho match", Toast.LENGTH_SHORT).show()
                                }
                            }
                        }
                    }
                } else {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(this, "Error al verificar coincidencia: $matchesResponseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                matchesConnection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking match", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun checkIfViewerIsAdmin(): Boolean {
        val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
        return prefs.getString("USER_ROLE", "") == "admin"
    }

    private fun createDirectChat(currentUserId: Int, otherUserId: Int, avatarUrl: String? = null) {
        val progressBar = findViewById<ProgressBar>(R.id.progressBar)

        thread {
            try {
                // Create a new match
                val createUrl = URL("$API_URL/admin/create-match")
                val createConnection = createUrl.openConnection() as HttpURLConnection
                createConnection.requestMethod = "POST"
                createConnection.doOutput = true
                createConnection.setRequestProperty("Content-Type", "application/json")
                createConnection.connectTimeout = 15000
                createConnection.readTimeout = 15000

                // Create JSON payload
                val jsonPayload = JSONObject()
                jsonPayload.put("userId", currentUserId)
                jsonPayload.put("otherUserId", otherUserId)

                // Send the request
                val outputStream = createConnection.outputStream
                outputStream.write(jsonPayload.toString().toByteArray())
                outputStream.close()

                val createResponseCode = createConnection.responseCode
                if (createResponseCode == HttpURLConnection.HTTP_OK) {
                    val createResponse = createConnection.inputStream.bufferedReader().readText()
                    val createJsonResponse = JSONObject(createResponse)
                    val success = createJsonResponse.optBoolean("success", false)

                    if (success) {
                        val matchId = createJsonResponse.optInt("matchId", -1)
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            if (matchId != -1) {
                                val intent = Intent(this, com.spyro.vmeet.activity.ChatActivity::class.java).apply {
                                    putExtra("MATCH_ID", matchId)
                                    putExtra("OTHER_USER_ID", otherUserId)
                                    putExtra("OTHER_USER_NAME", textViewUsername.text.toString())
                                    putExtra("OTHER_USER_AVATAR", avatarUrl)
                                    // Añadir flag para indicar que es un nuevo chat desde perfil
                                    putExtra("FROM_PROFILE", true)
                                }
                                startActivity(intent)

                                // Enviar broadcast para actualizar la lista de chats
                                val updateIntent = Intent("com.spyro.vmeet.UPDATE_CHAT_LIST")
                                // Asegurarse de que el broadcast sea explícito para Android 12+
                                updateIntent.setPackage(packageName)
                                sendBroadcast(updateIntent)
                            } else {
                                Toast.makeText(this, "Error: No se pudo obtener el ID de la coincidencia", Toast.LENGTH_SHORT).show()
                            }
                        }
                    } else {
                        val message = createJsonResponse.optString("message", "Error al crear el chat")
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    val errorBody = createConnection.errorStream?.bufferedReader()?.readText() ?: "No error details"
                    Log.e(TAG, "HTTP Error creating match: $createResponseCode, Body: $errorBody")
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(this, "Error del servidor: $createResponseCode", Toast.LENGTH_SHORT).show()
                    }
                }

                createConnection.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error creating direct chat", e)
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Toast.makeText(this, "Error de red: ${e.message}", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopStatusUpdates()
    }

    private fun checkIfUserAcceptsMessages(userId: Int, callback: (Boolean) -> Unit) {
        thread {
            try {
                val url = URL("$API_URL/settings/$userId")
                val conn = url.openConnection() as HttpURLConnection
                conn.requestMethod = "GET"
                conn.connectTimeout = 10000
                conn.readTimeout = 10000

                val responseCode = conn.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = conn.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)

                    val settings = jsonResponse.optJSONObject("settings")
                    val acceptsMessages = settings?.optBoolean("receive_messages_without_match", true) ?: true

                    callback(acceptsMessages)
                } else {
                    // Si no hay configuración, asumir que acepta mensajes (valor predeterminado)
                    callback(true)
                }

                conn.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "Error checking if user accepts messages", e)
                // En caso de error, asumir que acepta mensajes
                callback(true)
            }
        }
    }

    private fun setupListeners() {
        try {
            // Only allow edit/save functionality if it's the user's own profile
            buttonEditSave.setOnClickListener {
                val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
                val loggedInUserId = prefs.getInt("USER_ID", 0)
                val isOwnProfile = userId == loggedInUserId && !intent.getBooleanExtra("VIEW_ONLY_MODE", false)

                if (isOwnProfile) {
                    if (isEditMode) {
                        saveProfileData()
                    } else {
                        switchToEditMode()
                    }
                }
            }

            // Set up logout button
            buttonLogout.setOnClickListener {
                logoutUser()
            }

            // Set up other buttons if available
            try {
                buttonBlockedUsers?.setOnClickListener {
                    val intent = Intent(this, BlockedUsersActivity::class.java)
                    startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up blocked users button", e)
            }

            try {
                buttonBlockUser?.setOnClickListener {
                    showBlockUserConfirmation()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up block user button", e)
            }

            try {
                buttonReportUser?.setOnClickListener {
                    showReportUserDialog()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up report user button", e)
            }

            try {
                buttonSendMessage?.setOnClickListener {
                    openChatWithUser()
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up send message button", e)
            }

            try {
                buttonAdminPanel?.setOnClickListener {
                    val intent = AdminPanelActivity.createIntent(this, userId)
                    startActivity(intent)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up admin panel button", e)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up listeners", e)
        }
    }

    private fun checkAndShowProfileTips() {
        try {
            // Check if we should show profile tips for new users
            val showProfileTips = intent.getBooleanExtra("SHOW_PROFILE_TIPS", false)
            val prefsForTips = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            val isFirstLogin = prefsForTips.getBoolean("IS_FIRST_LOGIN", false)

            if (showProfileTips || isFirstLogin) {
                // Clear the first login flag since we're showing the tips
                prefsForTips.edit().putBoolean("IS_FIRST_LOGIN", false).apply()

                // Show profile completion tips
                showProfileCompletionTips()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking and showing profile tips", e)
        }
    }



    private fun setupPersonalityTraitsRecyclerView() {
        try {
            // Setup LinearLayoutManager for vertical category display
            val layoutManager = LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false)
            recyclerViewPersonalityTraits.layoutManager = layoutManager

            // Initialize empty adapter (will be updated when traits are loaded)
            val emptyCategories = mutableListOf<TraitCategory>()
            val categoryAdapter = PersonalityTraitCategoryAdapter(
                categories = emptyCategories,
                isEditMode = false
            )

            recyclerViewPersonalityTraits.adapter = categoryAdapter

            // Setup edit button click listener
            buttonEditTraits.setOnClickListener {
                openEditPersonalityTraits()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error setting up personality traits RecyclerView", e)
        }
    }

    private fun openEditPersonalityTraits() {
        try {
            val intent = Intent(this, EditPersonalityTraitsActivity::class.java)
            startActivityForResult(intent, EDIT_TRAITS_REQUEST_CODE)
        } catch (e: Exception) {
            Log.e(TAG, "Error opening edit personality traits activity", e)
            Toast.makeText(this, "Error al abrir editor de rasgos", Toast.LENGTH_SHORT).show()
        }
    }

    private fun loadPersonalityTraits() {
        thread {
            try {
                val url = URL("$API_URL/profile/personality-traits/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 15000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                Log.d(TAG, "Personality traits response code: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    Log.d(TAG, "Personality traits response: $response")

                    val jsonResponse = JSONObject(response)
                    if (jsonResponse.optBoolean("success", false)) {
                        val traitsArray = jsonResponse.optJSONArray("traits") ?: JSONArray()
                        val selectedTraitIds = mutableListOf<String>()

                        Log.d(TAG, "Traits array length: ${traitsArray.length()}")
                        for (i in 0 until traitsArray.length()) {
                            val traitId = traitsArray.getString(i)
                            selectedTraitIds.add(traitId)
                            Log.d(TAG, "Found trait ID from server: $traitId")
                        }

                        Log.d(TAG, "All trait IDs from server: $selectedTraitIds")
                        runOnUiThread {
                            updatePersonalityTraitsDisplay(selectedTraitIds)
                        }
                    } else {
                        val message = jsonResponse.optString("message", "No message")
                        Log.d(TAG, "Server response success=false, message: $message")
                        runOnUiThread {
                            updatePersonalityTraitsDisplay(emptyList())
                        }
                    }
                } else {
                    Log.w(TAG, "HTTP response code: $responseCode")
                    if (responseCode >= 400) {
                        val errorResponse = connection.errorStream?.bufferedReader()?.readText()
                        Log.w(TAG, "Error response: $errorResponse")
                    }
                    runOnUiThread {
                        updatePersonalityTraitsDisplay(emptyList())
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading personality traits", e)
                runOnUiThread {
                    updatePersonalityTraitsDisplay(emptyList())
                }
            }
        }
    }

    private fun updatePersonalityTraitsDisplay(selectedTraitIds: List<String>) {
        try {
            Log.d(TAG, "updatePersonalityTraitsDisplay called with ${selectedTraitIds.size} traits: $selectedTraitIds")

            // Create a new list instead of clearing the existing one
            val traitsToDisplay = mutableListOf<PersonalityTrait>()

            // Add only selected traits to display
            selectedTraitIds.forEach { traitId ->
                PersonalityTraits.getTraitById(traitId)?.let { trait ->
                    // Create a copy to avoid modifying the original
                    val traitCopy = trait.copy()
                    traitCopy.isSelected = true
                    traitsToDisplay.add(traitCopy)
                    Log.d(TAG, "Added trait to display: ${traitCopy.name}")
                } ?: run {
                    Log.w(TAG, "Trait with ID '$traitId' not found in PersonalityTraits")
                }
            }

            Log.d(TAG, "Total traits to display: ${traitsToDisplay.size}")

            // Update the main list
            personalityTraits.clear()
            personalityTraits.addAll(traitsToDisplay)

            // Organize traits by categories for display
            val categoriesWithTraits = organizeCategoriesForDisplay(traitsToDisplay)

            // Use category adapter for better organization
            val categoryAdapter = PersonalityTraitCategoryAdapter(
                categories = categoriesWithTraits.toMutableList(),
                isEditMode = false
            )

            // Set the new adapter
            recyclerViewPersonalityTraits.adapter = categoryAdapter

            Log.d(TAG, "Set category adapter with ${categoriesWithTraits.size} categories")

            // Show/hide edit button based on whether it's own profile
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            val loggedInUserId = prefs.getInt("USER_ID", 0)
            val isOwnProfile = userId == loggedInUserId && !intent.getBooleanExtra("VIEW_ONLY_MODE", false)

            Log.d(TAG, "Is own profile: $isOwnProfile, showing edit button: $isOwnProfile")
            buttonEditTraits.visibility = if (isOwnProfile) View.VISIBLE else View.GONE

            // Make sure RecyclerView is visible and has proper layout
            recyclerViewPersonalityTraits.visibility = View.VISIBLE

            // Force layout update
            recyclerViewPersonalityTraits.requestLayout()

            Log.d(TAG, "RecyclerView visibility: ${recyclerViewPersonalityTraits.visibility}")
            Log.d(TAG, "RecyclerView adapter item count: ${categoryAdapter.itemCount}")

            if (traitsToDisplay.isEmpty()) {
                Log.d(TAG, "No traits to display - RecyclerView will be empty")
            } else {
                Log.d(TAG, "Traits to display: ${traitsToDisplay.map { it.name }}")
                Log.d(TAG, "Categories with traits: ${categoriesWithTraits.map { "${it.name}: ${it.traits.size} traits" }}")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error updating personality traits display", e)
        }
    }

    private fun organizeCategoriesForDisplay(selectedTraits: List<PersonalityTrait>): List<TraitCategory> {
        try {
            // Group selected traits by category
            val traitsByCategory = selectedTraits.groupBy { it.category }

            // Create category map for Spanish names
            val categoryMap = mapOf(
                PersonalityTraits.CATEGORY_PRONOUNS to "Pronombres",
                PersonalityTraits.CATEGORY_SEXUALITY to "Sexualidad",
                PersonalityTraits.CATEGORY_RELATIONSHIP to "Relaciones",
                PersonalityTraits.CATEGORY_FAMILY to "Familia",
                PersonalityTraits.CATEGORY_DATING to "Citas",
                PersonalityTraits.CATEGORY_MATCHING to "Compatibilidad",
                PersonalityTraits.CATEGORY_DATE_TYPE to "Tipo de cita",
                PersonalityTraits.CATEGORY_FACE_REVEALS to "Revelar rostro",
                PersonalityTraits.CATEGORY_EDUCATION to "Educación",
                PersonalityTraits.CATEGORY_SCHOOL to "Estado escolar",
                PersonalityTraits.CATEGORY_WORK to "Trabajo",
                PersonalityTraits.CATEGORY_RELIGION to "Religión",
                PersonalityTraits.CATEGORY_PETS to "Mascotas",
                PersonalityTraits.CATEGORY_LIFESTYLE to "Estilo de vida"
            )

            // Create categories only for those that have selected traits
            val categories = traitsByCategory.map { (categoryId, traits) ->
                TraitCategory(
                    name = categoryMap[categoryId] ?: categoryId,
                    traits = traits.toMutableList()
                )
            }.sortedBy { category ->
                // Sort categories in a specific order
                when (category.name) {
                    "Pronombres" -> 1
                    "Sexualidad" -> 2
                    "Relaciones" -> 3
                    "Familia" -> 4
                    "Citas" -> 5
                    "Compatibilidad" -> 6
                    "Tipo de cita" -> 7
                    "Revelar rostro" -> 8
                    "Educación" -> 9
                    "Estado escolar" -> 10
                    "Trabajo" -> 11
                    "Religión" -> 12
                    "Mascotas" -> 13
                    "Estilo de vida" -> 14
                    else -> 15
                }
            }

            Log.d(TAG, "Organized ${selectedTraits.size} traits into ${categories.size} categories")
            return categories

        } catch (e: Exception) {
            Log.e(TAG, "Error organizing categories for display", e)
            return emptyList()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            PICK_IMAGE_REQUEST -> {
                if (resultCode == Activity.RESULT_OK && data != null && data.data != null) {
                    val imageUri: Uri = data.data!!
                    Log.d("ProfileActivity", "Image selected: $imageUri")
                    uploadAvatar(imageUri)
                } else {
                    Log.w("ProfileActivity", "Image selection cancelled or failed. Code: $resultCode")
                }
            }
            EDIT_TRAITS_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    // Reload personality traits after editing
                    loadPersonalityTraits()
                }
            }
        }
    }

    // Add the missing formatLocation function after the populateUI method
    /**
     * Format location string with city and country information
     *
     * @param location The user's original location string (if provided)
     * @param city The user's city
     * @param country The user's country
     * @return Formatted location string
     */
    private fun formatLocation(location: String, city: String, country: String): String {
        // First check if we have city and country data
        val locationData = when {
            !city.isNullOrEmpty() && !country.isNullOrEmpty() -> "$city, $country"
            !city.isNullOrEmpty() -> city
            !country.isNullOrEmpty() -> country
            else -> ""
        }

        // Log the data for debugging
        Log.d(TAG, "formatLocation - location: '$location', city: '$city', country: '$country', result: '$locationData'")

        // If we have location data from Radar (city/country) but no original location input,
        // use the location data from Radar
        if (locationData.isNotEmpty() && (location.isEmpty() || location == "null")) {
            return locationData
        }

        // If we have original location input but no Radar location data,
        // use the original location
        if (locationData.isEmpty() && !location.isNullOrEmpty() && location != "null") {
            return location
        }

        // If we have both, combine them only if they're different
        if (locationData.isNotEmpty() && !location.isNullOrEmpty() && location != "null") {
            // Check if the location already contains the city or country
            if (location.contains(city, ignoreCase = true) || location.contains(country, ignoreCase = true)) {
                // Location already has city or country info, just use it
                return location
            } else {
                // Combine them
                return "$location ($locationData)"
            }
        }

        // If we have neither, return empty string
        return ""
    }

    // Añadir después del método checkUserStatus o en un lugar apropiado

    /**
     * Actualiza los datos de ubicación del usuario desde el radar
     * Esta función debería ser llamada cuando recibimos datos actualizados del radar
     */
    fun updateLocationData(city: String?, country: String?) {
        if (city.isNullOrEmpty() && country.isNullOrEmpty()) {
            Log.d(TAG, "updateLocationData: Sin datos para actualizar")
            return // No hay datos para actualizar
        }

        Log.d(TAG, "Actualizando datos de ubicación: Ciudad='$city', País='$country'")

        // Guardar en cache
        saveUserLocation(userId, city, country)

        // Actualizar el textViewCityCountryValue con los datos de ciudad y país
        val cityCountryText = when {
            !city.isNullOrEmpty() && !country.isNullOrEmpty() -> "$city, $country"
            !city.isNullOrEmpty() -> city
            !country.isNullOrEmpty() -> country
            else -> ""
        }

        if (cityCountryText.isEmpty()) {
            Log.d(TAG, "No se generó texto de ubicación para mostrar")
            return
        }

        try {
            runOnUiThread {
                try {
                    // Verificar que la vista existe y está disponible
                    if (::textViewCityCountryValue.isInitialized && textViewCityCountryValue != null) {
                        val currentText = textViewCityCountryValue.text.toString()

                        // Solo actualizar si es necesario (está vacío o es "Por completar")
                        if (currentText.isEmpty() || currentText == "Por completar") {
                            textViewCityCountryValue.text = cityCountryText
                            Log.d(TAG, "Texto de ubicación actualizado a: '$cityCountryText'")
                        } else {
                            Log.d(TAG, "No se actualizó el texto de ubicación porque ya tiene valor: '$currentText'")
                        }
                    } else {
                        Log.e(TAG, "textViewCityCountryValue no está inicializado o es nulo")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error al actualizar la interfaz con los datos de ubicación", e)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error en runOnUiThread al actualizar ubicación", e)
        }
    }

    /**
     * Obtiene la información de ubicación desde el radar para un usuario específico
     */
    private fun fetchLocationFromRadar(userId: Int) {
        Log.d(TAG, "Solicitando información de ubicación del radar para el usuario $userId")

        // Primero intentar cargar desde el cache
        val cachedLocation = loadUserLocation(userId)
        if (cachedLocation != null) {
            val (city, country) = cachedLocation
            if (!city.isNullOrEmpty() || !country.isNullOrEmpty()) {
                Log.d(TAG, "Usando datos de ubicación del cache: Ciudad='$city', País='$country'")
                updateLocationData(city, country)
                return
            }
        }

        // Si no hay datos en cache, hacer la petición al servidor
        thread {
            try {
                // Construir la URL para la API del radar
                val url = URL("$API_URL/radar/user/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 15000

                Log.d(TAG, "Conectando a ${url}...")

                val responseCode = connection.responseCode
                Log.d(TAG, "Código de respuesta: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val inputStream = connection.inputStream
                    val response = inputStream.bufferedReader().use { it.readText() }

                    Log.d(TAG, "Respuesta recibida: $response")

                    try {
                        val jsonResponse = JSONObject(response)
                        if (jsonResponse.optBoolean("success", false)) {
                            val userData = jsonResponse.optJSONObject("user")
                            if (userData != null) {
                                val city = userData.optString("city", "")
                                val country = userData.optString("country", "")

                                Log.d(TAG, "Datos de radar obtenidos - Ciudad: '$city', País: '$country'")

                                if (!city.isNullOrEmpty() || !country.isNullOrEmpty()) {
                                    // Actualizar la interfaz con la nueva información
                                    updateLocationData(city, country)
                                } else {
                                    Log.d(TAG, "No se encontraron datos de ciudad/país en la respuesta del radar")
                                }
                            } else {
                                Log.d(TAG, "No se encontró objeto 'user' en la respuesta del radar")
                            }
                        } else {
                            val errorMessage = jsonResponse.optString("message", "Error desconocido")
                            Log.e(TAG, "Error en la respuesta del radar: $errorMessage")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error al procesar respuesta del radar", e)
                    }
                } else if (responseCode == HttpURLConnection.HTTP_NOT_FOUND) {
                    Log.d(TAG, "Usuario no encontrado en el radar (404)")

                    // Intentar con la API alternativa
                    fetchLocationFromAlternativeSource(userId)
                } else {
                    Log.e(TAG, "Error al obtener datos de radar. Código: $responseCode")

                    // Si la API principal falla, intentamos con la alternativa
                    fetchLocationFromAlternativeSource(userId)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error al conectar con el servidor de radar", e)

                // Si hay una excepción, intentamos con la alternativa
                fetchLocationFromAlternativeSource(userId)
            }
        }
    }

    /**
     * Intenta obtener la ubicación desde una fuente alternativa si el radar falla
     */
    private fun fetchLocationFromAlternativeSource(userId: Int) {
        Log.d(TAG, "Intentando obtener ubicación desde fuente alternativa para usuario $userId")

        thread {
            try {
                // Intentar obtener la ubicación desde la API de perfil que podría tener los datos de ubicación
                val url = URL("$API_URL/radar/nearby?userId=$userId&limit=1")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 15000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().use { it.readText() }

                    try {
                        val jsonResponse = JSONObject(response)
                        if (jsonResponse.optBoolean("success", false)) {
                            val usersArray = jsonResponse.optJSONArray("users")
                            if (usersArray != null && usersArray.length() > 0) {
                                // Buscar al usuario en la lista
                                for (i in 0 until usersArray.length()) {
                                    val userObj = usersArray.optJSONObject(i)
                                    if (userObj != null && userObj.optInt("id") == userId) {
                                        val city = userObj.optString("city", "")
                                        val country = userObj.optString("country", "")

                                        Log.d(TAG, "Datos alternativos obtenidos - Ciudad: '$city', País: '$country'")

                                        if (!city.isNullOrEmpty() || !country.isNullOrEmpty()) {
                                            updateLocationData(city, country)
                                            return@thread
                                        }
                                    }
                                }
                            }
                            Log.d(TAG, "No se encontró el usuario en la respuesta alternativa")
                        } else {
                            Log.e(TAG, "Error en respuesta alternativa: ${jsonResponse.optString("message")}")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error procesando respuesta alternativa", e)
                    }
                } else {
                    Log.e(TAG, "Error en solicitud alternativa. Código: $responseCode")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error conectando con fuente alternativa", e)
            }
        }
    }

    /**
     * Guarda la información de ubicación de un usuario en SharedPreferences
     */
    private fun saveUserLocation(userId: Int, city: String?, country: String?) {
        if (city.isNullOrEmpty() && country.isNullOrEmpty()) return

        try {
            Log.d(TAG, "Guardando ubicación para usuario $userId: Ciudad='$city', País='$country'")

            // Guardar en el cache en memoria
            locationCache[userId] = Pair(city, country)

            // Guardar en SharedPreferences
            val locationPrefs = getSharedPreferences("VMeetLocationCache", MODE_PRIVATE)
            val editor = locationPrefs.edit()

            // Usamos el ID de usuario como clave
            val key = "location_$userId"
            val locationData = JSONObject().apply {
                put("city", city ?: "")
                put("country", country ?: "")
                put("timestamp", System.currentTimeMillis())
            }

            editor.putString(key, locationData.toString())
            editor.apply()

            Log.d(TAG, "Ubicación guardada en cache para usuario $userId")
        } catch (e: Exception) {
            Log.e(TAG, "Error al guardar ubicación en cache", e)
        }
    }

    /**
     * Recupera la información de ubicación de un usuario desde SharedPreferences
     * @return Pair<String?, String?> con (city, country) o null si no hay datos
     */
    private fun loadUserLocation(userId: Int): Pair<String?, String?>? {
        try {
            // Primero verificar el cache en memoria
            val cachedLocation = locationCache[userId]
            if (cachedLocation != null) {
                Log.d(TAG, "Ubicación encontrada en cache en memoria para usuario $userId: ${cachedLocation.first}, ${cachedLocation.second}")
                return cachedLocation
            }

            // Si no está en memoria, verificar SharedPreferences
            val locationPrefs = getSharedPreferences("VMeetLocationCache", MODE_PRIVATE)
            val key = "location_$userId"
            val locationJson = locationPrefs.getString(key, null) ?: return null

            val locationData = JSONObject(locationJson)
            val city = locationData.optString("city", "")
            val country = locationData.optString("country", "")
            val timestamp = locationData.optLong("timestamp", 0)

            // Verificar si los datos son válidos
            if ((city.isNotEmpty() || country.isNotEmpty()) && timestamp > 0) {
                // Verificar si los datos no son demasiado antiguos (máximo 7 días)
                val ageInMillis = System.currentTimeMillis() - timestamp
                val maxAgeInMillis = 7 * 24 * 60 * 60 * 1000L // 7 días

                if (ageInMillis <= maxAgeInMillis) {
                    Log.d(TAG, "Ubicación encontrada en SharedPreferences para usuario $userId: $city, $country")

                    // Guardar en cache en memoria para uso futuro
                    val result = Pair(city.ifEmpty { null }, country.ifEmpty { null })
                    locationCache[userId] = result
                    return result
                } else {
                    Log.d(TAG, "Ubicación encontrada en SharedPreferences para usuario $userId pero es demasiado antigua")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error al cargar ubicación desde cache", e)
        }

        return null
    }

    // Method to check verification status and show badge
    private fun checkVerificationStatus(userId: Int) {
        Thread {
            try {
                val url = URL("$API_URL/verification/status/$userId")
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000

                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().readText()
                    val jsonResponse = JSONObject(response)
                    val status = jsonResponse.optString("status", "not_verified")

                    runOnUiThread {
                        if (status == "approved") {
                            textViewVerifiedBadge.visibility = View.VISIBLE
                        } else {
                            textViewVerifiedBadge.visibility = View.GONE
                        }
                    }
                } else {
                    runOnUiThread {
                        textViewVerifiedBadge.visibility = View.GONE
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking verification status", e)
                runOnUiThread {
                    textViewVerifiedBadge.visibility = View.GONE
                }
            }
        }.start()
    }
}