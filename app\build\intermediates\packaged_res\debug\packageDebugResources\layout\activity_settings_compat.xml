<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient"
    tools:context=".activity.SettingsActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#1A1A3A"
        android:elevation="4dp"
        android:minHeight="?attr/actionBarSize"
        app:titleTextColor="@color/comfy_blue"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ajustes"
            android:textColor="@color/comfy_blue"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_gravity="center"/>
    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintBottom_toTopOf="@id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Sección de Mensajes -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="#1A1A3A"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Configuración de Mensajes"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/comfy_blue"
                        android:layout_marginBottom="16dp"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Recibir mensajes sin coincidencia"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switchReceiveMessages"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"/>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Sección de Localización -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="#1A1A3A"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Configuración de Localización"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/comfy_blue"
                        android:layout_marginBottom="16dp"/>

                    <!-- Compartir ubicación -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Compartir mi ubicación"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switchLocationSharing"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true"
                            app:thumbTint="@color/neon_blue"
                            app:trackTint="#3F51B5"/>

                    </LinearLayout>

                    <!-- Nivel de precisión -->
                    <LinearLayout
                        android:id="@+id/layoutAccuracyLevel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Nivel de precisión"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <TextView
                            android:id="@+id/textAccuracyLevel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Aproximada"
                            android:textColor="@color/comfy_blue"
                            android:textSize="14sp"/>

                    </LinearLayout>

                    <!-- Distancia máxima visible -->
                    <LinearLayout
                        android:id="@+id/layoutMaxDistance"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp"
                        android:background="?attr/selectableItemBackground"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Distancia máxima visible"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <TextView
                            android:id="@+id/textMaxDistance"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="50 km"
                            android:textColor="@color/comfy_blue"
                            android:textSize="14sp"/>

                    </LinearLayout>

                    <!-- Solo matches -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Visible solo para matches"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switchMatchesOnly"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false"
                            app:thumbTint="@color/neon_blue"
                            app:trackTint="#3F51B5"/>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Sección de Cuenta -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardBackgroundColor="#1A1A3A"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Configuración de Cuenta"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/comfy_blue"
                        android:layout_marginBottom="16dp"/>

                    <!-- Cambiar nombre de usuario -->
                    <LinearLayout
                        android:id="@+id/layoutChangeUsername"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Cambiar nombre de usuario"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_media_play"/>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#303060"/>

                    <!-- Cambiar contraseña -->
                    <LinearLayout
                        android:id="@+id/layoutChangePassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Cambiar contraseña"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_media_play"/>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#303060"/>

                    <!-- Cambiar email -->
                    <LinearLayout
                        android:id="@+id/layoutChangeEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Cambiar email"
                            android:textColor="@color/white"
                            android:textSize="16sp"/>

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@android:drawable/ic_media_play"/>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:menu="@menu/bottom_nav_menu"/>

</androidx.constraintlayout.widget.ConstraintLayout>
