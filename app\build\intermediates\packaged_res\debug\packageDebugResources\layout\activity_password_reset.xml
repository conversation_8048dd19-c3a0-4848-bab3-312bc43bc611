<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/cyberpunk_gradient"
    tools:context=".activity.PasswordResetActivity">

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="64dp"
        android:layout_marginEnd="24dp"
        android:text="Restablecer contraseña"
        android:textColor="@color/comfy_blue"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewSubtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:text="Ingresa tu nueva contraseña"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewTitle" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayoutPassword"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:textColorHint="@color/white"
        app:boxBackgroundColor="#33FFFFFF"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:boxStrokeColor="@color/comfy_blue"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/comfy_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewSubtitle"
        app:passwordToggleEnabled="true"
        app:passwordToggleTint="@color/white">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editTextNewPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Nueva contraseña"
            android:inputType="textPassword"
            android:textColor="@color/white"
            android:textColorHint="@color/white" />
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/textInputLayoutConfirmPassword"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="24dp"
        android:textColorHint="@color/white"
        app:boxBackgroundColor="#33FFFFFF"
        app:boxCornerRadiusBottomEnd="8dp"
        app:boxCornerRadiusBottomStart="8dp"
        app:boxCornerRadiusTopEnd="8dp"
        app:boxCornerRadiusTopStart="8dp"
        app:boxStrokeColor="@color/comfy_blue"
        app:boxStrokeWidth="1dp"
        app:hintTextColor="@color/comfy_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textInputLayoutPassword"
        app:passwordToggleEnabled="true"
        app:passwordToggleTint="@color/white">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editTextConfirmPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Confirmar contraseña"
            android:inputType="textPassword"
            android:textColor="@color/white"
            android:textColorHint="@color/white" />
    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/buttonResetPassword"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="24dp"
        android:background="@drawable/rounded_button_background"
        android:text="Restablecer contraseña"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textInputLayoutConfirmPassword" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
