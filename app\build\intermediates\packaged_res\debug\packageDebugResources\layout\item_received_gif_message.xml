<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingTop="4dp"
    android:paddingEnd="60dp"
    android:paddingBottom="4dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="260dp"
        app:cardBackgroundColor="@color/card_background"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        app:cardUseCompatPadding="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="wrap"
        app:strokeWidth="1dp" 
        app:strokeColor="@color/neon_green">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="8dp"
            android:background="@color/card_background">

            <TextView
                android:id="@+id/textViewMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="240dp"
                android:text="GIF"
                android:textColor="@color/neon_green"
                android:textSize="16sp" />

            <com.google.android.material.image.ShapeableImageView
                android:id="@+id/imageViewGif"
                android:layout_width="match_parent"
                android:layout_height="220dp"
                android:layout_marginTop="4dp"
                android:adjustViewBounds="true"
                android:contentDescription="GIF Image"
                android:scaleType="centerCrop"
                app:shapeAppearanceOverlay="@style/ShapeAppearance.App.RoundedGif" />

            <com.google.android.flexbox.FlexboxLayout
                android:id="@+id/reactionsContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                app:flexWrap="wrap"
                app:alignItems="center"
                app:flexDirection="row" />

            <TextView
                android:id="@+id/textViewTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:textColor="@color/neon_green"
                android:textSize="12sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout> 