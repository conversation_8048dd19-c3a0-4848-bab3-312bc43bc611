package com.spyro.vmeet

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.KeyEvent
import android.view.MenuItem
import android.view.View
import android.view.Window
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.spyro.vmeet.activity.BaseActivity
import com.spyro.vmeet.adapter.UsersAdminAdapter
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONArray
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.TimeUnit

class UserManagementActivity : BaseActivity() {
    companion object {
        private const val TAG = "UserManagementActivity"
    }

    private lateinit var recyclerUsers: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvEmptyList: TextView
    private lateinit var tilSearch: TextInputLayout
    private lateinit var etSearch: TextInputEditText
    private lateinit var btnSearch: MaterialButton
    private var userId: Int = 0

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_management)

        // Get user ID from intent or shared preferences
        userId = intent.getIntExtra("USER_ID", -1)
        if (userId <= 0) {
            // If not in intent, try shared preferences
            val prefs = getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", -1)
        }

        if (userId <= 0) {
            // No valid user ID, redirect to login
            Toast.makeText(this, "Error de autenticación", Toast.LENGTH_SHORT).show()
            startActivity(Intent(this, LoginActivity::class.java))
            finish()
            return
        }

        // Set up toolbar
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Gestión de Usuarios"

        // Initialize views
        recyclerUsers = findViewById(R.id.recyclerUsers)
        progressBar = findViewById(R.id.progressBar)
        tvEmptyList = findViewById(R.id.tvEmptyList)
        tilSearch = findViewById(R.id.tilSearch)
        etSearch = findViewById(R.id.etSearch)
        btnSearch = findViewById(R.id.btnSearch)

        // Set up RecyclerView
        recyclerUsers.layoutManager = LinearLayoutManager(this)

        // Set up search
        setupSearch()

        // Verify admin status and load users
        checkAdminStatus()
    }

    private fun setupSearch() {
        // Buscar cuando se presiona el botón
        btnSearch.setOnClickListener {
            performSearch()
        }

        // Buscar cuando se presiona Enter
        etSearch.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)) {
                performSearch()
                return@setOnEditorActionListener true
            }
            return@setOnEditorActionListener false
        }

        // Limpiar resultados cuando se borra el texto
        etSearch.addTextChangedListener(object: TextWatcher {
            override fun beforeTextChanged(text: CharSequence?, start: Int, count: Int, after: Int) {
                // No necesitamos implementar esto
            }

            override fun onTextChanged(text: CharSequence?, start: Int, before: Int, count: Int) {
                // No necesitamos implementar esto
            }

            override fun afterTextChanged(editable: Editable?) {
                if (editable.isNullOrEmpty()) {
                    loadUsers() // Cargar todos los usuarios cuando se borra el texto
                }
            }
        })
    }

    private fun performSearch() {
        val query = etSearch.text.toString().trim()
        if (query.isNotEmpty()) {
            searchUsers(query)
        } else {
            loadUsers()
        }
        // Ocultar teclado después de buscar
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        currentFocus?.let { view ->
            imm.hideSoftInputFromWindow(view.windowToken, 0)
            view.clearFocus()
        }
    }

    private fun checkAdminStatus() {
        val url = "http://77.110.116.89:3000/user/check-admin/$userId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        progressBar.visibility = View.VISIBLE
        recyclerUsers.visibility = View.GONE
        tvEmptyList.visibility = View.GONE

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error checking admin status", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al verificar permisos de administrador",
                        Toast.LENGTH_SHORT
                    ).show()
                    finish()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val isAdmin = jsonObject.getBoolean("isAdmin")

                    if (!success || !isAdmin) {
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            Toast.makeText(
                                this@UserManagementActivity,
                                "No tienes permisos de administrador",
                                Toast.LENGTH_SHORT
                            ).show()
                            finish()
                        }
                    } else {
                        // Load users if admin check passes
                        runOnUiThread {
                            loadUsers()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing admin status response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al verificar permisos de administrador",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                }
            }
        })
    }

    private fun loadUsers() {
        val url = "http://77.110.116.89:3000/admin/users?adminId=$userId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        progressBar.visibility = View.VISIBLE
        recyclerUsers.visibility = View.GONE
        tvEmptyList.visibility = View.GONE

        Log.d(TAG, "Requesting users from: $url")

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error loading users", e)
                    tvEmptyList.text = "Error al cargar usuarios:\n${e.message}\n\nVerifique su conexión a internet y vuelva a intentarlo."
                    tvEmptyList.visibility = View.VISIBLE
                    recyclerUsers.visibility = View.GONE
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    // Log de depuración para ver la respuesta
                    Log.d(TAG, "Response from server: $responseBody")

                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")

                    if (success) {
                        val usersArray = jsonObject.getJSONArray("users")
                        Log.d(TAG, "Number of users received: ${usersArray.length()}")

                        val usersList = mutableListOf<JSONObject>()

                        for (i in 0 until usersArray.length()) {
                            val user = usersArray.getJSONObject(i)
                            usersList.add(preprocessUserData(user))
                        }

                        runOnUiThread {
                            progressBar.visibility = View.GONE

                            if (usersList.isEmpty()) {
                                Log.w(TAG, "No users found after processing")
                                tvEmptyList.text = "No se encontraron usuarios disponibles.\nVerifique la conexión a internet y vuelva a intentarlo."
                                tvEmptyList.visibility = View.VISIBLE
                                recyclerUsers.visibility = View.GONE
                            } else {
                                Log.d(TAG, "Displaying ${usersList.size} users")
                                tvEmptyList.visibility = View.GONE
                                recyclerUsers.visibility = View.VISIBLE

                                val adapter = UsersAdminAdapter(usersList, object : UsersAdminAdapter.UserAdminListener {
                                    override fun onBanUser(userObject: JSONObject) {
                                        showBanDialog(userObject)
                                    }

                                    override fun onUnbanUser(userObject: JSONObject) {
                                        confirmUnban(userObject)
                                    }

                                    override fun onViewHistory(userObject: JSONObject) {
                                        showUserHistory(userObject)
                                    }

                                    override fun onMuteUser(userObject: JSONObject) {
                                        try {
                                            val targetUserId = userObject.getInt("id")
                                            val username = userObject.getString("username")

                                            showMuteDialog(targetUserId, username)
                                        } catch (e: JSONException) {
                                            Log.e(TAG, "Error getting user data for mute dialog", e)
                                            Toast.makeText(this@UserManagementActivity, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    override fun onUnmuteUser(userObject: JSONObject) {
                                        try {
                                            val targetUserId = userObject.getInt("id")
                                            val username = userObject.getString("username")
                                            confirmUnmute(targetUserId, username)
                                        } catch (e: JSONException) {
                                            Log.e(TAG, "Error getting user data for unmute action", e)
                                            Toast.makeText(this@UserManagementActivity, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    override fun onVerifyUser(userObject: JSONObject) {
                                        try {
                                            val targetUserId = userObject.getInt("id")
                                            val username = userObject.getString("username")
                                            confirmVerifyUser(targetUserId, username)
                                        } catch (e: JSONException) {
                                            Log.e(TAG, "Error getting user data for verification", e)
                                            Toast.makeText(this@UserManagementActivity, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                })
                                recyclerUsers.adapter = adapter
                            }
                        }
                    } else {
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error loading users",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing users response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error processing server response",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun checkUserMuteStatus(userId: Int): JSONObject? {
        try {
            // Log para depuración
            Log.d(TAG, "Checking mute status for user: $userId")

            // Use asynchronous request instead of synchronous call
            val url = "http://77.110.116.89:3000/user/mute-status/$userId"
            val request = Request.Builder()
                .url(url)
                .get()
                .build()

            client.newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "Error checking mute status for user $userId", e)
                }

                override fun onResponse(call: Call, response: Response) {
                    // Handle response asynchronously if needed
                    try {
                        val responseBody = response.body?.string()
                        val jsonObject = JSONObject(responseBody)
                        Log.d(TAG, "Mute status for user $userId: $jsonObject")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing mute status response", e)
                    }
                }
            })

            return null
        } catch (e: Exception) {
            Log.e(TAG, "Error checking mute status for user $userId", e)
        }
        return null
    }

    private fun searchUsers(query: String) {
        val url = "http://77.110.116.89:3000/admin/users?adminId=$userId&search=$query"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        progressBar.visibility = View.VISIBLE
        recyclerUsers.visibility = View.GONE
        tvEmptyList.visibility = View.GONE

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error searching users", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al buscar usuarios",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")

                    if (success) {
                        val usersArray = jsonObject.getJSONArray("users")
                        val usersList = mutableListOf<JSONObject>()

                        // Filtrar usuarios localmente también
                        for (i in 0 until usersArray.length()) {
                            val user = usersArray.getJSONObject(i)
                            val username = user.getString("username").lowercase()
                            val email = user.optString("email", "").lowercase()
                            val searchQuery = query.lowercase()

                            // Buscar coincidencias en username o email
                            if (username.contains(searchQuery) || email.contains(searchQuery)) {
                                // Comprobar estado de silencio
                                val muteStatus = checkUserMuteStatus(user.getInt("id"))
                                if (muteStatus != null) {
                                    user.put("is_muted", muteStatus.optBoolean("isMuted", false))
                                    if (muteStatus.optBoolean("isMuted", false)) {
                                        user.put("mute_end_time", muteStatus.optString("endTime", ""))
                                        val mutedBy = muteStatus.optJSONObject("mutedBy")
                                        if (mutedBy != null) {
                                            user.put("muted_by", mutedBy)
                                        }
                                    }
                                }
                                usersList.add(preprocessUserData(user))
                            }
                        }

                        runOnUiThread {
                            progressBar.visibility = View.GONE

                            if (usersList.isEmpty()) {
                                tvEmptyList.visibility = View.VISIBLE
                                tvEmptyList.text = "No se encontraron usuarios para: $query"
                                recyclerUsers.visibility = View.GONE
                            } else {
                                tvEmptyList.visibility = View.GONE
                                recyclerUsers.visibility = View.VISIBLE

                                val adapter = UsersAdminAdapter(usersList, object : UsersAdminAdapter.UserAdminListener {
                                    override fun onBanUser(userObject: JSONObject) {
                                        showBanDialog(userObject)
                                    }

                                    override fun onUnbanUser(userObject: JSONObject) {
                                        confirmUnban(userObject)
                                    }

                                    override fun onViewHistory(userObject: JSONObject) {
                                        showUserHistory(userObject)
                                    }

                                    override fun onMuteUser(userObject: JSONObject) {
                                        try {
                                            val targetUserId = userObject.getInt("id")
                                            val username = userObject.getString("username")
                                            showMuteDialog(targetUserId, username)
                                        } catch (e: JSONException) {
                                            Log.e(TAG, "Error getting user data for mute dialog", e)
                                            Toast.makeText(this@UserManagementActivity, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    override fun onUnmuteUser(userObject: JSONObject) {
                                        try {
                                            val targetUserId = userObject.getInt("id")
                                            val username = userObject.getString("username")
                                            confirmUnmute(targetUserId, username)
                                        } catch (e: JSONException) {
                                            Log.e(TAG, "Error getting user data for unmute action", e)
                                            Toast.makeText(this@UserManagementActivity, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
                                        }
                                    }

                                    override fun onVerifyUser(userObject: JSONObject) {
                                        try {
                                            val targetUserId = userObject.getInt("id")
                                            val username = userObject.getString("username")
                                            confirmVerifyUser(targetUserId, username)
                                        } catch (e: JSONException) {
                                            Log.e(TAG, "Error getting user data for verification", e)
                                            Toast.makeText(this@UserManagementActivity, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
                                        }
                                    }
                                })
                                recyclerUsers.adapter = adapter
                            }
                        }
                    } else {
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error al buscar usuarios",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing search response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al procesar respuesta de búsqueda",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun showBanDialog(userObject: JSONObject) {
        try {
            val targetUserId = userObject.getInt("id")
            val username = userObject.getString("username")

            val builder = AlertDialog.Builder(this)
            builder.setTitle("Banear a $username")

            // Inflate the ban dialog layout
            val dialogView = layoutInflater.inflate(R.layout.dialog_ban_user, null)
            builder.setView(dialogView)

            // Add buttons
            builder.setPositiveButton("Banear") { dialog, _ ->
                // Extract values from dialog
                val reasonEditText = dialogView.findViewById<EditText>(R.id.editTextBanReason)
                val durationEditText = dialogView.findViewById<EditText>(R.id.editTextBanDuration)

                val reason = reasonEditText.text.toString().trim()
                val durationText = durationEditText.text.toString().trim()

                // Duration can be empty for permanent ban
                val duration = if (durationText.isEmpty()) null else durationText.toIntOrNull()

                if (reason.isEmpty()) {
                    Toast.makeText(this, "Debes especificar un motivo", Toast.LENGTH_SHORT).show()
                    return@setPositiveButton
                }

                // Ban user
                banUser(targetUserId, reason, duration)
                dialog.dismiss()
            }

            builder.setNegativeButton("Cancelar") { dialog, _ -> dialog.cancel() }

            builder.show()
        } catch (e: JSONException) {
            Log.e(TAG, "Error getting user data for ban dialog", e)
            Toast.makeText(this, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
        }
    }

    private fun banUser(targetUserId: Int, reason: String, duration: Int?) {
        val url = "http://77.110.116.89:3000/admin/ban-user"

        val jsonBody = JSONObject().apply {
            put("userId", targetUserId)
            put("adminId", userId)
            put("reason", reason)
            if (duration != null) {
                put("duration", duration)
            }
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Log.e(TAG, "Error banning user", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al banear usuario",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val message = jsonObject.optString("message", "Usuario baneado")

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(
                                this@UserManagementActivity,
                                message,
                                Toast.LENGTH_SHORT
                            ).show()
                            // Reload users list
                            loadUsers()
                        } else {
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error: $message",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        Log.e(TAG, "Error parsing ban response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al procesar respuesta",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun confirmUnban(userObject: JSONObject) {
        try {
            val targetUserId = userObject.getInt("id")
            val username = userObject.getString("username")

            AlertDialog.Builder(this)
                .setTitle("Desbanear usuario")
                .setMessage("¿Estás seguro de que quieres desbanear a $username?")
                .setPositiveButton("Desbanear") { _, _ ->
                    unbanUser(targetUserId)
                }
                .setNegativeButton("Cancelar", null)
                .show()
        } catch (e: JSONException) {
            Log.e(TAG, "Error getting user data for unban dialog", e)
            Toast.makeText(this, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
        }
    }

    private fun unbanUser(targetUserId: Int) {
        val url = "http://77.110.116.89:3000/admin/unban-user"

        val jsonBody = JSONObject().apply {
            put("userId", targetUserId)
            put("adminId", userId)
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Log.e(TAG, "Error unbanning user", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al desbanear usuario",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val message = jsonObject.optString("message", "Usuario desbaneado")

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(
                                this@UserManagementActivity,
                                message,
                                Toast.LENGTH_SHORT
                            ).show()
                            // Reload users list
                            loadUsers()
                        } else {
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error: $message",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        Log.e(TAG, "Error parsing unban response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al procesar respuesta",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun confirmUnmute(targetUserId: Int, username: String) {
        AlertDialog.Builder(this)
            .setTitle("Desilenciar Usuario")
            .setMessage("¿Estás seguro que deseas desilenciar a $username?")
            .setPositiveButton("Desilenciar") { _, _ ->
                executeUnmuteUser(targetUserId)
            }
            .setNegativeButton("Cancelar", null)
            .show()
    }

    private fun executeUnmuteUser(targetUserId: Int) {
        // Show progress
        progressBar.visibility = View.VISIBLE
          val url = "http://77.110.116.89:3000/user/unmute/$targetUserId?userId=$userId"

        val request = Request.Builder()
            .url(url)
            .delete()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error unmuting user", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al desilenciar usuario",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonResponse = JSONObject(responseBody)
                    val success = jsonResponse.optBoolean("success", false)
                    val message = jsonResponse.optString("message", "")

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        if (success) {
                            Toast.makeText(
                                this@UserManagementActivity,
                                message.ifEmpty { "Usuario desilenciado correctamente" },
                                Toast.LENGTH_SHORT
                            ).show()
                            // Reload users list to reflect changes
                            loadUsers()
                        } else {
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error: ${message.ifEmpty { "No se pudo desilenciar al usuario" }}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing unmute response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al procesar respuesta",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun showUserHistory(userObject: JSONObject) {
        try {
            val targetUserId = userObject.getInt("id")
            val username = userObject.getString("username")

            // For now, just show a toast
            Toast.makeText(this, "Historial para $username en desarrollo", Toast.LENGTH_SHORT).show()

            // TODO: Implement ban history view
        } catch (e: JSONException) {
            Log.e(TAG, "Error getting user data for history", e)
            Toast.makeText(this, "Error al procesar datos de usuario", Toast.LENGTH_SHORT).show()
        }
    }

    private fun showMuteDialog(targetUserId: Int, username: String) {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(R.layout.dialog_mute_user)
        dialog.window?.setGravity(Gravity.CENTER)
        dialog.window?.setBackgroundDrawableResource(android.R.color.transparent)

        // Update description with username
        val textViewDescription = dialog.findViewById<TextView>(R.id.textViewMuteDescription)
        textViewDescription.text = "Elige por cuánto tiempo quieres silenciar a $username:"

        // Set up the radio group
        val radioGroup = dialog.findViewById<RadioGroup>(R.id.radioGroupMuteDuration)

        // Set up button actions
        val buttonCancel = dialog.findViewById<Button>(R.id.buttonCancelMute)
        val buttonConfirm = dialog.findViewById<Button>(R.id.buttonConfirmMute)

        buttonCancel.setOnClickListener {
            dialog.dismiss()
        }

        buttonConfirm.setOnClickListener {
            // Get selected duration in minutes
            val durationMinutes = when (radioGroup.checkedRadioButtonId) {
                R.id.radio5min -> 5
                R.id.radio15min -> 15
                R.id.radio1hour -> 60
                R.id.radio24hours -> 24 * 60
                R.id.radio7days -> 7 * 24 * 60
                else -> 5 // Default to 5 minutes
            }

            // Execute mute action
            executeMuteUser(targetUserId, durationMinutes)

            dialog.dismiss()
        }

        dialog.show()
    }

    private fun executeMuteUser(targetUserId: Int, durationMinutes: Int) {
        Log.d(TAG, "Muting user $targetUserId for $durationMinutes minutes")

        // Show loading indicator
        progressBar.visibility = View.VISIBLE

        val url = "http://77.110.116.89:3000/user/mute"
        val jsonBody = JSONObject().apply {
            put("userId", userId) // Admin user ID
            put("targetUserId", targetUserId) // User to be muted
            put("durationMinutes", durationMinutes)
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error muting user", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al silenciar usuario",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonResponse = JSONObject(responseBody)
                    val success = jsonResponse.optBoolean("success", false)
                    val message = jsonResponse.optString("message", "")

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        if (success) {
                            Toast.makeText(
                                this@UserManagementActivity,
                                message.ifEmpty { "Usuario silenciado correctamente" },
                                Toast.LENGTH_SHORT
                            ).show()
                            // Reload users list
                            loadUsers()
                        } else {
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error: ${message.ifEmpty { "No se pudo silenciar al usuario" }}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing mute response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al procesar respuesta",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    private fun preprocessUserData(user: JSONObject): JSONObject {
        try {
            // Si no existe array de IP history, crearlo
            if (!user.has("ip_history")) {
                user.put("ip_history", JSONArray())
            }

            // Si no existe array de device history, crearlo
            if (!user.has("device_history")) {
                user.put("device_history", JSONArray())
            }

            // Si last_ip existe y no está en ip_history, añadirlo
            val lastIp = user.optString("last_ip")
            if (!lastIp.isNullOrEmpty() && lastIp != "null") {
                val ipHistory = user.getJSONArray("ip_history")
                var found = false

                for (i in 0 until ipHistory.length()) {
                    if (ipHistory.getString(i) == lastIp) {
                        found = true
                        break
                    }
                }

                if (!found) {
                    ipHistory.put(lastIp)
                }
            }

            // Si device_serial existe y no está en device_history, añadirlo
            val deviceSerial = user.optString("device_serial")
            if (!deviceSerial.isNullOrEmpty() && deviceSerial != "null") {
                val deviceHistory = user.getJSONArray("device_history")
                var found = false

                for (i in 0 until deviceHistory.length()) {
                    if (deviceHistory.getString(i) == deviceSerial) {
                        found = true
                        break
                    }
                }

                if (!found) {
                    deviceHistory.put(deviceSerial)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error preprocessing user data", e)
        }

        return user
    }

    private fun confirmVerifyUser(targetUserId: Int, username: String) {
        AlertDialog.Builder(this)
            .setTitle("Verificar Usuario")
            .setMessage("¿Estás seguro de que quieres verificar a $username?\n\nEsto le otorgará el badge de usuario verificado que aparecerá en su perfil.")
            .setPositiveButton("Verificar") { _, _ ->
                verifyUser(targetUserId, username)
            }
            .setNegativeButton("Cancelar", null)
            .show()
    }

    private fun verifyUser(targetUserId: Int, username: String) {
        progressBar.visibility = View.VISIBLE

        val url = "http://77.110.116.89:3000/verification/admin-verify"

        val jsonBody = JSONObject().apply {
            put("userId", targetUserId)
            put("adminId", userId)
            put("notes", "Verificado directamente por administrador desde panel de gestión")
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error verifying user", e)
                    Toast.makeText(
                        this@UserManagementActivity,
                        "Error al verificar usuario",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val message = jsonObject.optString("message", "Usuario verificado exitosamente")

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        if (success) {
                            Toast.makeText(
                                this@UserManagementActivity,
                                "✅ $username ha sido verificado exitosamente",
                                Toast.LENGTH_LONG
                            ).show()
                            // Reload users list to reflect changes
                            loadUsers()
                        } else {
                            Toast.makeText(
                                this@UserManagementActivity,
                                "Error: $message",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: JSONException) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing verification response", e)
                        Toast.makeText(
                            this@UserManagementActivity,
                            "Error al procesar respuesta de verificación",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }
}