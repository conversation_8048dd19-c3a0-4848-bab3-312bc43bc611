package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0007\u0018\u0000 *2\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0003)*+B\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010\u000f\u001a\u00020\tH\u0016J \u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00052\u0006\u0010\u0013\u001a\u00020\u00022\u0006\u0010\u0014\u001a\u00020\tH\u0002J\u0018\u0010\u0015\u001a\u00020\u00112\u0006\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002J \u0010\u0019\u001a\u00020\u00112\u0006\u0010\u001a\u001a\u00020\t2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u0018H\u0002J\u0018\u0010\u001e\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00022\u0006\u0010\u0014\u001a\u00020\tH\u0016J\u0018\u0010\u001f\u001a\u00020\u00022\u0006\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\tH\u0016J\u0018\u0010#\u001a\u00020\u00112\u0006\u0010$\u001a\u00020%2\u0006\u0010\u001a\u001a\u00020\tH\u0002J\u0018\u0010&\u001a\u00020\u00112\u0006\u0010\u0013\u001a\u00020\u00022\u0006\u0010\'\u001a\u00020(H\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006,"}, d2 = {"Lcom/spyro/vmeet/adapter/CommentsAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/CommentsAdapter$CommentViewHolder;", "comments", "", "Lcom/spyro/vmeet/model/Comment;", "(Ljava/util/List;)V", "userInfoCache", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/spyro/vmeet/adapter/CommentsAdapter$UserInfo;", "formatTimestamp", "", "timestamp", "", "getItemCount", "likeComment", "", "comment", "holder", "position", "loadAvatarImage", "avatarUrl", "imageView", "Landroid/widget/ImageView;", "loadUserInfo", "userId", "usernameTextView", "Landroid/widget/TextView;", "avatarImageView", "onBindViewHolder", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "openUserProfile", "context", "Landroid/content/Context;", "updateLikeButton", "isLiked", "", "CommentViewHolder", "Companion", "UserInfo", "app_release"})
public final class CommentsAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.CommentsAdapter.CommentViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.model.Comment> comments = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.Integer, com.spyro.vmeet.adapter.CommentsAdapter.UserInfo> userInfoCache = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.adapter.CommentsAdapter.Companion Companion = null;
    
    public CommentsAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.model.Comment> comments) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.CommentsAdapter.CommentViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.CommentsAdapter.CommentViewHolder holder, int position) {
    }
    
    private final void updateLikeButton(com.spyro.vmeet.adapter.CommentsAdapter.CommentViewHolder holder, boolean isLiked) {
    }
    
    private final void likeComment(com.spyro.vmeet.model.Comment comment, com.spyro.vmeet.adapter.CommentsAdapter.CommentViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    private final java.lang.String formatTimestamp(long timestamp) {
        return null;
    }
    
    private final void loadUserInfo(int userId, android.widget.TextView usernameTextView, android.widget.ImageView avatarImageView) {
    }
    
    private final void loadAvatarImage(java.lang.String avatarUrl, android.widget.ImageView imageView) {
    }
    
    private final void openUserProfile(android.content.Context context, int userId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0011\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0011\u0010\u0013\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0011\u0010\u0015\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0010\u00a8\u0006\u0017"}, d2 = {"Lcom/spyro/vmeet/adapter/CommentsAdapter$CommentViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "avatarImageView", "Landroid/widget/ImageView;", "getAvatarImageView", "()Landroid/widget/ImageView;", "buttonLike", "Landroid/widget/ImageButton;", "getButtonLike", "()Landroid/widget/ImageButton;", "textComment", "Landroid/widget/TextView;", "getTextComment", "()Landroid/widget/TextView;", "textLikes", "getTextLikes", "textTimestamp", "getTextTimestamp", "textUsername", "getTextUsername", "app_release"})
    public static final class CommentViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textUsername = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textComment = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textTimestamp = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageButton buttonLike = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textLikes = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView avatarImageView = null;
        
        public CommentViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextUsername() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextComment() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextTimestamp() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageButton getButtonLike() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextLikes() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getAvatarImageView() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/adapter/CommentsAdapter$Companion;", "", "()V", "API_URL", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u001f\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/adapter/CommentsAdapter$UserInfo;", "", "username", "", "avatarUrl", "(Ljava/lang/String;Ljava/lang/String;)V", "getAvatarUrl", "()Ljava/lang/String;", "getUsername", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
    public static final class UserInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String username = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String avatarUrl = null;
        
        public UserInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String username, @org.jetbrains.annotations.Nullable()
        java.lang.String avatarUrl) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUsername() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getAvatarUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.adapter.CommentsAdapter.UserInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String username, @org.jetbrains.annotations.Nullable()
        java.lang.String avatarUrl) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}