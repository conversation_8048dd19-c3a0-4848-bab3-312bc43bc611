<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <LinearLayout
        android:id="@+id/layoutBuzzMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bg_buzz_message_sent"
        android:padding="16dp"
        android:layout_marginStart="64dp"
        android:layout_marginEnd="16dp"
        android:elevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_vibration"
                android:tint="@color/buzz_text_color"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textViewBuzzMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¡BuZzZzzZzZZ!"
                android:textColor="@color/buzz_text_color"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:shadowColor="@color/buzz_text_shadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2"
                android:letterSpacing="0.1" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_vibration"
                android:tint="@color/buzz_text_color"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/textViewBuzzTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12:34"
            android:textColor="#B0BEC5"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            android:gravity="end" />

    </LinearLayout>

    <View
        android:id="@+id/viewBuzzStatus"
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:background="@drawable/status_sent"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        app:layout_constraintEnd_toEndOf="@id/layoutBuzzMessage"
        app:layout_constraintTop_toBottomOf="@id/layoutBuzzMessage" />

</androidx.constraintlayout.widget.ConstraintLayout>
