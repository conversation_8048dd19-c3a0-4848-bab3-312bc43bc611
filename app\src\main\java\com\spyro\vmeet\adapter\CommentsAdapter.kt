package com.spyro.vmeet.adapter

import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.spyro.vmeet.R
import com.spyro.vmeet.model.Comment
import com.spyro.vmeet.ProfileActivity
import okhttp3.*
import org.json.JSONObject
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap

class CommentsAdapter(private val comments: List<Comment>) : RecyclerView.Adapter<CommentsAdapter.CommentViewHolder>() {

    companion object {
        private const val API_URL = "http://77.110.116.89:3000"
    }

    // Enhanced cache for complete user info
    data class UserInfo(val username: String, val avatarUrl: String?)
    private val userInfoCache = ConcurrentHashMap<Int, UserInfo>()

    class CommentViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textUsername: TextView = itemView.findViewById(R.id.textViewCommentUsername)
        val textComment: TextView = itemView.findViewById(R.id.textViewCommentContent)
        val textTimestamp: TextView = itemView.findViewById(R.id.textViewCommentTimestamp)
        val buttonLike: android.widget.ImageButton = itemView.findViewById(R.id.buttonCommentLike)
        val textLikes: TextView = itemView.findViewById(R.id.textViewCommentLikes)
        val avatarImageView: android.widget.ImageView = itemView.findViewById(R.id.imageViewCommentUserAvatar)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CommentViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_comment, parent, false)
        return CommentViewHolder(view)
    }

    override fun onBindViewHolder(holder: CommentViewHolder, position: Int) {
        val comment = comments[position]

        holder.textUsername.text = comment.username
        holder.textComment.text = comment.text
        holder.textTimestamp.text = formatTimestamp(comment.timestamp)
        holder.textLikes.text = comment.likesCount.toString()

        // Update like button appearance based on like status
        updateLikeButton(holder, comment.isLiked)

        // Set up like button click listener
        holder.buttonLike.setOnClickListener {
            likeComment(comment, holder, position)
        }

        // Load user avatar and info
        loadUserInfo(comment.userId, holder.textUsername, holder.avatarImageView)

        // Set up click listeners for profile navigation
        holder.avatarImageView.setOnClickListener {
            openUserProfile(holder.itemView.context, comment.userId)
        }

        holder.textUsername.setOnClickListener {
            openUserProfile(holder.itemView.context, comment.userId)
        }
    }

    private fun updateLikeButton(holder: CommentViewHolder, isLiked: Boolean) {
        if (isLiked) {
            holder.buttonLike.setImageResource(R.drawable.ic_heart) // Filled heart
            holder.buttonLike.setColorFilter(holder.itemView.context.getColor(R.color.neon_pink))
        } else {
            holder.buttonLike.setImageResource(R.drawable.ic_like_outline) // Outline heart
            holder.buttonLike.setColorFilter(holder.itemView.context.getColor(R.color.white))
        }
    }

    private fun likeComment(comment: Comment, holder: CommentViewHolder, position: Int) {
        Log.d("CommentsAdapter", "Liking comment ${comment.id}")

        val client = OkHttpClient()
        val requestBody = FormBody.Builder()
            .add("userId", "1") // TODO: Get real user ID
            .build()

        val request = Request.Builder()
            .url("$API_URL/videos/comments/${comment.id}/like")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("CommentsAdapter", "Error liking comment", e)
                holder.itemView.post {
                    Toast.makeText(holder.itemView.context, "Error al dar like al comentario", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    Log.d("CommentsAdapter", "Like response: $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonObject = JSONObject(responseBody)
                        val liked = jsonObject.getBoolean("liked")
                        val likeCount = jsonObject.getInt("likeCount")

                        // Update comment data
                        comment.isLiked = liked
                        comment.likesCount = likeCount

                        // Update UI on main thread
                        holder.itemView.post {
                            holder.textLikes.text = likeCount.toString()
                            updateLikeButton(holder, liked)

                            val message = if (liked) "¡Te gusta este comentario!" else "Like eliminado"
                            Toast.makeText(holder.itemView.context, message, Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        Log.e("CommentsAdapter", "Server error: ${response.code}")
                        holder.itemView.post {
                            Toast.makeText(holder.itemView.context, "Error del servidor", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: Exception) {
                    Log.e("CommentsAdapter", "Error parsing like response", e)
                    holder.itemView.post {
                        Toast.makeText(holder.itemView.context, "Error al procesar respuesta", Toast.LENGTH_SHORT).show()
                    }
                }
            }
        })
    }

    override fun getItemCount() = comments.size

    private fun formatTimestamp(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < 60000 -> "ahora"
            diff < 3600000 -> "${diff / 60000}m"
            diff < 86400000 -> "${diff / 3600000}h"
            diff < 604800000 -> "${diff / 86400000}d"
            else -> {
                val sdf = SimpleDateFormat("dd/MM", Locale.getDefault())
                sdf.format(Date(timestamp))
            }
        }
    }

    private fun loadUserInfo(userId: Int, usernameTextView: TextView, avatarImageView: android.widget.ImageView) {
        Log.d("CommentsAdapter", "Loading user info for userId: $userId")

        // Check cache first
        userInfoCache[userId]?.let { cachedUserInfo ->
            Log.d("CommentsAdapter", "Found cached info for userId $userId: ${cachedUserInfo.username}, avatar: ${cachedUserInfo.avatarUrl}")
            usernameTextView.text = cachedUserInfo.username
            // Load cached avatar if available
            if (!cachedUserInfo.avatarUrl.isNullOrEmpty()) {
                loadAvatarImage(cachedUserInfo.avatarUrl, avatarImageView)
            } else {
                // Only set default if no avatar URL in cache
                avatarImageView.setImageResource(R.drawable.default_avatar)
            }
            return
        }

        // Set default username while loading from API
        usernameTextView.text = "Usuario"
        Log.d("CommentsAdapter", "No cache found for userId $userId, loading from API...")

        // Load from API
        val client = OkHttpClient()
        val request = Request.Builder()
            .url("$API_URL/profile/profile/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e("CommentsAdapter", "Error loading user info for userId $userId", e)
                // Set default avatar on failure
                avatarImageView.post {
                    avatarImageView.setImageResource(R.drawable.default_avatar)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    Log.d("CommentsAdapter", "API response for userId $userId: $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonObject = JSONObject(responseBody)
                        val userObject = jsonObject.optJSONObject("user")
                        val username = userObject?.optString("username", "Usuario") ?: "Usuario"
                        val avatarUrl = userObject?.optString("avatar_url", null)

                        Log.d("CommentsAdapter", "Parsed user info - username: $username, avatarUrl: $avatarUrl")

                        // Cache the complete user info
                        userInfoCache[userId] = UserInfo(username, avatarUrl)

                        // Update UI on main thread
                        usernameTextView.post {
                            usernameTextView.text = username

                            // Load avatar if available, otherwise set default
                            if (!avatarUrl.isNullOrEmpty()) {
                                Log.d("CommentsAdapter", "Loading avatar image for userId $userId: $avatarUrl")
                                loadAvatarImage(avatarUrl, avatarImageView)
                            } else {
                                Log.d("CommentsAdapter", "No avatar URL for userId $userId, setting default")
                                avatarImageView.setImageResource(R.drawable.default_avatar)
                            }
                        }
                    } else {
                        Log.e("CommentsAdapter", "API error for userId $userId: ${response.code}")
                        avatarImageView.post {
                            avatarImageView.setImageResource(R.drawable.default_avatar)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("CommentsAdapter", "Error parsing user info for userId $userId", e)
                    avatarImageView.post {
                        avatarImageView.setImageResource(R.drawable.default_avatar)
                    }
                }
            }
        })
    }

    private fun loadAvatarImage(avatarUrl: String, imageView: android.widget.ImageView) {
        // Use Glide to load the avatar image with enhanced caching and smooth transitions
        try {
            val fullUrl = if (avatarUrl.startsWith("http")) {
                avatarUrl
            } else {
                "$API_URL$avatarUrl"
            }

            Log.d("CommentsAdapter", "Loading avatar image with Glide: $fullUrl")

            com.bumptech.glide.Glide.with(imageView.context)
                .load(fullUrl)
                .placeholder(R.drawable.default_avatar)
                .error(R.drawable.default_avatar)
                .fallback(R.drawable.default_avatar)
                .diskCacheStrategy(com.bumptech.glide.load.engine.DiskCacheStrategy.ALL)
                .skipMemoryCache(false) // Enable memory cache for faster loading
                .circleCrop() // Make it circular like other avatars
                .transition(com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions.withCrossFade(200))
                .into(imageView)

            Log.d("CommentsAdapter", "Glide request initiated for avatar: $fullUrl")
        } catch (e: Exception) {
            Log.e("CommentsAdapter", "Error loading avatar image", e)
            imageView.setImageResource(R.drawable.default_avatar)
        }
    }

    private fun openUserProfile(context: Context, userId: Int) {
        Log.d("CommentsAdapter", "Opening profile for userId: $userId")
        val intent = Intent(context, ProfileActivity::class.java)
        intent.putExtra("USER_ID", userId)
        intent.putExtra("VIEW_ONLY_MODE", true)
        context.startActivity(intent)
    }
}
