package com.spyro.vmeet.ui

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.spyro.vmeet.R

class VerificationBadge @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ImageView(context, attrs, defStyleAttr) {

    init {
        // Set default properties
        setImageResource(R.drawable.ic_check)
        scaleType = ScaleType.CENTER_CROP
        visibility = View.GONE
        
        // Set default size (16dp)
        val size = (16 * context.resources.displayMetrics.density).toInt()
        layoutParams = layoutParams?.apply {
            width = size
            height = size
        } ?: android.view.ViewGroup.LayoutParams(size, size)
    }

    fun setVerificationStatus(status: String) {
        when (status) {
            "approved" -> {
                visibility = View.VISIBLE
                setColorFilter(ContextCompat.getColor(context, R.color.neon_green))
                contentDescription = "Usuario verificado"
            }
            "pending" -> {
                visibility = View.VISIBLE
                setColorFilter(ContextCompat.getColor(context, R.color.cyberpunk_accent_secondary))
                contentDescription = "Verificación pendiente"
            }
            "rejected" -> {
                visibility = View.GONE
                contentDescription = null
            }
            else -> {
                visibility = View.GONE
                contentDescription = null
            }
        }
    }

    fun hide() {
        visibility = View.GONE
        contentDescription = null
    }

    fun showApproved() {
        setVerificationStatus("approved")
    }

    fun showPending() {
        setVerificationStatus("pending")
    }
}
