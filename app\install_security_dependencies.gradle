// Add these dependencies to your app/build.gradle file

dependencies {
    // JWT and Security dependencies
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    
    // HTTP client with interceptors
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // JSON handling
    implementation 'org.json:json:20230227'
    
    // Existing dependencies (keep these)
    // implementation 'androidx.core:core-ktx:1.8.0'
    // implementation 'androidx.appcompat:appcompat:1.6.1'
    // implementation 'com.google.android.material:material:1.5.0'
    // implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    // ... other existing dependencies
}
