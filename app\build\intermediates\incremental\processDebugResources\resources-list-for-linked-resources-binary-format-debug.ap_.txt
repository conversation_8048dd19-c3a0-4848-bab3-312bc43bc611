C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_admin_badge_glow.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_admin_badge_shimmer.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_buzz_earthquake.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_buzz_flash.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_buzz_intense_shake.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_buzz_shake.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_fade_in.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_pulse.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_rotate.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_slide_up.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_text_fade_in.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_text_rotate.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_text_slide_in_left.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable-v26_ic_notification_vmeet.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_badge_shimmer.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_gradient_1.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_gradient_2.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_gradient_3.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_gradient_animation.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_name_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_name_gradient.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_name_gradient_1.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_name_gradient_2.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_admin_name_gradient_3.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_avatar_circle_border.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_background_reply_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_background_reply_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_badge_admin.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_badge_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_badge_verified.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_buzz_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_buzz_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_document_type_chip.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_edit_text.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_image_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_status_chip.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bottom_sheet_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_cyberpunk.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_outline_cyberpunk.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_rounded_accent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_buzz_pulse_animation.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_button_bg.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_dot.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_online_status.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circular_button.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circular_button_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_color_preview_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_color_square_shape.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_color_swatch_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_comment_input_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_cyberpunk_circle_button.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_cyberpunk_gradient.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_default_avatar.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_default_room_icon.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_distance_badge_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_glowing_border.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_glowing_neon_ring.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_bottom_overlay.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_overlay.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add_gif.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add_image.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add_mic.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add_post.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_admin_crown.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arrow_back.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arrow_forward.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_audiotrack.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_chat.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_chat_bubble.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_chats.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_check.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_check_outlined.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_close_circle.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_comment_outline.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_community.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_default_avatar.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_delete.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_document_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_emoji.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_eye.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_filter.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_gif.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_heart.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_launcher_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_launcher_foreground.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_like_outline.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_location.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_location_permission.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_mark_read.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_matches.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_more_vert.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_music_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_mute.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_notification.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_notification_vmeet.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_pause.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_person_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_placeholder_image.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_play.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_play_arrow.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_profile.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_profile_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_radar_empty.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_refresh.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_reply.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_search.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_send.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_send_comment.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_settings.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_stop_mic.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_swipe.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_topic_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_verified_check.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_vibration.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_image_error.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_image_placeholder.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_indicator_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_login_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_message_input_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_message_received_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_message_sent_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_mic_button_selector.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_modern_button.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_modern_button_alt.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_modern_edit_text_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_neon_button.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_neon_button_alt.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_neon_button_blue.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_neon_button_green.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_neon_button_red.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_neon_circle.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_online_status_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_online_status_indicator.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_post_edit_text_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_preview_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_progress_bar_cyberpunk.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_radar_header_gradient.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_reaction_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_reaction_background_own.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_reaction_container_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_reaction_selector_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_recording_button_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_recording_cancel_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_reply_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_button_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_button_outline_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_corner_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_dark_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_dialog_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_edittext_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_edittext_bg.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_search_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_section_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_selected_color_border.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_spinner_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_status_error.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_status_pending.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_status_read.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_status_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_story_ring_seen.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_story_ring_unseen.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_story_ring_unseen_gradient.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_swipe_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tab_dot_default.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tab_dot_selected.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tab_selected.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tab_selector.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tab_unselected.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_trait_selected_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_trait_unselected_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_unread_counter_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_unread_indicator.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_voice_note_player_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_youtube_play_button_background.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_app_font.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_combackhomeregularjemd9.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_greatsracingfreeforpersonalitalicbll.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_inter.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_interitalic.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_knightwarriorw16n8.otf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_orbitron.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_steelarj9vnj.otf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_supremespikekvo8d.otf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_veniteadoremusrgrba.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_veniteadoremusstraightyzo6v.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\font_wowdinog33vp.ttf.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_admin_panel.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_blind_date.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_blocked_users.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_chat.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_chat_list.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_chat_room.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_comments.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_community_guidelines.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_community_host.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_edit_personality_traits.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_email_verification.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_forgot_password.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_full_screen_image.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_login.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_main_empty.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_matches.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_my_videos.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_password_reset.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_profile.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_reports.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_room_loader.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_settings.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_settings_compat.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_story_editor.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_story_viewer.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_swipe.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_tutorial.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_upload_video.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_user_management.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_user_profile.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_verification.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_verification_management.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_video_feed.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_add_text_with_style.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_ban_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_change_email.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_change_password.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_change_username.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_color_palette_picker.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_comments_bottom_sheet.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_create_post.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_create_room.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_custom_color_picker.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_delete_room_confirmation.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_edit_room.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_emoji_picker.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_gif_picker.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_global_notification.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_improved_emoji_picker.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_message_options.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_music_search.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_mute_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_radar_filters.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_reject_verification.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_report_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_rules_acceptance.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_story_viewers.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dropdown_item.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_blind_date.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_chat_rooms.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_community.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_private_chats.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_profiles.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_radar.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_grid_emoji_category.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_admin_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_blind_date_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_blind_date_message_received_modern.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_blind_date_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_blind_date_message_sent_modern.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_blind_date_topic.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_blocked_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_chat_list.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_chat_room.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_comment.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_emoji.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_gif.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_gif_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_gif_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_image_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_image_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_match.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_mention_suggestion.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_message_buzz_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_message_buzz_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_message_reaction.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_message_sent.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_music_search.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_my_video.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_personality_trait.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_personality_trait_category.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_post.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_profile_image.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_radar_user.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_received_gif_message.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_report.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_sent_gif_message.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_story.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_story_viewer.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_user_card.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_verification.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_video.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_voice_message.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_voice_message_received.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_blind_date_chat.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_blind_date_chat_modern.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_blind_date_result.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_blind_date_reveal.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_blind_date_waiting.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_join_room_prompt.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_profile_image_slider.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_reaction_selector.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_user_muted.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_menu_community_dropdown.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_profile_dropdown_menu.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_tutorial_page.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_bottom_nav_menu.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_chat_menu.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_location_options.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_match_item.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_post_options.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_room_admin.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_menu_swipe.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_profile_menu.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_room_chat_menu.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v26_ic_launcher.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v26_ic_launcher_round.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher_foreground.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher_round.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher_foreground.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher_round.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher_foreground.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher_round.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher_foreground.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher_round.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher_round.webp.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\raw_welcome.mp3.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-af_values-af.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-am_values-am.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ar_values-ar.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-as_values-as.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-az_values-az.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+es+419_values-b+es+419.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+sr+Latn_values-b+sr+Latn.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-be_values-be.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bg_values-bg.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bn_values-bn.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bs_values-bs.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ca_values-ca.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-cs_values-cs.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-da_values-da.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-de_values-de.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-el_values-el.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rAU_values-en-rAU.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rCA_values-en-rCA.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rGB_values-en-rGB.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rIN_values-en-rIN.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rXC_values-en-rXC.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es-rUS_values-es-rUS.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es_values-es.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-et_values-et.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-eu_values-eu.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fa_values-fa.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fi_values-fi.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr-rCA_values-fr-rCA.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr_values-fr.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gl_values-gl.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gu_values-gu.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h320dp-port-v13_values-h320dp-port-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h360dp-land-v13_values-h360dp-land-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h480dp-land-v13_values-h480dp-land-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h550dp-port-v13_values-h550dp-port-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h720dp-v13_values-h720dp-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hdpi-v4_values-hdpi-v4.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hi_values-hi.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hr_values-hr.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hu_values-hu.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hy_values-hy.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-in_values-in.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-is_values-is.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-it_values-it.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-iw_values-iw.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ja_values-ja.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ka_values-ka.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kk_values-kk.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-km_values-km.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kn_values-kn.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ko_values-ko.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ky_values-ky.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-land_values-land.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-large-v4_values-large-v4.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldltr-v21_values-ldltr-v21.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldrtl-v17_values-ldrtl-v17.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lo_values-lo.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lt_values-lt.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lv_values-lv.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mk_values-mk.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ml_values-ml.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mn_values-mn.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mr_values-mr.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ms_values-ms.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-my_values-my.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nb_values-nb.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ne_values-ne.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-night-v8_values-night-v8.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nl_values-nl.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-or_values-or.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pa_values-pa.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pl_values-pl.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-port_values-port.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rBR_values-pt-rBR.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rPT_values-pt-rPT.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt_values-pt.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ro_values-ro.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ru_values-ru.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-si_values-si.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sk_values-sk.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sl_values-sl.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-small-v4_values-small-v4.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sq_values-sq.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sr_values-sr.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sv_values-sv.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw600dp-v13_values-sw600dp-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw_values-sw.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ta_values-ta.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-te_values-te.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-th_values-th.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tl_values-tl.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tr_values-tr.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uk_values-uk.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ur_values-ur.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uz_values-uz.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v16_values-v16.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v17_values-v17.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v18_values-v18.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v21_values-v21.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v22_values-v22.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v23_values-v23.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v24_values-v24.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v25_values-v25.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v26_values-v26.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v28_values-v28.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v29_values-v29.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v31_values-v31.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v34_values-v34.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-vi_values-vi.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w320dp-land-v13_values-w320dp-land-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w360dp-port-v13_values-w360dp-port-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w400dp-port-v13_values-w400dp-port-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w600dp-land-v13_values-w600dp-land-v13.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v20_values-watch-v20.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v21_values-watch-v21.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-xlarge-v4_values-xlarge-v4.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rCN_values-zh-rCN.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rHK_values-zh-rHK.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rTW_values-zh-rTW.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zu_values-zu.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\values_values.arsc.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_backup_rules.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_data_extraction_rules.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_file_paths.xml.flat C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_network_security_config.xml.flat 