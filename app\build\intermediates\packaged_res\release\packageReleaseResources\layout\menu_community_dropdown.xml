<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="180dp"
    android:background="@drawable/rounded_dialog_background"
    android:backgroundTint="@color/cyberpunk_card_background"
    android:elevation="8dp"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- Publicaciones Option -->
    <LinearLayout
        android:id="@+id/layoutCommunity"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="14dp"
        android:minHeight="48dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_community"
            android:tint="@color/cyberpunk_text_primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/inter"
            android:text="Publicaciones"
            android:textColor="@color/cyberpunk_text_primary"
            android:textSize="16sp" />

    </LinearLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="4dp"
        android:background="@color/cyberpunk_divider" />

    <!-- Videos Option -->
    <LinearLayout
        android:id="@+id/layoutVideos"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="14dp"
        android:minHeight="48dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_play_arrow"
            android:tint="@color/cyberpunk_text_primary" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/inter"
            android:text="Videos"
            android:textColor="@color/cyberpunk_text_primary"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>
