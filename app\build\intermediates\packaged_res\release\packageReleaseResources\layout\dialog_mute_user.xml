<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="#212138"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Silenciar usuario"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp"/>

        <TextView
            android:id="@+id/textViewMuteDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Elige por cuánto tiempo quieres silenciar a este usuario:"
            android:textColor="#EEEEEE"
            android:textSize="14sp"
            android:layout_marginBottom="16dp"/>

        <RadioGroup
            android:id="@+id/radioGroupMuteDuration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RadioButton
                android:id="@+id/radio5min"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="5 minutos"
                android:textColor="#FFFFFF"
                android:buttonTint="#FFFFFF"
                android:checked="true"
                android:padding="8dp"/>

            <RadioButton
                android:id="@+id/radio15min"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="15 minutos"
                android:textColor="#FFFFFF"
                android:buttonTint="#FFFFFF"
                android:padding="8dp"/>

            <RadioButton
                android:id="@+id/radio1hour"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="1 hora"
                android:textColor="#FFFFFF"
                android:buttonTint="#FFFFFF"
                android:padding="8dp"/>

            <RadioButton
                android:id="@+id/radio24hours"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="24 horas"
                android:textColor="#FFFFFF"
                android:buttonTint="#FFFFFF"
                android:padding="8dp"/>

            <RadioButton
                android:id="@+id/radio7days"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="7 días"
                android:textColor="#FFFFFF"
                android:buttonTint="#FFFFFF"
                android:padding="8dp"/>
        </RadioGroup>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonCancelMute"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Cancelar"
                android:textAllCaps="false"
                android:backgroundTint="#444466"
                android:textColor="#FFFFFF"
                android:lines="1"
                android:ellipsize="end"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonConfirmMute"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Silenciar"
                android:textAllCaps="false"
                android:backgroundTint="#FF9800"
                android:textColor="#FFFFFF"
                android:lines="1"
                android:ellipsize="end"/>
        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>