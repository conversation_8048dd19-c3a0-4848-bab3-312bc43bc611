package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b9\b\u0087\b\u0018\u00002\u00020\u0001Bu\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\r\u001a\u00020\u0003\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0010\u0012\b\b\u0002\u0010\u0013\u001a\u00020\n\u00a2\u0006\u0002\u0010\u0014J\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\t\u00108\u001a\u00020\u0010H\u00c6\u0003J\t\u00109\u001a\u00020\u0010H\u00c6\u0003J\t\u0010:\u001a\u00020\u0010H\u00c6\u0003J\t\u0010;\u001a\u00020\nH\u00c6\u0003J\t\u0010<\u001a\u00020\u0005H\u00c6\u0003J\t\u0010=\u001a\u00020\u0007H\u00c6\u0003J\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\t\u0010?\u001a\u00020\nH\u00c6\u0003J\t\u0010@\u001a\u00020\nH\u00c6\u0003J\t\u0010A\u001a\u00020\u0005H\u00c6\u0003J\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\u008b\u0001\u0010D\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00102\b\b\u0002\u0010\u0012\u001a\u00020\u00102\b\b\u0002\u0010\u0013\u001a\u00020\nH\u00c6\u0001J\u0013\u0010E\u001a\u00020\n2\b\u0010F\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010G\u001a\u00020\u0005H\u00d6\u0001J\t\u0010H\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\r\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R\u001a\u0010\f\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\u001a\"\u0004\b\u001b\u0010\u001cR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u0010\u001a\"\u0004\b\u001e\u0010\u001cR\u001a\u0010\u000e\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010\u0016\"\u0004\b \u0010\u0018R\u001a\u0010\b\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010\u0016\"\u0004\b\"\u0010\u0018R\u001a\u0010\t\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\t\u0010#\"\u0004\b$\u0010%R\u001a\u0010\u000b\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010#\"\u0004\b&\u0010%R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010\u0016\"\u0004\b(\u0010\u0018R\u001a\u0010\u000f\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b)\u0010*\"\u0004\b+\u0010,R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b-\u0010.\"\u0004\b/\u00100R\u001a\u0010\u0013\u001a\u00020\nX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010#\"\u0004\b2\u0010%R\u001a\u0010\u0011\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010*\"\u0004\b4\u0010,R\u001a\u0010\u0012\u001a\u00020\u0010X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010*\"\u0004\b6\u0010,\u00a8\u0006I"}, d2 = {"Lcom/spyro/vmeet/ui/community/TextProperties;", "", "text", "", "color", "", "typeface", "Landroid/graphics/Typeface;", "fontResourceName", "isBold", "", "isItalic", "animationResId", "animationName", "fontDisplayName", "textSize", "", "xPercent", "yPercent", "wasDragged", "(Ljava/lang/String;ILandroid/graphics/Typeface;Ljava/lang/String;ZZILjava/lang/String;Ljava/lang/String;FFFZ)V", "getAnimationName", "()Ljava/lang/String;", "setAnimationName", "(Ljava/lang/String;)V", "getAnimationResId", "()I", "setAnimationResId", "(I)V", "getColor", "setColor", "getFontDisplayName", "setFontDisplayName", "getFontResourceName", "setFontResourceName", "()Z", "setBold", "(Z)V", "setItalic", "getText", "setText", "getTextSize", "()F", "setTextSize", "(F)V", "getTypeface", "()Landroid/graphics/Typeface;", "setTypeface", "(Landroid/graphics/Typeface;)V", "getWasDragged", "setWasDragged", "getXPercent", "setXPercent", "getYPercent", "setYPercent", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
public final class TextProperties {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String text;
    private int color;
    @org.jetbrains.annotations.NotNull()
    private android.graphics.Typeface typeface;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String fontResourceName;
    private boolean isBold;
    private boolean isItalic;
    private int animationResId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String animationName;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String fontDisplayName;
    private float textSize;
    private float xPercent;
    private float yPercent;
    private boolean wasDragged;
    
    public TextProperties(@org.jetbrains.annotations.NotNull()
    java.lang.String text, int color, @org.jetbrains.annotations.NotNull()
    android.graphics.Typeface typeface, @org.jetbrains.annotations.NotNull()
    java.lang.String fontResourceName, boolean isBold, boolean isItalic, int animationResId, @org.jetbrains.annotations.NotNull()
    java.lang.String animationName, @org.jetbrains.annotations.NotNull()
    java.lang.String fontDisplayName, float textSize, float xPercent, float yPercent, boolean wasDragged) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getText() {
        return null;
    }
    
    public final void setText(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getColor() {
        return 0;
    }
    
    public final void setColor(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Typeface getTypeface() {
        return null;
    }
    
    public final void setTypeface(@org.jetbrains.annotations.NotNull()
    android.graphics.Typeface p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFontResourceName() {
        return null;
    }
    
    public final void setFontResourceName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean isBold() {
        return false;
    }
    
    public final void setBold(boolean p0) {
    }
    
    public final boolean isItalic() {
        return false;
    }
    
    public final void setItalic(boolean p0) {
    }
    
    public final int getAnimationResId() {
        return 0;
    }
    
    public final void setAnimationResId(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAnimationName() {
        return null;
    }
    
    public final void setAnimationName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFontDisplayName() {
        return null;
    }
    
    public final void setFontDisplayName(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final float getTextSize() {
        return 0.0F;
    }
    
    public final void setTextSize(float p0) {
    }
    
    public final float getXPercent() {
        return 0.0F;
    }
    
    public final void setXPercent(float p0) {
    }
    
    public final float getYPercent() {
        return 0.0F;
    }
    
    public final void setYPercent(float p0) {
    }
    
    public final boolean getWasDragged() {
        return false;
    }
    
    public final void setWasDragged(boolean p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.Typeface component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final int component7() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.spyro.vmeet.ui.community.TextProperties copy(@org.jetbrains.annotations.NotNull()
    java.lang.String text, int color, @org.jetbrains.annotations.NotNull()
    android.graphics.Typeface typeface, @org.jetbrains.annotations.NotNull()
    java.lang.String fontResourceName, boolean isBold, boolean isItalic, int animationResId, @org.jetbrains.annotations.NotNull()
    java.lang.String animationName, @org.jetbrains.annotations.NotNull()
    java.lang.String fontDisplayName, float textSize, float xPercent, float yPercent, boolean wasDragged) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}