package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a8\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u0000 U2\u00020\u00012\u00020\u0002:\u0001UB\u0005\u00a2\u0006\u0002\u0010\u0003J\u0010\u00108\u001a\u0002092\u0006\u0010:\u001a\u00020;H\u0003J\u0018\u0010<\u001a\u0002092\u0006\u0010=\u001a\u00020\u00102\u0006\u0010:\u001a\u00020;H\u0002J\u0010\u0010>\u001a\u0002092\u0006\u0010?\u001a\u000201H\u0002J\u0012\u0010@\u001a\u0002092\b\u0010A\u001a\u0004\u0018\u00010BH\u0014J\b\u0010C\u001a\u000209H\u0014J\u0010\u0010D\u001a\u0002092\u0006\u0010?\u001a\u000201H\u0016J\b\u0010E\u001a\u000209H\u0014J\b\u0010F\u001a\u000209H\u0002J\b\u0010G\u001a\u000209H\u0002J\b\u0010H\u001a\u000209H\u0002J\u0014\u0010I\u001a\u0002092\n\b\u0002\u0010J\u001a\u0004\u0018\u00010\u0010H\u0002J$\u0010K\u001a\u0002092\u0006\u0010L\u001a\u00020M2\u0012\u0010N\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u0002090OH\u0002J\b\u0010P\u001a\u000209H\u0002J\u0010\u0010Q\u001a\u0002092\u0006\u0010R\u001a\u00020\nH\u0002J\b\u0010S\u001a\u000209H\u0002J\u0018\u0010T\u001a\u0002092\u0006\u0010=\u001a\u00020\u00102\u0006\u0010:\u001a\u00020;H\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\n0\u0017X\u0082.\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\t0\bX\u0082.\u00a2\u0006\u0002\n\u0000R\'\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n0\b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001c\u0010\u001d\u001a\u0004\b\u001a\u0010\u001bR\u000e\u0010\u001e\u001a\u00020\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010%\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020(X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00100\u001a\u0004\u0018\u000101X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u000204X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006V"}, d2 = {"Lcom/spyro/vmeet/ui/community/StoryEditorActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "Lcom/spyro/vmeet/ui/community/MusicSearchDialog$MusicSelectionListener;", "()V", "addMusicButton", "Landroid/widget/Button;", "addTextButton", "animationResourceMap", "", "", "", "closeEditorButton", "Landroid/widget/ImageButton;", "communityViewModel", "Lcom/spyro/vmeet/ui/community/CommunityViewModel;", "currentDraggableTextView", "Landroid/widget/TextView;", "deleteTextTrashCan", "Landroid/widget/ImageView;", "doneButton", "editorProgressBar", "Landroid/widget/ProgressBar;", "fontDisplayNames", "", "fontResourceMap", "fontResourceNameToDisplayNameMap", "getFontResourceNameToDisplayNameMap", "()Ljava/util/Map;", "fontResourceNameToDisplayNameMap$delegate", "Lkotlin/Lazy;", "isTextViewOverTrashCan", "", "mediaType", "mediaUri", "Landroid/net/Uri;", "musicArtistText", "musicCoverImage", "musicPlayer", "Landroid/media/MediaPlayer;", "musicPreviewLayout", "Landroid/widget/LinearLayout;", "musicTitleText", "publishingStatusText", "removeHMusicButton", "selectedAnimationResId", "selectedFont", "Landroid/graphics/Typeface;", "selectedFontDisplayName", "selectedMusic", "Lcom/spyro/vmeet/data/community/MusicData;", "selectedTextColor", "storyEditorFrame", "Landroid/widget/FrameLayout;", "storyEditorImageView", "userId", "username", "addTextToStory", "", "properties", "Lcom/spyro/vmeet/ui/community/TextProperties;", "applyTextProperties", "textView", "displaySelectedMusic", "music", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onMusicSelected", "onPause", "removeSelectedMusic", "saveAndUploadStory", "setupStoryUploadObservers", "showAddTextDialogWithStyle", "existingTextView", "showCustomColorPickerDialog", "previewView", "Landroid/view/View;", "onColorSelected", "Lkotlin/Function1;", "showMusicSearchDialog", "startMusicPreview", "previewUrl", "stopMusicPreview", "updateTextInStory", "Companion", "app_release"})
public final class StoryEditorActivity extends androidx.appcompat.app.AppCompatActivity implements com.spyro.vmeet.ui.community.MusicSearchDialog.MusicSelectionListener {
    private android.widget.FrameLayout storyEditorFrame;
    private android.widget.ImageView storyEditorImageView;
    private android.widget.Button addTextButton;
    private android.widget.Button addMusicButton;
    private android.widget.Button doneButton;
    private android.widget.ImageButton closeEditorButton;
    private android.widget.ProgressBar editorProgressBar;
    private android.widget.TextView publishingStatusText;
    private android.widget.ImageView deleteTextTrashCan;
    private boolean isTextViewOverTrashCan = false;
    private android.widget.LinearLayout musicPreviewLayout;
    private android.widget.ImageView musicCoverImage;
    private android.widget.TextView musicTitleText;
    private android.widget.TextView musicArtistText;
    private android.widget.ImageButton removeHMusicButton;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.community.MusicData selectedMusic;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaPlayer musicPlayer;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri mediaUri;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String mediaType = "image";
    @org.jetbrains.annotations.Nullable()
    private android.widget.TextView currentDraggableTextView;
    private com.spyro.vmeet.ui.community.CommunityViewModel communityViewModel;
    private int userId = -1;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String username = "Usuario";
    private int selectedTextColor = android.graphics.Color.WHITE;
    @org.jetbrains.annotations.NotNull()
    private android.graphics.Typeface selectedFont;
    private int selectedAnimationResId = 0;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String selectedFontDisplayName = "Predeterminada";
    private java.util.List<java.lang.String> fontDisplayNames;
    private java.util.Map<java.lang.String, java.lang.Integer> fontResourceMap;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.Integer, java.lang.String> animationResourceMap = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy fontResourceNameToDisplayNameMap$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_MEDIA_URI = "extra_media_uri";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_MEDIA_TYPE = "extra_media_type";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "StoryEditorActivity";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.community.StoryEditorActivity.Companion Companion = null;
    
    public StoryEditorActivity() {
        super();
    }
    
    private final java.util.Map<java.lang.String, java.lang.String> getFontResourceNameToDisplayNameMap() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void showMusicSearchDialog() {
    }
    
    @java.lang.Override()
    public void onMusicSelected(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.data.community.MusicData music) {
    }
    
    private final void displaySelectedMusic(com.spyro.vmeet.data.community.MusicData music) {
    }
    
    private final void removeSelectedMusic() {
    }
    
    private final void startMusicPreview(java.lang.String previewUrl) {
    }
    
    private final void stopMusicPreview() {
    }
    
    private final void showAddTextDialogWithStyle(android.widget.TextView existingTextView) {
    }
    
    private final void showCustomColorPickerDialog(android.view.View previewView, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onColorSelected) {
    }
    
    @android.annotation.SuppressLint(value = {"ClickableViewAccessibility"})
    private final void addTextToStory(com.spyro.vmeet.ui.community.TextProperties properties) {
    }
    
    private final void applyTextProperties(android.widget.TextView textView, com.spyro.vmeet.ui.community.TextProperties properties) {
    }
    
    private final void updateTextInStory(android.widget.TextView textView, com.spyro.vmeet.ui.community.TextProperties properties) {
    }
    
    private final void saveAndUploadStory() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    private final void setupStoryUploadObservers() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/ui/community/StoryEditorActivity$Companion;", "", "()V", "EXTRA_MEDIA_TYPE", "", "EXTRA_MEDIA_URI", "TAG", "newIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "mediaUri", "Landroid/net/Uri;", "mediaType", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.content.Intent newIntent(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        android.net.Uri mediaUri, @org.jetbrains.annotations.NotNull()
        java.lang.String mediaType) {
            return null;
        }
    }
}