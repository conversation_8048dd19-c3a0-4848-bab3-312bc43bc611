package com.spyro.vmeet.data.community

import com.spyro.vmeet.data.MessageReaction // Assuming you want to reuse MessageReaction

data class Post(
    val id: String, // Or Int, depending on your backend
    val userId: Int,
    val username: String?,
    var userAvatar: String?,
    val timestamp: Long, // Or String for ISO date
    val textContent: String? = null,
    var imageUrl: String? = null,
    val gifUrl: String? = null,
    val gifPreviewUrl: String? = null,
    var voiceNoteUrl: String? = null,
    var voiceNoteDuration: Int? = null, // in seconds
    var reactions: List<MessageReaction>? = listOf(),
    var commentCount: Int = 0,
    var youtubeVideoId: String? = null, // Added for YouTube video embedding
    // Add any other fields like 'tags', 'location', etc.
) 