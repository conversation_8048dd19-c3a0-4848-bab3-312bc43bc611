<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground">

    <ImageView
        android:id="@+id/imageViewChatAvatar"
        android:layout_width="50dp"
        android:layout_height="50dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:src="@drawable/ic_profile_placeholder" />

    <TextView
        android:id="@+id/textViewChatUsername"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="?android:attr/textColorPrimary"
        app:layout_constraintStart_toEndOf="@id/imageViewChatAvatar"
        app:layout_constraintTop_toTopOf="@id/imageViewChatAvatar"
        app:layout_constraintEnd_toStartOf="@+id/textViewChatTimestamp"
        tools:text="Username" />
        
    <TextView
        android:id="@+id/textViewChatTimestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="?android:attr/textColorTertiary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/textViewChatUsername"
        app:layout_constraintBottom_toBottomOf="@id/textViewChatUsername"
        tools:text="10:30" />

    <TextView
        android:id="@+id/textViewChatLastMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="4dp"
        android:textSize="14sp"
        android:textColor="?android:attr/textColorSecondary"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintStart_toEndOf="@id/imageViewChatAvatar"
        app:layout_constraintTop_toBottomOf="@id/textViewChatUsername"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/imageViewChatAvatar"
        tools:text="This is the last message preview..." />
        
    <ImageView
        android:id="@+id/imageViewSwipeIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@android:drawable/ic_menu_revert"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:tint="#4CAF50" />

</androidx.constraintlayout.widget.ConstraintLayout> 