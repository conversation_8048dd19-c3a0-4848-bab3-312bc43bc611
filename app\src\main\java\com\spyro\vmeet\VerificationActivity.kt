package com.spyro.vmeet

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.View
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import com.google.android.material.appbar.MaterialToolbar
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class VerificationActivity : AppCompatActivity() {

    private lateinit var toolbar: MaterialToolbar
    private lateinit var cardDniPhoto: MaterialCardView
    private lateinit var cardSelfiePhoto: MaterialCardView
    private lateinit var imageDni: ImageView
    private lateinit var imageSelfie: ImageView
    private lateinit var textDniStatus: TextView
    private lateinit var textSelfieStatus: TextView
    private lateinit var buttonTakeDni: MaterialButton
    private lateinit var buttonTakeSelfie: MaterialButton
    private lateinit var buttonSubmit: MaterialButton
    private lateinit var progressBar: ProgressBar
    private lateinit var textInstructions: TextView
    private lateinit var textBenefits: TextView

    private var dniPhotoUri: Uri? = null
    private var selfiePhotoUri: Uri? = null
    private var dniPhotoFile: File? = null
    private var selfiePhotoFile: File? = null
    private var currentPhotoType: PhotoType? = null

    private var userId: Int = 0
    private val API_URL = "http://*************:3000"

    private enum class PhotoType {
        DNI, SELFIE
    }

    // Camera permission launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            openCamera()
        } else {
            Toast.makeText(this, "Se necesita permiso de cámara para tomar fotos", Toast.LENGTH_SHORT).show()
        }
    }

    // Camera launcher
    private val takePictureLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success: Boolean ->
        if (success) {
            currentPhotoType?.let { photoType ->
                when (photoType) {
                    PhotoType.DNI -> {
                        dniPhotoUri?.let { uri ->
                            loadImageIntoView(uri, imageDni)
                            textDniStatus.text = "✓ Foto del DNI capturada"
                            textDniStatus.setTextColor(ContextCompat.getColor(this, R.color.neon_green))
                            buttonTakeDni.text = "Cambiar foto"
                        }
                    }
                    PhotoType.SELFIE -> {
                        selfiePhotoUri?.let { uri ->
                            loadImageIntoView(uri, imageSelfie)
                            textSelfieStatus.text = "✓ Selfie capturada"
                            textSelfieStatus.setTextColor(ContextCompat.getColor(this, R.color.neon_green))
                            buttonTakeSelfie.text = "Cambiar foto"
                        }
                    }
                }
                updateSubmitButton()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_verification)

        // Get userId
        userId = intent.getIntExtra("USER_ID", 0)
        if (userId == 0) {
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", 0)
            if (userId == 0) {
                finish()
                return
            }
        }

        initializeViews()
        setupListeners()
        checkVerificationStatus()
    }

    private fun initializeViews() {
        toolbar = findViewById(R.id.toolbar)
        cardDniPhoto = findViewById(R.id.cardDniPhoto)
        cardSelfiePhoto = findViewById(R.id.cardSelfiePhoto)
        imageDni = findViewById(R.id.imageDni)
        imageSelfie = findViewById(R.id.imageSelfie)
        textDniStatus = findViewById(R.id.textDniStatus)
        textSelfieStatus = findViewById(R.id.textSelfieStatus)
        buttonTakeDni = findViewById(R.id.buttonTakeDni)
        buttonTakeSelfie = findViewById(R.id.buttonTakeSelfie)
        buttonSubmit = findViewById(R.id.buttonSubmit)
        progressBar = findViewById(R.id.progressBar)
        textInstructions = findViewById(R.id.textInstructions)
        textBenefits = findViewById(R.id.textBenefits)

        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Verificación de Usuario"

        // Set initial state
        buttonSubmit.isEnabled = false
        updateSubmitButton()
    }

    private fun setupListeners() {
        toolbar.setNavigationOnClickListener {
            finish()
        }

        buttonTakeDni.setOnClickListener {
            currentPhotoType = PhotoType.DNI
            checkCameraPermissionAndTakePhoto()
        }

        buttonTakeSelfie.setOnClickListener {
            currentPhotoType = PhotoType.SELFIE
            checkCameraPermissionAndTakePhoto()
        }

        buttonSubmit.setOnClickListener {
            submitVerification()
        }
    }

    private fun checkCameraPermissionAndTakePhoto() {
        when {
            ContextCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                openCamera()
            }
            else -> {
                requestPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun openCamera() {
        try {
            val photoFile = createImageFile()
            val photoURI = FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                photoFile
            )

            when (currentPhotoType) {
                PhotoType.DNI -> {
                    dniPhotoUri = photoURI
                    dniPhotoFile = photoFile
                }
                PhotoType.SELFIE -> {
                    selfiePhotoUri = photoURI
                    selfiePhotoFile = photoFile
                }
                null -> return
            }

            takePictureLauncher.launch(photoURI)

        } catch (ex: IOException) {
            Toast.makeText(this, "Error al crear archivo de imagen", Toast.LENGTH_SHORT).show()
        }
    }

    private fun createImageFile(): File {
        val timeStamp = System.currentTimeMillis()
        val imageFileName = "VERIFICATION_${currentPhotoType}_${timeStamp}"
        val storageDir = File(filesDir, "verification_photos")
        if (!storageDir.exists()) {
            storageDir.mkdirs()
        }
        return File(storageDir, "$imageFileName.jpg")
    }

    private fun loadImageIntoView(uri: Uri, imageView: ImageView) {
        try {
            val inputStream = contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            imageView.setImageBitmap(bitmap)
            imageView.visibility = View.VISIBLE
        } catch (e: Exception) {
            Log.e("VerificationActivity", "Error loading image", e)
        }
    }

    private fun updateSubmitButton() {
        val bothPhotosReady = dniPhotoUri != null && selfiePhotoUri != null
        buttonSubmit.isEnabled = bothPhotosReady
        buttonSubmit.alpha = if (bothPhotosReady) 1.0f else 0.5f
    }

    private fun checkVerificationStatus() {
        // Check current verification status
        val client = OkHttpClient()
        val request = Request.Builder()
            .url("$API_URL/verification/status/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    // Continue with normal flow if can't check status
                }
            }

            override fun onResponse(call: Call, response: Response) {
                response.body?.let { responseBody ->
                    val responseText = responseBody.string()
                    runOnUiThread {
                        // Parse response and show current status if needed
                        // For now, continue with normal flow
                    }
                }
            }
        })
    }

    private fun submitVerification() {
        if (dniPhotoFile == null || selfiePhotoFile == null) {
            Toast.makeText(this, "Por favor, toma ambas fotos antes de enviar", Toast.LENGTH_SHORT).show()
            return
        }

        progressBar.visibility = View.VISIBLE
        buttonSubmit.isEnabled = false

        val client = OkHttpClient()

        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart("userId", userId.toString())
            .addFormDataPart(
                "dni_photo",
                "dni_photo.jpg",
                dniPhotoFile!!.asRequestBody("image/jpeg".toMediaTypeOrNull())
            )
            .addFormDataPart(
                "selfie_photo",
                "selfie_photo.jpg",
                selfiePhotoFile!!.asRequestBody("image/jpeg".toMediaTypeOrNull())
            )
            .build()

        val request = Request.Builder()
            .url("$API_URL/verification/submit")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonSubmit.isEnabled = true
                    Toast.makeText(
                        this@VerificationActivity,
                        "Error de conexión. Inténtalo de nuevo.",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                runOnUiThread {
                    progressBar.visibility = View.GONE

                    if (response.isSuccessful) {
                        Toast.makeText(
                            this@VerificationActivity,
                            "Solicitud de verificación enviada correctamente",
                            Toast.LENGTH_LONG
                        ).show()

                        // Return to previous activity
                        setResult(RESULT_OK)
                        finish()
                    } else {
                        buttonSubmit.isEnabled = true
                        Toast.makeText(
                            this@VerificationActivity,
                            "Error al enviar la solicitud. Inténtalo de nuevo.",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }
}
