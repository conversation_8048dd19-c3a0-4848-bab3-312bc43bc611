-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:163:9-171:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:167:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:165:13-60
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:166:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:2:1-175:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:2:1-175:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:2:1-175:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:2:1-175:12
MERGED from [com.vanniktech:emoji-material:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca3d9a12a33d6c69763d63e99c3a3f9b\transformed\emoji-material-0.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0825f58c9c9dfc18f502349536013ef2\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9089e37b891a565e38fc8a7552e1d27\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\275f4754ed8e4dd69673def1d8a6c0a8\transformed\PhotoView-2.3.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8366de1373238a26e1cec9498d53ae0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.vanniktech:emoji:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62c23bb1740beb8ca1187076bf010da1\transformed\emoji-0.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09b775847530a523f5664d9561d9e519\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5d9d5c2fc99b5d41eee90499e2a7d8a\transformed\media3-exoplayer-dash-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-extractor:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef4b182400efde8ab6d3994169a26464\transformed\media3-extractor-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc569ba968fbc34ed64843effcb9a4fa\transformed\media3-container-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abb556bb51389798cfa0ae9122429652\transformed\media3-datasource-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a51e2ccd9151b4f9726deffb50e9926\transformed\media3-decoder-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b549c6ea8e30893e59d40e11090e180\transformed\media3-database-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a94b5fd90d7acd900bbc8f0f401e06\transformed\media3-common-1.3.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86ff0429b92cf0cf9a2921fd8c52f631\transformed\media3-ui-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8166d58de671606c211048037abf175d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8149a1bec5236edc03fa56807cb57f6\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f19594bc72fa7cb05ef33471449d5bc5\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985fc81b0350c2049b05d5e56f86a9c6\transformed\emoji2-views-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9d86f7f3857bfa08879c937cffa697a\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e96d89a0d03d85904b177759d1955228\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f4e89583dad3cb128663f1f5c665bf8\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2175db29b0e9dec6a1306be12f09cda1\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e66b30f07ea04d7e139c7bcf4f2cffc3\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2811904dfbb970a24e446f20309c4400\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b3340f04d71405a40cd653d353e798c\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fcace14a677cc4efa6a7e8310fe0aab\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc3da2900ee6d632323856474aa5080\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b79858c854defb2979f2ebd6ce7e7d\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1dd2b6f45df86860c23aa555f5006d79\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5be9f6e6b125895126ba04779385995a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c26966326f5ead1e0e4c6c731ee9f88\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaaf17c026ffa11b4c8d2eee67ad6abf\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c4a4448301b9e9fcfc2daf8958e0f1\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eef44633481174c378aacde9bfca6fdd\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d83f4a4bfa14b2d549f7fcb99fd55672\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de66242e8571eefe055a95479a5f4f51\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ba69f4fd7ccf53a46b411c7f8285155\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c255ba4110a7af6ca98906c28c73f510\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\033d378e952794890827781279236a89\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8d20f94010c36e986cb2179f4db37c6\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c516a3307b20d707bcb5e9cadc0657\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d0ca34c3cdb75956f107655453f3a04\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d7e50e4e39e9a58b31d0de5a64f920\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47643bc3eb6309bc93b51d7e073d9f04\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08a547eaa9782bfba7c564c64937f9f3\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932b5bb54ade9327a34a97e4ce7cb6aa\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90510453f8412948a35d8ec71808d347\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f77c181f1da72df78b66e4e63ac9a01\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f946ffcc2f84625d5b04c00f2e17ea33\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9b63197379ffc6a053ea41079aaba2\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a977c976ae7bb812586253a6694d762d\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a42b989797f2f077e4e356ca937c5f19\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0728dd76c1d554e6c8b6ca7d4607cf9\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3892a561a4d1db061beb7b45b1069f0a\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d404fa38aae90c3ee05e4778700f6e1\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de0cd836d3744f9a73ac960e7858012f\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d0948d9a2d6b15398c2119cec01ba4\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59cc66194f3076999af6b770e73cbf5a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20e4ea871dfd2bc074dc5104e483991c\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cdb427a4125fe7a81c51d937f29dc19\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5b017bced6d8dc732d7ae8475ce0a9\transformed\activity-1.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33d15bcb6ccba97e6039b33d5c95b830\transformed\activity-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9223c644c7dd206f7bd058d19178b46\transformed\activity-compose-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f80b51d56d568461b2fe773f95a970d9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f551acba5ca8c6f78b5c9b174b919f6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33eafefa3a3e366eda6fb21fc0c0a61c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fafed8a275794e48dcd878d69bba5771\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2d479c6d441ac713c7a667f3ccd54eb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\441e80c33220fbc8866a53362916ecd8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4e6bd1022b7db86fbac4369df4c78c4\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14613f36c50802ac6eaf387f5b9d27bd\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\649ed82fc821f36a0304ac15ee9085e4\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8627b9014da5c50eca694a38f83f7c8a\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51a535f97b9169f15a7ea9cf1953cebe\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58453f4dfab8155cdd89e307e1408347\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03610e98a83e9362c4e91f38553c6bd4\transformed\flexbox-3.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c51b75000d53a73510a31e43717c906\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc90c1a61cfd402c9bdcf8bd4e46f464\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6a7df188d787a730a55759e97891c41\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afaae4a86dab28e940e24438291904b2\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9cb1399c76459a2b7fd3ba8270fb4ad\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f793ee9ea2f7af17330256e2146f2c7c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a27cf0e5f16aa879ed1645bf06727bd\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d479431022b7ab66fb6e0a5d3ce664c\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb517d4783e75af2b85bcb95ceef7c63\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0343112ab3501995e297b65cafcaf9f8\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\010d8034a07934476251d68f4af4f1c4\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d04432d6ce2c3bd12246c26d28cb931\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b001e8e7bc2b66b3cf2b8adda1ca977f\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d8b8575a15bc963feb856c5b87ec858\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\392140fe0d73847a91959447b67f702a\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9aab8716d9b1d9d7e61a43b7ce2aca4a\transformed\circleimageview-3.1.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:5-66
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:7:5-67
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:5-106
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:78-104
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:5-75
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:5-70
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:5-10:50
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:10:22-48
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:5-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:22-72
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:22-63
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:5-76
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:22-74
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:5-64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:22-62
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:5-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:22-72
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:5-80
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:22-78
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:21:5-173:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:21:5-173:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0825f58c9c9dfc18f502349536013ef2\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0825f58c9c9dfc18f502349536013ef2\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9089e37b891a565e38fc8a7552e1d27\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9089e37b891a565e38fc8a7552e1d27\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e96d89a0d03d85904b177759d1955228\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e96d89a0d03d85904b177759d1955228\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f4e89583dad3cb128663f1f5c665bf8\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f4e89583dad3cb128663f1f5c665bf8\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2175db29b0e9dec6a1306be12f09cda1\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2175db29b0e9dec6a1306be12f09cda1\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc3da2900ee6d632323856474aa5080\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc3da2900ee6d632323856474aa5080\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b79858c854defb2979f2ebd6ce7e7d\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b79858c854defb2979f2ebd6ce7e7d\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d0948d9a2d6b15398c2119cec01ba4\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d0948d9a2d6b15398c2119cec01ba4\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59cc66194f3076999af6b770e73cbf5a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59cc66194f3076999af6b770e73cbf5a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20e4ea871dfd2bc074dc5104e483991c\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20e4ea871dfd2bc074dc5104e483991c\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb517d4783e75af2b85bcb95ceef7c63\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb517d4783e75af2b85bcb95ceef7c63\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9aab8716d9b1d9d7e61a43b7ce2aca4a\transformed\circleimageview-3.1.0\AndroidManifest.xml:9:5-20
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9aab8716d9b1d9d7e61a43b7ce2aca4a\transformed\circleimageview-3.1.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:25:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:28:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:32:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:26:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:30:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:31:9-69
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:24:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:22:9-41
activity#com.spyro.vmeet.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:33:9-42:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:36:13-45
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:37:13-47
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:34:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:38:13-41:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:27-74
activity#com.spyro.vmeet.LoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:9-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:19-48
activity#com.spyro.vmeet.TutorialActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:9-54
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:19-51
activity#com.spyro.vmeet.ProfileActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:45:9-47:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:47:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:46:13-44
activity#com.spyro.vmeet.BlockedUsersActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:48:9-50:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:49:13-49
activity#com.spyro.vmeet.SwipeActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:9-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:19-48
activity#com.spyro.vmeet.MatchesActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:9-53
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:19-50
activity#com.spyro.vmeet.CommunityHostActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:9-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:19-56
activity#com.spyro.vmeet.ui.community.CommentsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:9-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:19-64
activity#com.spyro.vmeet.ui.community.StoryEditorActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:55:9-56:64
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:56:13-61
activity#com.spyro.vmeet.activity.BlindDateActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:57:9-60:76
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:60:13-73
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:59:13-83
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:58:13-55
activity#com.spyro.vmeet.activity.ChatActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:61:9-64:76
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:64:13-73
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:63:13-83
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:62:13-50
activity#com.spyro.vmeet.activity.ChatListActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:9-63
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:19-60
activity#com.spyro.vmeet.activity.SettingsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:9-63
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:19-60
activity#com.spyro.vmeet.activity.EmailVerificationActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:9-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:19-69
activity#com.spyro.vmeet.activity.PasswordResetActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:9-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:19-65
activity#com.spyro.vmeet.activity.ForgotPasswordActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:9-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:19-66
activity#com.spyro.vmeet.activity.RoomLoaderActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:74:9-78:58
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:78:13-55
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:77:13-83
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:76:13-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:75:13-56
activity#com.spyro.vmeet.activity.ChatRoomActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:81:9-87:50
	android:excludeFromRecents
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:87:13-47
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:85:13-43
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:84:13-73
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:83:13-83
	android:taskAffinity
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:86:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:82:13-54
activity#com.spyro.vmeet.VideoFeedActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:90:9-94:61
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:93:13-49
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:92:13-74
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:94:13-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:91:13-46
activity#com.spyro.vmeet.UploadVideoActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:95:9-97:61
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:97:13-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:96:13-48
activity#com.spyro.vmeet.MyVideosActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:98:9-100:61
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:100:13-59
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:99:13-45
activity#com.spyro.vmeet.AdminPanelActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:103:9-105:55
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:105:13-52
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:104:13-47
activity#com.spyro.vmeet.UserManagementActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:106:9-108:51
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:108:13-48
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:107:13-51
activity#com.spyro.vmeet.ReportsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:109:9-111:52
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:111:13-49
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:110:13-44
activity#com.spyro.vmeet.FullScreenImageActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:114:9-116:66
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:116:13-63
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:115:13-52
activity#com.spyro.vmeet.ui.community.StoryViewerActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:119:9-121:66
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:121:13-63
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:120:13-61
activity#com.spyro.vmeet.activity.UserProfileActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:124:9-126:49
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:126:13-46
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:125:13-57
activity#com.spyro.vmeet.activity.EditPersonalityTraitsActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:129:9-131:61
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:131:13-58
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:130:13-67
activity#com.spyro.vmeet.activity.CommunityGuidelinesActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:134:9-136:54
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:136:13-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:135:13-65
service#com.spyro.vmeet.notifications.VMeetFirebaseMessagingService
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:139:9-145:19
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:141:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:140:13-72
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:142:13-144:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:143:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:143:25-75
receiver#com.spyro.vmeet.notifications.MessageActionReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:148:9-155:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:150:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:13-64
intent-filter#action:name:com.spyro.vmeet.ACTION_MARK_AS_READ+action:name:com.spyro.vmeet.ACTION_REPLY
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:151:13-154:29
action#com.spyro.vmeet.ACTION_MARK_AS_READ
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:152:17-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:152:25-75
action#com.spyro.vmeet.ACTION_REPLY
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:153:17-71
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:153:25-68
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:158:9-160:69
	android:value
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:160:13-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:13-69
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:168:13-170:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:170:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:169:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
MERGED from [com.vanniktech:emoji-material:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca3d9a12a33d6c69763d63e99c3a3f9b\transformed\emoji-material-0.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:emoji-material:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ca3d9a12a33d6c69763d63e99c3a3f9b\transformed\emoji-material-0.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0825f58c9c9dfc18f502349536013ef2\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0825f58c9c9dfc18f502349536013ef2\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9089e37b891a565e38fc8a7552e1d27\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9089e37b891a565e38fc8a7552e1d27\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\275f4754ed8e4dd69673def1d8a6c0a8\transformed\PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.chrisbanes:PhotoView:2.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\275f4754ed8e4dd69673def1d8a6c0a8\transformed\PhotoView-2.3.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8366de1373238a26e1cec9498d53ae0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8366de1373238a26e1cec9498d53ae0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:emoji:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62c23bb1740beb8ca1187076bf010da1\transformed\emoji-0.8.0\AndroidManifest.xml:5:5-44
MERGED from [com.vanniktech:emoji:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62c23bb1740beb8ca1187076bf010da1\transformed\emoji-0.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09b775847530a523f5664d9561d9e519\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09b775847530a523f5664d9561d9e519\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5d9d5c2fc99b5d41eee90499e2a7d8a\transformed\media3-exoplayer-dash-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5d9d5c2fc99b5d41eee90499e2a7d8a\transformed\media3-exoplayer-dash-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef4b182400efde8ab6d3994169a26464\transformed\media3-extractor-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef4b182400efde8ab6d3994169a26464\transformed\media3-extractor-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc569ba968fbc34ed64843effcb9a4fa\transformed\media3-container-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc569ba968fbc34ed64843effcb9a4fa\transformed\media3-container-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abb556bb51389798cfa0ae9122429652\transformed\media3-datasource-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abb556bb51389798cfa0ae9122429652\transformed\media3-datasource-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a51e2ccd9151b4f9726deffb50e9926\transformed\media3-decoder-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a51e2ccd9151b4f9726deffb50e9926\transformed\media3-decoder-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b549c6ea8e30893e59d40e11090e180\transformed\media3-database-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b549c6ea8e30893e59d40e11090e180\transformed\media3-database-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a94b5fd90d7acd900bbc8f0f401e06\transformed\media3-common-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a94b5fd90d7acd900bbc8f0f401e06\transformed\media3-common-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86ff0429b92cf0cf9a2921fd8c52f631\transformed\media3-ui-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86ff0429b92cf0cf9a2921fd8c52f631\transformed\media3-ui-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8166d58de671606c211048037abf175d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8166d58de671606c211048037abf175d\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8149a1bec5236edc03fa56807cb57f6\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8149a1bec5236edc03fa56807cb57f6\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f19594bc72fa7cb05ef33471449d5bc5\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f19594bc72fa7cb05ef33471449d5bc5\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985fc81b0350c2049b05d5e56f86a9c6\transformed\emoji2-views-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985fc81b0350c2049b05d5e56f86a9c6\transformed\emoji2-views-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9d86f7f3857bfa08879c937cffa697a\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9d86f7f3857bfa08879c937cffa697a\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e96d89a0d03d85904b177759d1955228\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e96d89a0d03d85904b177759d1955228\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f4e89583dad3cb128663f1f5c665bf8\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f4e89583dad3cb128663f1f5c665bf8\transformed\firebase-analytics-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2175db29b0e9dec6a1306be12f09cda1\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2175db29b0e9dec6a1306be12f09cda1\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e66b30f07ea04d7e139c7bcf4f2cffc3\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e66b30f07ea04d7e139c7bcf4f2cffc3\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2811904dfbb970a24e446f20309c4400\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2811904dfbb970a24e446f20309c4400\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b3340f04d71405a40cd653d353e798c\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b3340f04d71405a40cd653d353e798c\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fcace14a677cc4efa6a7e8310fe0aab\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7fcace14a677cc4efa6a7e8310fe0aab\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc3da2900ee6d632323856474aa5080\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfc3da2900ee6d632323856474aa5080\transformed\play-services-measurement-sdk-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b79858c854defb2979f2ebd6ce7e7d\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76b79858c854defb2979f2ebd6ce7e7d\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1dd2b6f45df86860c23aa555f5006d79\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1dd2b6f45df86860c23aa555f5006d79\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5be9f6e6b125895126ba04779385995a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5be9f6e6b125895126ba04779385995a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c26966326f5ead1e0e4c6c731ee9f88\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c26966326f5ead1e0e4c6c731ee9f88\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaaf17c026ffa11b4c8d2eee67ad6abf\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eaaf17c026ffa11b4c8d2eee67ad6abf\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c4a4448301b9e9fcfc2daf8958e0f1\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77c4a4448301b9e9fcfc2daf8958e0f1\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eef44633481174c378aacde9bfca6fdd\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eef44633481174c378aacde9bfca6fdd\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d83f4a4bfa14b2d549f7fcb99fd55672\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d83f4a4bfa14b2d549f7fcb99fd55672\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de66242e8571eefe055a95479a5f4f51\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de66242e8571eefe055a95479a5f4f51\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ba69f4fd7ccf53a46b411c7f8285155\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0ba69f4fd7ccf53a46b411c7f8285155\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c255ba4110a7af6ca98906c28c73f510\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c255ba4110a7af6ca98906c28c73f510\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\033d378e952794890827781279236a89\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\033d378e952794890827781279236a89\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8d20f94010c36e986cb2179f4db37c6\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8d20f94010c36e986cb2179f4db37c6\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c516a3307b20d707bcb5e9cadc0657\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91c516a3307b20d707bcb5e9cadc0657\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d0ca34c3cdb75956f107655453f3a04\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d0ca34c3cdb75956f107655453f3a04\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d7e50e4e39e9a58b31d0de5a64f920\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d7e50e4e39e9a58b31d0de5a64f920\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47643bc3eb6309bc93b51d7e073d9f04\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47643bc3eb6309bc93b51d7e073d9f04\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08a547eaa9782bfba7c564c64937f9f3\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08a547eaa9782bfba7c564c64937f9f3\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932b5bb54ade9327a34a97e4ce7cb6aa\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\932b5bb54ade9327a34a97e4ce7cb6aa\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90510453f8412948a35d8ec71808d347\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90510453f8412948a35d8ec71808d347\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f77c181f1da72df78b66e4e63ac9a01\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f77c181f1da72df78b66e4e63ac9a01\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f946ffcc2f84625d5b04c00f2e17ea33\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f946ffcc2f84625d5b04c00f2e17ea33\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9b63197379ffc6a053ea41079aaba2\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9b63197379ffc6a053ea41079aaba2\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a977c976ae7bb812586253a6694d762d\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a977c976ae7bb812586253a6694d762d\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a42b989797f2f077e4e356ca937c5f19\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a42b989797f2f077e4e356ca937c5f19\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0728dd76c1d554e6c8b6ca7d4607cf9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b0728dd76c1d554e6c8b6ca7d4607cf9\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3892a561a4d1db061beb7b45b1069f0a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3892a561a4d1db061beb7b45b1069f0a\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d404fa38aae90c3ee05e4778700f6e1\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d404fa38aae90c3ee05e4778700f6e1\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de0cd836d3744f9a73ac960e7858012f\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\de0cd836d3744f9a73ac960e7858012f\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d0948d9a2d6b15398c2119cec01ba4\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18d0948d9a2d6b15398c2119cec01ba4\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59cc66194f3076999af6b770e73cbf5a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59cc66194f3076999af6b770e73cbf5a\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20e4ea871dfd2bc074dc5104e483991c\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20e4ea871dfd2bc074dc5104e483991c\transformed\play-services-measurement-base-21.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cdb427a4125fe7a81c51d937f29dc19\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0cdb427a4125fe7a81c51d937f29dc19\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5b017bced6d8dc732d7ae8475ce0a9\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa5b017bced6d8dc732d7ae8475ce0a9\transformed\activity-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33d15bcb6ccba97e6039b33d5c95b830\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33d15bcb6ccba97e6039b33d5c95b830\transformed\activity-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9223c644c7dd206f7bd058d19178b46\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9223c644c7dd206f7bd058d19178b46\transformed\activity-compose-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f80b51d56d568461b2fe773f95a970d9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f80b51d56d568461b2fe773f95a970d9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f551acba5ca8c6f78b5c9b174b919f6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f551acba5ca8c6f78b5c9b174b919f6\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33eafefa3a3e366eda6fb21fc0c0a61c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33eafefa3a3e366eda6fb21fc0c0a61c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fafed8a275794e48dcd878d69bba5771\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fafed8a275794e48dcd878d69bba5771\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2d479c6d441ac713c7a667f3ccd54eb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2d479c6d441ac713c7a667f3ccd54eb\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\441e80c33220fbc8866a53362916ecd8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\441e80c33220fbc8866a53362916ecd8\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4e6bd1022b7db86fbac4369df4c78c4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f4e6bd1022b7db86fbac4369df4c78c4\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14613f36c50802ac6eaf387f5b9d27bd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\14613f36c50802ac6eaf387f5b9d27bd\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\649ed82fc821f36a0304ac15ee9085e4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\649ed82fc821f36a0304ac15ee9085e4\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8627b9014da5c50eca694a38f83f7c8a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8627b9014da5c50eca694a38f83f7c8a\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51a535f97b9169f15a7ea9cf1953cebe\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51a535f97b9169f15a7ea9cf1953cebe\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58453f4dfab8155cdd89e307e1408347\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\58453f4dfab8155cdd89e307e1408347\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03610e98a83e9362c4e91f38553c6bd4\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.flexbox:flexbox:3.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03610e98a83e9362c4e91f38553c6bd4\transformed\flexbox-3.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c51b75000d53a73510a31e43717c906\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3c51b75000d53a73510a31e43717c906\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc90c1a61cfd402c9bdcf8bd4e46f464\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc90c1a61cfd402c9bdcf8bd4e46f464\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6a7df188d787a730a55759e97891c41\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6a7df188d787a730a55759e97891c41\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afaae4a86dab28e940e24438291904b2\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afaae4a86dab28e940e24438291904b2\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9cb1399c76459a2b7fd3ba8270fb4ad\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9cb1399c76459a2b7fd3ba8270fb4ad\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f793ee9ea2f7af17330256e2146f2c7c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f793ee9ea2f7af17330256e2146f2c7c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a27cf0e5f16aa879ed1645bf06727bd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a27cf0e5f16aa879ed1645bf06727bd\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d479431022b7ab66fb6e0a5d3ce664c\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7d479431022b7ab66fb6e0a5d3ce664c\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb517d4783e75af2b85bcb95ceef7c63\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb517d4783e75af2b85bcb95ceef7c63\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0343112ab3501995e297b65cafcaf9f8\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0343112ab3501995e297b65cafcaf9f8\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\010d8034a07934476251d68f4af4f1c4\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\010d8034a07934476251d68f4af4f1c4\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d04432d6ce2c3bd12246c26d28cb931\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d04432d6ce2c3bd12246c26d28cb931\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b001e8e7bc2b66b3cf2b8adda1ca977f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b001e8e7bc2b66b3cf2b8adda1ca977f\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d8b8575a15bc963feb856c5b87ec858\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8d8b8575a15bc963feb856c5b87ec858\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\392140fe0d73847a91959447b67f702a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\392140fe0d73847a91959447b67f702a\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9aab8716d9b1d9d7e61a43b7ce2aca4a\transformed\circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9aab8716d9b1d9d7e61a43b7ce2aca4a\transformed\circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a94b5fd90d7acd900bbc8f0f401e06\transformed\media3-common-1.3.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80a94b5fd90d7acd900bbc8f0f401e06\transformed\media3-common-1.3.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:8:5-79
MERGED from [com.pierfrancescosoffritti.androidyoutubeplayer:core:12.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e1047e52305c2eb7b0d0de887c7807bf\transformed\core-12.1.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:22-76
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\410cb4dbf76ed19a717e42c47a53716e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:29:13-31:39
REJECTED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747e1b0d92dbfa1ffce97b6f861ead4d\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	tools:node
		ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:31:17-36
	android:name
		ADDED from [androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:30:17-75
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
queries
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:25:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:24:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:27:17-129
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:22-65
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232c169bcee9b1cc6cb888f25801c3ad\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:12:17-129
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f144f348a023c2b0274651faebd09710\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bb48e4f54c591b3c2362988eb8fe11e6\transformed\play-services-measurement-sdk-api-21.5.1\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d9bc025b89b8b992e4892fda6ef0522\transformed\play-services-measurement-impl-21.5.1\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
