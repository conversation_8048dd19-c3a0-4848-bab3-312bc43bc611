package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u000eH\u0002J\b\u0010\u0012\u001a\u00020\u0010H\u0002J\b\u0010\u0013\u001a\u00020\u0010H\u0002J\u0012\u0010\u0014\u001a\u00020\u00102\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016H\u0014J\b\u0010\u0017\u001a\u00020\u0010H\u0002J\u0010\u0010\u0018\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u000eH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/spyro/vmeet/MyVideosActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "adapter", "Lcom/spyro/vmeet/adapter/MyVideosAdapter;", "client", "Lokhttp3/OkHttpClient;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "toolbar", "Lcom/google/android/material/appbar/MaterialToolbar;", "userId", "", "deleteVideo", "", "videoId", "initializeViews", "loadVideos", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "setupToolbar", "showDeleteConfirmation", "app_debug"})
public final class MyVideosActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.google.android.material.appbar.MaterialToolbar toolbar;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private com.spyro.vmeet.adapter.MyVideosAdapter adapter;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    
    public MyVideosActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeViews() {
    }
    
    private final void setupToolbar() {
    }
    
    private final void loadVideos() {
    }
    
    private final void showDeleteConfirmation(int videoId) {
    }
    
    private final void deleteVideo(int videoId) {
    }
}