package com.spyro.vmeet.data.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b4\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u00ac\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u000b\u0012\u0006\u0010\r\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\u0015\b\u0002\u0010\u0010\u001a\u000f\u0012\t\u0012\u00070\u0012\u00a2\u0006\u0002\b\u0013\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0019J\t\u00103\u001a\u00020\u0003H\u00c6\u0003J\t\u00104\u001a\u00020\u000fH\u00c6\u0003J\u0016\u00105\u001a\u000f\u0012\t\u0012\u00070\u0012\u00a2\u0006\u0002\b\u0013\u0018\u00010\u0011H\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00107\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00108\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u00109\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010:\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003\u00a2\u0006\u0002\u0010#J\t\u0010;\u001a\u00020\u0005H\u00c6\u0003J\t\u0010<\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010=\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\t\u0010?\u001a\u00020\u0003H\u00c6\u0003J\t\u0010@\u001a\u00020\u000bH\u00c6\u0003J\t\u0010A\u001a\u00020\u000bH\u00c6\u0003J\t\u0010B\u001a\u00020\u0005H\u00c6\u0003J\u00c7\u0001\u0010C\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\u0015\b\u0002\u0010\u0010\u001a\u000f\u0012\t\u0012\u00070\u0012\u00a2\u0006\u0002\b\u0013\u0018\u00010\u00112\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010DJ\t\u0010E\u001a\u00020\u0005H\u00d6\u0001J\u0013\u0010F\u001a\u00020\u000f2\b\u0010G\u001a\u0004\u0018\u00010HH\u00d6\u0003J\t\u0010I\u001a\u00020\u0005H\u00d6\u0001J\t\u0010J\u001a\u00020\u0003H\u00d6\u0001J\u0019\u0010K\u001a\u00020L2\u0006\u0010M\u001a\u00020N2\u0006\u0010O\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001dR\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001dR\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001dR\u0015\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\"\u0010#R\u0013\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001dR\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001dR\u001a\u0010\u000e\u001a\u00020\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R\u001e\u0010\u0010\u001a\u000f\u0012\t\u0012\u00070\u0012\u00a2\u0006\u0002\b\u0013\u0018\u00010\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u001bR\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\u001dR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\u001dR\u0011\u0010\r\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00100\u00a8\u0006P"}, d2 = {"Lcom/spyro/vmeet/data/community/Story;", "Landroid/os/Parcelable;", "id", "", "userId", "", "username", "userAvatar", "mediaUrl", "mediaType", "timestamp", "", "expiresAt", "views", "seenByCurrentUser", "", "textOverlays", "", "Lcom/spyro/vmeet/ui/community/TextOverlayData;", "Lkotlin/jvm/JvmSuppressWildcards;", "musicTitle", "musicArtist", "musicPreviewUrl", "musicCoverArt", "musicDuration", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JJIZLjava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "getExpiresAt", "()J", "getId", "()Ljava/lang/String;", "getMediaType", "getMediaUrl", "getMusicArtist", "getMusicCoverArt", "getMusicDuration", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getMusicPreviewUrl", "getMusicTitle", "getSeenByCurrentUser", "()Z", "setSeenByCurrentUser", "(Z)V", "getTextOverlays", "()Ljava/util/List;", "getTimestamp", "getUserAvatar", "getUserId", "()I", "getUsername", "getViews", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JJIZLjava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Lcom/spyro/vmeet/data/community/Story;", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "parcel", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class Story implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    private final int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String username = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String userAvatar = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String mediaUrl = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String mediaType = null;
    private final long timestamp = 0L;
    private final long expiresAt = 0L;
    private final int views = 0;
    private boolean seenByCurrentUser;
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String musicTitle = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String musicArtist = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String musicPreviewUrl = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String musicCoverArt = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer musicDuration = null;
    
    public Story(@org.jetbrains.annotations.NotNull()
    java.lang.String id, int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.Nullable()
    java.lang.String userAvatar, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, long timestamp, long expiresAt, int views, boolean seenByCurrentUser, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays, @org.jetbrains.annotations.Nullable()
    java.lang.String musicTitle, @org.jetbrains.annotations.Nullable()
    java.lang.String musicArtist, @org.jetbrains.annotations.Nullable()
    java.lang.String musicPreviewUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String musicCoverArt, @org.jetbrains.annotations.Nullable()
    java.lang.Integer musicDuration) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    public final int getUserId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUsername() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUserAvatar() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMediaUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMediaType() {
        return null;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    public final long getExpiresAt() {
        return 0L;
    }
    
    public final int getViews() {
        return 0;
    }
    
    public final boolean getSeenByCurrentUser() {
        return false;
    }
    
    public final void setSeenByCurrentUser(boolean p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> getTextOverlays() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMusicTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMusicArtist() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMusicPreviewUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMusicCoverArt() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getMusicDuration() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component16() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final long component8() {
        return 0L;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.spyro.vmeet.data.community.Story copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.Nullable()
    java.lang.String userAvatar, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, long timestamp, long expiresAt, int views, boolean seenByCurrentUser, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays, @org.jetbrains.annotations.Nullable()
    java.lang.String musicTitle, @org.jetbrains.annotations.Nullable()
    java.lang.String musicArtist, @org.jetbrains.annotations.Nullable()
    java.lang.String musicPreviewUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String musicCoverArt, @org.jetbrains.annotations.Nullable()
    java.lang.Integer musicDuration) {
        return null;
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
}