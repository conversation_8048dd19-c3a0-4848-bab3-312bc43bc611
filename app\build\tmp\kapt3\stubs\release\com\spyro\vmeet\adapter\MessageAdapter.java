package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0011\b\u0007\u0018\u0000 22\b\u0012\u0004\u0012\u00020\u00020\u0001:\u000e23456789:;<=>?B\u00a3\u0001\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0016\b\u0002\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\u001c\b\u0002\u0010\u000b\u001a\u0016\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n\u0018\u00010\f\u0012\u0016\b\u0002\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0010\u0012\u0016\b\u0002\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\u0016\b\u0002\u0010\u0012\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010\u001d\u001a\u00020\n2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J\b\u0010 \u001a\u00020\u0007H\u0016J\u0010\u0010!\u001a\u00020\u00072\u0006\u0010\"\u001a\u00020\u0007H\u0016J\u0010\u0010#\u001a\u00020\u00102\u0006\u0010$\u001a\u00020\u0007H\u0002J\u0018\u0010%\u001a\u00020\n2\u0006\u0010&\u001a\u00020\u00022\u0006\u0010\"\u001a\u00020\u0007H\u0016J\u0018\u0010\'\u001a\u00020\u00022\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020\u0007H\u0016J\u0010\u0010+\u001a\u00020\n2\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016J\u0018\u0010,\u001a\u00020\n2\u0006\u0010-\u001a\u00020\u00052\u0006\u0010.\u001a\u00020/H\u0002J\u0018\u00100\u001a\u00020\n2\u0006\u0010-\u001a\u00020\u00052\u0006\u0010.\u001a\u00020/H\u0002J\u0006\u00101\u001a\u00020\nR\u000e\u0010\u0014\u001a\u00020\rX\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0018\u00010\u0018R\u00020\u0000X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0011\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u0012\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\"\u0010\u000b\u001a\u0016\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\n\u0018\u00010\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\b\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\n\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006@"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "messages", "", "Lcom/spyro/vmeet/data/Message;", "currentUserId", "", "onReplyListener", "Lkotlin/Function1;", "", "onReactionListener", "Lkotlin/Function2;", "", "onProfileClickListener", "isAdmin", "", "onDeleteMessageListener", "onMuteUserListener", "(Ljava/util/List;ILkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;ZLkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "API_URL", "audioPlayer", "Lcom/spyro/vmeet/audio/AudioPlayer;", "currentlyPlayingHolder", "Lcom/spyro/vmeet/adapter/MessageAdapter$VoiceMessageViewHolder;", "currentlyPlayingPosition", "inputFormat", "Ljava/text/SimpleDateFormat;", "outputFormat", "applyAdminGradient", "textView", "Landroid/widget/TextView;", "getItemCount", "getItemViewType", "position", "isUserAdmin", "userId", "onBindViewHolder", "holder", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setAudioPlayer", "showAdminOptions", "message", "context", "Landroid/content/Context;", "showDeleteConfirmation", "stopPlayback", "Companion", "ImageMessageViewHolder", "MessageViewHolder", "ReceivedBuzzMessageViewHolder", "ReceivedGifMessageViewHolder", "ReceivedImageMessageViewHolder", "ReceivedMessageViewHolder", "ReceivedVoiceMessageViewHolder", "SentBuzzMessageViewHolder", "SentGifMessageViewHolder", "SentImageMessageViewHolder", "SentMessageViewHolder", "SentVoiceMessageViewHolder", "VoiceMessageViewHolder", "app_release"})
public final class MessageAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<androidx.recyclerview.widget.RecyclerView.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.Message> messages = null;
    private final int currentUserId = 0;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.Message, kotlin.Unit> onReplyListener = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function2<com.spyro.vmeet.data.Message, java.lang.String, kotlin.Unit> onReactionListener = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<java.lang.Integer, kotlin.Unit> onProfileClickListener = null;
    private final boolean isAdmin = false;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.Message, kotlin.Unit> onDeleteMessageListener = null;
    @org.jetbrains.annotations.Nullable()
    private final kotlin.jvm.functions.Function1<com.spyro.vmeet.data.Message, kotlin.Unit> onMuteUserListener = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "MessageAdapter";
    private static final int VIEW_TYPE_SENT = 1;
    private static final int VIEW_TYPE_RECEIVED = 2;
    private static final int VIEW_TYPE_VOICE_SENT = 3;
    private static final int VIEW_TYPE_VOICE_RECEIVED = 4;
    private static final int VIEW_TYPE_IMAGE_SENT = 5;
    private static final int VIEW_TYPE_IMAGE_RECEIVED = 6;
    private static final int VIEW_TYPE_GIF_SENT = 7;
    private static final int VIEW_TYPE_GIF_RECEIVED = 8;
    private static final int VIEW_TYPE_BUZZ_SENT = 9;
    private static final int VIEW_TYPE_BUZZ_RECEIVED = 10;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.audio.AudioPlayer audioPlayer;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.adapter.MessageAdapter.VoiceMessageViewHolder currentlyPlayingHolder;
    private int currentlyPlayingPosition = -1;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.text.SimpleDateFormat inputFormat = null;
    @org.jetbrains.annotations.NotNull()
    private final java.text.SimpleDateFormat outputFormat = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.adapter.MessageAdapter.Companion Companion = null;
    
    public MessageAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.data.Message> messages, int currentUserId, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.Message, kotlin.Unit> onReplyListener, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super com.spyro.vmeet.data.Message, ? super java.lang.String, kotlin.Unit> onReactionListener, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onProfileClickListener, boolean isAdmin, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.Message, kotlin.Unit> onDeleteMessageListener, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.spyro.vmeet.data.Message, kotlin.Unit> onMuteUserListener) {
        super();
    }
    
    @java.lang.Override()
    public int getItemViewType(int position) {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.recyclerview.widget.RecyclerView.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    androidx.recyclerview.widget.RecyclerView.ViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void setAudioPlayer(@org.jetbrains.annotations.Nullable()
    com.spyro.vmeet.audio.AudioPlayer audioPlayer) {
    }
    
    public final void stopPlayback() {
    }
    
    private final void showAdminOptions(com.spyro.vmeet.data.Message message, android.content.Context context) {
    }
    
    private final void showDeleteConfirmation(com.spyro.vmeet.data.Message message, android.content.Context context) {
    }
    
    /**
     * Check if a user is an admin by user ID
     * This queries backend or caches results
     */
    private final boolean isUserAdmin(int userId) {
        return false;
    }
    
    private final void applyAdminGradient(android.widget.TextView textView) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$Companion;", "", "()V", "TAG", "", "VIEW_TYPE_BUZZ_RECEIVED", "", "VIEW_TYPE_BUZZ_SENT", "VIEW_TYPE_GIF_RECEIVED", "VIEW_TYPE_GIF_SENT", "VIEW_TYPE_IMAGE_RECEIVED", "VIEW_TYPE_IMAGE_SENT", "VIEW_TYPE_RECEIVED", "VIEW_TYPE_SENT", "VIEW_TYPE_VOICE_RECEIVED", "VIEW_TYPE_VOICE_SENT", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\b\u00a6\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H&J\u0010\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020$H\u0004J\u0010\u0010&\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0004J\u0010\u0010\'\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0004J\u0010\u0010(\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0004J(\u0010)\u001a\u00020 2\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020$2\u0006\u0010!\u001a\u00020\"H\u0002J\u0010\u0010/\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0004J\u0010\u00100\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0004J\u0010\u00101\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0002R\u0014\u0010\u0005\u001a\u00020\u0006X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0014\u0010\t\u001a\u00020\nX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0014\u0010\r\u001a\u00020\u000eX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0014\u0010\u0011\u001a\u00020\u0012X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0014\u0010\u0015\u001a\u00020\u0016X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0014\u0010\u0019\u001a\u00020\u001aX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0014\u0010\u001d\u001a\u00020\u0016X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0018\u00a8\u00062"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$ImageMessageViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "cardViewMessage", "Landroidx/cardview/widget/CardView;", "getCardViewMessage", "()Landroidx/cardview/widget/CardView;", "imageViewPhoto", "Landroid/widget/ImageView;", "getImageViewPhoto", "()Landroid/widget/ImageView;", "layoutReply", "Landroid/widget/LinearLayout;", "getLayoutReply", "()Landroid/widget/LinearLayout;", "reactionsContainer", "Lcom/google/android/flexbox/FlexboxLayout;", "getReactionsContainer", "()Lcom/google/android/flexbox/FlexboxLayout;", "textViewReplyLabel", "Landroid/widget/TextView;", "getTextViewReplyLabel", "()Landroid/widget/TextView;", "textViewReplyText", "Landroidx/emoji2/widget/EmojiTextView;", "getTextViewReplyText", "()Landroidx/emoji2/widget/EmojiTextView;", "textViewTime", "getTextViewTime", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "extractTime", "", "isoTimeString", "loadImage", "setupMessageClick", "setupMessageLongClick", "setupReactionClick", "dialog", "Landroid/app/Dialog;", "viewId", "", "reaction", "setupReactions", "setupReplyView", "showReactionSelector", "app_release"})
    public abstract class ImageMessageViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewPhoto = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewTime = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.cardview.widget.CardView cardViewMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout layoutReply = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewReplyLabel = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.emoji2.widget.EmojiTextView textViewReplyText = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.flexbox.FlexboxLayout reactionsContainer = null;
        
        public ImageMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.ImageView getImageViewPhoto() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.TextView getTextViewTime() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final androidx.cardview.widget.CardView getCardViewMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.LinearLayout getLayoutReply() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.TextView getTextViewReplyLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final androidx.emoji2.widget.EmojiTextView getTextViewReplyText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final com.google.android.flexbox.FlexboxLayout getReactionsContainer() {
            return null;
        }
        
        public abstract void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message);
        
        protected final void loadImage(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupReplyView(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupMessageLongClick(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupMessageClick(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        private final void showReactionSelector(com.spyro.vmeet.data.Message message) {
        }
        
        private final void setupReactionClick(android.app.Dialog dialog, int viewId, java.lang.String reaction, com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupReactions(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final java.lang.String extractTime(@org.jetbrains.annotations.NotNull()
        java.lang.String isoTimeString) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\b\u00a6\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\u0016H\u0004J\u0010\u0010\"\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$H&J\u0010\u0010%\u001a\u00020 2\u0006\u0010&\u001a\u00020 H\u0004J\u0010\u0010\'\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$H\u0004J\u0010\u0010(\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$H\u0004J(\u0010)\u001a\u00020\u001e2\u0006\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020 2\u0006\u0010#\u001a\u00020$H\u0002J\u0010\u0010/\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$H\u0004J\u0010\u00100\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$H\u0004J\u0010\u00101\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$H\u0002R\u0014\u0010\u0005\u001a\u00020\u0006X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0014\u0010\t\u001a\u00020\nX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0014\u0010\r\u001a\u00020\u000eX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0014\u0010\u0011\u001a\u00020\u0012X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0014\u0010\u0015\u001a\u00020\u0016X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0014\u0010\u0019\u001a\u00020\u0012X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0014\u0010\u001b\u001a\u00020\u0016X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0018\u00a8\u00062"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$MessageViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "cardViewMessage", "Landroidx/cardview/widget/CardView;", "getCardViewMessage", "()Landroidx/cardview/widget/CardView;", "layoutReply", "Landroid/widget/LinearLayout;", "getLayoutReply", "()Landroid/widget/LinearLayout;", "reactionsContainer", "Lcom/google/android/flexbox/FlexboxLayout;", "getReactionsContainer", "()Lcom/google/android/flexbox/FlexboxLayout;", "textViewMessage", "Landroidx/emoji2/widget/EmojiTextView;", "getTextViewMessage", "()Landroidx/emoji2/widget/EmojiTextView;", "textViewReplyLabel", "Landroid/widget/TextView;", "getTextViewReplyLabel", "()Landroid/widget/TextView;", "textViewReplyText", "getTextViewReplyText", "textViewTime", "getTextViewTime", "applyMentionStyling", "", "messageText", "", "textView", "bind", "message", "Lcom/spyro/vmeet/data/Message;", "extractTime", "isoTimeString", "setupMessageClick", "setupMessageLongClick", "setupReactionClick", "dialog", "Landroid/app/Dialog;", "viewId", "", "reaction", "setupReactions", "setupReplyView", "showReactionSelector", "app_release"})
    public abstract class MessageViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final androidx.emoji2.widget.EmojiTextView textViewMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewTime = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.cardview.widget.CardView cardViewMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout layoutReply = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewReplyLabel = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.emoji2.widget.EmojiTextView textViewReplyText = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.flexbox.FlexboxLayout reactionsContainer = null;
        
        public MessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final androidx.emoji2.widget.EmojiTextView getTextViewMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.TextView getTextViewTime() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final androidx.cardview.widget.CardView getCardViewMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.LinearLayout getLayoutReply() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.TextView getTextViewReplyLabel() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final androidx.emoji2.widget.EmojiTextView getTextViewReplyText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final com.google.android.flexbox.FlexboxLayout getReactionsContainer() {
            return null;
        }
        
        public abstract void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message);
        
        protected final void applyMentionStyling(@org.jetbrains.annotations.NotNull()
        java.lang.String messageText, @org.jetbrains.annotations.NotNull()
        android.widget.TextView textView) {
        }
        
        protected final void setupReplyView(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupMessageLongClick(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupMessageClick(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        private final void showReactionSelector(com.spyro.vmeet.data.Message message) {
        }
        
        private final void setupReactionClick(android.app.Dialog dialog, int viewId, java.lang.String reaction, com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupReactions(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final java.lang.String extractTime(@org.jetbrains.annotations.NotNull()
        java.lang.String isoTimeString) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rJ\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002J\u0010\u0010\u0011\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$ReceivedBuzzMessageViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewAvatar", "Landroid/widget/ImageView;", "textViewBuzzMessage", "Landroid/widget/TextView;", "textViewBuzzTime", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "extractTime", "", "isoTimeString", "loadAvatar", "app_release"})
    public final class ReceivedBuzzMessageViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewBuzzMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewBuzzTime = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewAvatar = null;
        
        public ReceivedBuzzMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        private final void loadAvatar(com.spyro.vmeet.data.Message message) {
        }
        
        private final java.lang.String extractTime(java.lang.String isoTimeString) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0016J\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$ReceivedGifMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$MessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewGif", "Landroid/widget/ImageView;", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class ReceivedGifMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.MessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewGif = null;
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        
        public ReceivedGifMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0016J\u000e\u0010\f\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$ReceivedImageMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$ImageMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class ReceivedImageMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.ImageMessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        
        public ReceivedImageMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0016J\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$ReceivedMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$MessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "textViewSenderName", "Landroid/widget/TextView;", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class ReceivedMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.MessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewSenderName = null;
        
        public ReceivedMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0016J\u000e\u0010\f\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$ReceivedVoiceMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$VoiceMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class ReceivedVoiceMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.VoiceMessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        
        public ReceivedVoiceMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$SentBuzzMessageViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "textViewBuzzMessage", "Landroid/widget/TextView;", "textViewBuzzTime", "viewBuzzStatus", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "extractTime", "", "isoTimeString", "app_release"})
    public final class SentBuzzMessageViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewBuzzMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewBuzzTime = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View viewBuzzStatus = null;
        
        public SentBuzzMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        private final java.lang.String extractTime(java.lang.String isoTimeString) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u000e\u0010\u000f\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$SentGifMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$MessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewGif", "Landroid/widget/ImageView;", "imageViewReadStatus", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class SentGifMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.MessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewGif = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewReadStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        
        public SentGifMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016J\u000e\u0010\r\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$SentImageMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$ImageMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "viewMessageStatus", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class SentImageMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.ImageMessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.view.View viewMessageStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        
        public SentImageMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u000e\u0010\u000f\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$SentMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$MessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "textViewSenderName", "Landroid/widget/TextView;", "viewMessageStatus", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class SentMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.MessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.view.View viewMessageStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        @org.jetbrains.annotations.Nullable()
        private final android.widget.TextView textViewSenderName = null;
        
        public SentMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0004\u0018\u00002\u00060\u0001R\u00020\u0002B\r\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016J\u000e\u0010\r\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$SentVoiceMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter$VoiceMessageViewHolder;", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "imageViewUserProfile", "Lde/hdodenhof/circleimageview/CircleImageView;", "viewMessageStatus", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "loadAvatar", "app_release"})
    public final class SentVoiceMessageViewHolder extends com.spyro.vmeet.adapter.MessageAdapter.VoiceMessageViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.view.View viewMessageStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageViewUserProfile = null;
        
        public SentVoiceMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null, null);
        }
        
        @java.lang.Override()
        public void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void loadAvatar(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000v\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\t\b\u00a6\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020/H&J\u0010\u00100\u001a\u0002012\u0006\u00102\u001a\u000201H\u0004J\u0010\u00103\u001a\u0002012\u0006\u00104\u001a\u000205H\u0004J\u0006\u00106\u001a\u00020-J(\u00107\u001a\u00020-2\u0006\u00108\u001a\u0002092\u0006\u0010:\u001a\u0002052\u0006\u0010;\u001a\u0002012\u0006\u0010.\u001a\u00020/H\u0002J\u0010\u0010<\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0004J\u0010\u0010=\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0004J\u0010\u0010>\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0004J\u0010\u0010?\u001a\u00020-2\u0006\u0010.\u001a\u00020/H\u0002J\u0010\u0010@\u001a\u00020-2\u0006\u0010A\u001a\u000201H\u0002R\u0014\u0010\u0005\u001a\u00020\u0006X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0014\u0010\t\u001a\u00020\nX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u001a\u0010\r\u001a\u00020\u000eX\u0084\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u001c\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0084\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R\u0016\u0010\u0018\u001a\u0004\u0018\u00010\u0019X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0014\u0010\u001c\u001a\u00020\u001dX\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0014\u0010 \u001a\u00020!X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u001c\u0010$\u001a\u0004\u0018\u00010%X\u0084\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\'\"\u0004\b(\u0010)R\u0014\u0010*\u001a\u00020!X\u0084\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010#\u00a8\u0006B"}, d2 = {"Lcom/spyro/vmeet/adapter/MessageAdapter$VoiceMessageViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/MessageAdapter;Landroid/view/View;)V", "buttonPlayPause", "Landroid/widget/ImageButton;", "getButtonPlayPause", "()Landroid/widget/ImageButton;", "cardViewVoice", "Landroidx/cardview/widget/CardView;", "getCardViewVoice", "()Landroidx/cardview/widget/CardView;", "isPlaying", "", "()Z", "setPlaying", "(Z)V", "layoutReply", "Landroid/widget/LinearLayout;", "getLayoutReply", "()Landroid/widget/LinearLayout;", "setLayoutReply", "(Landroid/widget/LinearLayout;)V", "reactionsContainer", "Lcom/google/android/flexbox/FlexboxLayout;", "getReactionsContainer", "()Lcom/google/android/flexbox/FlexboxLayout;", "seekBarAudio", "Landroid/widget/SeekBar;", "getSeekBarAudio", "()Landroid/widget/SeekBar;", "textViewDuration", "Landroid/widget/TextView;", "getTextViewDuration", "()Landroid/widget/TextView;", "textViewReplyText", "Landroidx/emoji2/widget/EmojiTextView;", "getTextViewReplyText", "()Landroidx/emoji2/widget/EmojiTextView;", "setTextViewReplyText", "(Landroidx/emoji2/widget/EmojiTextView;)V", "textViewTime", "getTextViewTime", "bind", "", "message", "Lcom/spyro/vmeet/data/Message;", "extractTime", "", "isoTimeString", "formatDuration", "durationMs", "", "resetPlayingState", "setupReactionClick", "dialog", "Landroid/app/Dialog;", "viewId", "reaction", "setupReactions", "setupReplyView", "setupVoiceMessage", "showReactionSelector", "togglePlayback", "audioFilePath", "app_release"})
    public abstract class VoiceMessageViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageButton buttonPlayPause = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.SeekBar seekBarAudio = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewDuration = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewTime = null;
        @org.jetbrains.annotations.NotNull()
        private final androidx.cardview.widget.CardView cardViewVoice = null;
        @org.jetbrains.annotations.Nullable()
        private final com.google.android.flexbox.FlexboxLayout reactionsContainer = null;
        @org.jetbrains.annotations.Nullable()
        private android.widget.LinearLayout layoutReply;
        @org.jetbrains.annotations.Nullable()
        private androidx.emoji2.widget.EmojiTextView textViewReplyText;
        private boolean isPlaying = false;
        
        public VoiceMessageViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.ImageButton getButtonPlayPause() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.SeekBar getSeekBarAudio() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.TextView getTextViewDuration() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final android.widget.TextView getTextViewTime() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final androidx.cardview.widget.CardView getCardViewVoice() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        protected final com.google.android.flexbox.FlexboxLayout getReactionsContainer() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        protected final android.widget.LinearLayout getLayoutReply() {
            return null;
        }
        
        protected final void setLayoutReply(@org.jetbrains.annotations.Nullable()
        android.widget.LinearLayout p0) {
        }
        
        @org.jetbrains.annotations.Nullable()
        protected final androidx.emoji2.widget.EmojiTextView getTextViewReplyText() {
            return null;
        }
        
        protected final void setTextViewReplyText(@org.jetbrains.annotations.Nullable()
        androidx.emoji2.widget.EmojiTextView p0) {
        }
        
        protected final boolean isPlaying() {
            return false;
        }
        
        protected final void setPlaying(boolean p0) {
        }
        
        public abstract void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message);
        
        protected final void setupReplyView(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupVoiceMessage(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        private final void showReactionSelector(com.spyro.vmeet.data.Message message) {
        }
        
        private final void setupReactionClick(android.app.Dialog dialog, int viewId, java.lang.String reaction, com.spyro.vmeet.data.Message message) {
        }
        
        protected final void setupReactions(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.data.Message message) {
        }
        
        public final void resetPlayingState() {
        }
        
        private final void togglePlayback(java.lang.String audioFilePath) {
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final java.lang.String formatDuration(int durationMs) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        protected final java.lang.String extractTime(@org.jetbrains.annotations.NotNull()
        java.lang.String isoTimeString) {
            return null;
        }
    }
}