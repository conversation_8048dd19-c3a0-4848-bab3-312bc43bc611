package com.spyro.vmeet.ui.community;

/**
 * Helper class to handle image upload functionality, including:
 * - Requesting permissions
 * - Taking photos with camera
 * - Selecting from gallery
 * - Processing and preparing image for upload
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001:\u0001\u001eB-\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\nJ\u0006\u0010\u0016\u001a\u00020\u0017J\b\u0010\u0018\u001a\u00020\u0017H\u0002J\b\u0010\u0019\u001a\u00020\u0017H\u0002J\u0006\u0010\u001a\u001a\u00020\u0017J\u000e\u0010\u001b\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\u0015J\u0006\u0010\u001d\u001a\u00020\u0017R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00110\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00110\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/spyro/vmeet/ui/community/ImageUploadHelper;", "", "context", "Landroid/content/Context;", "isFragment", "", "fragment", "Landroidx/fragment/app/Fragment;", "activity", "Landroidx/appcompat/app/AppCompatActivity;", "(Landroid/content/Context;ZLandroidx/fragment/app/Fragment;Landroidx/appcompat/app/AppCompatActivity;)V", "cameraImageUri", "Landroid/net/Uri;", "cameraLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "cameraPermissionLauncher", "", "galleryLauncher", "galleryPermissionLauncher", "imageSelectedListener", "Lcom/spyro/vmeet/ui/community/ImageUploadHelper$ImageSelectedListener;", "initialize", "", "openCamera", "openGallery", "selectImageFromGallery", "setImageSelectedListener", "listener", "takePhotoWithCamera", "ImageSelectedListener", "app_debug"})
public final class ImageUploadHelper {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final boolean isFragment = false;
    @org.jetbrains.annotations.Nullable()
    private final androidx.fragment.app.Fragment fragment = null;
    @org.jetbrains.annotations.Nullable()
    private final androidx.appcompat.app.AppCompatActivity activity = null;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri cameraImageUri;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.ui.community.ImageUploadHelper.ImageSelectedListener imageSelectedListener;
    private androidx.activity.result.ActivityResultLauncher<android.content.Intent> cameraLauncher;
    private androidx.activity.result.ActivityResultLauncher<java.lang.String> galleryLauncher;
    private androidx.activity.result.ActivityResultLauncher<java.lang.String> cameraPermissionLauncher;
    private androidx.activity.result.ActivityResultLauncher<java.lang.String> galleryPermissionLauncher;
    
    public ImageUploadHelper(@org.jetbrains.annotations.NotNull()
    android.content.Context context, boolean isFragment, @org.jetbrains.annotations.Nullable()
    androidx.fragment.app.Fragment fragment, @org.jetbrains.annotations.Nullable()
    androidx.appcompat.app.AppCompatActivity activity) {
        super();
    }
    
    public final void setImageSelectedListener(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.ui.community.ImageUploadHelper.ImageSelectedListener listener) {
    }
    
    public final void initialize() {
    }
    
    public final void selectImageFromGallery() {
    }
    
    public final void takePhotoWithCamera() {
    }
    
    private final void openGallery() {
    }
    
    private final void openCamera() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0012\u0010\u0002\u001a\u00020\u00032\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/spyro/vmeet/ui/community/ImageUploadHelper$ImageSelectedListener;", "", "onImageSelected", "", "imageUri", "Landroid/net/Uri;", "app_debug"})
    public static abstract interface ImageSelectedListener {
        
        public abstract void onImageSelected(@org.jetbrains.annotations.Nullable()
        android.net.Uri imageUri);
    }
}