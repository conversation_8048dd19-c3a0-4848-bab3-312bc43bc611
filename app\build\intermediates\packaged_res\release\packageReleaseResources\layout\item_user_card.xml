<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    android:layout_margin="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/imageViewUserCardAvatar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:srcCompat="@drawable/ic_profile_placeholder" />

        <!-- Gradient Overlay for Text Readability -->
        <View
            android:id="@+id/gradientOverlay"
            android:layout_width="0dp"
            android:layout_height="120dp"
            android:background="@drawable/gradient_bottom_overlay"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Online Status Indicator -->
        <View
            android:id="@+id/onlineStatusIndicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_margin="12dp"
            android:background="@drawable/online_status_indicator"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toBottomOf="@id/gradientOverlay"
            app:layout_constraintEnd_toEndOf="@id/gradientOverlay"
            app:layout_constraintStart_toStartOf="@id/gradientOverlay">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/textViewUserCardNameAge"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@android:color/white"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    tools:text="Username, 25" />

                <TextView
                    android:id="@+id/textViewOnlineStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="En línea"
                    android:textColor="#4CAF50"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:background="@drawable/online_status_background"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="4dp"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <TextView
                android:id="@+id/textViewUserCardBio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:maxLines="2"
                android:ellipsize="end"
                android:shadowColor="#80000000"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1.5"
                tools:text="This is a short bio about the user, gamer and enthusiast..." />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>