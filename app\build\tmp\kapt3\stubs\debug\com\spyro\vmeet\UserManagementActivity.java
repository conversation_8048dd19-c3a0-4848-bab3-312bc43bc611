package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000h\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u0000 82\u00020\u0001:\u00018B\u0005\u00a2\u0006\u0002\u0010\u0002J\'\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0012H\u0002\u00a2\u0006\u0002\u0010\u0019J\b\u0010\u001a\u001a\u00020\u0014H\u0002J\u0012\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J\u0010\u0010\u001d\u001a\u00020\u00142\u0006\u0010\u001e\u001a\u00020\u001cH\u0002J\u0018\u0010\u001f\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u0017H\u0002J\u0018\u0010!\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u0017H\u0002J\u0018\u0010\"\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010#\u001a\u00020\u0012H\u0002J\u0010\u0010$\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0012H\u0002J\b\u0010%\u001a\u00020\u0014H\u0002J\u0012\u0010&\u001a\u00020\u00142\b\u0010\'\u001a\u0004\u0018\u00010(H\u0014J\u0010\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020,H\u0016J\b\u0010-\u001a\u00020\u0014H\u0002J\u0010\u0010.\u001a\u00020\u001c2\u0006\u0010/\u001a\u00020\u001cH\u0002J\u0010\u00100\u001a\u00020\u00142\u0006\u00101\u001a\u00020\u0017H\u0002J\b\u00102\u001a\u00020\u0014H\u0002J\u0010\u00103\u001a\u00020\u00142\u0006\u0010\u001e\u001a\u00020\u001cH\u0002J\u0018\u00104\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u0017H\u0002J\u0010\u00105\u001a\u00020\u00142\u0006\u0010\u001e\u001a\u00020\u001cH\u0002J\u0010\u00106\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0012H\u0002J\u0018\u00107\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010 \u001a\u00020\u0017H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00069"}, d2 = {"Lcom/spyro/vmeet/UserManagementActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "()V", "btnSearch", "Lcom/google/android/material/button/MaterialButton;", "client", "Lokhttp3/OkHttpClient;", "etSearch", "Lcom/google/android/material/textfield/TextInputEditText;", "progressBar", "Landroid/widget/ProgressBar;", "recyclerUsers", "Landroidx/recyclerview/widget/RecyclerView;", "tilSearch", "Lcom/google/android/material/textfield/TextInputLayout;", "tvEmptyList", "Landroid/widget/TextView;", "userId", "", "banUser", "", "targetUserId", "reason", "", "duration", "(ILjava/lang/String;Ljava/lang/Integer;)V", "checkAdminStatus", "checkUserMuteStatus", "Lorg/json/JSONObject;", "confirmUnban", "userObject", "confirmUnmute", "username", "confirmVerifyUser", "executeMuteUser", "durationMinutes", "executeUnmuteUser", "loadUsers", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onOptionsItemSelected", "", "item", "Landroid/view/MenuItem;", "performSearch", "preprocessUserData", "user", "searchUsers", "query", "setupSearch", "showBanDialog", "showMuteDialog", "showUserHistory", "unbanUser", "verifyUser", "Companion", "app_debug"})
public final class UserManagementActivity extends com.spyro.vmeet.activity.BaseActivity {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "UserManagementActivity";
    private androidx.recyclerview.widget.RecyclerView recyclerUsers;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView tvEmptyList;
    private com.google.android.material.textfield.TextInputLayout tilSearch;
    private com.google.android.material.textfield.TextInputEditText etSearch;
    private com.google.android.material.button.MaterialButton btnSearch;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.UserManagementActivity.Companion Companion = null;
    
    public UserManagementActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupSearch() {
    }
    
    private final void performSearch() {
    }
    
    private final void checkAdminStatus() {
    }
    
    private final void loadUsers() {
    }
    
    private final org.json.JSONObject checkUserMuteStatus(int userId) {
        return null;
    }
    
    private final void searchUsers(java.lang.String query) {
    }
    
    private final void showBanDialog(org.json.JSONObject userObject) {
    }
    
    private final void banUser(int targetUserId, java.lang.String reason, java.lang.Integer duration) {
    }
    
    private final void confirmUnban(org.json.JSONObject userObject) {
    }
    
    private final void unbanUser(int targetUserId) {
    }
    
    private final void confirmUnmute(int targetUserId, java.lang.String username) {
    }
    
    private final void executeUnmuteUser(int targetUserId) {
    }
    
    private final void showUserHistory(org.json.JSONObject userObject) {
    }
    
    private final void showMuteDialog(int targetUserId, java.lang.String username) {
    }
    
    private final void executeMuteUser(int targetUserId, int durationMinutes) {
    }
    
    private final org.json.JSONObject preprocessUserData(org.json.JSONObject user) {
        return null;
    }
    
    private final void confirmVerifyUser(int targetUserId, java.lang.String username) {
    }
    
    private final void verifyUser(int targetUserId, java.lang.String username) {
    }
    
    @java.lang.Override()
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull()
    android.view.MenuItem item) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/UserManagementActivity$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}