<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="@drawable/rounded_dialog_background">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Editar Texto"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="16dp"/>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Escribe algo..."
            android:textColorHint="@android:color/darker_gray"
            app:boxBackgroundColor="#33FFFFFF"
            app:boxCornerRadiusBottomEnd="12dp"
            app:boxCornerRadiusBottomStart="12dp"
            app:boxCornerRadiusTopEnd="12dp"
            app:boxCornerRadiusTopStart="12dp"
            app:boxStrokeColor="@color/neon_blue"
            app:hintTextColor="@color/neon_blue">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextStoryText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:inputType="textMultiLine"
                android:maxLines="5" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Live Preview Area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/preview_background">

            <TextView
                android:id="@+id/textPreview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="60dp"
                android:gravity="center"
                android:padding="12dp"
                android:text="Vista Previa"
                android:textColor="@android:color/white" />
        </FrameLayout>

        <!-- Color Picker -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp"
            android:background="@drawable/section_background"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Color del Texto"
                android:textColor="@color/neon_blue"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/pickColorButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Elegir Color"
                    app:cornerRadius="20dp"
                    android:layout_marginEnd="12dp"
                    app:backgroundTint="@color/neon_pink"/>

                <View
                    android:id="@+id/selectedColorPreview"
                    android:layout_width="0dp"
                    android:layout_height="24dp"
                    android:layout_weight="1"
                    android:background="@drawable/color_preview_background" />
            </LinearLayout>
        </LinearLayout>

        <!-- Font and Style Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp"
            android:background="@drawable/section_background"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fuente y Estilo"
                android:textColor="@color/neon_blue"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <!-- Font Selector -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fuente:"
                android:textColor="@android:color/white"
                android:layout_marginTop="8dp"/>

            <Spinner
                android:id="@+id/fontSpinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/spinner_background"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp" />

            <!-- Text Style (Bold/Italic) -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Estilo:"
                android:textColor="@android:color/white"
                android:layout_marginTop="12dp"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/boldCheckbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Negrita"
                    android:textColor="@android:color/white"
                    app:buttonTint="@color/neon_pink"/>

                <com.google.android.material.checkbox.MaterialCheckBox
                    android:id="@+id/italicCheckbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cursiva"
                    android:textColor="@android:color/white"
                    app:buttonTint="@color/neon_pink"
                    android:layout_marginStart="24dp"/>
            </LinearLayout>
        </LinearLayout>

        <!-- Animation Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp"
            android:background="@drawable/section_background"
            android:padding="12dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Animación"
                android:textColor="@color/neon_blue"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"/>

            <Spinner
                android:id="@+id/animationSpinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/spinner_background"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp" />
        </LinearLayout>
    </LinearLayout>
</ScrollView> 