package com.spyro.vmeet.fragment;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008c\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000e\b\u0007\u0018\u0000 ;2\u00020\u0001:\u0001;B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\tH\u0002J\u0010\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020\u001eH\u0002J\"\u0010 \u001a\u0014\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\r0!2\u0006\u0010\"\u001a\u00020\u0017H\u0002J\u001c\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001e0$2\u0006\u0010\u0016\u001a\u00020\u0017H\u0002J\u0012\u0010%\u001a\u00020\u001b2\b\b\u0002\u0010&\u001a\u00020\rH\u0002J&\u0010\'\u001a\u0004\u0018\u00010(2\u0006\u0010)\u001a\u00020*2\b\u0010+\u001a\u0004\u0018\u00010,2\b\u0010-\u001a\u0004\u0018\u00010.H\u0016J\b\u0010/\u001a\u00020\u001bH\u0016J\b\u00100\u001a\u00020\u001bH\u0016J\b\u00101\u001a\u00020\u001bH\u0016J\u0010\u00102\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\tH\u0002J\u0010\u00103\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\tH\u0002J\b\u00104\u001a\u00020\u001bH\u0002J\b\u00105\u001a\u00020\u001bH\u0002J\u0010\u00106\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\tH\u0002J\u0010\u00107\u001a\u00020\u001b2\u0006\u00108\u001a\u00020\u001eH\u0002J\b\u00109\u001a\u00020\u001bH\u0002J\b\u0010:\u001a\u00020\u001bH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006<"}, d2 = {"Lcom/spyro/vmeet/fragment/PrivateChatsFragment;", "Landroidx/fragment/app/Fragment;", "()V", "POLLING_INTERVAL", "", "chatListAdapter", "Lcom/spyro/vmeet/adapter/ChatListAdapter;", "chatSummaryList", "", "Lcom/spyro/vmeet/data/ChatSummary;", "chatUpdateReceiver", "Landroid/content/BroadcastReceiver;", "isPollingActive", "", "pollingHandler", "Landroid/os/Handler;", "progressBarChats", "Landroid/widget/ProgressBar;", "recyclerViewChatList", "Landroidx/recyclerview/widget/RecyclerView;", "textViewEmptyChats", "Landroid/widget/TextView;", "userId", "", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "deleteChat", "", "chatSummary", "formatTimestamp", "", "timestamp", "getLastMessage", "Lkotlin/Triple;", "matchId", "getUserInfo", "Lkotlin/Pair;", "loadChatList", "showLoading", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "onPause", "onResume", "openChatActivity", "replyToChat", "setupSwipeToReply", "setupWebSocket", "showDeleteChatDialog", "showError", "message", "startPolling", "stopPolling", "Companion", "app_release"})
public final class PrivateChatsFragment extends androidx.fragment.app.Fragment {
    private androidx.recyclerview.widget.RecyclerView recyclerViewChatList;
    private android.widget.TextView textViewEmptyChats;
    private android.widget.ProgressBar progressBarChats;
    private com.spyro.vmeet.adapter.ChatListAdapter chatListAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.ChatSummary> chatSummaryList = null;
    private int userId = 0;
    private boolean isPollingActive = false;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler pollingHandler = null;
    private final long POLLING_INTERVAL = 1000L;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.WebSocketClient webSocketClient;
    @org.jetbrains.annotations.NotNull()
    private final android.content.BroadcastReceiver chatUpdateReceiver = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "PrivateChatsFragment";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String WS_URL = "ws://77.110.116.89:3000/ws";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String UPDATE_CHAT_ACTION = "com.spyro.vmeet.UPDATE_CHAT_LIST";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.fragment.PrivateChatsFragment.Companion Companion = null;
    
    public PrivateChatsFragment() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onResume() {
    }
    
    @java.lang.Override()
    public void onPause() {
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    private final void setupSwipeToReply() {
    }
    
    private final void replyToChat(com.spyro.vmeet.data.ChatSummary chatSummary) {
    }
    
    private final void openChatActivity(com.spyro.vmeet.data.ChatSummary chatSummary) {
    }
    
    private final void loadChatList(boolean showLoading) {
    }
    
    private final kotlin.Pair<java.lang.String, java.lang.String> getUserInfo(int userId) {
        return null;
    }
    
    private final kotlin.Triple<java.lang.String, java.lang.String, java.lang.Boolean> getLastMessage(int matchId) {
        return null;
    }
    
    private final java.lang.String formatTimestamp(java.lang.String timestamp) {
        return null;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void startPolling() {
    }
    
    private final void stopPolling() {
    }
    
    private final void showDeleteChatDialog(com.spyro.vmeet.data.ChatSummary chatSummary) {
    }
    
    private final void deleteChat(com.spyro.vmeet.data.ChatSummary chatSummary) {
    }
    
    private final void setupWebSocket() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/spyro/vmeet/fragment/PrivateChatsFragment$Companion;", "", "()V", "API_URL", "", "TAG", "UPDATE_CHAT_ACTION", "WS_URL", "newInstance", "Lcom/spyro/vmeet/fragment/PrivateChatsFragment;", "userId", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.fragment.PrivateChatsFragment newInstance(int userId) {
            return null;
        }
    }
}