package com.spyro.vmeet.dialog;

/**
 * Custom dialog that shows a tabbed grid of emojis for selection
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 \u00192\u00020\u0001:\u0002\u0019\u001aB0\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012!\u0010\u0004\u001a\u001d\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0004\u0012\u00020\n0\u0005\u00a2\u0006\u0002\u0010\u000bJ\u0012\u0010\u0015\u001a\u00020\n2\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0014J\b\u0010\u0018\u001a\u00020\nH\u0002R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0012\u0010\u000f\u001a\u00060\u0010R\u00020\u0000X\u0082.\u00a2\u0006\u0002\n\u0000R)\u0010\u0004\u001a\u001d\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\u0007\u0012\b\b\b\u0012\u0004\b\b(\t\u0012\u0004\u0012\u00020\n0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/spyro/vmeet/dialog/EmojiPickerDialog;", "Landroid/app/Dialog;", "context", "Landroid/content/Context;", "onEmojiSelected", "Lkotlin/Function1;", "", "Lkotlin/ParameterName;", "name", "emoji", "", "(Landroid/content/Context;Lkotlin/jvm/functions/Function1;)V", "categories", "", "Lcom/spyro/vmeet/dialog/EmojiCategory;", "emojiAdapter", "Lcom/spyro/vmeet/dialog/EmojiPickerDialog$EmojiAdapter;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "tabLayout", "Lcom/google/android/material/tabs/TabLayout;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "setupTabs", "Companion", "EmojiAdapter", "app_release"})
public final class EmojiPickerDialog extends android.app.Dialog {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onEmojiSelected = null;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private com.google.android.material.tabs.TabLayout tabLayout;
    private com.spyro.vmeet.dialog.EmojiPickerDialog.EmojiAdapter emojiAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.dialog.EmojiCategory> categories = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> smileys = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> people = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> animals = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> food = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> travel = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> activities = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> objects = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> symbols = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.dialog.EmojiPickerDialog.Companion Companion = null;
    
    public EmojiPickerDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEmojiSelected) {
        super(null);
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupTabs() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/spyro/vmeet/dialog/EmojiPickerDialog$Companion;", "", "()V", "activities", "", "", "animals", "food", "objects", "people", "smileys", "symbols", "travel", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0082\u0004\u0018\u00002\u0010\u0012\f\u0012\n0\u0002R\u00060\u0000R\u00020\u00030\u0001:\u0001\u0019B6\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012!\u0010\u0007\u001a\u001d\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\f0\b\u00a2\u0006\u0002\u0010\rJ\b\u0010\u000e\u001a\u00020\u000fH\u0016J \u0010\u0010\u001a\u00020\f2\u000e\u0010\u0011\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u000fH\u0016J \u0010\u0013\u001a\n0\u0002R\u00060\u0000R\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u000fH\u0016J\u0014\u0010\u0017\u001a\u00020\f2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005R\u0014\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R)\u0010\u0007\u001a\u001d\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0004\u0012\u00020\f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/spyro/vmeet/dialog/EmojiPickerDialog$EmojiAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/dialog/EmojiPickerDialog$EmojiAdapter$EmojiViewHolder;", "Lcom/spyro/vmeet/dialog/EmojiPickerDialog;", "emojis", "", "", "onEmojiClick", "Lkotlin/Function1;", "Lkotlin/ParameterName;", "name", "emoji", "", "(Lcom/spyro/vmeet/dialog/EmojiPickerDialog;Ljava/util/List;Lkotlin/jvm/functions/Function1;)V", "getItemCount", "", "onBindViewHolder", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "updateEmojis", "newEmojis", "EmojiViewHolder", "app_release"})
    final class EmojiAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.dialog.EmojiPickerDialog.EmojiAdapter.EmojiViewHolder> {
        @org.jetbrains.annotations.NotNull()
        private java.util.List<java.lang.String> emojis;
        @org.jetbrains.annotations.NotNull()
        private final kotlin.jvm.functions.Function1<java.lang.String, kotlin.Unit> onEmojiClick = null;
        
        public EmojiAdapter(@org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> emojis, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onEmojiClick) {
            super();
        }
        
        public final void updateEmojis(@org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> newEmojis) {
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.spyro.vmeet.dialog.EmojiPickerDialog.EmojiAdapter.EmojiViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.ViewGroup parent, int viewType) {
            return null;
        }
        
        @java.lang.Override()
        public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.dialog.EmojiPickerDialog.EmojiAdapter.EmojiViewHolder holder, int position) {
        }
        
        @java.lang.Override()
        public int getItemCount() {
            return 0;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/spyro/vmeet/dialog/EmojiPickerDialog$EmojiAdapter$EmojiViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/dialog/EmojiPickerDialog$EmojiAdapter;Landroid/view/View;)V", "textEmoji", "Landroid/widget/TextView;", "bind", "", "emoji", "", "app_release"})
        public final class EmojiViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
            @org.jetbrains.annotations.NotNull()
            private final android.widget.TextView textEmoji = null;
            
            public EmojiViewHolder(@org.jetbrains.annotations.NotNull()
            android.view.View itemView) {
                super(null);
            }
            
            public final void bind(@org.jetbrains.annotations.NotNull()
            java.lang.String emoji) {
            }
        }
    }
}