<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cyberpunk_background"
    android:fitsSystemWindows="true"
    tools:context=".ui.community.CommentsActivity">

    <!-- AppBar with Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/darker_blue">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
            android:background="@color/darker_blue">

        <TextView
            android:id="@+id/textViewToolbarTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Comentarios"
            android:textColor="@color/neon_blue"
            android:textSize="20sp"
            android:textStyle="bold" />

    </androidx.appcompat.widget.Toolbar>

    <!-- Glowing edge -->
    <View
        android:id="@+id/toolbarDivider"
        android:layout_width="match_parent"
        android:layout_height="2dp"
            android:background="@color/neon_blue" />
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content in ScrollView with explicit ID for insets handling -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:paddingBottom="80dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

    <!-- Original Post Card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewOriginalPost"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardBackgroundColor="@color/darker_blue"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:strokeColor="@color/neon_blue"
                app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/imageViewPostUserAvatar"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/avatar_circle_border"
                    android:padding="2dp"
                    android:src="@drawable/ic_profile_placeholder" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="12dp">

                    <TextView
                        android:id="@+id/textViewPostUsername"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Username"
                        android:textColor="@color/cyberpunk_text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/textViewPostTimestamp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Timestamp"
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:id="@+id/textViewPostContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Original post content..."
                android:textColor="@color/cyberpunk_text_primary"
                        android:maxLines="5"
                android:ellipsize="end" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Comments list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewComments"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
                android:nestedScrollingEnabled="false"
        tools:listitem="@layout/item_comment" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- Comment input at bottom (fixed) with top border -->
    <LinearLayout
        android:id="@+id/layoutCommentInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="bottom">
        
        <!-- Top border line -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/neon_blue" />
            
        <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
            android:background="@color/darker_blue">

        <EditText
            android:id="@+id/editTextComment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Escribe un comentario..."
            android:textColorHint="@color/cyberpunk_text_secondary"
            android:textColor="@color/cyberpunk_text_primary"
            android:background="@drawable/post_edit_text_background"
            android:padding="12dp"
            android:maxLines="3"
            android:inputType="textMultiLine|textCapSentences" />

        <ImageButton
            android:id="@+id/buttonSendComment"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_send_comment"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:tint="@color/neon_blue" />
        </LinearLayout>
    </LinearLayout>

    <!-- Loading indicator -->
    <ProgressBar
        android:id="@+id/progressBarComments"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_gravity="center" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 