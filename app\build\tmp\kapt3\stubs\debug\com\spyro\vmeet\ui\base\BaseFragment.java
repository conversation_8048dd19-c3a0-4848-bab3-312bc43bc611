package com.spyro.vmeet.ui.base;

/**
 * Base fragment that all fragments in the app should extend.
 * Handles common functionality like applying PixelPerfectTextView replacement.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \f2\u00020\u0001:\u0001\fB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0004J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u0006H\u0004J\u001a\u0010\t\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u00062\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0016\u00a8\u0006\r"}, d2 = {"Lcom/spyro/vmeet/ui/base/BaseFragment;", "Landroidx/fragment/app/Fragment;", "()V", "applyPixelPerfectTextViews", "", "rootView", "Landroid/view/View;", "applyPixelPerfectToView", "view", "onViewCreated", "savedInstanceState", "Landroid/os/Bundle;", "Companion", "app_debug"})
public abstract class BaseFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "BaseFragment";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.base.BaseFragment.Companion Companion = null;
    
    public BaseFragment() {
        super();
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    /**
     * Apply PixelPerfectTextView replacement to eliminate number gradients
     * Call this method after inflating new views or updating content
     */
    protected final void applyPixelPerfectTextViews(@org.jetbrains.annotations.NotNull()
    android.view.View rootView) {
    }
    
    /**
     * Apply PixelPerfectTextView replacement to a specific view
     * Useful when dynamically adding views or updating content
     */
    protected final void applyPixelPerfectToView(@org.jetbrains.annotations.NotNull()
    android.view.View view) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/ui/base/BaseFragment$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}