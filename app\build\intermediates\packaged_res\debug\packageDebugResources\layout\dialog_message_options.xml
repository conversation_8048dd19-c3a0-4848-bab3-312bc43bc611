<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="#212138"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Opciones de mensaje"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="16dp"/>

        <TextView
            android:id="@+id/buttonReply"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Responder"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:background="?attr/selectableItemBackground"
            android:drawableStart="@drawable/ic_reply"
            android:drawablePadding="16dp"/>

        <TextView
            android:id="@+id/buttonMuteUser"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Silenciar usuario"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:background="?attr/selectableItemBackground"
            android:drawableStart="@drawable/ic_mute"
            android:drawablePadding="16dp"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/buttonDelete"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Eliminar mensaje"
            android:textColor="#FF5252"
            android:textSize="16sp"
            android:gravity="center_vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:background="?attr/selectableItemBackground"
            android:drawableStart="@drawable/ic_delete"
            android:drawablePadding="16dp"
            android:visibility="gone"/>

    </LinearLayout>

</androidx.cardview.widget.CardView> 