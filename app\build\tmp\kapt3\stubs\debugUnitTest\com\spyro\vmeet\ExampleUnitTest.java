package com.spyro.vmeet;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/ExampleUnitTest;", "", "()V", "addition_isCorrect", "", "app_debugUnitTest"})
public final class ExampleUnitTest {
    
    public ExampleUnitTest() {
        super();
    }
    
    @org.junit.Test()
    public final void addition_isCorrect() {
    }
}