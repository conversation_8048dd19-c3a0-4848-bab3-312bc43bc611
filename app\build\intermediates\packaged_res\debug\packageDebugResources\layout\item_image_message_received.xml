<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp"
    android:paddingStart="8dp"
    android:layout_marginEnd="48dp">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/imageViewUserProfile"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_profile_placeholder"
        app:civ_border_width="1dp"
        app:civ_border_color="#CCCCCC"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/messageContentLayout"
        android:contentDescription="User profile picture" />

    <LinearLayout
        android:id="@+id/messageContentLayout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="8dp"
        app:layout_constraintStart_toEndOf="@+id/imageViewUserProfile"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <LinearLayout
            android:id="@+id/layoutReply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/reply_background"
            android:padding="8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:id="@+id/textViewReplyLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Respondiendo a:"
                android:textColor="#64B5F6"
                android:textSize="12sp"
                android:textStyle="italic" />

            <androidx.emoji2.widget.EmojiTextView
                android:id="@+id/textViewReplyText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:maxLines="2"
                android:ellipsize="end" />
        </LinearLayout>

        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="300dp"
            app:cardCornerRadius="12dp"
            app:cardBackgroundColor="#303F9F">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">
                
                <ImageView
                    android:id="@+id/imageViewPhoto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxWidth="300dp"
                    android:maxHeight="300dp"
                    android:minHeight="100dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:background="@drawable/image_placeholder"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"/>

                <TextView
                    android:id="@+id/textViewTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12:34"
                    android:textColor="#CCCCCC"
                    android:textSize="12sp"
                    android:padding="4dp"
                    android:layout_marginStart="8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"/>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/reactionsContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="4dp"
            app:flexWrap="wrap"
            app:justifyContent="flex_start"
            app:alignItems="flex_start"
            android:background="@drawable/reaction_container_background"
            android:paddingStart="4dp"
            android:paddingEnd="4dp"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:visibility="gone"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>