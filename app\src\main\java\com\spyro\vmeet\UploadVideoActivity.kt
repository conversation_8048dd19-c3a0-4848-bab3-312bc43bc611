package com.spyro.vmeet

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.Toast
import android.widget.VideoView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.google.android.material.appbar.MaterialToolbar
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class UploadVideoActivity : AppCompatActivity() {
    private lateinit var toolbar: MaterialToolbar
    private lateinit var videoView: VideoView
    private lateinit var editDescription: EditText
    private lateinit var buttonSelectVideo: Button
    private lateinit var buttonUpload: Button
    private lateinit var progressBar: ProgressBar

    private var selectedVideoUri: Uri? = null
    private var userId: Int = 0
    private val API_URL = "http://*************:3000"

    // Activity Result Launchers
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            selectedVideoUri = it
            setupVideoView(it)
            buttonUpload.isEnabled = true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_upload_video)

        // Get userId
        userId = intent.getIntExtra("USER_ID", 0)
        if (userId == 0) {
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", 0)
            if (userId == 0) {
                finish()
                return
            }
        }

        initializeViews()
        setupListeners()
    }

    private fun initializeViews() {
        toolbar = findViewById(R.id.toolbar)
        videoView = findViewById(R.id.videoView)
        editDescription = findViewById(R.id.editDescriptionText)
        buttonSelectVideo = findViewById(R.id.buttonSelectVideo)
        buttonUpload = findViewById(R.id.buttonUpload)
        progressBar = findViewById(R.id.progressBar)

        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Subir Video"

        buttonUpload.isEnabled = false
    }

    private fun setupListeners() {
        toolbar.setNavigationOnClickListener {
            finish()
        }

        buttonSelectVideo.setOnClickListener {
            pickVideo()
        }

        buttonUpload.setOnClickListener {
            uploadVideo()
        }
    }

    private fun pickVideo() {
        try {
            // Use the modern file picker that doesn't require permissions
            videoPickerLauncher.launch("video/*")
        } catch (e: Exception) {
            Toast.makeText(this, "Error al abrir el selector de videos", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupVideoView(uri: Uri) {
        try {
            videoView.setVideoURI(uri)

            // Set up video prepared listener to handle aspect ratio
            videoView.setOnPreparedListener { mediaPlayer ->
                // Get video dimensions
                val videoWidth = mediaPlayer.videoWidth
                val videoHeight = mediaPlayer.videoHeight

                if (videoWidth > 0 && videoHeight > 0) {
                    // Calculate aspect ratio
                    val aspectRatio = videoWidth.toFloat() / videoHeight.toFloat()

                    // Adjust VideoView layout parameters to maintain aspect ratio
                    val layoutParams = videoView.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

                    if (aspectRatio > 1) {
                        // Landscape video - fit width, adjust height
                        layoutParams.dimensionRatio = "H,$videoWidth:$videoHeight"
                    } else {
                        // Portrait video (like TikTok) - fit height, adjust width
                        layoutParams.dimensionRatio = "W,$videoWidth:$videoHeight"
                    }

                    videoView.layoutParams = layoutParams
                }

                // Start playing the video
                mediaPlayer.start()
                mediaPlayer.isLooping = true
            }

            videoView.setOnErrorListener { _, what, extra ->
                Toast.makeText(this, "Error al cargar el video", Toast.LENGTH_SHORT).show()
                false
            }

        } catch (e: Exception) {
            Toast.makeText(this, "Error al configurar el video", Toast.LENGTH_SHORT).show()
        }
    }

    private fun uploadVideo() {
        val videoUri = selectedVideoUri ?: return
        val description = editDescription.text.toString()

        if (description.isBlank()) {
            Toast.makeText(this, "Por favor, añade una descripción", Toast.LENGTH_SHORT).show()
            return
        }

        // Show progress
        progressBar.visibility = View.VISIBLE
        buttonUpload.isEnabled = false

        // Create temp file from uri
        val inputStream = contentResolver.openInputStream(videoUri)
        val tempFile = File.createTempFile("video", ".mp4", cacheDir)
        val outputStream = FileOutputStream(tempFile)
        inputStream?.copyTo(outputStream)

        // Create multipart request
        val client = OkHttpClient()
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(
                "video",
                "video.mp4",
                tempFile.asRequestBody("video/mp4".toMediaTypeOrNull())
            )
            .addFormDataPart("description", description)
            .addFormDataPart("userId", userId.toString())
            .build()

        val request = Request.Builder()
            .url("$API_URL/videos/upload")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonUpload.isEnabled = true
                    Toast.makeText(
                        this@UploadVideoActivity,
                        "Error al subir el video",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    if (response.isSuccessful) {
                        Toast.makeText(
                            this@UploadVideoActivity,
                            "Video subido correctamente",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    } else {
                        buttonUpload.isEnabled = true
                        Toast.makeText(
                            this@UploadVideoActivity,
                            "Error al subir el video",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }
}
