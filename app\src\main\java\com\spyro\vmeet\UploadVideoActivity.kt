package com.spyro.vmeet

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.ui.PlayerView
import com.google.android.material.appbar.MaterialToolbar
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

class UploadVideoActivity : AppCompatActivity() {
    private lateinit var toolbar: MaterialToolbar
    private lateinit var playerView: PlayerView
    private lateinit var editDescription: EditText
    private lateinit var buttonSelectVideo: Button
    private lateinit var buttonUpload: Button
    private lateinit var progressBar: ProgressBar

    private var selectedVideoUri: Uri? = null
    private var userId: Int = 0
    private var exoPlayer: ExoPlayer? = null
    private val API_URL = "http://*************:3000"

    // Activity Result Launchers
    private val videoPickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            selectedVideoUri = it
            setupVideoView(it)
            buttonUpload.isEnabled = true
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_upload_video)

        // Get userId
        userId = intent.getIntExtra("USER_ID", 0)
        if (userId == 0) {
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", 0)
            if (userId == 0) {
                finish()
                return
            }
        }

        initializeViews()
        setupListeners()
    }

    private fun initializeViews() {
        toolbar = findViewById(R.id.toolbar)
        playerView = findViewById(R.id.playerView)
        editDescription = findViewById(R.id.editDescriptionText)
        buttonSelectVideo = findViewById(R.id.buttonSelectVideo)
        buttonUpload = findViewById(R.id.buttonUpload)
        progressBar = findViewById(R.id.progressBar)

        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Subir Video"

        buttonUpload.isEnabled = false

        // Initialize PlayerView
        playerView.visibility = View.VISIBLE
        playerView.setBackgroundColor(getColor(R.color.cyberpunk_background))

        // Initialize ExoPlayer
        exoPlayer = ExoPlayer.Builder(this).build()
        playerView.player = exoPlayer
    }

    private fun setupListeners() {
        toolbar.setNavigationOnClickListener {
            finish()
        }

        buttonSelectVideo.setOnClickListener {
            pickVideo()
        }

        buttonUpload.setOnClickListener {
            uploadVideo()
        }
    }

    private fun pickVideo() {
        try {
            // Use the modern file picker that doesn't require permissions
            videoPickerLauncher.launch("video/*")
        } catch (e: Exception) {
            Toast.makeText(this, "Error al abrir el selector de videos", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupVideoView(uri: Uri) {
        try {
            exoPlayer?.let { player ->
                // Create media item from URI
                val mediaItem = MediaItem.fromUri(uri)

                // Set media item and prepare
                player.setMediaItem(mediaItem)
                player.prepare()

                // Set repeat mode for preview
                player.repeatMode = Player.REPEAT_MODE_ONE

                // Start playing
                player.playWhenReady = true

                // Add listener for when video is ready
                player.addListener(object : Player.Listener {
                    override fun onPlaybackStateChanged(playbackState: Int) {
                        when (playbackState) {
                            Player.STATE_READY -> {
                                // Video is ready and can be played
                                Toast.makeText(this@UploadVideoActivity, "Video cargado correctamente", Toast.LENGTH_SHORT).show()

                                // Get video format for aspect ratio adjustment
                                val videoFormat = player.videoFormat
                                if (videoFormat != null) {
                                    val videoWidth = videoFormat.width
                                    val videoHeight = videoFormat.height

                                    if (videoWidth > 0 && videoHeight > 0) {
                                        adjustPlayerViewAspectRatio(videoWidth, videoHeight)
                                    }
                                }
                            }
                            Player.STATE_BUFFERING -> {
                                // Video is loading
                            }
                            Player.STATE_ENDED -> {
                                // Video ended (shouldn't happen with repeat mode)
                            }
                            Player.STATE_IDLE -> {
                                // Player is idle
                            }
                        }
                    }

                    override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                        Toast.makeText(this@UploadVideoActivity, "Error al cargar el video", Toast.LENGTH_SHORT).show()
                    }
                })
            }

        } catch (e: Exception) {
            Toast.makeText(this, "Error al configurar el video: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun adjustPlayerViewAspectRatio(videoWidth: Int, videoHeight: Int) {
        try {
            val aspectRatio = videoWidth.toFloat() / videoHeight.toFloat()
            val layoutParams = playerView.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams

            if (aspectRatio > 1) {
                // Landscape video - fit width, adjust height
                layoutParams.dimensionRatio = "H,$videoWidth:$videoHeight"
            } else {
                // Portrait video (like TikTok) - fit height, adjust width
                layoutParams.dimensionRatio = "W,$videoWidth:$videoHeight"
            }

            playerView.layoutParams = layoutParams
            playerView.requestLayout()

        } catch (e: Exception) {
            // If aspect ratio adjustment fails, keep default
        }
    }

    private fun uploadVideo() {
        val videoUri = selectedVideoUri ?: return
        val description = editDescription.text.toString()

        if (description.isBlank()) {
            Toast.makeText(this, "Por favor, añade una descripción", Toast.LENGTH_SHORT).show()
            return
        }

        // Show progress
        progressBar.visibility = View.VISIBLE
        buttonUpload.isEnabled = false

        // Create temp file from uri
        val inputStream = contentResolver.openInputStream(videoUri)
        val tempFile = File.createTempFile("video", ".mp4", cacheDir)
        val outputStream = FileOutputStream(tempFile)
        inputStream?.copyTo(outputStream)

        // Create multipart request
        val client = OkHttpClient()
        val requestBody = MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(
                "video",
                "video.mp4",
                tempFile.asRequestBody("video/mp4".toMediaTypeOrNull())
            )
            .addFormDataPart("description", description)
            .addFormDataPart("userId", userId.toString())
            .build()

        val request = Request.Builder()
            .url("$API_URL/videos/upload")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    buttonUpload.isEnabled = true
                    Toast.makeText(
                        this@UploadVideoActivity,
                        "Error al subir el video",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    if (response.isSuccessful) {
                        Toast.makeText(
                            this@UploadVideoActivity,
                            "Video subido correctamente",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    } else {
                        buttonUpload.isEnabled = true
                        Toast.makeText(
                            this@UploadVideoActivity,
                            "Error al subir el video",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    override fun onDestroy() {
        super.onDestroy()
        exoPlayer?.release()
        exoPlayer = null
    }

    override fun onPause() {
        super.onPause()
        exoPlayer?.pause()
    }

    override fun onResume() {
        super.onResume()
        if (selectedVideoUri != null) {
            exoPlayer?.play()
        }
    }
}
