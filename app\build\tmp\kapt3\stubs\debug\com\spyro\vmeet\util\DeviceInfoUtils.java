package com.spyro.vmeet.util;

/**
 * Utility class for obtaining device information
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J$\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0014\u0010\t\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0004\u0012\u00020\u00060\nJ\u000e\u0010\u000b\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/spyro/vmeet/util/DeviceInfoUtils;", "", "()V", "TAG", "", "getDeviceIp", "", "context", "Landroid/content/Context;", "callback", "Lkotlin/Function1;", "getDeviceSerial", "app_debug"})
public final class DeviceInfoUtils {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "DeviceInfoUtils";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.util.DeviceInfoUtils INSTANCE = null;
    
    private DeviceInfoUtils() {
        super();
    }
    
    /**
     * Gets a unique device identifier that persists across app reinstalls
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceSerial(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * Gets the device's public IP address
     */
    public final void getDeviceIp(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
}