<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:shareInterpolator="false"
    android:fillAfter="true">
    
    <!-- Animación de entrada con rebote -->
    <translate
        android:fromXDelta="-100%p" 
        android:toXDelta="0%p"
        android:interpolator="@android:anim/overshoot_interpolator"
        android:duration="800" />
    
    <!-- Fade in -->
    <alpha
        android:fromAlpha="0.0"
        android:toAlpha="1.0" 
        android:duration="400" />
        
    <!-- Leve oscilación horizontal continua después de entrar -->
    <translate
        android:startOffset="800"
        android:fromXDelta="-3%p"
        android:toXDelta="3%p"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:interpolator="@android:anim/accelerate_decelerate_interpolator"
        android:duration="1000" />
</set> 