1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.spyro.vmeet"
4    android:versionCode="29"
5    android:versionName="0.90" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:5-66
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:5-106
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:78-104
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:22-73
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:5-70
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:22-68
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:5-10:50
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:22-78
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:10:22-48
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:5-74
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:22-72
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:5-65
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:22-63
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:5-76
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:22-74
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:5-64
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:22-62
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:5-74
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:22-72
25
26    <!-- Location permissions for Radar feature - ONLY foreground access -->
27    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:5-78
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:22-76
28    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:5-80
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:22-78
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:5-79
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:22-76
30
31    <uses-feature
31-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
32        android:glEsVersion="0x00020000"
32-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
33        android:required="true" />
33-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
34
35    <queries>
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
36
37        <!-- Needs to be explicitly declared on Android R+ -->
38        <package android:name="com.google.android.apps.maps" />
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
39    </queries>
40
41    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:22-65
42    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
44    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
45    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
46    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
54-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:21:5-179:19
55        android:name="com.spyro.vmeet.VMeetApplication"
55-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:22:9-41
56        android:allowBackup="true"
56-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:23:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:dataExtractionRules="@xml/data_extraction_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:24:9-65
59        android:debuggable="true"
60        android:extractNativeLibs="false"
61        android:fullBackupContent="@xml/backup_rules"
61-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:25:9-54
62        android:icon="@mipmap/ic_launcher"
62-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:26:9-43
63        android:label="@string/app_name"
63-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:27:9-41
64        android:networkSecurityConfig="@xml/network_security_config"
64-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:31:9-69
65        android:roundIcon="@mipmap/ic_launcher_round"
65-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:28:9-54
66        android:supportsRtl="true"
66-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:29:9-35
67        android:theme="@style/Theme.VMeet" >
67-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:30:9-43
68        <activity
68-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:33:9-42:20
69            android:name="com.spyro.vmeet.MainActivity"
69-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:34:13-41
70            android:exported="true"
70-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:35:13-36
71            android:label="@string/app_name"
71-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:36:13-45
72            android:theme="@style/Theme.VMeet" >
72-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:37:13-47
73            <intent-filter>
73-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:38:13-41:29
74                <action android:name="android.intent.action.MAIN" />
74-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:17-69
74-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:25-66
75
76                <category android:name="android.intent.category.LAUNCHER" />
76-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:17-77
76-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:27-74
77            </intent-filter>
78        </activity>
79        <activity android:name="com.spyro.vmeet.LoginActivity" />
79-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:9-51
79-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:19-48
80        <activity android:name="com.spyro.vmeet.TutorialActivity" />
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:9-54
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:19-51
81        <activity
81-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:45:9-47:40
82            android:name="com.spyro.vmeet.ProfileActivity"
82-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:46:13-44
83            android:exported="false" />
83-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:47:13-37
84        <activity
84-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:48:9-50:40
85            android:name="com.spyro.vmeet.BlockedUsersActivity"
85-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:49:13-49
86            android:exported="false" />
86-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:50:13-37
87        <activity android:name="com.spyro.vmeet.SwipeActivity" />
87-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:9-51
87-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:19-48
88        <activity android:name="com.spyro.vmeet.MatchesActivity" />
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:9-53
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:19-50
89        <activity android:name="com.spyro.vmeet.CommunityHostActivity" />
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:9-59
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:19-56
90        <activity android:name="com.spyro.vmeet.ui.community.CommentsActivity" />
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:9-67
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:19-64
91        <activity android:name="com.spyro.vmeet.ui.community.StoryEditorActivity" />
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:55:9-56:64
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:56:13-61
92        <activity
92-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:57:9-60:76
93            android:name="com.spyro.vmeet.activity.BlindDateActivity"
93-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:58:13-55
94            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
94-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:59:13-83
95            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
95-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:60:13-73
96        <activity
96-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:61:9-64:76
97            android:name="com.spyro.vmeet.activity.ChatActivity"
97-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:62:13-50
98            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
98-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:63:13-83
99            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
99-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:64:13-73
100        <activity android:name="com.spyro.vmeet.activity.ChatListActivity" />
100-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:9-63
100-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:19-60
101        <activity android:name="com.spyro.vmeet.activity.SettingsActivity" />
101-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:9-63
101-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:19-60
102
103        <!-- Email verification and password reset activities -->
104        <activity android:name="com.spyro.vmeet.activity.EmailVerificationActivity" />
104-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:9-72
104-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:19-69
105        <activity android:name="com.spyro.vmeet.activity.PasswordResetActivity" />
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:9-68
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:19-65
106        <activity android:name="com.spyro.vmeet.activity.ForgotPasswordActivity" />
106-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:9-69
106-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:19-66
107
108        <!-- Chat Room Loader - handles safe transition to chat room -->
109        <activity
109-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:74:9-78:58
110            android:name="com.spyro.vmeet.activity.RoomLoaderActivity"
110-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:75:13-56
111            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
111-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:77:13-83
112            android:theme="@style/Theme.VMeet.NoActionBar"
112-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:76:13-59
113            android:windowSoftInputMode="adjustResize" />
113-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:78:13-55
114
115        <!-- Chat Room Activity for group chat functionality -->
116        <activity
116-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:81:9-87:50
117            android:name="com.spyro.vmeet.activity.ChatRoomActivity"
117-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:82:13-54
118            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
118-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:83:13-83
119            android:excludeFromRecents="false"
119-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:87:13-47
120            android:launchMode="singleTop"
120-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:85:13-43
121            android:taskAffinity=""
121-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:86:13-36
122            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
122-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:84:13-73
123
124        <!-- Video feature activities -->
125        <activity
125-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:90:9-94:61
126            android:name="com.spyro.vmeet.VideoFeedActivity"
126-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:91:13-46
127            android:configChanges="orientation|screenSize|keyboardHidden"
127-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:92:13-74
128            android:screenOrientation="portrait"
128-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:93:13-49
129            android:theme="@style/Theme.VMeet.NoActionBar" />
129-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:94:13-59
130        <activity
130-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:95:9-97:61
131            android:name="com.spyro.vmeet.UploadVideoActivity"
131-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:96:13-48
132            android:theme="@style/Theme.VMeet.NoActionBar" />
132-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:97:13-59
133        <activity
133-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:98:9-100:61
134            android:name="com.spyro.vmeet.MyVideosActivity"
134-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:99:13-45
135            android:theme="@style/Theme.VMeet.NoActionBar" />
135-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:100:13-59
136
137        <!-- Admin panel activities -->
138        <activity
138-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:103:9-105:55
139            android:name="com.spyro.vmeet.AdminPanelActivity"
139-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:104:13-47
140            android:label="Panel de Administración" />
140-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:105:13-52
141        <activity
141-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:106:9-108:51
142            android:name="com.spyro.vmeet.UserManagementActivity"
142-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:107:13-51
143            android:label="Gestión de Usuarios" />
143-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:108:13-48
144        <activity
144-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:109:9-111:52
145            android:name="com.spyro.vmeet.ReportsActivity"
145-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:110:13-44
146            android:label="Reportes de Usuarios" />
146-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:111:13-49
147
148        <!-- Full-screen image viewer activity with a special theme for immersive viewing -->
149        <activity
149-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:114:9-116:66
150            android:name="com.spyro.vmeet.FullScreenImageActivity"
150-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:115:13-52
151            android:theme="@style/Theme.AppCompat.NoActionBar" />
151-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:116:13-63
152
153        <!-- Story Viewer Activity -->
154        <activity
154-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:119:9-121:66
155            android:name="com.spyro.vmeet.ui.community.StoryViewerActivity"
155-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:120:13-61
156            android:theme="@style/Theme.AppCompat.NoActionBar" />
156-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:121:13-63
157
158        <!-- User Profile Activity -->
159        <activity
159-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:124:9-126:49
160            android:name="com.spyro.vmeet.activity.UserProfileActivity"
160-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:125:13-57
161            android:label="Perfil de Usuario" />
161-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:126:13-46
162
163        <!-- Edit Personality Traits Activity -->
164        <activity
164-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:129:9-131:61
165            android:name="com.spyro.vmeet.activity.EditPersonalityTraitsActivity"
165-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:130:13-67
166            android:label="Editar Rasgos de Personalidad" />
166-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:131:13-58
167
168        <!-- Community Guidelines Activity -->
169        <activity
169-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:134:9-136:54
170            android:name="com.spyro.vmeet.activity.CommunityGuidelinesActivity"
170-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:135:13-65
171            android:label="Normas de la comunidad" />
171-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:136:13-51
172
173        <!-- User Verification Activity -->
174        <activity
174-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:139:9-142:62
175            android:name="com.spyro.vmeet.VerificationActivity"
175-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:140:13-49
176            android:label="Verificación de Usuario"
176-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:141:13-52
177            android:theme="@style/Theme.VMeet.NoActionBar" />
177-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:142:13-59
178
179        <!-- Firebase Cloud Messaging Service -->
180        <service
180-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:145:9-151:19
181            android:name="com.spyro.vmeet.notifications.VMeetFirebaseMessagingService"
181-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:146:13-72
182            android:exported="false" >
182-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:147:13-37
183            <intent-filter>
183-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:148:13-150:29
184                <action android:name="com.google.firebase.MESSAGING_EVENT" />
184-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:17-78
184-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:25-75
185            </intent-filter>
186        </service>
187
188        <!-- BroadcastReceiver for notification actions -->
189        <receiver
189-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:9-161:20
190            android:name="com.spyro.vmeet.notifications.MessageActionReceiver"
190-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:155:13-64
191            android:exported="false" >
191-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:156:13-37
192            <intent-filter>
192-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:157:13-160:29
193                <action android:name="com.spyro.vmeet.ACTION_MARK_AS_READ" />
193-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:158:17-78
193-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:158:25-75
194                <action android:name="com.spyro.vmeet.ACTION_REPLY" />
194-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:17-71
194-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:25-68
195            </intent-filter>
196        </receiver>
197
198        <!-- Sample AdMob App ID. Replace with your real App ID before production. -->
199        <meta-data
199-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:9-166:69
200            android:name="com.google.android.gms.ads.APPLICATION_ID"
200-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:165:13-69
201            android:value="ca-app-pub-3940256099942544~**********" />
201-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:166:13-67
202
203        <!-- FileProvider for sharing images from camera -->
204        <provider
205            android:name="androidx.core.content.FileProvider"
205-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:170:13-62
206            android:authorities="com.spyro.vmeet.provider"
206-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:171:13-60
207            android:exported="false"
207-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:172:13-37
208            android:grantUriPermissions="true" >
208-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:173:13-47
209            <meta-data
209-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:174:13-176:54
210                android:name="android.support.FILE_PROVIDER_PATHS"
210-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:175:17-67
211                android:resource="@xml/file_paths" />
211-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:176:17-51
212        </provider>
213        <provider
213-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:24:9-32:20
214            android:name="androidx.startup.InitializationProvider"
214-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:25:13-67
215            android:authorities="com.spyro.vmeet.androidx-startup"
215-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:26:13-68
216            android:exported="false" >
216-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:27:13-37
217            <meta-data
217-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
218                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
218-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
219                android:value="androidx.startup" />
219-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
220            <meta-data
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
221                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
222                android:value="androidx.startup" />
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
223        </provider>
224
225        <activity
225-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
226            android:name="androidx.compose.ui.tooling.PreviewActivity"
226-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
227            android:exported="true" />
227-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
228        <activity
228-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:23:9-25:39
229            android:name="androidx.activity.ComponentActivity"
229-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:24:13-63
230            android:exported="true" /> <!-- Needs to be explicitly declared on P+ -->
230-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:25:13-36
231        <uses-library
231-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
232            android:name="org.apache.http.legacy"
232-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
233            android:required="false" />
233-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
234
235        <service
235-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:23:9-29:19
236            android:name="com.google.firebase.components.ComponentDiscoveryService"
236-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:24:13-84
237            android:directBootAware="true"
237-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
238            android:exported="false" >
238-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:25:13-37
239            <meta-data
239-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:26:13-28:85
240                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
240-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:27:17-129
241                android:value="com.google.firebase.components.ComponentRegistrar" />
241-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:28:17-82
242            <meta-data
242-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
243                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
243-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
244                android:value="com.google.firebase.components.ComponentRegistrar" />
244-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
245            <meta-data
245-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
246                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
246-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
247                android:value="com.google.firebase.components.ComponentRegistrar" />
247-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
248            <meta-data
248-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:11:13-13:85
249                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
249-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:12:17-129
250                android:value="com.google.firebase.components.ComponentRegistrar" />
250-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:13:17-82
251            <meta-data
251-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
252                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
252-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
253                android:value="com.google.firebase.components.ComponentRegistrar" />
253-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
254            <meta-data
254-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
255                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
255-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
256                android:value="com.google.firebase.components.ComponentRegistrar" />
256-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
257            <meta-data
257-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
258                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
258-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
259                android:value="com.google.firebase.components.ComponentRegistrar" />
259-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
260            <meta-data
260-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
261                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
261-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
262                android:value="com.google.firebase.components.ComponentRegistrar" />
262-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
263            <meta-data
263-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
264                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
264-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
265                android:value="com.google.firebase.components.ComponentRegistrar" />
265-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
266            <meta-data
266-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
267                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
267-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
268                android:value="com.google.firebase.components.ComponentRegistrar" />
268-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
269        </service>
270
271        <receiver
271-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
272            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
272-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
273            android:exported="true"
273-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
274            android:permission="com.google.android.c2dm.permission.SEND" >
274-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
275            <intent-filter>
275-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
276                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
276-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
276-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
277            </intent-filter>
278
279            <meta-data
279-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
280                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
280-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
281                android:value="true" />
281-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
282        </receiver>
283        <!--
284             FirebaseMessagingService performs security checks at runtime,
285             but set to not exported to explicitly avoid allowing another app to call it.
286        -->
287        <service
287-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
288            android:name="com.google.firebase.messaging.FirebaseMessagingService"
288-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
289            android:directBootAware="true"
289-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
290            android:exported="false" >
290-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
291            <intent-filter android:priority="-500" >
291-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:148:13-150:29
292                <action android:name="com.google.firebase.MESSAGING_EVENT" />
292-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:17-78
292-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:25-75
293            </intent-filter>
294        </service>
295
296        <activity
296-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
297            android:name="com.google.android.gms.common.api.GoogleApiActivity"
297-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
298            android:exported="false"
298-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
299            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
299-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
300
301        <property
301-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
302            android:name="android.adservices.AD_SERVICES_CONFIG"
302-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
303            android:resource="@xml/ga_ad_services_config" />
303-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
304
305        <provider
305-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
306            android:name="com.google.firebase.provider.FirebaseInitProvider"
306-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
307            android:authorities="com.spyro.vmeet.firebaseinitprovider"
307-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
308            android:directBootAware="true"
308-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
309            android:exported="false"
309-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
310            android:initOrder="100" />
310-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
311
312        <receiver
312-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
313            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
313-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
314            android:enabled="true"
314-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
315            android:exported="false" >
315-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
316        </receiver>
317
318        <service
318-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
319            android:name="com.google.android.gms.measurement.AppMeasurementService"
319-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
320            android:enabled="true"
320-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
321            android:exported="false" />
321-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
322        <service
322-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
323            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
323-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
324            android:enabled="true"
324-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
325            android:exported="false"
325-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
326            android:permission="android.permission.BIND_JOB_SERVICE" />
326-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
327
328        <uses-library
328-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
329            android:name="android.ext.adservices"
329-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
330            android:required="false" />
330-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
331
332        <meta-data
332-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
333            android:name="com.google.android.gms.version"
333-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
334            android:value="@integer/google_play_services_version" />
334-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
335
336        <receiver
336-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
337            android:name="androidx.profileinstaller.ProfileInstallReceiver"
337-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
338            android:directBootAware="false"
338-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
339            android:enabled="true"
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
340            android:exported="true"
340-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
341            android:permission="android.permission.DUMP" >
341-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
342            <intent-filter>
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
343                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
344            </intent-filter>
345            <intent-filter>
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
346                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
347            </intent-filter>
348            <intent-filter>
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
349                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
350            </intent-filter>
351            <intent-filter>
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
352                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
353            </intent-filter>
354        </receiver>
355
356        <service
356-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
357            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
357-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
358            android:exported="false" >
358-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
359            <meta-data
359-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
360                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
360-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
361                android:value="cct" />
361-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
362        </service>
363        <service
363-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
364            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
364-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
365            android:exported="false"
365-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
366            android:permission="android.permission.BIND_JOB_SERVICE" >
366-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
367        </service>
368
369        <receiver
369-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
370            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
370-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
371            android:exported="false" />
371-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
372    </application>
373
374</manifest>
