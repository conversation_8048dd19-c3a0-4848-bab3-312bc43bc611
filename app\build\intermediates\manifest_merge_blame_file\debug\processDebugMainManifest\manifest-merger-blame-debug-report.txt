1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.spyro.vmeet"
4    android:versionCode="29"
5    android:versionName="0.90" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:5-66
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:5-106
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:78-104
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:22-73
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:5-70
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:22-68
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:5-10:50
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:22-78
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:10:22-48
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:5-74
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:22-72
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:5-65
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:22-63
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:5-76
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:22-74
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:5-64
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:22-62
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:5-74
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:22-72
25
26    <!-- Location permissions for Radar feature - ONLY foreground access -->
27    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:5-78
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:22-76
28    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:5-80
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:22-78
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:5-79
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:22-76
30
31    <uses-feature
31-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
32        android:glEsVersion="0x00020000"
32-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
33        android:required="true" />
33-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
34
35    <queries>
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
36
37        <!-- Needs to be explicitly declared on Android R+ -->
38        <package android:name="com.google.android.apps.maps" />
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
39    </queries>
40
41    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:22-65
42    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
44    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
45    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
46    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
54-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:21:5-179:19
55        android:name="com.spyro.vmeet.VMeetApplication"
55-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:22:9-41
56        android:allowBackup="true"
56-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:23:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:dataExtractionRules="@xml/data_extraction_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:24:9-65
59        android:debuggable="true"
60        android:extractNativeLibs="false"
61        android:fullBackupContent="@xml/backup_rules"
61-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:25:9-54
62        android:icon="@mipmap/ic_launcher"
62-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:26:9-43
63        android:label="@string/app_name"
63-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:27:9-41
64        android:networkSecurityConfig="@xml/network_security_config"
64-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:31:9-69
65        android:roundIcon="@mipmap/ic_launcher_round"
65-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:28:9-54
66        android:supportsRtl="true"
66-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:29:9-35
67        android:testOnly="true"
68        android:theme="@style/Theme.VMeet" >
68-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:30:9-43
69        <activity
69-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:33:9-42:20
70            android:name="com.spyro.vmeet.MainActivity"
70-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:34:13-41
71            android:exported="true"
71-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:35:13-36
72            android:label="@string/app_name"
72-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:36:13-45
73            android:theme="@style/Theme.VMeet" >
73-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:37:13-47
74            <intent-filter>
74-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:38:13-41:29
75                <action android:name="android.intent.action.MAIN" />
75-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:17-69
75-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:17-77
77-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:27-74
78            </intent-filter>
79        </activity>
80        <activity android:name="com.spyro.vmeet.LoginActivity" />
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:9-51
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:19-48
81        <activity android:name="com.spyro.vmeet.TutorialActivity" />
81-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:9-54
81-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:19-51
82        <activity
82-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:45:9-47:40
83            android:name="com.spyro.vmeet.ProfileActivity"
83-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:46:13-44
84            android:exported="false" />
84-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:47:13-37
85        <activity
85-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:48:9-50:40
86            android:name="com.spyro.vmeet.BlockedUsersActivity"
86-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:49:13-49
87            android:exported="false" />
87-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:50:13-37
88        <activity android:name="com.spyro.vmeet.SwipeActivity" />
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:9-51
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:19-48
89        <activity android:name="com.spyro.vmeet.MatchesActivity" />
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:9-53
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:19-50
90        <activity android:name="com.spyro.vmeet.CommunityHostActivity" />
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:9-59
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:19-56
91        <activity android:name="com.spyro.vmeet.ui.community.CommentsActivity" />
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:9-67
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:19-64
92        <activity android:name="com.spyro.vmeet.ui.community.StoryEditorActivity" />
92-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:55:9-56:64
92-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:56:13-61
93        <activity
93-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:57:9-60:76
94            android:name="com.spyro.vmeet.activity.BlindDateActivity"
94-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:58:13-55
95            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
95-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:59:13-83
96            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
96-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:60:13-73
97        <activity
97-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:61:9-64:76
98            android:name="com.spyro.vmeet.activity.ChatActivity"
98-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:62:13-50
99            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
99-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:63:13-83
100            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
100-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:64:13-73
101        <activity android:name="com.spyro.vmeet.activity.ChatListActivity" />
101-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:9-63
101-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:19-60
102        <activity android:name="com.spyro.vmeet.activity.SettingsActivity" />
102-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:9-63
102-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:19-60
103
104        <!-- Email verification and password reset activities -->
105        <activity android:name="com.spyro.vmeet.activity.EmailVerificationActivity" />
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:9-72
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:19-69
106        <activity android:name="com.spyro.vmeet.activity.PasswordResetActivity" />
106-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:9-68
106-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:19-65
107        <activity android:name="com.spyro.vmeet.activity.ForgotPasswordActivity" />
107-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:9-69
107-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:19-66
108
109        <!-- Chat Room Loader - handles safe transition to chat room -->
110        <activity
110-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:74:9-78:58
111            android:name="com.spyro.vmeet.activity.RoomLoaderActivity"
111-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:75:13-56
112            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
112-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:77:13-83
113            android:theme="@style/Theme.VMeet.NoActionBar"
113-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:76:13-59
114            android:windowSoftInputMode="adjustResize" />
114-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:78:13-55
115
116        <!-- Chat Room Activity for group chat functionality -->
117        <activity
117-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:81:9-87:50
118            android:name="com.spyro.vmeet.activity.ChatRoomActivity"
118-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:82:13-54
119            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
119-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:83:13-83
120            android:excludeFromRecents="false"
120-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:87:13-47
121            android:launchMode="singleTop"
121-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:85:13-43
122            android:taskAffinity=""
122-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:86:13-36
123            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
123-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:84:13-73
124
125        <!-- Video feature activities -->
126        <activity
126-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:90:9-94:61
127            android:name="com.spyro.vmeet.VideoFeedActivity"
127-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:91:13-46
128            android:configChanges="orientation|screenSize|keyboardHidden"
128-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:92:13-74
129            android:screenOrientation="portrait"
129-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:93:13-49
130            android:theme="@style/Theme.VMeet.NoActionBar" />
130-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:94:13-59
131        <activity
131-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:95:9-97:61
132            android:name="com.spyro.vmeet.UploadVideoActivity"
132-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:96:13-48
133            android:theme="@style/Theme.VMeet.NoActionBar" />
133-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:97:13-59
134        <activity
134-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:98:9-100:61
135            android:name="com.spyro.vmeet.MyVideosActivity"
135-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:99:13-45
136            android:theme="@style/Theme.VMeet.NoActionBar" />
136-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:100:13-59
137
138        <!-- Admin panel activities -->
139        <activity
139-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:103:9-105:55
140            android:name="com.spyro.vmeet.AdminPanelActivity"
140-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:104:13-47
141            android:label="Panel de Administración" />
141-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:105:13-52
142        <activity
142-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:106:9-108:51
143            android:name="com.spyro.vmeet.UserManagementActivity"
143-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:107:13-51
144            android:label="Gestión de Usuarios" />
144-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:108:13-48
145        <activity
145-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:109:9-111:52
146            android:name="com.spyro.vmeet.ReportsActivity"
146-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:110:13-44
147            android:label="Reportes de Usuarios" />
147-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:111:13-49
148
149        <!-- Full-screen image viewer activity with a special theme for immersive viewing -->
150        <activity
150-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:114:9-116:66
151            android:name="com.spyro.vmeet.FullScreenImageActivity"
151-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:115:13-52
152            android:theme="@style/Theme.AppCompat.NoActionBar" />
152-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:116:13-63
153
154        <!-- Story Viewer Activity -->
155        <activity
155-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:119:9-121:66
156            android:name="com.spyro.vmeet.ui.community.StoryViewerActivity"
156-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:120:13-61
157            android:theme="@style/Theme.AppCompat.NoActionBar" />
157-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:121:13-63
158
159        <!-- User Profile Activity -->
160        <activity
160-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:124:9-126:49
161            android:name="com.spyro.vmeet.activity.UserProfileActivity"
161-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:125:13-57
162            android:label="Perfil de Usuario" />
162-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:126:13-46
163
164        <!-- Edit Personality Traits Activity -->
165        <activity
165-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:129:9-131:61
166            android:name="com.spyro.vmeet.activity.EditPersonalityTraitsActivity"
166-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:130:13-67
167            android:label="Editar Rasgos de Personalidad" />
167-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:131:13-58
168
169        <!-- Community Guidelines Activity -->
170        <activity
170-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:134:9-136:54
171            android:name="com.spyro.vmeet.activity.CommunityGuidelinesActivity"
171-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:135:13-65
172            android:label="Normas de la comunidad" />
172-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:136:13-51
173
174        <!-- User Verification Activity -->
175        <activity
175-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:139:9-142:62
176            android:name="com.spyro.vmeet.VerificationActivity"
176-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:140:13-49
177            android:label="Verificación de Usuario"
177-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:141:13-52
178            android:theme="@style/Theme.VMeet.NoActionBar" />
178-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:142:13-59
179
180        <!-- Firebase Cloud Messaging Service -->
181        <service
181-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:145:9-151:19
182            android:name="com.spyro.vmeet.notifications.VMeetFirebaseMessagingService"
182-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:146:13-72
183            android:exported="false" >
183-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:147:13-37
184            <intent-filter>
184-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:148:13-150:29
185                <action android:name="com.google.firebase.MESSAGING_EVENT" />
185-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:17-78
185-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:25-75
186            </intent-filter>
187        </service>
188
189        <!-- BroadcastReceiver for notification actions -->
190        <receiver
190-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:9-161:20
191            android:name="com.spyro.vmeet.notifications.MessageActionReceiver"
191-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:155:13-64
192            android:exported="false" >
192-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:156:13-37
193            <intent-filter>
193-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:157:13-160:29
194                <action android:name="com.spyro.vmeet.ACTION_MARK_AS_READ" />
194-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:158:17-78
194-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:158:25-75
195                <action android:name="com.spyro.vmeet.ACTION_REPLY" />
195-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:17-71
195-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:25-68
196            </intent-filter>
197        </receiver>
198
199        <!-- Sample AdMob App ID. Replace with your real App ID before production. -->
200        <meta-data
200-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:9-166:69
201            android:name="com.google.android.gms.ads.APPLICATION_ID"
201-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:165:13-69
202            android:value="ca-app-pub-3940256099942544~**********" />
202-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:166:13-67
203
204        <!-- FileProvider for sharing images from camera -->
205        <provider
206            android:name="androidx.core.content.FileProvider"
206-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:170:13-62
207            android:authorities="com.spyro.vmeet.provider"
207-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:171:13-60
208            android:exported="false"
208-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:172:13-37
209            android:grantUriPermissions="true" >
209-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:173:13-47
210            <meta-data
210-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:174:13-176:54
211                android:name="android.support.FILE_PROVIDER_PATHS"
211-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:175:17-67
212                android:resource="@xml/file_paths" />
212-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:176:17-51
213        </provider>
214        <provider
214-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:24:9-32:20
215            android:name="androidx.startup.InitializationProvider"
215-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:25:13-67
216            android:authorities="com.spyro.vmeet.androidx-startup"
216-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:26:13-68
217            android:exported="false" >
217-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:27:13-37
218            <meta-data
218-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
219-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
220                android:value="androidx.startup" />
220-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
221            <meta-data
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
222                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
223                android:value="androidx.startup" />
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
224        </provider>
225
226        <activity
226-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
227            android:name="androidx.compose.ui.tooling.PreviewActivity"
227-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
228            android:exported="true" />
228-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
229        <activity
229-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:23:9-25:39
230            android:name="androidx.activity.ComponentActivity"
230-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:24:13-63
231            android:exported="true" /> <!-- Needs to be explicitly declared on P+ -->
231-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:25:13-36
232        <uses-library
232-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
233            android:name="org.apache.http.legacy"
233-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
234            android:required="false" />
234-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
235
236        <service
236-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:23:9-29:19
237            android:name="com.google.firebase.components.ComponentDiscoveryService"
237-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:24:13-84
238            android:directBootAware="true"
238-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
239            android:exported="false" >
239-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:25:13-37
240            <meta-data
240-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:26:13-28:85
241                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
241-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:27:17-129
242                android:value="com.google.firebase.components.ComponentRegistrar" />
242-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:28:17-82
243            <meta-data
243-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
244                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
244-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
245                android:value="com.google.firebase.components.ComponentRegistrar" />
245-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
246            <meta-data
246-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
247                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
247-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
248                android:value="com.google.firebase.components.ComponentRegistrar" />
248-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
249            <meta-data
249-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:11:13-13:85
250                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
250-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:12:17-129
251                android:value="com.google.firebase.components.ComponentRegistrar" />
251-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:13:17-82
252            <meta-data
252-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
253                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
253-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
254                android:value="com.google.firebase.components.ComponentRegistrar" />
254-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
255            <meta-data
255-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
256                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
256-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
258            <meta-data
258-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
259                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
259-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
261            <meta-data
261-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
262                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
262-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
264            <meta-data
264-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
265                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
265-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
267            <meta-data
267-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
268                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
268-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
270        </service>
271
272        <receiver
272-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
273            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
273-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
274            android:exported="true"
274-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
275            android:permission="com.google.android.c2dm.permission.SEND" >
275-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
276            <intent-filter>
276-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
277                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
277-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
277-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
278            </intent-filter>
279
280            <meta-data
280-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
281                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
281-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
282                android:value="true" />
282-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
283        </receiver>
284        <!--
285             FirebaseMessagingService performs security checks at runtime,
286             but set to not exported to explicitly avoid allowing another app to call it.
287        -->
288        <service
288-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
289            android:name="com.google.firebase.messaging.FirebaseMessagingService"
289-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
290            android:directBootAware="true"
290-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
291            android:exported="false" >
291-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
292            <intent-filter android:priority="-500" >
292-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:148:13-150:29
293                <action android:name="com.google.firebase.MESSAGING_EVENT" />
293-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:17-78
293-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:149:25-75
294            </intent-filter>
295        </service>
296
297        <activity
297-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
298            android:name="com.google.android.gms.common.api.GoogleApiActivity"
298-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
299            android:exported="false"
299-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
300            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
300-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
301
302        <property
302-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
303            android:name="android.adservices.AD_SERVICES_CONFIG"
303-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
304            android:resource="@xml/ga_ad_services_config" />
304-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
305
306        <provider
306-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
307            android:name="com.google.firebase.provider.FirebaseInitProvider"
307-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
308            android:authorities="com.spyro.vmeet.firebaseinitprovider"
308-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
309            android:directBootAware="true"
309-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
310            android:exported="false"
310-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
311            android:initOrder="100" />
311-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
312
313        <receiver
313-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
314            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
314-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
315            android:enabled="true"
315-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
316            android:exported="false" >
316-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
317        </receiver>
318
319        <service
319-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
320            android:name="com.google.android.gms.measurement.AppMeasurementService"
320-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
321            android:enabled="true"
321-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
322            android:exported="false" />
322-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
323        <service
323-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
324            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
324-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
325            android:enabled="true"
325-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
326            android:exported="false"
326-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
327            android:permission="android.permission.BIND_JOB_SERVICE" />
327-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
328
329        <uses-library
329-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
330            android:name="android.ext.adservices"
330-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
331            android:required="false" />
331-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
332
333        <meta-data
333-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
334            android:name="com.google.android.gms.version"
334-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
335            android:value="@integer/google_play_services_version" />
335-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
336
337        <receiver
337-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
338            android:name="androidx.profileinstaller.ProfileInstallReceiver"
338-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
339            android:directBootAware="false"
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
340            android:enabled="true"
340-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
341            android:exported="true"
341-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
342            android:permission="android.permission.DUMP" >
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
343            <intent-filter>
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
344                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
345            </intent-filter>
346            <intent-filter>
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
347                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
348            </intent-filter>
349            <intent-filter>
349-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
350                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
351            </intent-filter>
352            <intent-filter>
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
353                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
354            </intent-filter>
355        </receiver>
356
357        <service
357-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
358            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
358-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
359            android:exported="false" >
359-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
360            <meta-data
360-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
361                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
361-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
362                android:value="cct" />
362-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
363        </service>
364        <service
364-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
365            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
365-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
366            android:exported="false"
366-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
367            android:permission="android.permission.BIND_JOB_SERVICE" >
367-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
368        </service>
369
370        <receiver
370-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
371            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
371-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
372            android:exported="false" />
372-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
373    </application>
374
375</manifest>
