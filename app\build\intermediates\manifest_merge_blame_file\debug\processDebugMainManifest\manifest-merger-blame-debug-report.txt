1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.spyro.vmeet"
4    android:versionCode="33"
5    android:versionName="0.94" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:5-66
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:5-106
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:78-104
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:22-73
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:5-70
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:22-68
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:5-10:50
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:22-78
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:10:22-48
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:5-74
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:22-72
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:5-65
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:22-63
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:5-76
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:22-74
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:5-64
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:22-62
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:5-74
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:22-72
25
26    <!-- Location permissions for Radar feature - ONLY foreground access -->
27    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:5-78
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:22-76
28    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:5-80
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:22-78
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:5-79
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:22-76
30
31    <uses-feature
31-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
32        android:glEsVersion="0x00020000"
32-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
33        android:required="true" />
33-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
34
35    <queries>
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
36
37        <!-- Needs to be explicitly declared on Android R+ -->
38        <package android:name="com.google.android.apps.maps" />
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
39    </queries>
40
41    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:22-65
42    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
44    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
45    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
46    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
54-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:21:5-184:19
55        android:name="com.spyro.vmeet.VMeetApplication"
55-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:22:9-41
56        android:allowBackup="true"
56-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:23:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:dataExtractionRules="@xml/data_extraction_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:24:9-65
59        android:debuggable="true"
60        android:extractNativeLibs="false"
61        android:fullBackupContent="@xml/backup_rules"
61-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:25:9-54
62        android:icon="@mipmap/ic_launcher"
62-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:26:9-43
63        android:label="@string/app_name"
63-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:27:9-41
64        android:networkSecurityConfig="@xml/network_security_config"
64-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:31:9-69
65        android:roundIcon="@mipmap/ic_launcher_round"
65-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:28:9-54
66        android:supportsRtl="true"
66-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:29:9-35
67        android:testOnly="true"
68        android:theme="@style/Theme.VMeet" >
68-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:30:9-43
69        <activity
69-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:33:9-42:20
70            android:name="com.spyro.vmeet.MainActivity"
70-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:34:13-41
71            android:exported="true"
71-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:35:13-36
72            android:label="@string/app_name"
72-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:36:13-45
73            android:theme="@style/Theme.VMeet" >
73-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:37:13-47
74            <intent-filter>
74-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:38:13-41:29
75                <action android:name="android.intent.action.MAIN" />
75-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:17-69
75-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:25-66
76
77                <category android:name="android.intent.category.LAUNCHER" />
77-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:17-77
77-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:27-74
78            </intent-filter>
79        </activity>
80        <activity android:name="com.spyro.vmeet.LoginActivity" />
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:9-51
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:19-48
81        <activity android:name="com.spyro.vmeet.TutorialActivity" />
81-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:9-54
81-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:19-51
82        <activity
82-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:45:9-47:40
83            android:name="com.spyro.vmeet.ProfileActivity"
83-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:46:13-44
84            android:exported="false" />
84-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:47:13-37
85        <activity
85-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:48:9-50:40
86            android:name="com.spyro.vmeet.BlockedUsersActivity"
86-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:49:13-49
87            android:exported="false" />
87-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:50:13-37
88        <activity android:name="com.spyro.vmeet.SwipeActivity" />
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:9-51
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:19-48
89        <activity android:name="com.spyro.vmeet.MatchesActivity" />
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:9-53
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:19-50
90        <activity android:name="com.spyro.vmeet.CommunityHostActivity" />
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:9-59
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:19-56
91        <activity android:name="com.spyro.vmeet.ui.community.CommentsActivity" />
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:9-67
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:19-64
92        <activity android:name="com.spyro.vmeet.ui.community.StoryEditorActivity" />
92-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:55:9-56:64
92-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:56:13-61
93        <activity
93-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:57:9-60:76
94            android:name="com.spyro.vmeet.activity.BlindDateActivity"
94-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:58:13-55
95            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
95-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:59:13-83
96            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
96-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:60:13-73
97        <activity
97-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:61:9-64:76
98            android:name="com.spyro.vmeet.activity.ChatActivity"
98-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:62:13-50
99            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
99-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:63:13-83
100            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
100-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:64:13-73
101        <activity android:name="com.spyro.vmeet.activity.ChatListActivity" />
101-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:9-63
101-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:19-60
102        <activity android:name="com.spyro.vmeet.activity.SettingsActivity" />
102-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:9-63
102-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:19-60
103
104        <!-- Email verification and password reset activities -->
105        <activity android:name="com.spyro.vmeet.activity.EmailVerificationActivity" />
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:9-72
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:19-69
106        <activity android:name="com.spyro.vmeet.activity.PasswordResetActivity" />
106-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:9-68
106-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:19-65
107        <activity android:name="com.spyro.vmeet.activity.ForgotPasswordActivity" />
107-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:9-69
107-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:19-66
108
109        <!-- Chat Room Loader - handles safe transition to chat room -->
110        <activity
110-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:74:9-78:58
111            android:name="com.spyro.vmeet.activity.RoomLoaderActivity"
111-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:75:13-56
112            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
112-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:77:13-83
113            android:theme="@style/Theme.VMeet.NoActionBar"
113-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:76:13-59
114            android:windowSoftInputMode="adjustResize" />
114-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:78:13-55
115
116        <!-- Chat Room Activity for group chat functionality -->
117        <activity
117-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:81:9-87:50
118            android:name="com.spyro.vmeet.activity.ChatRoomActivity"
118-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:82:13-54
119            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
119-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:83:13-83
120            android:excludeFromRecents="false"
120-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:87:13-47
121            android:launchMode="singleTop"
121-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:85:13-43
122            android:taskAffinity=""
122-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:86:13-36
123            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
123-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:84:13-73
124
125        <!-- Video feature activities -->
126        <activity
126-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:90:9-94:61
127            android:name="com.spyro.vmeet.VideoFeedActivity"
127-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:91:13-46
128            android:configChanges="orientation|screenSize|keyboardHidden"
128-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:92:13-74
129            android:screenOrientation="portrait"
129-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:93:13-49
130            android:theme="@style/Theme.VMeet.NoActionBar" />
130-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:94:13-59
131        <activity
131-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:95:9-97:61
132            android:name="com.spyro.vmeet.UploadVideoActivity"
132-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:96:13-48
133            android:theme="@style/Theme.VMeet.NoActionBar" />
133-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:97:13-59
134        <activity
134-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:98:9-100:61
135            android:name="com.spyro.vmeet.MyVideosActivity"
135-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:99:13-45
136            android:theme="@style/Theme.VMeet.NoActionBar" />
136-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:100:13-59
137
138        <!-- Admin panel activities -->
139        <activity
139-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:103:9-105:55
140            android:name="com.spyro.vmeet.AdminPanelActivity"
140-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:104:13-47
141            android:label="Panel de Administración" />
141-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:105:13-52
142        <activity
142-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:106:9-108:51
143            android:name="com.spyro.vmeet.UserManagementActivity"
143-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:107:13-51
144            android:label="Gestión de Usuarios" />
144-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:108:13-48
145        <activity
145-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:109:9-111:52
146            android:name="com.spyro.vmeet.ReportsActivity"
146-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:110:13-44
147            android:label="Reportes de Usuarios" />
147-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:111:13-49
148
149        <!-- Full-screen image viewer activity with a special theme for immersive viewing -->
150        <activity
150-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:114:9-116:66
151            android:name="com.spyro.vmeet.FullScreenImageActivity"
151-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:115:13-52
152            android:theme="@style/Theme.AppCompat.NoActionBar" />
152-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:116:13-63
153
154        <!-- Story Viewer Activity -->
155        <activity
155-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:119:9-121:66
156            android:name="com.spyro.vmeet.ui.community.StoryViewerActivity"
156-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:120:13-61
157            android:theme="@style/Theme.AppCompat.NoActionBar" />
157-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:121:13-63
158
159        <!-- User Profile Activity -->
160        <activity
160-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:124:9-126:49
161            android:name="com.spyro.vmeet.activity.UserProfileActivity"
161-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:125:13-57
162            android:label="Perfil de Usuario" />
162-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:126:13-46
163
164        <!-- Edit Personality Traits Activity -->
165        <activity
165-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:129:9-131:61
166            android:name="com.spyro.vmeet.activity.EditPersonalityTraitsActivity"
166-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:130:13-67
167            android:label="Editar Rasgos de Personalidad" />
167-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:131:13-58
168
169        <!-- Community Guidelines Activity -->
170        <activity
170-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:134:9-136:54
171            android:name="com.spyro.vmeet.activity.CommunityGuidelinesActivity"
171-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:135:13-65
172            android:label="Normas de la comunidad" />
172-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:136:13-51
173
174        <!-- User Verification Activity -->
175        <activity
175-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:139:9-142:62
176            android:name="com.spyro.vmeet.VerificationActivity"
176-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:140:13-49
177            android:label="Verificación de Usuario"
177-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:141:13-52
178            android:theme="@style/Theme.VMeet.NoActionBar" />
178-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:142:13-59
179        <activity
179-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:144:9-147:62
180            android:name="com.spyro.vmeet.VerificationManagementActivity"
180-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:145:13-59
181            android:label="Gestión de Verificaciones"
181-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:146:13-54
182            android:theme="@style/Theme.VMeet.NoActionBar" />
182-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:147:13-59
183
184        <!-- Firebase Cloud Messaging Service -->
185        <service
185-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:150:9-156:19
186            android:name="com.spyro.vmeet.notifications.VMeetFirebaseMessagingService"
186-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:151:13-72
187            android:exported="false" >
187-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:152:13-37
188            <intent-filter>
188-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:153:13-155:29
189                <action android:name="com.google.firebase.MESSAGING_EVENT" />
189-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:17-78
189-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:25-75
190            </intent-filter>
191        </service>
192
193        <!-- BroadcastReceiver for notification actions -->
194        <receiver
194-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:9-166:20
195            android:name="com.spyro.vmeet.notifications.MessageActionReceiver"
195-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:160:13-64
196            android:exported="false" >
196-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:161:13-37
197            <intent-filter>
197-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:162:13-165:29
198                <action android:name="com.spyro.vmeet.ACTION_MARK_AS_READ" />
198-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:163:17-78
198-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:163:25-75
199                <action android:name="com.spyro.vmeet.ACTION_REPLY" />
199-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:17-71
199-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:25-68
200            </intent-filter>
201        </receiver>
202
203        <!-- Sample AdMob App ID. Replace with your real App ID before production. -->
204        <meta-data
204-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:169:9-171:69
205            android:name="com.google.android.gms.ads.APPLICATION_ID"
205-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:170:13-69
206            android:value="ca-app-pub-3940256099942544~**********" />
206-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:171:13-67
207
208        <!-- FileProvider for sharing images from camera -->
209        <provider
210            android:name="androidx.core.content.FileProvider"
210-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:175:13-62
211            android:authorities="com.spyro.vmeet.provider"
211-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:176:13-60
212            android:exported="false"
212-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:177:13-37
213            android:grantUriPermissions="true" >
213-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:178:13-47
214            <meta-data
214-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:179:13-181:54
215                android:name="android.support.FILE_PROVIDER_PATHS"
215-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:180:17-67
216                android:resource="@xml/file_paths" />
216-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:181:17-51
217        </provider>
218        <provider
218-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:24:9-32:20
219            android:name="androidx.startup.InitializationProvider"
219-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:25:13-67
220            android:authorities="com.spyro.vmeet.androidx-startup"
220-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:26:13-68
221            android:exported="false" >
221-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:27:13-37
222            <meta-data
222-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
223                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
223-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
224                android:value="androidx.startup" />
224-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
225            <meta-data
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
226                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
227                android:value="androidx.startup" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
228        </provider>
229
230        <activity
230-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
231            android:name="androidx.compose.ui.tooling.PreviewActivity"
231-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
232            android:exported="true" />
232-->[androidx.compose.ui:ui-tooling-android:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e603384cf1ab4cd28a6d559f328112b1\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
233        <activity
233-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:23:9-25:39
234            android:name="androidx.activity.ComponentActivity"
234-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:24:13-63
235            android:exported="true" /> <!-- Needs to be explicitly declared on P+ -->
235-->[androidx.compose.ui:ui-test-manifest:1.6.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61ff2c476d665ac4c992546e0dac558\transformed\ui-test-manifest-1.6.8\AndroidManifest.xml:25:13-36
236        <uses-library
236-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
237            android:name="org.apache.http.legacy"
237-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
238            android:required="false" />
238-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
239
240        <service
240-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:23:9-29:19
241            android:name="com.google.firebase.components.ComponentDiscoveryService"
241-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:24:13-84
242            android:directBootAware="true"
242-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
243            android:exported="false" >
243-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:25:13-37
244            <meta-data
244-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:26:13-28:85
245                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
245-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:27:17-129
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:28:17-82
247            <meta-data
247-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
248                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
248-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
250            <meta-data
250-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
251                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
251-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
253            <meta-data
253-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:11:13-13:85
254                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
254-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:12:17-129
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:13:17-82
256            <meta-data
256-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
257                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
257-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
259            <meta-data
259-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
260                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
260-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
262            <meta-data
262-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
263                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
263-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
265            <meta-data
265-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
266                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
266-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
267                android:value="com.google.firebase.components.ComponentRegistrar" />
267-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
268            <meta-data
268-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
269                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
269-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
270                android:value="com.google.firebase.components.ComponentRegistrar" />
270-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
271            <meta-data
271-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
272                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
272-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
273                android:value="com.google.firebase.components.ComponentRegistrar" />
273-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
274        </service>
275
276        <receiver
276-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
277            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
277-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
278            android:exported="true"
278-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
279            android:permission="com.google.android.c2dm.permission.SEND" >
279-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
280            <intent-filter>
280-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
281                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
281-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
281-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
282            </intent-filter>
283
284            <meta-data
284-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
285                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
285-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
286                android:value="true" />
286-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
287        </receiver>
288        <!--
289             FirebaseMessagingService performs security checks at runtime,
290             but set to not exported to explicitly avoid allowing another app to call it.
291        -->
292        <service
292-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
293            android:name="com.google.firebase.messaging.FirebaseMessagingService"
293-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
294            android:directBootAware="true"
294-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
295            android:exported="false" >
295-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
296            <intent-filter android:priority="-500" >
296-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:153:13-155:29
297                <action android:name="com.google.firebase.MESSAGING_EVENT" />
297-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:17-78
297-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:25-75
298            </intent-filter>
299        </service>
300
301        <activity
301-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
302            android:name="com.google.android.gms.common.api.GoogleApiActivity"
302-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
303            android:exported="false"
303-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
304            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
304-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
305
306        <property
306-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
307            android:name="android.adservices.AD_SERVICES_CONFIG"
307-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
308            android:resource="@xml/ga_ad_services_config" />
308-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
309
310        <provider
310-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
311            android:name="com.google.firebase.provider.FirebaseInitProvider"
311-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
312            android:authorities="com.spyro.vmeet.firebaseinitprovider"
312-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
313            android:directBootAware="true"
313-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
314            android:exported="false"
314-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
315            android:initOrder="100" />
315-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
316
317        <receiver
317-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
318            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
318-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
319            android:enabled="true"
319-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
320            android:exported="false" >
320-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
321        </receiver>
322
323        <service
323-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
324            android:name="com.google.android.gms.measurement.AppMeasurementService"
324-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
325            android:enabled="true"
325-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
326            android:exported="false" />
326-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
327        <service
327-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
328            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
328-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
329            android:enabled="true"
329-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
330            android:exported="false"
330-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
331            android:permission="android.permission.BIND_JOB_SERVICE" />
331-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
332
333        <uses-library
333-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
334            android:name="android.ext.adservices"
334-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
335            android:required="false" />
335-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
336
337        <meta-data
337-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
338            android:name="com.google.android.gms.version"
338-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
339            android:value="@integer/google_play_services_version" />
339-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
340
341        <receiver
341-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
342            android:name="androidx.profileinstaller.ProfileInstallReceiver"
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
343            android:directBootAware="false"
343-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
344            android:enabled="true"
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
345            android:exported="true"
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
346            android:permission="android.permission.DUMP" >
346-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
347            <intent-filter>
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
348                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
349            </intent-filter>
350            <intent-filter>
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
351                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
352            </intent-filter>
353            <intent-filter>
353-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
354                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
354-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
355            </intent-filter>
356            <intent-filter>
356-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
357                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
357-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
358            </intent-filter>
359        </receiver>
360
361        <service
361-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
362            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
362-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
363            android:exported="false" >
363-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
364            <meta-data
364-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
365                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
365-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
366                android:value="cct" />
366-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
367        </service>
368        <service
368-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
369            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
369-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
370            android:exported="false"
370-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
371            android:permission="android.permission.BIND_JOB_SERVICE" >
371-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
372        </service>
373
374        <receiver
374-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
375            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
375-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
376            android:exported="false" />
376-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
377    </application>
378
379</manifest>
