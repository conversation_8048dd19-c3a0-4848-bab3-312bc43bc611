<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Title with background -->
        <TextView
            android:id="@+id/textViewCreateRoomTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/neon_blue"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:text="Crear nueva sala"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Room Name Section -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutName"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:hint="Nombre de la sala"
            app:boxCornerRadiusBottomEnd="8dp"
            app:boxCornerRadiusBottomStart="8dp"
            app:boxCornerRadiusTopEnd="8dp"
            app:boxCornerRadiusTopStart="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textViewCreateRoomTitle"
            app:startIconDrawable="@android:drawable/ic_dialog_info">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextRoomName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLength="50" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Description Section -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutDescription"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Descripción"
            app:boxCornerRadiusBottomEnd="8dp"
            app:boxCornerRadiusBottomStart="8dp"
            app:boxCornerRadiusTopEnd="8dp"
            app:boxCornerRadiusTopStart="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textInputLayoutName"
            app:startIconDrawable="@android:drawable/ic_menu_edit">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextRoomDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top|start"
                android:inputType="textMultiLine"
                android:maxLength="200"
                android:maxLines="4"
                android:minLines="2" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Icon URL Section -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutIconUrl"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="URL del icono (opcional)"
            app:boxCornerRadiusBottomEnd="8dp"
            app:boxCornerRadiusBottomStart="8dp"
            app:boxCornerRadiusTopEnd="8dp"
            app:boxCornerRadiusTopStart="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textInputLayoutDescription"
            app:startIconDrawable="@android:drawable/ic_menu_gallery">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextRoomIconUrl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textUri" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Rules Switch -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switchCustomRules"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:padding="8dp"
            android:text="Añadir reglas personalizadas"
            android:textColor="@color/neon_blue"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textInputLayoutIconUrl" />

        <!-- Rules Section (initially hidden) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/textInputLayoutRules"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:hint="Reglas de la sala"
            android:visibility="gone"
            app:boxCornerRadiusBottomEnd="8dp"
            app:boxCornerRadiusBottomStart="8dp"
            app:boxCornerRadiusTopEnd="8dp"
            app:boxCornerRadiusTopStart="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/switchCustomRules"
            app:startIconDrawable="@android:drawable/ic_menu_agenda">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextRoomRules"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="top|start"
                android:inputType="textMultiLine"
                android:maxLength="500"
                android:maxLines="6"
                android:minLines="3"
                android:padding="8dp" />
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Hidden labels to maintain compatibility with existing code -->
        <TextView
            android:id="@+id/textViewNameLabel"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textViewDescriptionLabel"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textViewIconLabel"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/textViewRulesLabel"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Buttons Section -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/textInputLayoutRules">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonCancelRoom"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="Cancelar"
                android:textColor="#8888AA"
                app:strokeColor="#8888AA" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonCreateRoom"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:backgroundTint="@color/neon_blue"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="Crear Sala"
                android:textColor="@android:color/white" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 