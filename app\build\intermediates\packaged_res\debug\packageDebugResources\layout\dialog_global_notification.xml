<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Título *"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/etNotificationTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="Título de la notificación"
        android:inputType="text"
        android:maxLines="1" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Mensaje *"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/etNotificationMessage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="top"
        android:hint="Mensaje para todos los usuarios"
        android:inputType="textMultiLine"
        android:lines="3"
        android:maxLines="5" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="URL de imagen (opcional)"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/etImageUrl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="https://ejemplo.com/imagen.jpg"
        android:inputType="textUri" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="URL del botón (opcional)"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/etButtonUrl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="https://ejemplo.com/page"
        android:inputType="textUri" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Texto del botón (opcional)"
        android:textStyle="bold" />

    <EditText
        android:id="@+id/etButtonText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Más información"
        android:inputType="text"
        android:maxLines="1" />

</LinearLayout> 