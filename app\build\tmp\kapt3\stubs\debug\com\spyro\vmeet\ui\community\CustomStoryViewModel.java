package com.spyro.vmeet.ui.community;

/**
 * ViewModel personalizado para extender las funcionalidades de CommunityViewModel
 * específicamente para historias con música
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004JB\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00062\u0010\b\u0002\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0017\u0018\u00010\u00162\u0006\u0010\u0018\u001a\u00020\u0019H\u0002J@\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0014\u001a\u00020\u00062\u0010\b\u0002\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0017\u0018\u00010\u00162\u0006\u0010\u0018\u001a\u00020\u0019JB\u0010\u001d\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u0014\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00062\u0010\b\u0002\u0010\u0015\u001a\n\u0012\u0004\u0012\u00020\u0017\u0018\u00010\u00162\u0006\u0010\u0018\u001a\u00020\u0019H\u0002R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u001b\u0010\b\u001a\u00020\t8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\f\u0010\r\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u001e"}, d2 = {"Lcom/spyro/vmeet/ui/community/CustomStoryViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "API_URL", "", "TAG", "communityViewModel", "Lcom/spyro/vmeet/ui/community/CommunityViewModel;", "getCommunityViewModel", "()Lcom/spyro/vmeet/ui/community/CommunityViewModel;", "communityViewModel$delegate", "Lkotlin/Lazy;", "createStoryEntryWithMusic", "", "userId", "", "username", "mediaUrl", "mediaType", "textOverlays", "", "Lcom/spyro/vmeet/ui/community/TextOverlayData;", "musicData", "Lcom/spyro/vmeet/data/community/MusicData;", "createStoryWithMusic", "mediaUri", "Landroid/net/Uri;", "uploadStoryMediaWithMusic", "app_debug"})
public final class CustomStoryViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "CustomStoryViewModel";
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy communityViewModel$delegate = null;
    
    public CustomStoryViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    private final com.spyro.vmeet.ui.community.CommunityViewModel getCommunityViewModel() {
        return null;
    }
    
    /**
     * Crear una historia con música
     */
    public final void createStoryWithMusic(int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    android.net.Uri mediaUri, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays, @org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.data.community.MusicData musicData) {
    }
    
    private final void uploadStoryMediaWithMusic(android.net.Uri mediaUri, java.lang.String mediaType, int userId, java.lang.String username, java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays, com.spyro.vmeet.data.community.MusicData musicData) {
    }
    
    private final void createStoryEntryWithMusic(int userId, java.lang.String username, java.lang.String mediaUrl, java.lang.String mediaType, java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays, com.spyro.vmeet.data.community.MusicData musicData) {
    }
}