<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".ui.community.StoryEditorActivity">

    <FrameLayout
        android:id="@+id/storyEditorFrame"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/bottomControlsLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/storyEditorImageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitCenter"
            tools:srcCompat="@tools:sample/backgrounds/scenic" />

        <!-- Los TextViews se añadirán aquí dinámicamente -->

    </FrameLayout>

    <LinearLayout
        android:id="@+id/topControlsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="#80000000" 
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/closeEditorButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:tint="@android:color/white" />

        <Space
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />
        
        <Button
            android:id="@+id/addMusicButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Música"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/neon_pink"
            android:layout_marginEnd="8dp"/>
        
        <Button
            android:id="@+id/addTextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Texto"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/neon_blue" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/bottomControlsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:padding="16dp"
        android:background="#80000000"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <Button
            android:id="@+id/doneButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hecho"
            android:textColor="@android:color/white"
            android:backgroundTint="@color/neon_pink"/>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/editorProgressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/publishingStatusText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Subiendo historia..."
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:background="#80000000" 
        android:padding="8dp"
        android:visibility="gone" 
        app:layout_constraintTop_toBottomOf="@id/editorProgressBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5" />

    <ImageView
        android:id="@+id/deleteTextTrashCan"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@android:drawable/ic_menu_delete"
        app:tint="@android:color/white"
        android:padding="16dp"
        android:background="#80FF0000" 
        android:visibility="gone" 
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:id="@+id/musicPreviewLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:background="#80000000"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/bottomControlsLayout"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/imageMusicCover"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="centerCrop"
            tools:src="@tools:sample/avatars" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">

            <TextView
                android:id="@+id/textMusicTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textStyle="bold"
                tools:text="Título de la canción" />

            <TextView
                android:id="@+id/textMusicArtist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:ellipsize="end"
                android:maxLines="1"
                tools:text="Nombre del artista" />
        </LinearLayout>

        <ImageButton
            android:id="@+id/buttonRemoveMusic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:tint="@android:color/white" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 