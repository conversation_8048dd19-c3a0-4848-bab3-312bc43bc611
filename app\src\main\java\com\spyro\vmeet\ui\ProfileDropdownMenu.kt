package com.spyro.vmeet.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.util.Log
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import android.widget.TextView
import android.widget.Toast
import com.bumptech.glide.Glide
import com.spyro.vmeet.AdminPanelActivity
import com.spyro.vmeet.ProfileActivity
import com.spyro.vmeet.R
import com.spyro.vmeet.VerificationActivity
import com.spyro.vmeet.activity.CommunityGuidelinesActivity
import com.spyro.vmeet.activity.SettingsActivity
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import org.json.JSONObject
import java.io.IOException

class ProfileDropdownMenu(
    private val context: Context,
    private val userId: Int
) {

    private var popupWindow: PopupWindow? = null
    private val client = OkHttpClient()
    private val API_URL = "http://77.110.116.89:3000"

    companion object {
        private const val TAG = "ProfileDropdownMenu"
    }

    fun show(anchorView: View) {
        try {
            Log.d(TAG, "Showing profile dropdown menu for userId: $userId")

            val inflater = LayoutInflater.from(context)
            val popupView = inflater.inflate(R.layout.profile_dropdown_menu, null)

            // Measure the popup view to get its dimensions
            popupView.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )

            // Create popup window
            popupWindow = PopupWindow(
                popupView,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                true
            ).apply {
                setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                elevation = 8f
                isOutsideTouchable = true
                isFocusable = true
            }

            // Setup user info
            setupUserInfo(popupView)

            // Setup menu items
            setupMenuItems(popupView)

            // Show popup
            val location = IntArray(2)
            anchorView.getLocationOnScreen(location)

            // Calculate position to show above the bottom navigation
            val popupHeight = popupView.measuredHeight
            val xOffset = 0
            val yOffset = -(popupHeight + anchorView.height + 20) // 20dp margin

            Log.d(TAG, "Showing popup at offset: x=$xOffset, y=$yOffset")

            popupWindow?.showAtLocation(anchorView, Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL, xOffset, yOffset)

        } catch (e: Exception) {
            Log.e(TAG, "Error showing profile dropdown menu", e)
            Toast.makeText(context, "Error al mostrar el menú", Toast.LENGTH_SHORT).show()
        }
    }

    private fun setupUserInfo(popupView: View) {
        val imageViewUserAvatar = popupView.findViewById<ImageView>(R.id.imageViewUserAvatar)
        val textViewUserName = popupView.findViewById<TextView>(R.id.textViewUserName)
        val textViewUserHandle = popupView.findViewById<TextView>(R.id.textViewUserHandle)
        val layoutUserInfo = popupView.findViewById<LinearLayout>(R.id.layoutUserInfo)

        // Load user info from API
        loadUserInfo(imageViewUserAvatar, textViewUserName, textViewUserHandle)

        // Set click listener for user info (go to profile)
        layoutUserInfo.setOnClickListener {
            navigateToProfile()
            dismiss()
        }
    }

    private fun setupMenuItems(popupView: View) {
        val layoutMyProfile = popupView.findViewById<LinearLayout>(R.id.layoutMyProfile)
        val layoutVerification = popupView.findViewById<LinearLayout>(R.id.layoutVerification)
        val layoutSettings = popupView.findViewById<LinearLayout>(R.id.layoutSettings)
        val layoutRecruitAndEarn = popupView.findViewById<LinearLayout>(R.id.layoutRecruitAndEarn)
        val layoutCommunityGuidelines = popupView.findViewById<LinearLayout>(R.id.layoutCommunityGuidelines)
        val layoutAdministration = popupView.findViewById<LinearLayout>(R.id.layoutAdministration)
        val layoutLogout = popupView.findViewById<LinearLayout>(R.id.layoutLogout)

        // Mi perfil
        layoutMyProfile.setOnClickListener {
            navigateToProfile()
            dismiss()
        }

        // Veríficate
        layoutVerification.setOnClickListener {
            navigateToVerification()
            dismiss()
        }

        // Check verification status and update UI
        checkVerificationStatus(popupView)

        // Configuración y privacidad
        layoutSettings.setOnClickListener {
            navigateToSettings()
            dismiss()
        }

        // Recluta y gana (placeholder)
        layoutRecruitAndEarn.setOnClickListener {
            Toast.makeText(context, "Función próximamente disponible", Toast.LENGTH_SHORT).show()
            dismiss()
        }

        // Normas de la comunidad
        layoutCommunityGuidelines.setOnClickListener {
            navigateToCommunityGuidelines()
            dismiss()
        }

        // Administración (check if user is admin)
        checkAdminStatus(layoutAdministration)
        layoutAdministration.setOnClickListener {
            navigateToAdminPanel()
            dismiss()
        }

        // Cerrar sesión
        layoutLogout.setOnClickListener {
            showLogoutConfirmation()
            dismiss()
        }
    }

    private fun loadUserInfo(imageView: ImageView, nameTextView: TextView, handleTextView: TextView) {
        Log.d(TAG, "Loading user info for userId: $userId")

        val request = Request.Builder()
            .url("$API_URL/profile/profile/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error loading user info for userId $userId", e)
                (context as? Activity)?.runOnUiThread {
                    nameTextView.text = "Usuario"
                    handleTextView.text = "@usuario"
                    // Set default avatar
                    imageView.setImageResource(R.drawable.ic_default_avatar)
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    Log.d(TAG, "User info response code: ${response.code}")
                    Log.d(TAG, "User info response body: $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonResponse = JSONObject(responseBody)
                        if (jsonResponse.optBoolean("success", false)) {
                            val user = jsonResponse.getJSONObject("user")
                            val username = user.optString("username", "Usuario")
                            val avatarUrl = user.optString("avatar_url", "")

                            Log.d(TAG, "Parsed username: $username")
                            Log.d(TAG, "Parsed avatar_url: $avatarUrl")

                            (context as? Activity)?.runOnUiThread {
                                nameTextView.text = username
                                handleTextView.text = "@$username"

                                // Better avatar URL validation and loading
                                if (avatarUrl.isNotEmpty() &&
                                    avatarUrl != "null" &&
                                    avatarUrl != "placeholder" &&
                                    !avatarUrl.contains("placeholder")) {

                                    val fullAvatarUrl = if (avatarUrl.startsWith("http")) {
                                        avatarUrl // Already complete URL
                                    } else {
                                        "$API_URL$avatarUrl" // Append API_URL if it's just a path
                                    }

                                    Log.d(TAG, "Loading avatar from: $fullAvatarUrl")

                                    Glide.with(context)
                                        .load(fullAvatarUrl)
                                        .placeholder(R.drawable.ic_default_avatar)
                                        .error(R.drawable.ic_default_avatar)
                                        .circleCrop()
                                        .into(imageView)
                                } else {
                                    Log.d(TAG, "No valid avatar URL, using default")
                                    imageView.setImageResource(R.drawable.ic_default_avatar)
                                }
                            }
                        } else {
                            Log.w(TAG, "API response success=false")
                            (context as? Activity)?.runOnUiThread {
                                nameTextView.text = "Usuario"
                                handleTextView.text = "@usuario"
                                imageView.setImageResource(R.drawable.ic_default_avatar)
                            }
                        }
                    } else {
                        Log.w(TAG, "Response not successful or body is null")
                        (context as? Activity)?.runOnUiThread {
                            nameTextView.text = "Usuario"
                            handleTextView.text = "@usuario"
                            imageView.setImageResource(R.drawable.ic_default_avatar)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing user info response", e)
                    (context as? Activity)?.runOnUiThread {
                        nameTextView.text = "Usuario"
                        handleTextView.text = "@usuario"
                        imageView.setImageResource(R.drawable.ic_default_avatar)
                    }
                }
            }
        })
    }

    private fun checkAdminStatus(adminLayout: LinearLayout) {
        Log.d(TAG, "Checking admin status for userId: $userId")

        val request = Request.Builder()
            .url("$API_URL/user/check-admin/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error checking admin status for userId $userId", e)
                (context as? Activity)?.runOnUiThread {
                    adminLayout.visibility = View.GONE
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()
                    Log.d(TAG, "Admin status response code: ${response.code}")
                    Log.d(TAG, "Admin status response body: $responseBody")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonResponse = JSONObject(responseBody)
                        val success = jsonResponse.optBoolean("success", false)
                        val isAdmin = jsonResponse.optBoolean("isAdmin", false)

                        Log.d(TAG, "Admin check success: $success, User is admin: $isAdmin")

                        (context as? Activity)?.runOnUiThread {
                            adminLayout.visibility = if (success && isAdmin) View.VISIBLE else View.GONE
                        }
                    } else {
                        Log.w(TAG, "Admin status response not successful, code: ${response.code}")
                        (context as? Activity)?.runOnUiThread {
                            adminLayout.visibility = View.GONE
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing admin status response", e)
                    (context as? Activity)?.runOnUiThread {
                        adminLayout.visibility = View.GONE
                    }
                }
            }
        })
    }

    private fun navigateToProfile() {
        val intent = Intent(context, ProfileActivity::class.java).apply {
            putExtra("USER_ID", userId)
            putExtra("VIEW_ONLY_MODE", false)
            flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
        }
        context.startActivity(intent)
    }

    private fun navigateToSettings() {
        val intent = Intent(context, SettingsActivity::class.java).apply {
            putExtra("USER_ID", userId)
        }
        context.startActivity(intent)
    }

    private fun navigateToCommunityGuidelines() {
        val intent = Intent(context, CommunityGuidelinesActivity::class.java)
        context.startActivity(intent)
    }

    private fun navigateToAdminPanel() {
        val intent = Intent(context, AdminPanelActivity::class.java).apply {
            putExtra("USER_ID", userId)
        }
        context.startActivity(intent)
    }

    private fun navigateToVerification() {
        val intent = Intent(context, VerificationActivity::class.java).apply {
            putExtra("USER_ID", userId)
        }
        context.startActivity(intent)
    }

    private fun showLogoutConfirmation() {
        Log.d(TAG, "Showing logout confirmation dialog")

        AlertDialog.Builder(context)
            .setTitle("Cerrar sesión")
            .setMessage("¿Estás seguro de que quieres cerrar sesión?")
            .setPositiveButton("Cerrar sesión") { _, _ ->
                performLogout()
            }
            .setNegativeButton("Cancelar") { dialog, _ ->
                dialog.dismiss()
            }
            .setIcon(android.R.drawable.ic_dialog_alert)
            .show()
    }

    private fun performLogout() {
        Log.d(TAG, "Performing logout for userId: $userId")

        try {
            // Clear user session data
            val sharedPrefs = context.getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
            with(sharedPrefs.edit()) {
                clear() // Clear all stored data
                apply()
            }

            Log.d(TAG, "User session data cleared")

            // Navigate to login activity
            navigateToLogin()

            // Show logout success message
            Toast.makeText(context, "Sesión cerrada exitosamente", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "Error during logout", e)
            Toast.makeText(context, "Error al cerrar sesión", Toast.LENGTH_SHORT).show()
        }
    }

    private fun navigateToLogin() {
        try {
            // Create intent to login activity
            val intent = Intent(context, com.spyro.vmeet.LoginActivity::class.java).apply {
                // Clear the activity stack so user can't go back
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            }

            context.startActivity(intent)

            // Finish current activity if it's an Activity
            if (context is Activity) {
                context.finish()
            }

            Log.d(TAG, "Navigated to login activity")

        } catch (e: Exception) {
            Log.e(TAG, "Error navigating to login", e)
            Toast.makeText(context, "Error al navegar al login", Toast.LENGTH_SHORT).show()
        }
    }

    private fun checkVerificationStatus(popupView: View) {
        val imageViewVerificationBadge = popupView.findViewById<ImageView>(R.id.imageViewVerificationBadge)

        // Make API call to check verification status
        val client = OkHttpClient()
        val request = Request.Builder()
            .url("http://77.110.116.89:3000/verification/status/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                // Hide badge on error
                (context as? Activity)?.runOnUiThread {
                    imageViewVerificationBadge.visibility = View.GONE
                }
            }

            override fun onResponse(call: Call, response: Response) {
                response.body?.let { responseBody ->
                    val responseText = responseBody.string()
                    try {
                        val jsonResponse = JSONObject(responseText)
                        val status = jsonResponse.optString("status", "not_verified")
                        val verifiedStatus = jsonResponse.optString("verified_status", "not_verified")

                        (context as? Activity)?.runOnUiThread {
                            when (status) {
                                "approved" -> {
                                    imageViewVerificationBadge.visibility = View.VISIBLE
                                    imageViewVerificationBadge.setColorFilter(
                                        ContextCompat.getColor(context, R.color.neon_green)
                                    )
                                }
                                "pending" -> {
                                    imageViewVerificationBadge.visibility = View.VISIBLE
                                    imageViewVerificationBadge.setColorFilter(
                                        ContextCompat.getColor(context, R.color.cyberpunk_accent_secondary)
                                    )
                                }
                                else -> {
                                    imageViewVerificationBadge.visibility = View.GONE
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing verification status", e)
                        (context as? Activity)?.runOnUiThread {
                            imageViewVerificationBadge.visibility = View.GONE
                        }
                    }
                }
            }
        })
    }

    fun dismiss() {
        popupWindow?.dismiss()
        popupWindow = null
    }
}
