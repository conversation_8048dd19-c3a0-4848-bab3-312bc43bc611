<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/emoji_picker_bg">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Emojis"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/emoji_picker_title_text"
        android:gravity="center"
        android:layout_marginBottom="8dp"/>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayoutCategories"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabMode="scrollable"
        app:tabGravity="fill"
        app:tabIndicatorColor="@color/emoji_picker_tab_indicator"
        app:tabSelectedTextColor="@color/emoji_picker_tab_selected"
        app:tabTextColor="@color/emoji_picker_tab_text"
        android:background="@color/emoji_picker_bg"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewEmojis"
        android:layout_width="match_parent"
        android:layout_height="250dp"
        android:layout_marginTop="8dp"/>

</LinearLayout> 