package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001:\u00014B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010%\u001a\u00020&H\u0002J\b\u0010\'\u001a\u00020&H\u0002J\b\u0010(\u001a\u00020\u000fH\u0002J\b\u0010)\u001a\u00020&H\u0002J\u0018\u0010*\u001a\u00020&2\u0006\u0010+\u001a\u00020\u00112\u0006\u0010,\u001a\u00020\u0013H\u0002J\u0012\u0010-\u001a\u00020&2\b\u0010.\u001a\u0004\u0018\u00010/H\u0014J\b\u00100\u001a\u00020&H\u0002J\b\u00101\u001a\u00020&H\u0002J\b\u00102\u001a\u00020&H\u0002J\b\u00103\u001a\u00020&H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00040\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00110\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001dX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\"X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020$X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00065"}, d2 = {"Lcom/spyro/vmeet/VerificationActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "buttonSubmit", "Lcom/google/android/material/button/MaterialButton;", "buttonTakeDni", "buttonTakeSelfie", "cardDniPhoto", "Lcom/google/android/material/card/MaterialCardView;", "cardSelfiePhoto", "currentPhotoType", "Lcom/spyro/vmeet/VerificationActivity$PhotoType;", "dniPhotoFile", "Ljava/io/File;", "dniPhotoUri", "Landroid/net/Uri;", "imageDni", "Landroid/widget/ImageView;", "imageSelfie", "progressBar", "Landroid/widget/ProgressBar;", "requestPermissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "selfiePhotoFile", "selfiePhotoUri", "takePictureLauncher", "textBenefits", "Landroid/widget/TextView;", "textDniStatus", "textInstructions", "textSelfieStatus", "toolbar", "Lcom/google/android/material/appbar/MaterialToolbar;", "userId", "", "checkCameraPermissionAndTakePhoto", "", "checkVerificationStatus", "createImageFile", "initializeViews", "loadImageIntoView", "uri", "imageView", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "openCamera", "setupListeners", "submitVerification", "updateSubmitButton", "PhotoType", "app_debug"})
public final class VerificationActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.google.android.material.appbar.MaterialToolbar toolbar;
    private com.google.android.material.card.MaterialCardView cardDniPhoto;
    private com.google.android.material.card.MaterialCardView cardSelfiePhoto;
    private android.widget.ImageView imageDni;
    private android.widget.ImageView imageSelfie;
    private android.widget.TextView textDniStatus;
    private android.widget.TextView textSelfieStatus;
    private com.google.android.material.button.MaterialButton buttonTakeDni;
    private com.google.android.material.button.MaterialButton buttonTakeSelfie;
    private com.google.android.material.button.MaterialButton buttonSubmit;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textInstructions;
    private android.widget.TextView textBenefits;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri dniPhotoUri;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri selfiePhotoUri;
    @org.jetbrains.annotations.Nullable()
    private java.io.File dniPhotoFile;
    @org.jetbrains.annotations.Nullable()
    private java.io.File selfiePhotoFile;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.VerificationActivity.PhotoType currentPhotoType;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://*************:3000";
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String> requestPermissionLauncher = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.net.Uri> takePictureLauncher = null;
    
    public VerificationActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeViews() {
    }
    
    private final void setupListeners() {
    }
    
    private final void checkCameraPermissionAndTakePhoto() {
    }
    
    private final void openCamera() {
    }
    
    private final java.io.File createImageFile() {
        return null;
    }
    
    private final void loadImageIntoView(android.net.Uri uri, android.widget.ImageView imageView) {
    }
    
    private final void updateSubmitButton() {
    }
    
    private final void checkVerificationStatus() {
    }
    
    private final void submitVerification() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0082\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/VerificationActivity$PhotoType;", "", "(Ljava/lang/String;I)V", "DNI", "SELFIE", "app_debug"})
    static enum PhotoType {
        /*public static final*/ DNI /* = new DNI() */,
        /*public static final*/ SELFIE /* = new SELFIE() */;
        
        PhotoType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.spyro.vmeet.VerificationActivity.PhotoType> getEntries() {
            return null;
        }
    }
}