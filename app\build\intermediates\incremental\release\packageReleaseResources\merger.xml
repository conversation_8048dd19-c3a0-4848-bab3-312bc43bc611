<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res"><file name="buzz_earthquake" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\buzz_earthquake.xml" qualifiers="" type="anim"/><file name="buzz_flash" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\buzz_flash.xml" qualifiers="" type="anim"/><file name="buzz_intense_shake" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\buzz_intense_shake.xml" qualifiers="" type="anim"/><file name="buzz_shake" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\buzz_shake.xml" qualifiers="" type="anim"/><file name="fade_in" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="pulse" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\pulse.xml" qualifiers="" type="anim"/><file name="rotate" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\rotate.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="text_fade_in" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\text_fade_in.xml" qualifiers="" type="anim"/><file name="text_rotate" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\text_rotate.xml" qualifiers="" type="anim"/><file name="text_slide_in_left" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\anim\text_slide_in_left.xml" qualifiers="" type="anim"/><file name="admin_gradient_1" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_gradient_1.xml" qualifiers="" type="drawable"/><file name="admin_gradient_2" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_gradient_2.xml" qualifiers="" type="drawable"/><file name="admin_gradient_3" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_gradient_3.xml" qualifiers="" type="drawable"/><file name="admin_gradient_animation" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_gradient_animation.xml" qualifiers="" type="drawable"/><file name="admin_name_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_name_background.xml" qualifiers="" type="drawable"/><file name="admin_name_gradient" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_name_gradient.xml" qualifiers="" type="drawable"/><file name="admin_name_gradient_1" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_name_gradient_1.xml" qualifiers="" type="drawable"/><file name="admin_name_gradient_2" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_name_gradient_2.xml" qualifiers="" type="drawable"/><file name="admin_name_gradient_3" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\admin_name_gradient_3.xml" qualifiers="" type="drawable"/><file name="avatar_circle_border" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\avatar_circle_border.xml" qualifiers="" type="drawable"/><file name="background_reply_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\background_reply_received.xml" qualifiers="" type="drawable"/><file name="background_reply_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\background_reply_sent.xml" qualifiers="" type="drawable"/><file name="badge_admin" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\badge_admin.xml" qualifiers="" type="drawable"/><file name="badge_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\badge_user.xml" qualifiers="" type="drawable"/><file name="bg_buzz_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\bg_buzz_message_received.xml" qualifiers="" type="drawable"/><file name="bg_buzz_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\bg_buzz_message_sent.xml" qualifiers="" type="drawable"/><file name="bg_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\bg_message_received.xml" qualifiers="" type="drawable"/><file name="bg_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\bg_message_sent.xml" qualifiers="" type="drawable"/><file name="bottom_sheet_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\bottom_sheet_background.xml" qualifiers="" type="drawable"/><file name="button_cyberpunk" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\button_cyberpunk.xml" qualifiers="" type="drawable"/><file name="button_outline_cyberpunk" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\button_outline_cyberpunk.xml" qualifiers="" type="drawable"/><file name="button_rounded_accent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\button_rounded_accent.xml" qualifiers="" type="drawable"/><file name="buzz_pulse_animation" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\buzz_pulse_animation.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_button_bg" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\circle_button_bg.xml" qualifiers="" type="drawable"/><file name="circle_dot" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\circle_dot.xml" qualifiers="" type="drawable"/><file name="circle_online_status" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\circle_online_status.xml" qualifiers="" type="drawable"/><file name="circular_button" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\circular_button.xml" qualifiers="" type="drawable"/><file name="circular_button_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\circular_button_background.xml" qualifiers="" type="drawable"/><file name="color_preview_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\color_preview_background.xml" qualifiers="" type="drawable"/><file name="color_square_shape" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\color_square_shape.xml" qualifiers="" type="drawable"/><file name="color_swatch_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\color_swatch_background.xml" qualifiers="" type="drawable"/><file name="comment_input_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\comment_input_background.xml" qualifiers="" type="drawable"/><file name="cyberpunk_circle_button" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\cyberpunk_circle_button.xml" qualifiers="" type="drawable"/><file name="cyberpunk_gradient" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\cyberpunk_gradient.xml" qualifiers="" type="drawable"/><file name="default_avatar" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\default_avatar.xml" qualifiers="" type="drawable"/><file name="default_room_icon" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\default_room_icon.xml" qualifiers="" type="drawable"/><file name="distance_badge_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\distance_badge_background.xml" qualifiers="" type="drawable"/><file name="glowing_border" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\glowing_border.xml" qualifiers="" type="drawable"/><file name="glowing_neon_ring" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\glowing_neon_ring.xml" qualifiers="" type="drawable"/><file name="gradient_bottom_overlay" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\gradient_bottom_overlay.xml" qualifiers="" type="drawable"/><file name="gradient_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\gradient_message_received.xml" qualifiers="" type="drawable"/><file name="gradient_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\gradient_message_sent.xml" qualifiers="" type="drawable"/><file name="gradient_overlay" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\gradient_overlay.xml" qualifiers="" type="drawable"/><file name="ic_add_gif" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_add_gif.xml" qualifiers="" type="drawable"/><file name="ic_add_image" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_add_image.xml" qualifiers="" type="drawable"/><file name="ic_add_mic" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_add_mic.xml" qualifiers="" type="drawable"/><file name="ic_add_post" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_add_post.xml" qualifiers="" type="drawable"/><file name="ic_admin_crown" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_admin_crown.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_audiotrack" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_audiotrack.xml" qualifiers="" type="drawable"/><file name="ic_chat" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_chat.xml" qualifiers="" type="drawable"/><file name="ic_chats" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_chats.xml" qualifiers="" type="drawable"/><file name="ic_chat_bubble" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_chat_bubble.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_check_outlined" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_check_outlined.xml" qualifiers="" type="drawable"/><file name="ic_close_circle" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_close_circle.xml" qualifiers="" type="drawable"/><file name="ic_comment_outline" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_comment_outline.xml" qualifiers="" type="drawable"/><file name="ic_community" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_community.xml" qualifiers="" type="drawable"/><file name="ic_default_avatar" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_default_avatar.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_emoji" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_emoji.xml" qualifiers="" type="drawable"/><file name="ic_eye" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_eye.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_gif" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_gif.xml" qualifiers="" type="drawable"/><file name="ic_heart" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_heart.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_like_outline" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_like_outline.xml" qualifiers="" type="drawable"/><file name="ic_location" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="ic_location_permission" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_location_permission.xml" qualifiers="" type="drawable"/><file name="ic_mark_read" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_mark_read.xml" qualifiers="" type="drawable"/><file name="ic_matches" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_matches.xml" qualifiers="" type="drawable"/><file name="ic_more_vert" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_more_vert.xml" qualifiers="" type="drawable"/><file name="ic_music_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_music_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_mute" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_mute.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_notification_vmeet" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_notification_vmeet.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_placeholder_image" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_placeholder_image.xml" qualifiers="" type="drawable"/><file name="ic_play" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_profile_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_profile_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_radar_empty" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_radar_empty.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_reply" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_reply.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_send" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_send.xml" qualifiers="" type="drawable"/><file name="ic_send_comment" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_send_comment.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_stop_mic" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_stop_mic.xml" qualifiers="" type="drawable"/><file name="ic_swipe" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_swipe.xml" qualifiers="" type="drawable"/><file name="ic_topic_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_topic_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_vibration" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\ic_vibration.xml" qualifiers="" type="drawable"/><file name="image_error" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\image_error.xml" qualifiers="" type="drawable"/><file name="image_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\image_placeholder.xml" qualifiers="" type="drawable"/><file name="indicator_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\indicator_background.xml" qualifiers="" type="drawable"/><file name="login_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\login_background.xml" qualifiers="" type="drawable"/><file name="message_input_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\message_input_background.xml" qualifiers="" type="drawable"/><file name="message_received_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\message_received_background.xml" qualifiers="" type="drawable"/><file name="message_sent_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\message_sent_background.xml" qualifiers="" type="drawable"/><file name="mic_button_selector" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\mic_button_selector.xml" qualifiers="" type="drawable"/><file name="modern_button" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\modern_button.xml" qualifiers="" type="drawable"/><file name="modern_button_alt" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\modern_button_alt.xml" qualifiers="" type="drawable"/><file name="modern_edit_text_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\modern_edit_text_background.xml" qualifiers="" type="drawable"/><file name="neon_button" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\neon_button.xml" qualifiers="" type="drawable"/><file name="neon_button_alt" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\neon_button_alt.xml" qualifiers="" type="drawable"/><file name="neon_button_blue" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\neon_button_blue.xml" qualifiers="" type="drawable"/><file name="neon_button_green" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\neon_button_green.xml" qualifiers="" type="drawable"/><file name="neon_button_red" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\neon_button_red.xml" qualifiers="" type="drawable"/><file name="neon_circle" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\neon_circle.xml" qualifiers="" type="drawable"/><file name="online_status_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\online_status_background.xml" qualifiers="" type="drawable"/><file name="online_status_indicator" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\online_status_indicator.xml" qualifiers="" type="drawable"/><file name="post_edit_text_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\post_edit_text_background.xml" qualifiers="" type="drawable"/><file name="preview_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\preview_background.xml" qualifiers="" type="drawable"/><file name="progress_bar_cyberpunk" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\progress_bar_cyberpunk.xml" qualifiers="" type="drawable"/><file name="radar_header_gradient" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\radar_header_gradient.xml" qualifiers="" type="drawable"/><file name="reaction_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\reaction_background.xml" qualifiers="" type="drawable"/><file name="reaction_background_own" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\reaction_background_own.xml" qualifiers="" type="drawable"/><file name="reaction_container_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\reaction_container_background.xml" qualifiers="" type="drawable"/><file name="reaction_selector_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\reaction_selector_background.xml" qualifiers="" type="drawable"/><file name="recording_button_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\recording_button_background.xml" qualifiers="" type="drawable"/><file name="recording_cancel_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\recording_cancel_background.xml" qualifiers="" type="drawable"/><file name="reply_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\reply_background.xml" qualifiers="" type="drawable"/><file name="rounded_button_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_button_background.xml" qualifiers="" type="drawable"/><file name="rounded_button_outline_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_button_outline_background.xml" qualifiers="" type="drawable"/><file name="rounded_corner_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_corner_background.xml" qualifiers="" type="drawable"/><file name="rounded_dark_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_dark_background.xml" qualifiers="" type="drawable"/><file name="rounded_dialog_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_dialog_background.xml" qualifiers="" type="drawable"/><file name="rounded_edittext_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_edittext_background.xml" qualifiers="" type="drawable"/><file name="rounded_edittext_bg" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\rounded_edittext_bg.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="section_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\section_background.xml" qualifiers="" type="drawable"/><file name="selected_color_border" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\selected_color_border.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="status_error" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\status_error.xml" qualifiers="" type="drawable"/><file name="status_pending" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\status_pending.xml" qualifiers="" type="drawable"/><file name="status_read" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\status_read.xml" qualifiers="" type="drawable"/><file name="status_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\status_sent.xml" qualifiers="" type="drawable"/><file name="story_ring_seen" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\story_ring_seen.xml" qualifiers="" type="drawable"/><file name="story_ring_unseen" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\story_ring_unseen.xml" qualifiers="" type="drawable"/><file name="story_ring_unseen_gradient" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\story_ring_unseen_gradient.xml" qualifiers="" type="drawable"/><file name="swipe_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\swipe_background.xml" qualifiers="" type="drawable"/><file name="tab_dot_default" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\tab_dot_default.xml" qualifiers="" type="drawable"/><file name="tab_dot_selected" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\tab_dot_selected.xml" qualifiers="" type="drawable"/><file name="tab_selected" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\tab_selected.xml" qualifiers="" type="drawable"/><file name="tab_selector" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\tab_selector.xml" qualifiers="" type="drawable"/><file name="tab_unselected" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\tab_unselected.xml" qualifiers="" type="drawable"/><file name="trait_selected_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\trait_selected_background.xml" qualifiers="" type="drawable"/><file name="trait_unselected_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\trait_unselected_background.xml" qualifiers="" type="drawable"/><file name="unread_counter_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\unread_counter_background.xml" qualifiers="" type="drawable"/><file name="unread_indicator" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\unread_indicator.xml" qualifiers="" type="drawable"/><file name="voice_note_player_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\voice_note_player_background.xml" qualifiers="" type="drawable"/><file name="youtube_play_button_background" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable\youtube_play_button_background.xml" qualifiers="" type="drawable"/><file name="ic_notification_vmeet" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\drawable-v26\ic_notification_vmeet.xml" qualifiers="v26" type="drawable"/><file name="app_font" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\app_font.xml" qualifiers="" type="font"/><file name="combackhomeregularjemd9" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\combackhomeregularjemd9.ttf" qualifiers="" type="font"/><file name="greatsracingfreeforpersonalitalicbll" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\greatsracingfreeforpersonalitalicbll.ttf" qualifiers="" type="font"/><file name="inter" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\inter.ttf" qualifiers="" type="font"/><file name="interitalic" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\interitalic.ttf" qualifiers="" type="font"/><file name="knightwarriorw16n8" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\knightwarriorw16n8.otf" qualifiers="" type="font"/><file name="orbitron" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\orbitron.ttf" qualifiers="" type="font"/><file name="steelarj9vnj" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\steelarj9vnj.otf" qualifiers="" type="font"/><file name="supremespikekvo8d" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\supremespikekvo8d.otf" qualifiers="" type="font"/><file name="veniteadoremusrgrba" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\veniteadoremusrgrba.ttf" qualifiers="" type="font"/><file name="veniteadoremusstraightyzo6v" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\veniteadoremusstraightyzo6v.ttf" qualifiers="" type="font"/><file name="wowdinog33vp" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\font\wowdinog33vp.ttf" qualifiers="" type="font"/><file name="activity_admin_panel" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_admin_panel.xml" qualifiers="" type="layout"/><file name="activity_blind_date" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_blind_date.xml" qualifiers="" type="layout"/><file name="activity_blocked_users" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_blocked_users.xml" qualifiers="" type="layout"/><file name="activity_chat" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_chat.xml" qualifiers="" type="layout"/><file name="activity_chat_list" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_chat_list.xml" qualifiers="" type="layout"/><file name="activity_chat_room" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_chat_room.xml" qualifiers="" type="layout"/><file name="activity_comments" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_comments.xml" qualifiers="" type="layout"/><file name="activity_community_guidelines" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_community_guidelines.xml" qualifiers="" type="layout"/><file name="activity_community_host" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_community_host.xml" qualifiers="" type="layout"/><file name="activity_edit_personality_traits" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_edit_personality_traits.xml" qualifiers="" type="layout"/><file name="activity_email_verification" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_email_verification.xml" qualifiers="" type="layout"/><file name="activity_forgot_password" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_forgot_password.xml" qualifiers="" type="layout"/><file name="activity_full_screen_image" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_full_screen_image.xml" qualifiers="" type="layout"/><file name="activity_login" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main_empty" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_main_empty.xml" qualifiers="" type="layout"/><file name="activity_matches" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_matches.xml" qualifiers="" type="layout"/><file name="activity_my_videos" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_my_videos.xml" qualifiers="" type="layout"/><file name="activity_password_reset" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_password_reset.xml" qualifiers="" type="layout"/><file name="activity_profile" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_profile.xml" qualifiers="" type="layout"/><file name="activity_reports" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_reports.xml" qualifiers="" type="layout"/><file name="activity_room_loader" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_room_loader.xml" qualifiers="" type="layout"/><file name="activity_settings" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_settings.xml" qualifiers="" type="layout"/><file name="activity_settings_compat" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_settings_compat.xml" qualifiers="" type="layout"/><file name="activity_story_editor" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_story_editor.xml" qualifiers="" type="layout"/><file name="activity_story_viewer" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_story_viewer.xml" qualifiers="" type="layout"/><file name="activity_swipe" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_swipe.xml" qualifiers="" type="layout"/><file name="activity_tutorial" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_tutorial.xml" qualifiers="" type="layout"/><file name="activity_upload_video" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_upload_video.xml" qualifiers="" type="layout"/><file name="activity_user_management" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_user_management.xml" qualifiers="" type="layout"/><file name="activity_user_profile" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_user_profile.xml" qualifiers="" type="layout"/><file name="activity_video_feed" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\activity_video_feed.xml" qualifiers="" type="layout"/><file name="dialog_add_text_with_style" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_add_text_with_style.xml" qualifiers="" type="layout"/><file name="dialog_ban_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_ban_user.xml" qualifiers="" type="layout"/><file name="dialog_change_email" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_change_email.xml" qualifiers="" type="layout"/><file name="dialog_change_password" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_change_password.xml" qualifiers="" type="layout"/><file name="dialog_change_username" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_change_username.xml" qualifiers="" type="layout"/><file name="dialog_color_palette_picker" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_color_palette_picker.xml" qualifiers="" type="layout"/><file name="dialog_comments_bottom_sheet" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_comments_bottom_sheet.xml" qualifiers="" type="layout"/><file name="dialog_create_post" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_create_post.xml" qualifiers="" type="layout"/><file name="dialog_create_room" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_create_room.xml" qualifiers="" type="layout"/><file name="dialog_custom_color_picker" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_custom_color_picker.xml" qualifiers="" type="layout"/><file name="dialog_delete_room_confirmation" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_delete_room_confirmation.xml" qualifiers="" type="layout"/><file name="dialog_edit_room" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_edit_room.xml" qualifiers="" type="layout"/><file name="dialog_emoji_picker" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_emoji_picker.xml" qualifiers="" type="layout"/><file name="dialog_gif_picker" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_gif_picker.xml" qualifiers="" type="layout"/><file name="dialog_global_notification" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_global_notification.xml" qualifiers="" type="layout"/><file name="dialog_improved_emoji_picker" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_improved_emoji_picker.xml" qualifiers="" type="layout"/><file name="dialog_message_options" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_message_options.xml" qualifiers="" type="layout"/><file name="dialog_music_search" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_music_search.xml" qualifiers="" type="layout"/><file name="dialog_mute_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_mute_user.xml" qualifiers="" type="layout"/><file name="dialog_radar_filters" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_radar_filters.xml" qualifiers="" type="layout"/><file name="dialog_report_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_report_user.xml" qualifiers="" type="layout"/><file name="dialog_rules_acceptance" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_rules_acceptance.xml" qualifiers="" type="layout"/><file name="dialog_story_viewers" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dialog_story_viewers.xml" qualifiers="" type="layout"/><file name="dropdown_item" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\dropdown_item.xml" qualifiers="" type="layout"/><file name="fragment_blind_date" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\fragment_blind_date.xml" qualifiers="" type="layout"/><file name="fragment_chat_rooms" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\fragment_chat_rooms.xml" qualifiers="" type="layout"/><file name="fragment_community" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\fragment_community.xml" qualifiers="" type="layout"/><file name="fragment_private_chats" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\fragment_private_chats.xml" qualifiers="" type="layout"/><file name="fragment_profiles" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\fragment_profiles.xml" qualifiers="" type="layout"/><file name="fragment_radar" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\fragment_radar.xml" qualifiers="" type="layout"/><file name="grid_emoji_category" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\grid_emoji_category.xml" qualifiers="" type="layout"/><file name="item_admin_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_admin_user.xml" qualifiers="" type="layout"/><file name="item_blind_date_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_blind_date_message_received.xml" qualifiers="" type="layout"/><file name="item_blind_date_message_received_modern" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_blind_date_message_received_modern.xml" qualifiers="" type="layout"/><file name="item_blind_date_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_blind_date_message_sent.xml" qualifiers="" type="layout"/><file name="item_blind_date_message_sent_modern" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_blind_date_message_sent_modern.xml" qualifiers="" type="layout"/><file name="item_blind_date_topic" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_blind_date_topic.xml" qualifiers="" type="layout"/><file name="item_blocked_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_blocked_user.xml" qualifiers="" type="layout"/><file name="item_chat_list" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_chat_list.xml" qualifiers="" type="layout"/><file name="item_chat_room" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_chat_room.xml" qualifiers="" type="layout"/><file name="item_comment" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_comment.xml" qualifiers="" type="layout"/><file name="item_emoji" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_emoji.xml" qualifiers="" type="layout"/><file name="item_gif" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_gif.xml" qualifiers="" type="layout"/><file name="item_gif_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_gif_message_received.xml" qualifiers="" type="layout"/><file name="item_gif_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_gif_message_sent.xml" qualifiers="" type="layout"/><file name="item_image_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_image_message_received.xml" qualifiers="" type="layout"/><file name="item_image_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_image_message_sent.xml" qualifiers="" type="layout"/><file name="item_match" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_match.xml" qualifiers="" type="layout"/><file name="item_mention_suggestion" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_mention_suggestion.xml" qualifiers="" type="layout"/><file name="item_message_buzz_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_message_buzz_received.xml" qualifiers="" type="layout"/><file name="item_message_buzz_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_message_buzz_sent.xml" qualifiers="" type="layout"/><file name="item_message_reaction" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_message_reaction.xml" qualifiers="" type="layout"/><file name="item_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_message_received.xml" qualifiers="" type="layout"/><file name="item_message_sent" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_message_sent.xml" qualifiers="" type="layout"/><file name="item_music_search" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_music_search.xml" qualifiers="" type="layout"/><file name="item_my_video" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_my_video.xml" qualifiers="" type="layout"/><file name="item_personality_trait" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_personality_trait.xml" qualifiers="" type="layout"/><file name="item_personality_trait_category" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_personality_trait_category.xml" qualifiers="" type="layout"/><file name="item_post" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_post.xml" qualifiers="" type="layout"/><file name="item_profile_image" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_profile_image.xml" qualifiers="" type="layout"/><file name="item_radar_user" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_radar_user.xml" qualifiers="" type="layout"/><file name="item_received_gif_message" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_received_gif_message.xml" qualifiers="" type="layout"/><file name="item_report" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_report.xml" qualifiers="" type="layout"/><file name="item_sent_gif_message" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_sent_gif_message.xml" qualifiers="" type="layout"/><file name="item_story" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_story.xml" qualifiers="" type="layout"/><file name="item_story_viewer" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_story_viewer.xml" qualifiers="" type="layout"/><file name="item_user_card" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_user_card.xml" qualifiers="" type="layout"/><file name="item_video" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_video.xml" qualifiers="" type="layout"/><file name="item_voice_message" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_voice_message.xml" qualifiers="" type="layout"/><file name="item_voice_message_received" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\item_voice_message_received.xml" qualifiers="" type="layout"/><file name="layout_blind_date_chat" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_blind_date_chat.xml" qualifiers="" type="layout"/><file name="layout_blind_date_chat_modern" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_blind_date_chat_modern.xml" qualifiers="" type="layout"/><file name="layout_blind_date_result" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_blind_date_result.xml" qualifiers="" type="layout"/><file name="layout_blind_date_reveal" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_blind_date_reveal.xml" qualifiers="" type="layout"/><file name="layout_blind_date_waiting" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_blind_date_waiting.xml" qualifiers="" type="layout"/><file name="layout_join_room_prompt" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_join_room_prompt.xml" qualifiers="" type="layout"/><file name="layout_profile_image_slider" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_profile_image_slider.xml" qualifiers="" type="layout"/><file name="layout_reaction_selector" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_reaction_selector.xml" qualifiers="" type="layout"/><file name="layout_user_muted" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\layout_user_muted.xml" qualifiers="" type="layout"/><file name="menu_community_dropdown" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\menu_community_dropdown.xml" qualifiers="" type="layout"/><file name="profile_dropdown_menu" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\profile_dropdown_menu.xml" qualifiers="" type="layout"/><file name="tutorial_page" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\layout\tutorial_page.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="chat_menu" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\chat_menu.xml" qualifiers="" type="menu"/><file name="menu_location_options" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\menu_location_options.xml" qualifiers="" type="menu"/><file name="menu_match_item" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\menu_match_item.xml" qualifiers="" type="menu"/><file name="menu_post_options" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\menu_post_options.xml" qualifiers="" type="menu"/><file name="menu_room_admin" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\menu_room_admin.xml" qualifiers="" type="menu"/><file name="menu_swipe" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\menu_swipe.xml" qualifiers="" type="menu"/><file name="profile_menu" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\profile_menu.xml" qualifiers="" type="menu"/><file name="room_chat_menu" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\menu\room_chat_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="welcome" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\raw\welcome.mp3" qualifiers="" type="raw"/><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="PurpleButton">
        <attr format="boolean" name="isAlt"/>
    </declare-styleable><declare-styleable name="PixelPerfectTextView">
        <attr name="android:text"/>
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
    </declare-styleable></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="colorAccent">#FF00E4</color><color name="colorPrimary">#FF6200EE</color><color name="colorPrimaryDark">#FF3700B3</color><color name="neon_blue">#4DB6E6</color><color name="comfy_blue">#5DADE2</color><color name="neon_pink">#FF00E4</color><color name="neon_purple">#9D00FF</color><color name="neon_green">#00FF66</color><color name="dark_blue">#0B0B2B</color><color name="darker_blue">#050518</color><color name="background">#0A0A25</color><color name="card_background">#101033</color><color name="dark_red">#C62828</color><color name="comfy_red">#E74C3C</color><color name="login_button_color">#BB86FC</color><color name="register_button_color">#A066F0</color><color name="read_message_bg">#E0E0E0</color><color name="unread_message_bg">#C2C2FF</color><color name="status_delivered">#888888</color><color name="dark_purple">#2A2A60</color><color name="neon_blue_dark">#2E86AB</color><color name="emoji_picker_bg">#333333</color><color name="emoji_picker_tab_selected">#9C27B0</color><color name="emoji_picker_tab_indicator">#9C27B0</color><color name="emoji_picker_tab_text">#CCCCCC</color><color name="emoji_picker_title_text">#FFFFFF</color><color name="cyberpunk_background">#1A1A2E</color><color name="cyberpunk_card_background">#162447</color><color name="cyberpunk_card_stroke">#4DB6E6</color><color name="cyberpunk_text_primary">#E0E0E0</color><color name="cyberpunk_text_secondary">#A0A0A0</color><color name="cyberpunk_accent_primary">#FF00E4</color><color name="cyberpunk_accent_secondary">#00FF66</color><color name="cyberpunk_divider">#E0E0E0</color><color name="cyberpunk_highlight">#3A3A5D</color><color name="cyberpunk_shadow">#0A0A15</color><color name="cyberpunk_accent_voice">#00FF66</color><color name="cyberpunk_accent_voice_thumb">#A4FFC9</color><color name="cyberpunk_yellow">#FFFF00</color><color name="cyberpunk_text">#E0E0E0</color><color name="cyberpunk_accent">#FFEB3B</color><color name="cyberpunk_green">#00FFAB</color><color name="cyberpunk_purple">#B39DDB</color><color name="cyberpunk_orange">#FFAB40</color><color name="mention_highlight_color">#3F51B5</color><color name="buzz_text_color">#FFFFFF</color><color name="buzz_text_shadow">#000000</color><color name="buzz_sent_primary">#FF6B35</color><color name="buzz_sent_secondary">#F7931E</color><color name="buzz_received_primary">#9C27B0</color><color name="buzz_received_secondary">#673AB7</color><color name="online_green">#00FF66</color><color name="offline_gray">#888888</color><color name="neon_blue_transparent">#224DB6E6</color><color name="text_primary">#E0E0E0</color><color name="text_secondary">#A0A0A0</color><color name="dark_background">#0A0A25</color><color name="button_background">#162447</color><color name="gray_light">#A0A0A0</color><color name="gray_dark">#2A2A2A</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="story_item_spacing">8dp</dimen><dimen name="status_bar_height">24dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\ids.xml" qualifiers=""><item name="buttonCancelReveal" type="id"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">VMeet</string><string name="title_activity_login">LoginActivity</string><string name="title_activity_swipe">SwipeActivity</string><string name="title_swipe">Descubrir</string><string name="title_matches">Matches</string><string name="title_profile">Perfil</string><string name="title_chats">Chats</string><string name="hello_blank_fragment">Fragmento en blanco</string><string name="username_label">Nombre de usuario:</string><string name="email_label">Email:</string><string name="interests_label">Intereses:</string><string name="favorite_games_label">Juegos favoritos:</string><string name="edit_profile_button">Editar Perfil</string><string name="about_me_label">Acerca de mí:</string><string name="sample_admob_app_id">ca-app-pub-3940256099942544~3347511713</string><string name="title_activity_matches">MatchesActivity</string><string name="title_activity_profile">ProfileActivity</string><string name="first_fragment_label">Primer Fragmento</string><string name="second_fragment_label">Segundo Fragmento</string><string name="next">Siguiente</string><string name="previous">Anterior</string><string name="profile_button_edit">EDITAR</string><string name="profile_button_save">GUARDAR</string><string name="permission_denied_gallery">Permiso denegado para acceder a la galería</string><string name="avatar_update_success">Avatar actualizado con éxito</string><string name="avatar_update_error">Error al actualizar el avatar</string><string name="logged_out">Sesión cerrada</string><string name="profile_save_success">Perfil guardado con éxito</string><string name="reply">RESPONDER</string><string name="reply_label">Escribe tu respuesta</string><string name="mark_as_read">MARCAR COMO LEÍDO</string><string name="error_loading_gifs">Error cargando GIFs. Por favor, inténtalo de nuevo.</string><string name="title_community">Comunidad</string><string name="title_activity_main_menu">MainMenuActivity</string><string name="title_activity_main_menu_cyber_punk">MainMenuCyberPunk</string><string name="default_notification_channel_id">VMeetChannel</string><string name="create_new_post">Crear nueva publicación</string><string name="title_activity_comments">CommentsActivity</string><string name="personality_traits_title">Rasgos de Personalidad</string><string name="edit_personality_traits">Editar Rasgos</string><string name="trait_icon_description">Icono del rasgo</string><string name="personality_traits_subtitle">Estos rasgos ayudan a otros usuarios a conocerte mejor</string><string name="save_traits">Guardar Rasgos</string><string name="traits_saved_successfully">Rasgos guardados exitosamente</string><string name="error_saving_traits">Error al guardar rasgos</string><string name="error_loading_traits">Error al cargar rasgos</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\styles.xml" qualifiers=""><style name="Theme.VMeet.Dialog.FullWidth" parent="Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style><style name="Theme.VMeet.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:background">@color/darker_blue</item>
        <item name="android:textColorPrimary">@android:color/white</item>
        <item name="android:textColorSecondary">@color/neon_blue</item>
        <item name="colorAccent">@color/neon_pink</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
    </style><style name="AlertDialogButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/neon_pink</item>
    </style><style name="CustomSwitchStyle" parent="Theme.AppCompat">
        <item name="colorAccent">@color/neon_blue</item>
        <item name="colorControlActivated">@color/neon_blue</item>
        <item name="colorSwitchThumbNormal">@color/text_secondary</item>
        <item name="android:colorForeground">@color/text_secondary</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\text_styles.xml" qualifiers=""><style name="TextAppearance.VMeet" parent="TextAppearance.AppCompat">
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
    </style><style name="TextAppearance.VMeet.Headline1">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.VMeet.Headline2">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.VMeet.Headline3">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.VMeet.Body1">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.VMeet.Body2">
        <item name="android:textSize">14sp</item>
    </style><style name="TextAppearance.VMeet.Caption">
        <item name="android:textSize">12sp</item>
    </style><style name="TextAppearance.VMeet.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.VMeet" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="android:itemTextAppearance">@style/MenuTextStyle</item>
        <item name="popupMenuStyle">@style/PopupMenu</item>

        
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>        
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:statusBarColor">@color/darker_blue</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="MenuTextStyle">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:fontFamily">@font/app_font</item>
    </style><style name="VMeetTextStyle">
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
    </style><style name="VMeetTextStyle.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="VMeetTextStyle.Subtitle">
        <item name="android:textSize">16sp</item>
    </style><style name="VMeetTextStyle.Body">
        <item name="android:textSize">14sp</item>
    </style><style name="PopupMenu" parent="@style/Widget.Material3.PopupMenu">
        <item name="android:popupBackground">#212121</item>
    </style><style name="CustomBottomNavigation" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">#1A1A3A</item>
        <item name="itemIconTint">@color/comfy_blue</item>
        <item name="itemTextColor">@color/comfy_blue</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.VMeet.Caption</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.VMeet.Caption</item>
    </style><style name="CustomPurpleButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/modern_button</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
        <item name="android:textAppearance">@style/TextAppearance.VMeet.Button</item>
    </style><style name="CustomAltButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/modern_button_alt</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
        <item name="android:textAppearance">@style/TextAppearance.VMeet.Button</item>
    </style><style name="ShapeAppearance.App.RoundedButton" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style><style name="ShapeAppearance.App.RoundedGif" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style><style name="Theme.VMeet.NoActionBar" parent="Theme.VMeet">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style><style name="VMeetToolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">#1A1A3A</item>
        <item name="android:elevation">4dp</item>
        <item name="android:paddingTop">0dp</item>
        
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleTextColor">@color/comfy_blue</item>
        <item name="titleTextAppearance">@style/TextAppearance.VMeet.Headline2</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\values-v29\dimens.xml" qualifiers="v29"><dimen name="status_bar_height">@*android:dimen/status_bar_height</dimen></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\generated\res\processReleaseGoogleServices"><file path="C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">988461270314</string><string name="google_api_key" translatable="false">AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg</string><string name="google_app_id" translatable="false">1:988461270314:android:d0242913a2e29332704a53</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg</string><string name="google_storage_bucket" translatable="false">vmeet-5c6a2.firebasestorage.app</string><string name="project_id" translatable="false">vmeet-5c6a2</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems><configuration qualifiers=""><declare-styleable name="PurpleButton">
        <attr format="boolean" name="isAlt"/>
    </declare-styleable><declare-styleable name="PixelPerfectTextView">
        <attr name="android:text"/>
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
    </declare-styleable></configuration></mergedItems></merger>