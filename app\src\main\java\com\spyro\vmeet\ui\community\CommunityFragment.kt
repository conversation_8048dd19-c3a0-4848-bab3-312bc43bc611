package com.spyro.vmeet.ui.community

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.Toast
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.spyro.vmeet.R
import com.spyro.vmeet.adapter.StoriesAdapter
import com.spyro.vmeet.data.MessageReaction // For dummy data
import com.spyro.vmeet.data.community.Post
import com.spyro.vmeet.FullScreenImageActivity
import com.spyro.vmeet.ui.community.StoryViewerActivity
import com.spyro.vmeet.ui.community.StoryEditorActivity

class CommunityFragment : Fragment(), CreatePostDialog.CreatePostListener {

    private lateinit var recyclerViewPosts: RecyclerView
    private lateinit var postAdapter: PostAdapter
    private lateinit var recyclerViewStories: RecyclerView
    private lateinit var storiesAdapter: StoriesAdapter
    private lateinit var progressBar: ProgressBar
    private lateinit var fabCreatePost: FloatingActionButton
    private lateinit var buttonAddStory: Button
    private lateinit var viewModel: CommunityViewModel
    private var userId: Int = -1
    private var username: String = "Usuario"

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Try to inflate the layout with error handling
        val view: View
        try {
            // Inflate the layout for this fragment
            view = inflater.inflate(R.layout.fragment_community, container, false)
        } catch (e: Exception) {
            Log.e("CommunityFragment", "Error inflating fragment_community layout", e)

            // Create a fallback layout programmatically
            return createFallbackLayout(container)
        }

        try {
            recyclerViewPosts = view.findViewById(R.id.recyclerViewPosts)
            recyclerViewStories = view.findViewById(R.id.recyclerViewStories)
            progressBar = view.findViewById(R.id.progressBarCommunity)
            fabCreatePost = view.findViewById(R.id.fabCreatePost)
            buttonAddStory = view.findViewById(R.id.buttonAddStory)

            // Get user ID from shared preferences
            val prefs = requireActivity().getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", -1)
            username = prefs.getString("USERNAME", "Usuario") ?: "Usuario"

            // Initialize ViewModel
            viewModel = ViewModelProvider(this)[CommunityViewModel::class.java]

            setupRecyclerView()
            setupStoriesRecyclerView()
            observeViewModel()
            setupListeners()

            if (userId > 0) { // Ensure userId is valid before loading
                viewModel.loadStories(userId)
            } else {
                Log.w("CommunityFragment", "Invalid userId: $userId, not loading stories.")
                // Optionally show a message to the user to log in to see stories
            }
        } catch (e: Exception) {
            Log.e("CommunityFragment", "Error setting up community fragment", e)
            return createFallbackLayout(container)
        }

        return view
    }

    private fun createFallbackLayout(container: ViewGroup?): View {
        Log.d("CommunityFragment", "Creating fallback layout for community fragment")

        // Create root layout
        val context = requireContext()
        val rootLayout = androidx.constraintlayout.widget.ConstraintLayout(context)
        rootLayout.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        rootLayout.setBackgroundColor(context.resources.getColor(R.color.cyberpunk_background, context.theme))

        // Create a simple toolbar
        val toolbar = androidx.appcompat.widget.Toolbar(context)
        toolbar.id = View.generateViewId()
        val toolbarParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            context.resources.getDimensionPixelSize(android.R.dimen.app_icon_size)
        )
        toolbarParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        toolbarParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        toolbarParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        toolbar.layoutParams = toolbarParams
        toolbar.setBackgroundColor(context.resources.getColor(R.color.cyberpunk_card_background, context.theme))

        // Add a title to the toolbar
        val titleTextView = android.widget.TextView(context)
        titleTextView.text = "Comunidad"
        titleTextView.setTextColor(context.resources.getColor(R.color.neon_blue, context.theme))
        titleTextView.textSize = 20f
        val titleParams = androidx.appcompat.widget.Toolbar.LayoutParams(
            androidx.appcompat.widget.Toolbar.LayoutParams.WRAP_CONTENT,
            androidx.appcompat.widget.Toolbar.LayoutParams.WRAP_CONTENT
        )
        titleParams.gravity = android.view.Gravity.CENTER
        titleTextView.layoutParams = titleParams
        toolbar.addView(titleTextView)

        rootLayout.addView(toolbar)

        // Create a compatibility message TextView
        val messageTextView = android.widget.TextView(context)
        messageTextView.id = View.generateViewId()
        val messageParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        messageParams.topToBottom = toolbar.id
        messageParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        messageParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        messageParams.topMargin = 8
        messageParams.leftMargin = 16
        messageParams.rightMargin = 16
        messageTextView.layoutParams = messageParams
        messageTextView.text = "Modo de compatibilidad activado"
        messageTextView.setTextColor(context.resources.getColor(R.color.neon_blue, context.theme))
        messageTextView.textSize = 14f
        messageTextView.gravity = android.view.Gravity.CENTER
        messageTextView.setPadding(8, 8, 8, 8)
        messageTextView.setBackgroundColor(context.resources.getColor(R.color.cyberpunk_card_background, context.theme))

        rootLayout.addView(messageTextView)

        // Create "Add Story" button
        val addStoryButton = android.widget.Button(context)
        addStoryButton.id = View.generateViewId()
        val addStoryParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT,
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        addStoryParams.topToBottom = messageTextView.id
        addStoryParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        addStoryParams.topMargin = 16
        addStoryParams.leftMargin = 16
        addStoryButton.layoutParams = addStoryParams
        addStoryButton.text = "+ Historia"
        addStoryButton.setBackgroundColor(context.resources.getColor(R.color.neon_blue, context.theme))
        addStoryButton.setTextColor(context.resources.getColor(android.R.color.white, context.theme))

        addStoryButton.setOnClickListener {
            showStoryMediaPicker()
        }

        rootLayout.addView(addStoryButton)

        // Create horizontal ScrollView for stories
        val storiesScrollView = android.widget.HorizontalScrollView(context)
        storiesScrollView.id = View.generateViewId()
        val storiesScrollParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        storiesScrollParams.topToBottom = addStoryButton.id
        storiesScrollParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        storiesScrollParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        storiesScrollParams.topMargin = 16
        storiesScrollView.layoutParams = storiesScrollParams

        // Create LinearLayout for stories
        val storiesLayout = android.widget.LinearLayout(context)
        storiesLayout.orientation = android.widget.LinearLayout.HORIZONTAL
        storiesLayout.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        storiesLayout.setPadding(16, 8, 16, 8)

        storiesScrollView.addView(storiesLayout)
        rootLayout.addView(storiesScrollView)

        // Create divider
        val divider = View(context)
        divider.id = View.generateViewId()
        val dividerParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            1
        )
        dividerParams.topToBottom = storiesScrollView.id
        dividerParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        dividerParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        dividerParams.topMargin = 8
        divider.layoutParams = dividerParams
        divider.setBackgroundColor(context.resources.getColor(R.color.cyberpunk_divider, context.theme))

        rootLayout.addView(divider)

        // Create ScrollView for posts
        val postsScrollView = androidx.core.widget.NestedScrollView(context)
        postsScrollView.id = View.generateViewId()
        val postsScrollParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
            0
        )
        postsScrollParams.topToBottom = divider.id
        postsScrollParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        postsScrollParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        postsScrollParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        postsScrollParams.topMargin = 8
        postsScrollView.layoutParams = postsScrollParams

        // Create LinearLayout for posts
        val postsLayout = android.widget.LinearLayout(context)
        postsLayout.id = View.generateViewId()
        postsLayout.orientation = android.widget.LinearLayout.VERTICAL
        postsLayout.layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        postsLayout.setPadding(16, 8, 16, 8)

        postsScrollView.addView(postsLayout)
        rootLayout.addView(postsScrollView)

        // Create FAB for creating posts
        val fabCreatePost = com.google.android.material.floatingactionbutton.FloatingActionButton(context)
        fabCreatePost.id = View.generateViewId()
        val fabParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT,
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        fabParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        fabParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        fabParams.bottomMargin = 16
        fabParams.rightMargin = 16
        fabCreatePost.layoutParams = fabParams
        fabCreatePost.setBackgroundColor(context.resources.getColor(R.color.neon_pink, context.theme))
        fabCreatePost.setImageResource(android.R.drawable.ic_input_add)
        fabCreatePost.setColorFilter(context.resources.getColor(android.R.color.white, context.theme))

        fabCreatePost.setOnClickListener {
            showCreatePostDialog()
        }

        rootLayout.addView(fabCreatePost)

        // Create a progress bar
        val progressBar = android.widget.ProgressBar(context)
        progressBar.id = View.generateViewId()
        val progressParams = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT,
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT
        )
        progressParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        progressParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        progressParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        progressParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        progressBar.layoutParams = progressParams
        progressBar.visibility = View.GONE

        rootLayout.addView(progressBar)

        // Initialize required views
        this.progressBar = progressBar
        this.fabCreatePost = fabCreatePost
        this.buttonAddStory = addStoryButton

        // Initialize ViewModel and load data
        try {
            val prefs = requireActivity().getSharedPreferences("VMeetPrefs", Context.MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", -1)
            username = prefs.getString("USERNAME", "Usuario") ?: "Usuario"

            viewModel = ViewModelProvider(this)[CommunityViewModel::class.java]

            // Set up observers
            viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
                progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            }

            viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
                errorMessage?.let {
                    Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
                }
            }

            // Observe stories
            viewModel.stories.observe(viewLifecycleOwner) { stories ->
                // Clear existing stories
                storiesLayout.removeAllViews()

                if (stories.isEmpty()) {
                    val emptyText = android.widget.TextView(context)
                    emptyText.text = "No hay historias disponibles"
                    emptyText.setTextColor(context.resources.getColor(android.R.color.white, context.theme))
                    emptyText.setPadding(16, 8, 16, 8)
                    storiesLayout.addView(emptyText)
                } else {
                    // Add stories to horizontal layout
                    for (story in stories) {
                        val storyView = android.widget.ImageView(context)
                        storyView.layoutParams = ViewGroup.MarginLayoutParams(
                            100,
                            100
                        ).apply {
                            marginStart = 8
                            marginEnd = 8
                        }
                        storyView.setBackgroundColor(context.resources.getColor(R.color.cyberpunk_card_background, context.theme))
                        storyView.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP

                        // Try to load story image if available
                        try {
                            // Safely handle the media URL
                            val mediaUrl = story.mediaUrl ?: ""
                            if (mediaUrl.isNotEmpty()) {
                                val fullUrl = if (mediaUrl.startsWith("http")) {
                                    mediaUrl
                                } else {
                                    "http://77.110.116.89:3000$mediaUrl"
                                }

                                com.bumptech.glide.Glide.with(context)
                                    .load(fullUrl)
                                    .placeholder(R.drawable.image_placeholder)
                                    .error(R.drawable.ic_placeholder_image)
                                    .into(storyView)
                            } else {
                                storyView.setImageResource(R.drawable.ic_placeholder_image)
                            }
                        } catch (e: Exception) {
                            Log.e("CommunityFragment", "Error loading story image", e)
                            storyView.setImageResource(R.drawable.ic_placeholder_image)
                        }

                        // Set click listener
                        storyView.setOnClickListener {
                            if (userId > 0) {
                                val intent = StoryViewerActivity.newIntent(requireContext(), story, userId)
                                startActivity(intent)
                            } else {
                                Toast.makeText(context, "Por favor inicia sesión para ver historias", Toast.LENGTH_SHORT).show()
                            }
                        }

                        storiesLayout.addView(storyView)
                    }
                }
            }

            // Observe posts
            viewModel.posts.observe(viewLifecycleOwner) { posts ->
                // Clear existing posts
                postsLayout.removeAllViews()

                if (posts.isEmpty()) {
                    val emptyText = android.widget.TextView(context)
                    emptyText.text = "No hay publicaciones disponibles"
                    emptyText.setTextColor(context.resources.getColor(android.R.color.white, context.theme))
                    emptyText.setPadding(16, 8, 16, 8)
                    postsLayout.addView(emptyText)
                } else {
                    // Add posts to vertical layout
                    for (post in posts) {
                        // Create card for post
                        val cardView = androidx.cardview.widget.CardView(context)
                        cardView.layoutParams = ViewGroup.MarginLayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        ).apply {
                            bottomMargin = 16
                        }
                        cardView.radius = 8f
                        cardView.setCardBackgroundColor(context.resources.getColor(R.color.cyberpunk_card_background, context.theme))
                        cardView.cardElevation = 4f

                        // Create post content layout
                        val postLayout = android.widget.LinearLayout(context)
                        postLayout.orientation = android.widget.LinearLayout.VERTICAL
                        postLayout.layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.WRAP_CONTENT
                        )
                        postLayout.setPadding(16, 16, 16, 16)

                        // Add username
                        val usernameText = android.widget.TextView(context)
                        usernameText.text = post.username
                        usernameText.setTextColor(context.resources.getColor(R.color.neon_blue, context.theme))
                        usernameText.textSize = 16f
                        usernameText.setTypeface(null, android.graphics.Typeface.BOLD)
                        postLayout.addView(usernameText)

                        // Add post content
                        val contentText = android.widget.TextView(context)
                        contentText.text = post.textContent
                        contentText.setTextColor(context.resources.getColor(android.R.color.white, context.theme))
                        contentText.textSize = 14f
                        contentText.setPadding(0, 8, 0, 8)
                        postLayout.addView(contentText)

                        // Add image if available
                        if (!post.imageUrl.isNullOrEmpty() && post.imageUrl != "null") {
                            val imageView = android.widget.ImageView(context)
                            imageView.layoutParams = ViewGroup.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                300
                            )
                            imageView.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP

                            try {
                                // Safely handle the image URL
                                val imageUrl = post.imageUrl ?: ""
                                val fullUrl = if (imageUrl.startsWith("http")) {
                                    imageUrl
                                } else {
                                    "http://77.110.116.89:3000$imageUrl"
                                }

                                com.bumptech.glide.Glide.with(context)
                                    .load(fullUrl)
                                    .placeholder(R.drawable.image_placeholder)
                                    .error(R.drawable.ic_placeholder_image)
                                    .into(imageView)

                                // Set click listener to open full screen
                                imageView.setOnClickListener {
                                    val intent = Intent(requireContext(), FullScreenImageActivity::class.java).apply {
                                        putExtra(FullScreenImageActivity.EXTRA_IMAGE_URL, fullUrl)
                                    }
                                    startActivity(intent)
                                }
                            } catch (e: Exception) {
                                Log.e("CommunityFragment", "Error loading post image", e)
                                imageView.setImageResource(R.drawable.ic_placeholder_image)
                            }

                            postLayout.addView(imageView)
                        }

                        // Add reactions count
                        val reactionsText = android.widget.TextView(context)
                        val reactionsCount = post.reactions?.size ?: 0
                        reactionsText.text = "👍 $reactionsCount"
                        reactionsText.setTextColor(context.resources.getColor(android.R.color.white, context.theme))
                        reactionsText.textSize = 14f
                        reactionsText.setPadding(0, 8, 0, 0)
                        postLayout.addView(reactionsText)

                        // Add the post layout to the card
                        cardView.addView(postLayout)

                        // Add the card to the posts layout
                        postsLayout.addView(cardView)
                    }
                }
            }

            // Load data if user ID is valid
            if (userId > 0) {
                viewModel.loadStories(userId)
                viewModel.startPolling() // Start polling for posts
            } else {
                Log.w("CommunityFragment", "Invalid userId: $userId, not loading stories or posts.")
                Toast.makeText(context, "Por favor inicia sesión para ver el contenido completo", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e("CommunityFragment", "Error initializing ViewModel in fallback layout", e)
            Toast.makeText(context, "Error al cargar datos: ${e.message}", Toast.LENGTH_SHORT).show()
        }

        // Show a toast to inform the user
        Toast.makeText(context, "Modo de compatibilidad activado", Toast.LENGTH_LONG).show()

        return rootLayout
    }

    private fun setupRecyclerView() {
        postAdapter = PostAdapter(mutableListOf())
        recyclerViewPosts.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = postAdapter
        }

        // Setup item click listeners
        postAdapter.onLikeClickListener = { post ->
            viewModel.likePost(post.id, userId)
        }

        postAdapter.onCommentClickListener = { post ->
            // Launch the comments activity
            val intent = CommentsActivity.newIntent(requireContext(), post.id)
            startActivity(intent)
        }

        postAdapter.onUserClickListener = { post ->
            // Navigate to the user profile
            val intent = Intent(requireContext(), com.spyro.vmeet.ProfileActivity::class.java)
            intent.putExtra("USER_ID", post.userId)
            intent.putExtra("VIEW_ONLY_MODE", true)
            startActivity(intent)
        }

        postAdapter.onDeleteClickListener = { post ->
            viewModel.deletePost(post.id, userId)
        }

        postAdapter.onImageClickListener = { post ->
            val currentImageUrl = post.imageUrl
            if (!currentImageUrl.isNullOrEmpty() && currentImageUrl != "null") {
                val fullImageUrl = if (currentImageUrl.startsWith("http")) {
                    currentImageUrl
                } else {
                    "http://77.110.116.89:3000" + currentImageUrl // Construct full URL
                }
                val intent = Intent(requireContext(), FullScreenImageActivity::class.java).apply {
                    putExtra(FullScreenImageActivity.EXTRA_IMAGE_URL, fullImageUrl)
                }
                startActivity(intent)
            } else {
                Toast.makeText(context, "No image to display", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupStoriesRecyclerView() {
        storiesAdapter = StoriesAdapter(mutableListOf()) { story ->
            // Handle story click: open story viewer
            if (userId > 0) {
                val intent = StoryViewerActivity.newIntent(requireContext(), story, userId)
                startActivity(intent)
            } else {
                Toast.makeText(context, "Please log in to view stories", Toast.LENGTH_SHORT).show()
            }
        }
        recyclerViewStories.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = storiesAdapter

            // Add item decoration for visual separation between story items
            val horizontalSpacing = resources.getDimensionPixelSize(R.dimen.story_item_spacing)
            addItemDecoration(HorizontalSpaceItemDecoration(horizontalSpacing))
        }
    }

    private fun observeViewModel() {
        viewModel.posts.observe(viewLifecycleOwner) { posts ->
            postAdapter.updatePosts(posts)
        }

        viewModel.stories.observe(viewLifecycleOwner) { stories ->
            storiesAdapter.updateStories(stories)
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            errorMessage?.let {
                Toast.makeText(context, it, Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun setupListeners() {
        fabCreatePost.setOnClickListener {
            showCreatePostDialog()
        }
        buttonAddStory.setOnClickListener {
            if (userId <= 0) {
                Toast.makeText(context, "Please log in to create a story", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // Create and show the media picker dialog
            showStoryMediaPicker()
        }
    }

    private fun showCreatePostDialog() {
        val dialog = CreatePostDialog()
        dialog.show(childFragmentManager, "CreatePostDialog")
    }

    private fun showStoryMediaPicker() {
        val options = arrayOf("Imagen", "Video")
        AlertDialog.Builder(requireContext())
            .setTitle("Seleccionar Tipo de Historia")
            .setItems(options) { dialog, which ->
                when (which) {
                    0 -> pickImageForStory()
                    1 -> pickVideoForStory()
                }
            }
            .setNegativeButton("Cancelar", null)
            .show()
    }

    // Constants for request codes
    companion object {
        private const val REQUEST_IMAGE_PICK = 1001
        private const val REQUEST_VIDEO_PICK = 1002
        private const val REQUEST_STORY_EDITED = 1003 // New request code
    }

    private fun pickImageForStory() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        intent.type = "image/*"
        startActivityForResult(Intent.createChooser(intent, "Seleccionar Imagen"), REQUEST_IMAGE_PICK)
    }

    private fun pickVideoForStory() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Video.Media.EXTERNAL_CONTENT_URI)
        intent.type = "video/*"
        startActivityForResult(Intent.createChooser(intent, "Seleccionar Video"), REQUEST_VIDEO_PICK)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == android.app.Activity.RESULT_OK && data != null) {
            val mediaUri = data.data

            if (mediaUri == null) {
                Toast.makeText(context, "Error seleccionando media", Toast.LENGTH_SHORT).show()
                return
            }

            when (requestCode) {
                REQUEST_IMAGE_PICK -> {
                    // Launch StoryEditorActivity for images
                    val intent = StoryEditorActivity.newIntent(requireContext(), mediaUri, "image")
                    // Use a new request code for the editor result
                    startActivityForResult(intent, REQUEST_STORY_EDITED)
                }
                REQUEST_VIDEO_PICK -> {
                    // For videos, directly upload for now as editor doesn't fully support video
                    uploadStory(mediaUri, "video")
                }
                REQUEST_STORY_EDITED -> {
                    // This case might be used if StoryEditorActivity returns a result, e.g. success
                    // For now, StoryEditorActivity handles the upload itself.
                    // If it finishes successfully, we might want to refresh stories here.
                    Log.d("CommunityFragment", "Story editing finished, resultCode: $resultCode")
                    if (resultCode == android.app.Activity.RESULT_OK) {
                         viewModel.loadStories(userId) // Refresh stories
                    }
                }
            }
        }
    }

    private fun uploadStory(mediaUri: Uri, mediaType: String) {
        if (userId <= 0) {
            Toast.makeText(context, "Please log in to upload a story", Toast.LENGTH_SHORT).show()
            return
        }

        progressBar.visibility = View.VISIBLE
        Toast.makeText(context, "Subiendo historia...", Toast.LENGTH_SHORT).show()

        viewModel.createStory(userId, username, mediaUri, mediaType, null)
    }

    // Update interface to include GIF parameters
    interface CreatePostListener {
        fun onPostCreated(
            postContent: String,
            imageUri: Uri? = null,
            voiceNoteUri: Uri? = null,
            voiceNoteDuration: Int? = null,
            gifUrl: String? = null,
            gifPreviewUrl: String? = null
        )
    }

    // Default implementation for backward compatibility (can be removed if not needed elsewhere)
    fun onPostCreated(postContent: String) {
        // This might be called if CreatePostDialog's listener isn't correctly overridden by the new one.
        // For safety, call the more specific version or handle appropriately.
        onPostCreated(postContent, null, null, null, null, null)
    }

    // New implementation that handles images, voice notes, and GIFs
    override fun onPostCreated(
        postContent: String,
        imageUri: Uri?,
        voiceNoteUri: Uri?,
        voiceNoteDuration: Int?,
        gifUrl: String?,
        gifPreviewUrl: String?
    ) {
        if (userId <= 0) {
            Toast.makeText(context, "Por favor inicia sesión para crear publicaciones", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d("CommunityFragment", "Post Content: $postContent")
        imageUri?.let { Log.d("CommunityFragment", "Image URI: $it") }
        voiceNoteUri?.let { Log.d("CommunityFragment", "Voice Note URI: $it, Duration: $voiceNoteDuration seconds") }
        gifUrl?.let { Log.d("CommunityFragment", "GIF URL: $it") }

        val finalPostContent = postContent.ifEmpty { " " } // Ensure content is not empty for backend validation

        // GIF post (highest priority)
        if (gifUrl != null) {
            viewModel.createPost(
                userId = userId,
                username = username,
                content = finalPostContent,
                gifUrl = gifUrl,
                gifPreviewUrl = gifPreviewUrl
            )
            Toast.makeText(context, "¡Publicación con GIF creada!", Toast.LENGTH_SHORT).show()
            return
        }

        // Handle other types of posts as before
        if (imageUri == null && voiceNoteUri == null) {
            // No media, just create text post
            viewModel.createPost(userId, username, finalPostContent)
            Toast.makeText(context, "¡Publicación creada!", Toast.LENGTH_SHORT).show()
        } else if (imageUri != null && voiceNoteUri == null) {
            // Image only
            progressBar.visibility = View.VISIBLE
            viewModel.uploadImage(
                imageUri = imageUri,
                onSuccess = { imageUrl ->
                    viewModel.createPost(userId, username, finalPostContent, imageUrl = imageUrl)
                    activity?.runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(context, "¡Publicación con imagen creada!", Toast.LENGTH_SHORT).show()
                    }
                },
                onError = { errorMessage ->
                    activity?.runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(context, "Error al subir la imagen: $errorMessage", Toast.LENGTH_SHORT).show()
                    }
                }
            )
        } else if (imageUri == null && voiceNoteUri != null && voiceNoteDuration != null && voiceNoteDuration > 0) {
            // Voice note only
            progressBar.visibility = View.VISIBLE
            viewModel.uploadVoiceNote(
                audioFileUri = voiceNoteUri,
                duration = voiceNoteDuration,
                onSuccess = { voiceUrl, duration ->
                    Log.d("CommunityFragment", "Voice upload success (voice only). URL: $voiceUrl, Duration: $duration. Calling createPost.")
                    viewModel.createPost(userId, username, finalPostContent, voiceNoteUrl = voiceUrl, voiceNoteDuration = duration)
                    activity?.runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(context, "¡Publicación con nota de voz creada!", Toast.LENGTH_SHORT).show()
                    }
                },
                onError = { errorMessage ->
                    activity?.runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(context, "Error al subir la nota de voz: $errorMessage", Toast.LENGTH_SHORT).show()
                    }
                }
            )
        } else if (imageUri != null && voiceNoteUri != null && voiceNoteDuration != null && voiceNoteDuration > 0) {
            // Both image and voice note
            progressBar.visibility = View.VISIBLE
            Toast.makeText(context, "Subiendo imagen...", Toast.LENGTH_SHORT).show()
            viewModel.uploadImage(
                imageUri = imageUri,
                onSuccess = { imageUrl ->
                    activity?.runOnUiThread {
                        Toast.makeText(context, "Imagen subida. Subiendo nota de voz...", Toast.LENGTH_SHORT).show()
                    }
                    viewModel.uploadVoiceNote(
                        audioFileUri = voiceNoteUri,
                        duration = voiceNoteDuration,
                        onSuccess = { voiceUrl, duration ->
                            Log.d("CommunityFragment", "Voice upload success (image+voice). URL: $voiceUrl, Duration: $duration. Calling createPost with image: $imageUrl")
                            viewModel.createPost(userId, username, finalPostContent, imageUrl = imageUrl, voiceNoteUrl = voiceUrl, voiceNoteDuration = duration)
                            activity?.runOnUiThread {
                                progressBar.visibility = View.GONE
                                Toast.makeText(context, "¡Publicación con imagen y nota de voz creada!", Toast.LENGTH_SHORT).show()
                            }
                        },
                        onError = { voiceErrorMessage ->
                            activity?.runOnUiThread {
                                progressBar.visibility = View.GONE
                                Toast.makeText(context, "Error al subir nota de voz: $voiceErrorMessage. Se publicará solo con imagen.", Toast.LENGTH_LONG).show()
                                // Optionally create post with just image if voice upload fails
                                viewModel.createPost(userId, username, finalPostContent, imageUrl = imageUrl)
                            }
                        }
                    )
                },
                onError = { imageErrorMessage ->
                    activity?.runOnUiThread {
                        progressBar.visibility = View.GONE
                        Toast.makeText(context, "Error al subir imagen: $imageErrorMessage. No se creará la publicación.", Toast.LENGTH_LONG).show()
                    }
                }
            )
        } else {
             // This case might happen if voiceNoteUri is present but duration is 0 or null, or some other unexpected combination.
             // Fallback to text-only post or image-only if image is present.
            if (imageUri != null) {
                 // Try to post with image if voice note details are incomplete
                Log.w("CommunityFragment", "Incomplete voice note details. Attempting to post with image only.")
                onPostCreated(postContent, imageUri, null, null, null, null) // Recurse with image only
            } else {
                Log.w("CommunityFragment", "Incomplete media details. Creating text-only post.")
                viewModel.createPost(userId, username, finalPostContent)
                Toast.makeText(context, "Error en adjuntos, publicación creada solo con texto.", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // Start or restart polling when fragment becomes visible
        viewModel.startPolling()
    }

    override fun onPause() {
        super.onPause()
        // Stop polling when fragment is no longer visible to save resources
        viewModel.stopPolling()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        if (::postAdapter.isInitialized) { // Check if postAdapter has been initialized
            postAdapter.releaseAllPlayers()
        }
        // If using ViewBinding, set the binding to null here: e.g., _binding = null
    }

    private fun getDummyPosts(): List<Post> {
        val posts = mutableListOf<Post>()
        val currentTime = System.currentTimeMillis()

        posts.add(Post(
            id = "1", userId = 101, username = "CyberRunnerX", userAvatar = null, // Add avatar URLs later
            timestamp = currentTime - (1000 * 60 * 5), // 5 minutes ago
            textContent = "Just explored the neon alleys of Neo-Kyoto. The rain always makes the city lights pop! #cyberpunk #neokyoto",
            commentCount = 5,
            reactions = mutableListOf(MessageReaction(1, 1, 102, "\uD83D\uDC4D", ""), MessageReaction(2, 1, 103, "\uD83D\uDD25", ""))
        ))
        posts.add(Post(
            id = "2", userId = 102, username = "GlitchWitch", userAvatar = null,
            timestamp = currentTime - (1000 * 60 * 60 * 2), // 2 hours ago
            textContent = "My latest synthwave track just dropped. Inspired by late-night drives through the digital rain. Link in bio!",
            imageUrl = "https://via.placeholder.com/600x300.png/2C2C54/E0E0FF?Text=Synthwave+Album+Art", // Placeholder image
            commentCount = 12,
            reactions = mutableListOf()
        ))
        posts.add(Post(
            id = "3", userId = 103, username = "Retrograde", userAvatar = null,
            timestamp = currentTime - (1000 * 60 * 60 * 24), // 1 day ago
            textContent = "Anyone else obsessed with classic 80s cyberpunk films? Blade Runner still holds up!",
            commentCount = 23
        ))
        // Add more dummy posts as needed
        return posts
    }

    // Custom item decoration for horizontal spacing
    private class HorizontalSpaceItemDecoration(private val spacing: Int) : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: android.graphics.Rect,
            view: View,
            parent: RecyclerView,
            state: RecyclerView.State
        ) {
            // Add spacing to right side of each item except the last one
            val position = parent.getChildAdapterPosition(view)
            if (position != parent.adapter?.itemCount?.minus(1)) {
                outRect.right = spacing
            }
        }
    }
}