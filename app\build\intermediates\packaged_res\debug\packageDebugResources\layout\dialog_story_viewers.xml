<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/darker_blue"
    android:padding="16dp">

    <TextView
        android:id="@+id/titleText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Visto por"
        android:textColor="@color/neon_blue"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/closeButton"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        android:tint="@color/neon_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:contentDescription="Close" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/viewersRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@id/titleText"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:listitem="@layout/item_story_viewer"
        tools:itemCount="3" />

    <TextView
        android:id="@+id/emptyViewersText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Aún no hay visualizaciones"
        android:textColor="@android:color/darker_gray"
        android:textSize="16sp"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintTop_toBottomOf="@id/titleText"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 