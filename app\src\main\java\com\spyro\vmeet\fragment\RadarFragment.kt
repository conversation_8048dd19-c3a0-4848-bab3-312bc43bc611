package com.spyro.vmeet.fragment

import android.Manifest
import android.app.AlertDialog
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.gms.location.*
import com.spyro.vmeet.R
import com.spyro.vmeet.adapter.RadarUserAdapter
import com.spyro.vmeet.data.RadarFilters
import com.spyro.vmeet.data.RadarUser
import com.spyro.vmeet.ui.base.BaseFragment
import com.spyro.vmeet.ui.custom.GlobalTextViewReplacer
import com.spyro.vmeet.utils.LocationUtils
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import org.json.JSONArray
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit

/**
 * Fragment for the Radar feature - location-based user discovery
 */
class RadarFragment : BaseFragment() {

    // UI Components
    private lateinit var recyclerViewUsers: RecyclerView
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var progressBar: ProgressBar
    private lateinit var layoutEmptyState: View
    private lateinit var layoutPermissionRequest: View
    private lateinit var layoutLocationStatus: LinearLayout
    private lateinit var textViewLocationStatus: TextView
    private lateinit var buttonFilter: ImageButton
    private lateinit var buttonRefreshLocation: ImageButton
    private lateinit var buttonRequestPermission: Button
    private lateinit var buttonExpandSearch: Button

    // Adapter and Data
    private lateinit var radarAdapter: RadarUserAdapter
    private val nearbyUsers = mutableListOf<RadarUser>()
    private var currentFilters = RadarFilters.getDefaultFilters()

    // Location
    private lateinit var fusedLocationClient: FusedLocationProviderClient
    private var currentLocation: android.location.Location? = null
    private var locationCallback: LocationCallback? = null

    // User data
    private var userId: Int = 0    // Network
    private val client = OkHttpClient.Builder()
        .connectTimeout(15, TimeUnit.SECONDS)
        .readTimeout(45, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()

    // Retry mechanism
    private var retryCount = 0
    private val maxRetries = 3
    private var retryHandler: Handler? = null

    // Handlers
    private val mainHandler = Handler(Looper.getMainLooper())
    private var refreshHandler: Handler? = null
    private var refreshRunnable: Runnable? = null

    // Control variables para evitar actualizaciones concurrentes
    private var isUpdatingUsersList = false
    private var pendingUpdate = false
    private var lastUpdateTimestamp = 0L

    companion object {
        private const val TAG = "RadarFragment"
        private const val API_URL = "http://77.110.116.89:3000"
        private const val LOCATION_PERMISSION_REQUEST_CODE = 1001
        private const val AUTO_REFRESH_INTERVAL = 30000L // 30 seconds
        private const val MIN_UPDATE_INTERVAL = 5000L // 5 segundos mínimo entre actualizaciones

        fun newInstance(userId: Int): RadarFragment {
            val fragment = RadarFragment()
            val args = Bundle()
            args.putInt("USER_ID", userId)
            fragment.arguments = args
            return fragment
        }
    }

    // Modern permission launcher
    private val locationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        Log.d(TAG, "Permission launcher result: $permissions")

        val fineLocationGranted = permissions[Manifest.permission.ACCESS_FINE_LOCATION] ?: false
        val coarseLocationGranted = permissions[Manifest.permission.ACCESS_COARSE_LOCATION] ?: false

        if (fineLocationGranted || coarseLocationGranted) {
            Log.d(TAG, "Location permission granted via launcher, initializing radar...")

            // Hide permission request immediately
            hidePermissionRequest()

            // Show loading state
            showLoading(true)

            // Start location services with a small delay to ensure UI is updated
            mainHandler.postDelayed({
                try {
                    getCurrentLocation()
                    // Also start auto-refresh to ensure continuous updates
                    startAutoRefresh()
                } catch (e: Exception) {
                    Log.e(TAG, "Error starting location services after permission grant", e)
                    showError("Error iniciando servicios de ubicación")
                    showLoading(false)
                }
            }, 100)

            Toast.makeText(
                context,
                "Permisos concedidos. Obteniendo ubicación...",
                Toast.LENGTH_SHORT
            ).show()

        } else {
            Log.w(TAG, "Location permission denied via launcher")
            showPermissionRequest()
            Toast.makeText(
                context,
                "Los permisos de ubicación son necesarios para usar el Radar",
                Toast.LENGTH_LONG
            ).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            userId = it.getInt("USER_ID", 0)
        }

        // Initialize location client
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity())
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_radar, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initializeViews(view)
        setupRecyclerView()
        setupSwipeRefresh()
        setupClickListeners()

        // Check permissions and start location services
        checkLocationPermissions()
    }

    private fun initializeViews(view: View) {
        recyclerViewUsers = view.findViewById(R.id.recyclerViewUsers)
        swipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout)
        progressBar = view.findViewById(R.id.progressBar)
        layoutEmptyState = view.findViewById(R.id.layoutEmptyState)
        layoutPermissionRequest = view.findViewById(R.id.layoutPermissionRequest)
        layoutLocationStatus = view.findViewById(R.id.layoutLocationStatus)
        textViewLocationStatus = view.findViewById(R.id.textViewLocationStatus)
        buttonFilter = view.findViewById(R.id.buttonFilter)
        buttonRefreshLocation = view.findViewById(R.id.buttonRefreshLocation)
        buttonRequestPermission = view.findViewById(R.id.buttonRequestPermission)
        buttonExpandSearch = view.findViewById(R.id.buttonExpandSearch)
    }

    private fun setupRecyclerView() {
        radarAdapter = RadarUserAdapter(requireContext(), userId)
        recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = radarAdapter
        }
    }

    private fun setupSwipeRefresh() {
        swipeRefreshLayout.setColorSchemeColors(
            ContextCompat.getColor(requireContext(), R.color.neon_blue)
        )
        swipeRefreshLayout.setOnRefreshListener {
            refreshNearbyUsers()
        }
    }

    private fun setupClickListeners() {
        buttonFilter.setOnClickListener {
            showFiltersDialog()
        }

        buttonRefreshLocation.setOnClickListener {
            getCurrentLocation()
        }

        buttonRequestPermission.setOnClickListener {
            requestLocationPermissions()
        }

        buttonExpandSearch.setOnClickListener {
            expandSearchRadius()
        }

        // Añadir un menú contextual para el botón de refrescar ubicación
        buttonRefreshLocation.setOnLongClickListener {
            showLocationOptionsMenu()
            true
        }
    }

    private fun showLocationOptionsMenu() {
        val popup = PopupMenu(requireContext(), buttonRefreshLocation)
        popup.menuInflater.inflate(R.menu.menu_location_options, popup.menu)

        popup.setOnMenuItemClickListener { item ->
            when (item.itemId) {
                R.id.action_use_gps -> {
                    showToast("Usando GPS para ubicación")
                    forceLocationUpdate()
                    true
                }
                R.id.action_use_ip -> {
                    showToast("Usando geolocalización por IP")
                    forceIPGeolocation()
                    true
                }
                else -> false
            }
        }

        popup.show()
    }

    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    private fun checkLocationPermissions() {
        Log.d(TAG, "Checking location permissions...")

        when {
            hasLocationPermissions() -> {
                Log.d(TAG, "Location permissions already granted")
                hidePermissionRequest()
                showLoading(true)
                // Force location update when entering radar
                forceLocationUpdate()
            }
            else -> {
                Log.d(TAG, "Location permissions not granted, showing permission request")
                showPermissionRequest()
            }
        }
    }

    private fun hasLocationPermissions(): Boolean {
        return ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            requireContext(),
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestLocationPermissions() {
        Log.d(TAG, "Requesting location permissions using modern launcher...")

        // Use the modern permission launcher
        locationPermissionLauncher.launch(
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        )
    }

    private fun showPermissionRequest() {
        layoutPermissionRequest.visibility = View.VISIBLE
        recyclerViewUsers.visibility = View.GONE
        layoutEmptyState.visibility = View.GONE
    }

    private fun hidePermissionRequest() {
        layoutPermissionRequest.visibility = View.GONE
        recyclerViewUsers.visibility = View.VISIBLE
    }

    private fun forceLocationUpdate() {
        Log.d(TAG, "Forcing location update with geocoding...")

        if (!hasLocationPermissions()) {
            showPermissionRequest()
            return
        }

        showLocationStatus("Actualizando ubicación...")

        // Contador para seguimiento de intentos
        var gpsAttempted = false
        var networkAttempted = false

        try {
            // First try to get last known location
            fusedLocationClient.lastLocation.addOnSuccessListener { location ->
                if (location != null) {
                    Log.d(TAG, "Got last known location, updating with geocoding")
                    currentLocation = location
                    updateLocationOnServer(location)
                    loadNearbyUsers()
                    showLocationStatus("Ubicación actualizada")
                    hideLocationStatusAfterDelay()
                    startAutoRefresh()
                } else {
                    Log.d(TAG, "No last known location, requesting fresh location")
                    gpsAttempted = true
                    requestFreshLocation(true)
                }
            }.addOnFailureListener { exception ->
                Log.e(TAG, "Error getting last known location", exception)
                gpsAttempted = true
                requestFreshLocation(true)
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Location permission not granted", e)
            showPermissionRequest()
        }
    }

    private fun requestFreshLocation(withIpFallback: Boolean = false) {
        if (!hasLocationPermissions()) return

        val locationRequest = LocationRequest.create().apply {
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            interval = 5000
            fastestInterval = 2000
            numUpdates = 1 // Only get one update
        }

        val freshLocationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                locationResult.lastLocation?.let { location ->
                    Log.d(TAG, "Got fresh location, updating with geocoding")
                    currentLocation = location
                    updateLocationOnServer(location)
                    loadNearbyUsers()
                    showLocationStatus("Ubicación actualizada")
                    hideLocationStatusAfterDelay()
                    startAutoRefresh()

                    // Eliminar este callback ya que recibimos una ubicación
                    fusedLocationClient.removeLocationUpdates(this)
                } ?: run {
                    // Si no obtuvimos ubicación en el callback, intentar con IP como último recurso
                    if (withIpFallback) {
                        Log.d(TAG, "Failed to get fresh location, falling back to IP geolocation")
                        forceIPGeolocation()
                    }
                }
            }

            override fun onLocationAvailability(locationAvailability: LocationAvailability) {
                if (!locationAvailability.isLocationAvailable && withIpFallback) {
                    Log.d(TAG, "Location not available, falling back to IP geolocation")
                    fusedLocationClient.removeLocationUpdates(this)
                    forceIPGeolocation()
                }
            }
        }

        try {
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                freshLocationCallback,
                Looper.getMainLooper()
            )

            // Configuramos un timeout para el request de ubicación
            if (withIpFallback) {
                // Si después de 10 segundos no hay ubicación, usar IP
                mainHandler.postDelayed({
                    // Comprobar si ya tenemos ubicación
                    if (currentLocation == null) {
                        Log.d(TAG, "Location request timed out, falling back to IP geolocation")
                        fusedLocationClient.removeLocationUpdates(freshLocationCallback)
                        forceIPGeolocation()
                    }
                }, 10000) // 10 segundos de timeout
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Location permission not granted", e)
            showPermissionRequest()

            if (withIpFallback) {
                forceIPGeolocation()
            }
        }
    }

    private fun getCurrentLocation() {
        if (!hasLocationPermissions()) {
            showPermissionRequest()
            return
        }

        showLocationStatus("Obteniendo ubicación...")

        try {
            fusedLocationClient.lastLocation.addOnSuccessListener { location ->
                if (location != null) {
                    currentLocation = location
                    updateLocationOnServer(location)
                    loadNearbyUsers()
                    showLocationStatus("Ubicación actualizada")
                    hideLocationStatusAfterDelay()
                } else {
                    requestLocationUpdates()
                }
            }.addOnFailureListener { exception ->
                Log.e(TAG, "Error getting location", exception)
                showLocationStatus("Error obteniendo ubicación")
                hideLocationStatusAfterDelay()
            }
        } catch (e: SecurityException) {
            Log.e(TAG, "Location permission not granted", e)
            showPermissionRequest()
        }
    }

    private fun requestLocationUpdates() {
        if (!hasLocationPermissions()) return

        val locationRequest = LocationRequest.create().apply {
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            interval = 10000
            fastestInterval = 5000
        }

        locationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                locationResult.lastLocation?.let { location ->
                    currentLocation = location
                    updateLocationOnServer(location)
                    loadNearbyUsers()
                    showLocationStatus("Ubicación actualizada")
                    hideLocationStatusAfterDelay()

                    // Stop location updates after getting first location
                    fusedLocationClient.removeLocationUpdates(this)
                }
            }
        }

        try {
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                locationCallback!!,
                Looper.getMainLooper()
            )
        } catch (e: SecurityException) {
            Log.e(TAG, "Location permission not granted", e)
            showPermissionRequest()
        }
    }

    private fun showLocationStatus(message: String) {
        textViewLocationStatus.text = message
        layoutLocationStatus.visibility = View.VISIBLE
    }

    private fun hideLocationStatusAfterDelay() {
        mainHandler.postDelayed({
            layoutLocationStatus.visibility = View.GONE
        }, 3000)
    }

    private fun updateLocationOnServer(location: android.location.Location) {
        Log.d(TAG, "Updating location on server: ${location.latitude}, ${location.longitude}")

        // Get address information using geocoding
        lifecycleScope.launch {
            val locationInfo = LocationUtils.getAddressFromCoordinates(
                requireContext(),
                location.latitude,
                location.longitude
            )

            if (locationInfo != null) {
                Log.d(TAG, "Got location info: ${locationInfo.city}, ${locationInfo.country}")
            } else {
                Log.w(TAG, "Could not get address information from coordinates")
            }

            // Update server with location and geocoding info
            updateLocationOnServerWithGeocoding(location, locationInfo)
        }
    }

    private fun updateLocationOnServerWithGeocoding(
        location: android.location.Location,
        locationInfo: LocationUtils.LocationInfo?
    ) {
        // Verificar que la ubicación sea válida
        if (location.latitude < -90 || location.latitude > 90 ||
            location.longitude < -180 || location.longitude > 180) {
            Log.e(TAG, "Invalid coordinates: ${location.latitude}, ${location.longitude}")
            return
        }

        Log.d(TAG, "Sending location to server: ${location.latitude}, ${location.longitude}")

        // Crear el JSON con toda la información necesaria
        val json = JSONObject().apply {
            put("latitude", location.latitude)
            put("longitude", location.longitude)
            put("accuracy", location.accuracy.toDouble()) // Convert Float to Double for compatibility
            put("provider", location.provider)
            put("timestamp", location.time)
            locationInfo?.city?.let { put("city", it) }
            locationInfo?.country?.let { put("country", it) }
            locationInfo?.address?.let { put("address", it) }
        }

        val requestBody = RequestBody.create(
            "application/json; charset=utf-8".toMediaType(),
            json.toString()
        )

        val request = Request.Builder()
            .url("$API_URL/radar/update-location/$userId")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error updating location on server", e)

                // Mostrar un mensaje de error
                mainHandler.post {
                    showError("Error al actualizar la ubicación. Intenta de nuevo más tarde.")
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                if (response.isSuccessful) {
                    try {
                        val jsonResponse = JSONObject(responseBody)
                        val success = jsonResponse.optBoolean("success", false)
                        val message = jsonResponse.optString("message", "")
                        val locationObj = jsonResponse.optJSONObject("location")

                        val source = locationObj?.optString("source", "unknown") ?: "unknown"

                        Log.d(TAG, "Location updated successfully with source: $source, message: $message")

                        // Si fue exitoso, actualizamos el estado y cargamos usuarios cercanos
                        mainHandler.post {
                            // Actualizar la UI mostrando que se actualizó la ubicación
                            showLocationStatus("Ubicación actualizada")
                            hideLocationStatusAfterDelay()

                            // Cargar usuarios cercanos con la nueva ubicación
                            loadNearbyUsers()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing location update response", e)
                    }
                } else {
                    Log.w(TAG, "Failed to update location: ${response.code}, body: $responseBody")

                    // Intentamos interpretar el mensaje de error
                    try {
                        val jsonError = JSONObject(responseBody ?: "{}")
                        val errorMessage = jsonError.optString("message", "Error desconocido")

                        Log.e(TAG, "Server error: $errorMessage")

                        mainHandler.post {
                            showError("Error: $errorMessage")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing error response", e)

                        mainHandler.post {
                            showError("Error al actualizar la ubicación: ${response.code}")
                        }
                    }
                }
            }
        })
    }

    private fun loadNearbyUsers() {
        // Registrar cuándo se solicita una actualización manual
        Log.d(TAG, "Solicitud de carga manual de usuarios cercanos")

        // Si ya hay una actualización en curso, marcarla como pendiente
        if (isUpdatingUsersList) {
            Log.d(TAG, "Actualización en curso, marcando como pendiente")
            pendingUpdate = true
            return
        }

        // Verificar si ha pasado suficiente tiempo desde la última actualización
        val now = System.currentTimeMillis()
        if (now - lastUpdateTimestamp < MIN_UPDATE_INTERVAL) {
            Log.d(TAG, "Actualización demasiado frecuente, esperando ${MIN_UPDATE_INTERVAL - (now - lastUpdateTimestamp)}ms")
            // Programar la actualización para cuando haya pasado el tiempo mínimo
            mainHandler.postDelayed({
                loadNearbyUsersWithRetry()
            }, MIN_UPDATE_INTERVAL - (now - lastUpdateTimestamp))
            return
        }

        loadNearbyUsersWithRetry()
    }

    private fun refreshNearbyUsers() {
        // Registro de actividad para debug
        Log.d(TAG, "Refresh solicitado desde SwipeRefreshLayout")

        // Si ya hay una actualización en curso, cancelarla para dar prioridad a esta
        if (isUpdatingUsersList) {
            Log.d(TAG, "Cancelando actualización en curso para priorizar refresh manual")
            pendingUpdate = false
        }

        // Restablecer estado
        isUpdatingUsersList = false

        // Forzar actualización de ubicación
        getCurrentLocation()
    }

    private fun showFiltersDialog() {
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_radar_filters, null)

        // Initialize dialog components
        val spinnerDistance = dialogView.findViewById<Spinner>(R.id.spinnerDistance)
        val spinnerGender = dialogView.findViewById<Spinner>(R.id.spinnerGender)
        val editTextMinAge = dialogView.findViewById<EditText>(R.id.editTextMinAge)
        val editTextMaxAge = dialogView.findViewById<EditText>(R.id.editTextMaxAge)
        val switchOnlineOnly = dialogView.findViewById<androidx.appcompat.widget.SwitchCompat>(R.id.switchOnlineOnly)
        val switchGlobalSearch = dialogView.findViewById<androidx.appcompat.widget.SwitchCompat>(R.id.switchGlobalSearch)
        val buttonClearFilters = dialogView.findViewById<Button>(R.id.buttonClearFilters)
        val buttonApplyFilters = dialogView.findViewById<Button>(R.id.buttonApplyFilters)

        // Setup spinners
        setupDistanceSpinner(spinnerDistance)
        setupGenderSpinner(spinnerGender)

        // Set current values
        setCurrentFilterValues(dialogView)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .create()

        buttonClearFilters.setOnClickListener {
            currentFilters = RadarFilters.getDefaultFilters()
            setCurrentFilterValues(dialogView)
        }

        buttonApplyFilters.setOnClickListener {
            applyFiltersFromDialog(dialogView)
            dialog.dismiss()
            loadNearbyUsers()
        }

        dialog.show()
    }

    private fun expandSearchRadius() {
        currentFilters = currentFilters.copy(maxDistance = currentFilters.maxDistance * 2)
        loadNearbyUsers()
    }

    // Note: onRequestPermissionsResult is now replaced by the modern locationPermissionLauncher

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "RadarFragment onResume")

        // Re-check permissions in case they were granted in system settings
        if (hasLocationPermissions()) {
            if (layoutPermissionRequest.visibility == View.VISIBLE) {
                Log.d(TAG, "Permissions now available, hiding permission request and starting location services")
                hidePermissionRequest()
                showLoading(true)

                // Use a small delay to ensure UI is properly updated
                mainHandler.postDelayed({
                    forceLocationUpdate()
                }, 50)
            } else if (currentLocation != null) {
                // Permissions are granted and we have location, just start auto-refresh
                startAutoRefresh()
            } else {
                // Permissions are granted but no location yet, force location update
                forceLocationUpdate()
            }
        } else {
            // No permissions, make sure permission request is shown
            if (layoutPermissionRequest.visibility != View.VISIBLE) {
                showPermissionRequest()
            }
        }
    }

    override fun onPause() {
        super.onPause()
        stopAutoRefresh()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopLocationUpdates()
        stopAutoRefresh()

        // Clean up retry handler
        retryHandler?.removeCallbacksAndMessages(null)
        retryHandler = null
    }

    private fun stopLocationUpdates() {
        locationCallback?.let {
            fusedLocationClient.removeLocationUpdates(it)
        }
    }

    private fun startAutoRefresh() {
        // Detener cualquier refresh anterior si existe
        stopAutoRefresh()

        refreshHandler = Handler(Looper.getMainLooper())
        refreshRunnable = Runnable {
            if (currentLocation != null && !isUpdatingUsersList) {
                Log.d(TAG, "Ejecutando actualización automática programada")
                loadNearbyUsersWithMinimalUI()
            } else {
                Log.d(TAG, "Omitiendo actualización automática - actualización en progreso o sin ubicación")
            }

            // Programar la siguiente actualización
            refreshHandler?.postDelayed(refreshRunnable!!, AUTO_REFRESH_INTERVAL)
        }
        refreshHandler?.postDelayed(refreshRunnable!!, AUTO_REFRESH_INTERVAL)
        Log.d(TAG, "Actualización automática iniciada con intervalo de $AUTO_REFRESH_INTERVAL ms")
    }

    private fun stopAutoRefresh() {
        refreshRunnable?.let { refreshHandler?.removeCallbacks(it) }
        refreshHandler = null
        refreshRunnable = null
        Log.d(TAG, "Actualización automática detenida")
    }

    // Helper functions for UI state management
    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) View.VISIBLE else View.GONE
        if (show) {
            layoutEmptyState.visibility = View.GONE
        }
    }

    private fun showError(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        if (nearbyUsers.isEmpty()) {
            showEmptyState()
        }
    }

    private fun showEmptyState() {
        layoutEmptyState.visibility = View.VISIBLE
        recyclerViewUsers.visibility = View.GONE

        // Update empty state message based on search type
        val emptyTitle = view?.findViewById<TextView>(R.id.textViewEmptyTitle)
        val emptyMessage = view?.findViewById<TextView>(R.id.textViewEmptyMessage)

        if (currentFilters.showExtendedSearch) {
            emptyTitle?.text = "No hay usuarios disponibles"
            emptyMessage?.text = "No se encontraron usuarios con ubicación\nen todo el mundo"
        } else {
            emptyTitle?.text = "No hay usuarios cercanos"
            emptyMessage?.text = "Intenta ampliar el rango de búsqueda\no verifica tu ubicación"
        }
    }

    private fun hideEmptyState() {
        layoutEmptyState.visibility = View.GONE
        recyclerViewUsers.visibility = View.VISIBLE
    }

    // Network connectivity and retry functions
    private fun isNetworkAvailable(): Boolean {
        val connectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }

    private fun loadNearbyUsersWithRetry() {
        if (!isNetworkAvailable()) {
            Log.w(TAG, "No network connection available")
            mainHandler.post {
                showLoading(false)
                swipeRefreshLayout.isRefreshing = false
                showError("Sin conexión a internet. Verifica tu conectividad.")
            }
            return
        }

        retryCount = 0
        loadNearbyUsersInternal()
    }

    private fun loadNearbyUsersInternal() {
        if (currentLocation == null) {
            Log.w(TAG, "No current location available")
            mainHandler.post {
                showLoading(false)
                swipeRefreshLayout.isRefreshing = false
                showError("No se pudo obtener tu ubicación. Intenta actualizar de nuevo.")
            }
            return
        }

        // Capturar la ubicación actual para usarla de forma consistente en toda la solicitud
        val userLocation = currentLocation!!
        Log.d(TAG, "Calculando distancias desde: ${userLocation.latitude}, ${userLocation.longitude}")

        // Only show loading on first attempt
        if (retryCount == 0) {
            showLoading(true)
        }

        // Indicar que estamos procesando una actualización
        isUpdatingUsersList = true
        lastUpdateTimestamp = System.currentTimeMillis()

        val url = buildString {
            append("$API_URL/radar/nearby-users")
            append("?userId=$userId")
            append("&latitude=${userLocation.latitude}")
            append("&longitude=${userLocation.longitude}")
            append("&maxDistance=${currentFilters.maxDistance}")

            currentFilters.minAge?.let { append("&minAge=$it") }
            currentFilters.maxAge?.let { append("&maxAge=$it") }
            currentFilters.genderFilter?.let { append("&gender=$it") }

            if (currentFilters.onlineOnly) append("&onlineOnly=true")
            if (currentFilters.showExtendedSearch) append("&globalSearch=true")

            // Añadir un parámetro para evitar caché en el servidor
            append("&_t=${System.currentTimeMillis()}")
        }

        Log.d(TAG, "Loading nearby users (attempt ${retryCount + 1}/$maxRetries) with URL: $url")

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error loading nearby users (attempt ${retryCount + 1})", e)

                if (retryCount < maxRetries) {
                    retryCount++
                    Log.d(TAG, "Retrying in 2 seconds... (attempt $retryCount/$maxRetries)")

                    retryHandler = Handler(Looper.getMainLooper())
                    retryHandler?.postDelayed({
                        if (!isNetworkAvailable()) {
                            mainHandler.post {
                                isUpdatingUsersList = false
                                showLoading(false)
                                swipeRefreshLayout.isRefreshing = false
                                showError("Sin conexión a internet. Verifica tu conectividad.")
                            }
                        } else {
                            loadNearbyUsersInternal()
                        }
                    }, 2000)
                } else {
                    Log.e(TAG, "Max retry attempts reached, giving up")
                    mainHandler.post {
                        isUpdatingUsersList = false
                        showLoading(false)
                        swipeRefreshLayout.isRefreshing = false

                        val errorMessage = when {
                            !isNetworkAvailable() -> "Sin conexión a internet. Verifica tu conectividad."
                            e.message?.contains("timeout", ignoreCase = true) == true ->
                                "Tiempo de espera agotado. El servidor puede estar ocupado."
                            e.message?.contains("connection", ignoreCase = true) == true ->
                                "Error de conexión. Verifica tu conexión a internet."
                            else -> "Error cargando usuarios cercanos. Intenta de nuevo más tarde."
                        }

                        showError(errorMessage)
                    }
                }
            }

            override fun onResponse(call: Call, response: Response) {
                // Reset retry count on successful response
                retryCount = 0

                try {
                    val responseBody = response.body?.string()
                    Log.d(TAG, "Response code: ${response.code}")
                    Log.d(TAG, "Response body: ${responseBody?.take(500)}${if ((responseBody?.length ?: 0) > 500) "..." else ""}")

                    if (response.isSuccessful && responseBody != null) {
                        val jsonResponse = JSONObject(responseBody)
                        val success = jsonResponse.optBoolean("success", false)

                        if (success) {
                            val usersArray = jsonResponse.getJSONArray("users")

                            // Registra información de diagnóstico sobre las coordenadas
                            val searchParams = jsonResponse.optJSONObject("searchParams")
                            var userLatitude = 0.0
                            var userLongitude = 0.0

                            if (searchParams != null) {
                                val locationObj = searchParams.optJSONObject("userLocation")
                                if (locationObj != null) {
                                    userLatitude = locationObj.optDouble("latitude")
                                    userLongitude = locationObj.optDouble("longitude")
                                    val timestamp = searchParams.optString("timestamp", "")
                                    Log.d(TAG, "Distancias calculadas desde: $userLatitude, $userLongitude (timestamp: $timestamp)")
                                }
                            }

                            // Capturar el JSON original para verificación de consistencia
                            val originalUsersArray = usersArray

                            // Parsear usuarios con distancias correctas
                            val users = parseUsersFromJson(usersArray)

                            Log.d(TAG, "Successfully loaded ${users.size} users")
                            if (currentFilters.showExtendedSearch) {
                                Log.d(TAG, "Global search enabled - showing users from worldwide")
                            }

                            // Crear una nueva lista para asegurar que no haya referencias compartidas
                            val newUsersList = ArrayList(users)

                            mainHandler.post {
                                // Indicar que hemos terminado la actualización
                                isUpdatingUsersList = false

                                // Actualizar la UI con los nuevos datos
                                if (swipeRefreshLayout.isRefreshing || retryCount > 0) {
                                    // Si es una actualización manual o un reintento, usar el método normal
                                    updateUsersList(newUsersList)
                                } else {
                                    // Si es una actualización automática, usar el método seguro
                                    updateUsersListSafely(newUsersList)
                                }

                                showLoading(false)
                                swipeRefreshLayout.isRefreshing = false

                                // Informar al usuario sobre los resultados
                                if (users.isNotEmpty() && (swipeRefreshLayout.isRefreshing || retryCount > 0)) {
                                    val freshUsers = users.count { it.locationFreshness == "fresh" }
                                    val message = if (freshUsers > 0) {
                                        "Se encontraron ${users.size} usuarios, $freshUsers con ubicación reciente"
                                    } else {
                                        "Se encontraron ${users.size} usuarios cercanos"
                                    }
                                    showToast(message)
                                }

                                // Asegurar que se aplique PixelPerfect después de la actualización
                                view?.post {
                                    refreshUI()
                                }

                                // Procesar cualquier actualización pendiente
                                if (pendingUpdate) {
                                    pendingUpdate = false
                                    mainHandler.postDelayed({
                                        loadNearbyUsersWithMinimalUI()
                                    }, MIN_UPDATE_INTERVAL)
                                }
                            }
                        } else {
                            val message = jsonResponse.optString("message", "Error desconocido del servidor")
                            Log.w(TAG, "Server returned success=false with message: $message")
                            mainHandler.post {
                                isUpdatingUsersList = false
                                showLoading(false)
                                swipeRefreshLayout.isRefreshing = false
                                showError(message)

                                // Procesar cualquier actualización pendiente
                                if (pendingUpdate) {
                                    pendingUpdate = false
                                    mainHandler.postDelayed({
                                        loadNearbyUsersWithMinimalUI()
                                    }, MIN_UPDATE_INTERVAL)
                                }
                            }
                        }
                    } else {
                        Log.e(TAG, "Server error: ${response.code} - $responseBody")
                        mainHandler.post {
                            isUpdatingUsersList = false
                            showLoading(false)
                            swipeRefreshLayout.isRefreshing = false

                            val errorMessage = when (response.code) {
                                408 -> "Tiempo de espera agotado. Intenta de nuevo."
                                503, 502 -> "El servidor está temporalmente no disponible."
                                500 -> "Error interno del servidor. Intenta más tarde."
                                else -> "Error del servidor (${response.code}). Intenta más tarde."
                            }

                            showError(errorMessage)

                            // Procesar cualquier actualización pendiente
                            if (pendingUpdate) {
                                pendingUpdate = false
                                mainHandler.postDelayed({
                                    loadNearbyUsersWithMinimalUI()
                                }, MIN_UPDATE_INTERVAL)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing nearby users response", e)
                    mainHandler.post {
                        isUpdatingUsersList = false
                        showLoading(false)
                        swipeRefreshLayout.isRefreshing = false
                        showError("Error procesando respuesta del servidor")

                        // Procesar cualquier actualización pendiente
                        if (pendingUpdate) {
                            pendingUpdate = false
                            mainHandler.postDelayed({
                                loadNearbyUsersWithMinimalUI()
                            }, MIN_UPDATE_INTERVAL)
                        }
                    }
                }
            }
        })
    }

    // Helper function to get user-friendly error messages
    private fun getErrorMessage(exception: IOException): String {
        return when {
            exception.message?.contains("timeout", ignoreCase = true) == true ->
                "Tiempo de espera agotado. El servidor puede estar ocupado."
            exception.message?.contains("connection", ignoreCase = true) == true ->
                "Error de conexión. Verifica tu conexión a internet."
            exception.message?.contains("network", ignoreCase = true) == true ->
                "Error de red. Verifica tu conectividad."
            exception.message?.contains("host", ignoreCase = true) == true ->
                "No se puede conectar al servidor. Intenta más tarde."
            exception.message?.contains("ssl", ignoreCase = true) == true ->
                "Error de seguridad en la conexión."
            else -> "Error cargando usuarios cercanos. Intenta de nuevo más tarde."
        }
    }

    // Health check function to verify server connectivity
    private fun performServerHealthCheck(onResult: (Boolean) -> Unit) {
        val healthCheckUrl = "$API_URL/health"
        val request = Request.Builder()
            .url(healthCheckUrl)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.w(TAG, "Server health check failed", e)
                onResult(false)
            }

            override fun onResponse(call: Call, response: Response) {
                val isHealthy = response.isSuccessful
                Log.d(TAG, "Server health check result: $isHealthy (code: ${response.code})")
                onResult(isHealthy)
            }        })
    }

    // Enhanced error handling with server health check
    private fun handleNetworkError(exception: IOException) {
        Log.e(TAG, "Network error occurred", exception)

        // Perform server health check to provide better error information
        performServerHealthCheck { isServerHealthy ->
            mainHandler.post {
                showLoading(false)
                swipeRefreshLayout.isRefreshing = false

                val errorMessage = if (!isNetworkAvailable()) {
                    "Sin conexión a internet. Verifica tu conectividad."
                } else if (!isServerHealthy) {
                    "El servidor no está disponible. Intenta más tarde."
                } else {
                    getErrorMessage(exception)
                }

                showError(errorMessage)
            }
        }
    }

    // Missing filter dialog functions
    private fun setupDistanceSpinner(spinner: Spinner) {
        val distanceOptions = RadarFilters.getDistanceOptions()
        val distanceLabels = distanceOptions.map { it.first }

        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            distanceLabels
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }

    private fun setupGenderSpinner(spinner: Spinner) {
        val genderOptions = listOf(
            "Todos los géneros",
            "Hombre",
            "Mujer",
            "No binario",
            "Otro"
        )

        val adapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            genderOptions
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinner.adapter = adapter
    }

    private fun setCurrentFilterValues(dialogView: View) {
        val spinnerDistance = dialogView.findViewById<Spinner>(R.id.spinnerDistance)
        val spinnerGender = dialogView.findViewById<Spinner>(R.id.spinnerGender)
        val editTextMinAge = dialogView.findViewById<EditText>(R.id.editTextMinAge)
        val editTextMaxAge = dialogView.findViewById<EditText>(R.id.editTextMaxAge)
        val switchOnlineOnly = dialogView.findViewById<androidx.appcompat.widget.SwitchCompat>(R.id.switchOnlineOnly)
        val switchGlobalSearch = dialogView.findViewById<androidx.appcompat.widget.SwitchCompat>(R.id.switchGlobalSearch)

        // Set distance
        val distanceOptions = RadarFilters.getDistanceOptions()
        val currentDistanceIndex = distanceOptions.indexOfFirst { it.second == currentFilters.maxDistance }
        if (currentDistanceIndex >= 0) {
            spinnerDistance.setSelection(currentDistanceIndex)
        }

        // Set gender
        val genderOptions = listOf("Todos los géneros", "Hombre", "Mujer", "No binario", "Otro")
        val genderIndex = when (currentFilters.genderFilter) {
            null -> 0
            "male" -> 1
            "female" -> 2
            "non_binary" -> 3
            "other" -> 4
            else -> 0
        }
        spinnerGender.setSelection(genderIndex)

        // Set age range
        currentFilters.minAge?.let {
            editTextMinAge.setText(it.toString())
        }
        currentFilters.maxAge?.let {
            editTextMaxAge.setText(it.toString())
        }

        // Set switches
        switchOnlineOnly.isChecked = currentFilters.onlineOnly
        switchGlobalSearch.isChecked = currentFilters.showExtendedSearch
    }

    private fun applyFiltersFromDialog(dialogView: View) {
        val spinnerDistance = dialogView.findViewById<Spinner>(R.id.spinnerDistance)
        val spinnerGender = dialogView.findViewById<Spinner>(R.id.spinnerGender)
        val editTextMinAge = dialogView.findViewById<EditText>(R.id.editTextMinAge)
        val editTextMaxAge = dialogView.findViewById<EditText>(R.id.editTextMaxAge)
        val switchOnlineOnly = dialogView.findViewById<androidx.appcompat.widget.SwitchCompat>(R.id.switchOnlineOnly)
        val switchGlobalSearch = dialogView.findViewById<androidx.appcompat.widget.SwitchCompat>(R.id.switchGlobalSearch)

        // Get distance
        val distanceOptions = RadarFilters.getDistanceOptions()
        val selectedDistance = distanceOptions[spinnerDistance.selectedItemPosition].second

        // Get gender
        val genderFilter = when (spinnerGender.selectedItemPosition) {
            0 -> null // Todos los géneros
            1 -> "male"
            2 -> "female"
            3 -> "non_binary"
            4 -> "other"
            else -> null
        }

        // Get age range
        val minAge = editTextMinAge.text.toString().toIntOrNull()
        val maxAge = editTextMaxAge.text.toString().toIntOrNull()

        // Validate age range
        if (minAge != null && maxAge != null && minAge > maxAge) {
            Toast.makeText(context, "La edad mínima no puede ser mayor que la máxima", Toast.LENGTH_SHORT).show()
            return
        }

        // Apply filters
        currentFilters = RadarFilters(
            maxDistance = selectedDistance,
            minAge = minAge,
            maxAge = maxAge,
            genderFilter = genderFilter,
            onlineOnly = switchOnlineOnly.isChecked,
            showExtendedSearch = switchGlobalSearch.isChecked
        )
    }

    private fun verifyDataConsistency(usersArray: JSONArray, parsedUsers: List<RadarUser>) {
        try {
            // Crear un mapa de usuarios por ID para verificación rápida
            val idToUserMap = parsedUsers.associateBy { it.id }

            // Verificar que el número de usuarios coincida
            if (usersArray.length() != parsedUsers.size) {
                Log.e(TAG, "⚠️ Inconsistencia: número de usuarios no coincide. JSON: ${usersArray.length()}, Parseados: ${parsedUsers.size}")
            }

            // Comprobar cada usuario en el JSON original
            for (i in 0 until usersArray.length()) {
                val userJson = usersArray.getJSONObject(i)
                val userId = userJson.getInt("id")
                val username = userJson.getString("username")
                val jsonDistance = userJson.getDouble("distance")

                // Buscar este usuario en nuestra lista parseada
                val parsedUser = idToUserMap[userId]

                if (parsedUser == null) {
                    Log.e(TAG, "⚠️ Usuario ID $userId ($username) existe en JSON pero no en la lista parseada")
                    continue
                }

                // Verificar si las distancias coinciden
                if (Math.abs(jsonDistance - parsedUser.distance) > 1.0) { // Permitir una pequeña diferencia por redondeo
                    Log.e(TAG, "⚠️ Distancia inconsistente para usuario $username (ID: $userId): " +
                            "JSON: $jsonDistance, Parseado: ${parsedUser.distance}, " +
                            "Coordenadas: (${parsedUser.latitude}, ${parsedUser.longitude})")
                }
            }

            Log.d(TAG, "✅ Verificación de consistencia completada para ${parsedUsers.size} usuarios")
        } catch (e: Exception) {
            Log.e(TAG, "Error durante verificación de consistencia", e)
        }
    }

    private fun parseUsersFromJson(usersArray: JSONArray): List<RadarUser> {
        val users = mutableListOf<RadarUser>()

        for (i in 0 until usersArray.length()) {
            try {
                val userJson = usersArray.getJSONObject(i)

                // Verificar y asegurar que la distancia sea válida
                val distance = if (userJson.has("distance")) {
                    val dist = userJson.getDouble("distance")
                    if (dist.isNaN() || dist < 0) 0.0 else dist
                } else 0.0

                // Registrar para depuración
                Log.d(TAG, "Parseando usuario: ${userJson.optString("username")}, " +
                        "distancia: $distance, " +
                        "frescura: ${userJson.optString("location_freshness", "unknown")}")

                val userId = userJson.getInt("id")
                val city = userJson.optString("city", null)
                val country = userJson.optString("country", null)

                // Guardar en cache si hay información de ubicación
                if (!city.isNullOrEmpty() || !country.isNullOrEmpty()) {
                    saveUserLocation(userId, city, country)
                }

                val user = RadarUser(
                    id = userId,
                    username = userJson.getString("username"),
                    fullName = userJson.optString("full_name", null),
                    avatarUrl = userJson.optString("avatar_url", null),
                    bio = userJson.optString("bio", null),
                    age = userJson.optInt("age", -1).takeIf { it != -1 },
                    gender = userJson.optString("gender", null),
                    isOnline = userJson.optBoolean("is_online", false),
                    lastSeen = userJson.optString("last_seen", null),
                    latitude = userJson.getDouble("latitude"),
                    longitude = userJson.getDouble("longitude"),
                    distance = distance,
                    locationUpdatedAt = userJson.optString("location_updated_at", null),
                    locationAgeText = userJson.optString("location_age_text", null),
                    locationFreshness = userJson.optString("location_freshness", null),
                    city = city,
                    country = country
                )

                users.add(user)
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing user data at index $i", e)
            }
        }

        // Verificar consistencia entre JSON original y datos parseados
        verifyDataConsistency(usersArray, users)

        return users
    }

    /**
     * Versión con mínima UI de loadNearbyUsers para refresh automático
     * No muestra indicadores de carga ni mensajes de error
     */
    private fun loadNearbyUsersWithMinimalUI() {
        if (isUpdatingUsersList) {
            Log.d(TAG, "Actualización de usuarios en progreso, posponiendo nueva solicitud")
            pendingUpdate = true
            return
        }

        // Verificar intervalo mínimo entre actualizaciones
        val now = System.currentTimeMillis()
        if (now - lastUpdateTimestamp < MIN_UPDATE_INTERVAL) {
            Log.d(TAG, "Actualización demasiado frecuente, esperando intervalo mínimo")
            return
        }

        if (currentLocation == null) {
            Log.w(TAG, "No se puede actualizar sin ubicación actual")
            return
        }

        val userLocation = currentLocation!!
        Log.d(TAG, "Actualizando usuarios en segundo plano desde: ${userLocation.latitude}, ${userLocation.longitude}")

        val url = buildString {
            append("$API_URL/radar/nearby-users")
            append("?userId=$userId")
            append("&latitude=${userLocation.latitude}")
            append("&longitude=${userLocation.longitude}")
            append("&maxDistance=${currentFilters.maxDistance}")

            currentFilters.minAge?.let { append("&minAge=$it") }
            currentFilters.maxAge?.let { append("&maxAge=$it") }
            currentFilters.genderFilter?.let { append("&gender=$it") }

            if (currentFilters.onlineOnly) append("&onlineOnly=true")
            if (currentFilters.showExtendedSearch) append("&globalSearch=true")
        }

        isUpdatingUsersList = true
        lastUpdateTimestamp = now

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error en actualización silenciosa", e)

                mainHandler.post {
                    isUpdatingUsersList = false

                    // Procesar actualización pendiente si hay
                    if (pendingUpdate) {
                        pendingUpdate = false
                        mainHandler.postDelayed({
                            loadNearbyUsersWithMinimalUI()
                        }, MIN_UPDATE_INTERVAL)
                    }
                }
            }

            override fun onResponse(call: Call, response: Response) {
                try {
                    val responseBody = response.body?.string()

                    if (response.isSuccessful && responseBody != null) {
                        val jsonResponse = JSONObject(responseBody)
                        val success = jsonResponse.optBoolean("success", false)

                        if (success) {
                            val usersArray = jsonResponse.getJSONArray("users")
                            val users = parseUsersFromJson(usersArray)

                            mainHandler.post {
                                updateUsersListSafely(users)
                                isUpdatingUsersList = false

                                // Procesar actualización pendiente si hay
                                if (pendingUpdate) {
                                    pendingUpdate = false
                                    mainHandler.postDelayed({
                                        loadNearbyUsersWithMinimalUI()
                                    }, MIN_UPDATE_INTERVAL)
                                }
                            }
                        } else {
                            mainHandler.post {
                                isUpdatingUsersList = false

                                // Procesar actualización pendiente si hay
                                if (pendingUpdate) {
                                    pendingUpdate = false
                                    mainHandler.postDelayed({
                                        loadNearbyUsersWithMinimalUI()
                                    }, MIN_UPDATE_INTERVAL)
                                }
                            }
                        }
                    } else {
                        mainHandler.post {
                            isUpdatingUsersList = false

                            // Procesar actualización pendiente si hay
                            if (pendingUpdate) {
                                pendingUpdate = false
                                mainHandler.postDelayed({
                                    loadNearbyUsersWithMinimalUI()
                                }, MIN_UPDATE_INTERVAL)
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error procesando respuesta silenciosa", e)
                    mainHandler.post {
                        isUpdatingUsersList = false

                        // Procesar actualización pendiente si hay
                        if (pendingUpdate) {
                            pendingUpdate = false
                            mainHandler.postDelayed({
                                loadNearbyUsersWithMinimalUI()
                            }, MIN_UPDATE_INTERVAL)
                        }
                    }
                }
            }
        })
    }

    /**
     * Actualiza la lista de usuarios de forma segura, evitando que se mezclen datos
     */
    private fun updateUsersListSafely(users: List<RadarUser>) {
        try {
            Log.d(TAG, "Actualizando lista de usuarios: ${users.size} usuarios")

            // Crear una nueva lista para evitar referencias compartidas
            val newUsersList = ArrayList<RadarUser>(users)

            // Actualizar los datos en el adaptador directamente
            // sin modificar la lista nearbyUsers que podría estar siendo usada en otro lado
            radarAdapter.clearUsers()
            radarAdapter.addUsers(newUsersList)

            // Una vez actualizado el adaptador, actualizar nuestra lista de referencia
            nearbyUsers.clear()
            nearbyUsers.addAll(newUsersList)

            // Actualizar estado vacío si es necesario
            if (users.isEmpty()) {
                showEmptyState()
            } else {
                hideEmptyState()
            }

            // Aplicar PixelPerfect a las vistas recién creadas
            recyclerViewUsers.post {
                applyPixelPerfectTextViews(recyclerViewUsers)
            }

            // Guardar la información de ubicación de cada usuario en el cache
            users.forEach { user ->
                if (!user.city.isNullOrEmpty() || !user.country.isNullOrEmpty()) {
                    saveUserLocation(user.id, user.city, user.country)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error al actualizar usuarios de forma segura", e)
        }
    }

    /**
     * Versión original de updateUsersList para uso interactivo
     */
    private fun updateUsersList(users: List<RadarUser>) {
        nearbyUsers.clear()
        nearbyUsers.addAll(users)
        radarAdapter.updateUsers(users)

        if (users.isEmpty()) {
            showEmptyState()
        } else {
            hideEmptyState()
        }

        // Asegurar que se aplique PixelPerfect después de que RecyclerView haya actualizado sus vistas
        view?.post {
            recyclerViewUsers.post {
                applyPixelPerfectTextViews(recyclerViewUsers)
                Log.d(TAG, "PixelPerfect aplicado a RecyclerView después de actualización")
            }
        }
    }

    /**
     * Fuerza la actualización de ubicación usando el servidor, con fallback a IP
     * Esta función es útil cuando el GPS no está disponible o no es preciso
     */
    private fun forceIPGeolocation() {
        showLocationStatus("Usando geolocalización por IP...")

        // Solo necesitamos enviar el userId, el servidor intentará hacer geolocalización por IP
        val json = JSONObject().apply {
            put("useIpFallback", true)
        }

        val requestBody = RequestBody.create(
            "application/json; charset=utf-8".toMediaType(),
            json.toString()
        )

        val request = Request.Builder()
            .url("$API_URL/radar/update-location/$userId")
            .post(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error en geolocalización por IP", e)
                mainHandler.post {
                    showError("No se pudo obtener ubicación por IP. Verifica tu conexión.")
                    swipeRefreshLayout.isRefreshing = false
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()

                if (response.isSuccessful) {
                    try {
                        val jsonResponse = JSONObject(responseBody)
                        val success = jsonResponse.optBoolean("success", false)

                        if (success) {
                            val locationObj = jsonResponse.optJSONObject("location")
                            if (locationObj != null) {
                                val lat = locationObj.optDouble("latitude")
                                val lng = locationObj.optDouble("longitude")
                                val source = locationObj.optString("source", "ip_geolocation")

                                Log.d(TAG, "Ubicación obtenida por IP: $lat, $lng (fuente: $source)")

                                // Crear una ubicación artificial con los datos obtenidos
                                val ipLocation = Location("ip_provider").apply {
                                    latitude = lat
                                    longitude = lng
                                    // Precisión aproximada para IP (5km)
                                    accuracy = 5000f
                                    time = System.currentTimeMillis()
                                }

                                // Actualizar la ubicación actual
                                currentLocation = ipLocation

                                // Cargar usuarios cercanos con esta ubicación
                                mainHandler.post {
                                    showLocationStatus("Ubicación por IP actualizada")
                                    hideLocationStatusAfterDelay()
                                    loadNearbyUsers()
                                    swipeRefreshLayout.isRefreshing = false
                                }
                            } else {
                                throw Exception("No location data in response")
                            }
                        } else {
                            throw Exception(jsonResponse.optString("message", "Unknown error"))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error procesando respuesta de geolocalización IP", e)
                        mainHandler.post {
                            showError("Error: ${e.message}")
                            swipeRefreshLayout.isRefreshing = false
                        }
                    }
                } else {
                    Log.e(TAG, "Error en geolocalización por IP: ${response.code}")
                    mainHandler.post {
                        showError("Error obteniendo ubicación por IP (${response.code})")
                        swipeRefreshLayout.isRefreshing = false
                    }
                }
            }
        })
    }

    /**
     * Método para forzar la actualización de UI después de una actualización automática
     */
    private fun refreshUI() {
        // Volver a aplicar PixelPerfect a todas las vistas
        view?.let { rootView ->
            applyPixelPerfectTextViews(rootView)

            // Forzar relayout del RecyclerView
            recyclerViewUsers.post {
                recyclerViewUsers.invalidate()

                // Aplicar pixel perfect a cada item visible
                for (i in 0 until recyclerViewUsers.childCount) {
                    val child = recyclerViewUsers.getChildAt(i)
                    if (child is ViewGroup) {
                        applyPixelPerfectTextViews(child)
                    }
                }
            }
        }
    }

    /**
     * Guarda la información de ubicación de un usuario en SharedPreferences
     * para que esté disponible en otras partes de la aplicación
     */
    private fun saveUserLocation(userId: Int, city: String?, country: String?) {
        if (city.isNullOrEmpty() && country.isNullOrEmpty()) return

        try {
            // Guardar en SharedPreferences
            val locationPrefs = requireContext().getSharedPreferences("VMeetLocationCache", Context.MODE_PRIVATE)
            val editor = locationPrefs.edit()

            // Usamos el ID de usuario como clave
            val key = "location_$userId"
            val locationData = JSONObject().apply {
                put("city", city ?: "")
                put("country", country ?: "")
                put("timestamp", System.currentTimeMillis())
            }

            editor.putString(key, locationData.toString())
            editor.apply()

            Log.d(TAG, "Ubicación guardada en cache para usuario $userId: $city, $country")
        } catch (e: Exception) {
            Log.e(TAG, "Error al guardar ubicación en cache", e)
        }
    }

    /**
     * Actualiza la lista de usuarios cercanos
     */
    private fun updateNearbyUsers(users: List<RadarUser>) {
        Log.d(TAG, "Actualizando lista de ${users.size} usuarios cercanos")

        // Actualizar el RecyclerView
        radarAdapter.updateUsers(users)

        // Guardar la información de ubicación de cada usuario en el cache
        users.forEach { user ->
            if (!user.city.isNullOrEmpty() || !user.country.isNullOrEmpty()) {
                saveUserLocation(user.id, user.city, user.country)
            }
        }

        // Manejar visualización de estado vacío
        handleEmptyState(users.isEmpty())
    }

    /**
     * Añade usuarios a la lista existente
     */
    private fun addNearbyUsers(users: List<RadarUser>) {
        Log.d(TAG, "Añadiendo ${users.size} usuarios cercanos más")

        // Actualizar el RecyclerView
        radarAdapter.addUsers(users)

        // Guardar la información de ubicación de cada usuario en el cache
        users.forEach { user ->
            if (!user.city.isNullOrEmpty() || !user.country.isNullOrEmpty()) {
                saveUserLocation(user.id, user.city, user.country)
            }
        }

        // Manejar visualización de estado vacío
        handleEmptyState(radarAdapter.itemCount == 0)
    }

    /**
     * Gestiona la visualización del estado vacío cuando no hay usuarios
     */
    private fun handleEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            showEmptyState()
        } else {
            hideEmptyState()
        }
    }
}
