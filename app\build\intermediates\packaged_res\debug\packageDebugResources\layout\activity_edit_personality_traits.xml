<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cyberpunk_background"
    android:orientation="vertical"
    android:fitsSystemWindows="true">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/darker_blue"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Editar Rasgos"
        app:titleTextColor="@color/comfy_blue" />

    <!-- Content with NestedScrollView for better scroll performance -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true"
        android:scrollbars="none"
        android:overScrollMode="never">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="12dp"
            android:paddingBottom="8dp">

            <!-- Title -->
            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp"
                android:fontFamily="@font/inter"
                android:textStyle="bold"
                android:text="Rasgos de Personalidad"
                android:textColor="@color/cyberpunk_text_primary"
                android:textSize="22sp" />

            <!-- Subtitle -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/inter"
                android:text="Selecciona los rasgos que mejor te describan. Esto ayudará a otros usuarios a conocerte mejor."
                android:textColor="@color/cyberpunk_text_secondary"
                android:textSize="14sp" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="12dp"
                android:indeterminateTint="@color/cyberpunk_accent"
                android:visibility="gone" />

            <!-- Traits RecyclerView with nested scroll disabled -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewTraits"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:clipToPadding="false"
                android:nestedScrollingEnabled="false"
                android:paddingStart="4dp"
                android:paddingEnd="4dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Save Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/buttonSave"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp"
        android:backgroundTint="@color/cyberpunk_accent"
        android:fontFamily="@font/inter"
        android:text="Guardar Rasgos"
        android:textColor="@color/cyberpunk_background"
        android:textSize="16sp"
        app:cornerRadius="12dp"
        app:elevation="4dp" />

</LinearLayout>
