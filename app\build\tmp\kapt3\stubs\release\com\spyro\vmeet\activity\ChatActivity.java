package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00d4\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0015\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u001c\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0011\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010$\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b8\b\u0007\u0018\u0000 \u0087\u00022\u00020\u00012\u00020\u0002:\u0004\u0087\u0002\u0088\u0002B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010n\u001a\u00020oH\u0002J\b\u0010p\u001a\u00020oH\u0002J\b\u0010q\u001a\u00020oH\u0002J\b\u0010r\u001a\u00020oH\u0002J\b\u0010s\u001a\u00020oH\u0002J\b\u0010t\u001a\u00020oH\u0002J\u0010\u0010u\u001a\u00020o2\u0006\u0010v\u001a\u00020\u0007H\u0002J\u0010\u0010w\u001a\u00020o2\u0006\u0010v\u001a\u00020\u0007H\u0002J\b\u0010x\u001a\u00020oH\u0002J\b\u0010y\u001a\u00020oH\u0002J\b\u0010z\u001a\u00020oH\u0002J\b\u0010{\u001a\u00020oH\u0002J\u0010\u0010|\u001a\u00020o2\u0006\u0010}\u001a\u00020!H\u0002J\b\u0010~\u001a\u00020.H\u0002J\n\u0010\u007f\u001a\u0004\u0018\u000108H\u0002J\t\u0010\u0080\u0001\u001a\u00020!H\u0002J)\u0010\u0081\u0001\u001a\u00020o2\u0006\u0010?\u001a\u00020\u00072\u0006\u0010v\u001a\u00020\u00072\u0006\u0010I\u001a\u00020\u00072\u0006\u0010}\u001a\u00020!H\u0002J\t\u0010\u0082\u0001\u001a\u00020oH\u0002J\t\u0010\u0083\u0001\u001a\u00020oH\u0002J\t\u0010\u0084\u0001\u001a\u00020oH\u0002J\t\u0010\u0085\u0001\u001a\u00020oH\u0002J\t\u0010\u0086\u0001\u001a\u00020oH\u0002J\u0012\u0010\u0087\u0001\u001a\u00020\u00072\u0007\u0010\u0088\u0001\u001a\u00020FH\u0002J\t\u0010\u0089\u0001\u001a\u00020!H\u0002J%\u0010\u008a\u0001\u001a\u00020o2\u0011\u0010\u008b\u0001\u001a\f\u0012\u0005\u0012\u00030\u008d\u0001\u0018\u00010\u008c\u00012\u0007\u0010\u008e\u0001\u001a\u000203H\u0002J\u0012\u0010\u008f\u0001\u001a\u00020o2\u0007\u0010\u0088\u0001\u001a\u00020FH\u0002J\t\u0010\u0090\u0001\u001a\u00020oH\u0002J\t\u0010\u0091\u0001\u001a\u00020oH\u0002J\t\u0010\u0092\u0001\u001a\u00020oH\u0002J\t\u0010\u0093\u0001\u001a\u00020oH\u0002J\u0011\u0010\u0094\u0001\u001a\u00020o2\u0006\u0010}\u001a\u00020!H\u0002J\u0019\u0010\u0095\u0001\u001a\u00020o2\u000e\u0010\u0096\u0001\u001a\t\u0012\u0004\u0012\u00020\u00070\u008c\u0001H\u0002J\t\u0010\u0097\u0001\u001a\u00020oH\u0002J\u001d\u0010\u0098\u0001\u001a\u00020o2\t\u0010\u0099\u0001\u001a\u0004\u0018\u00010\u00112\u0007\u0010\u009a\u0001\u001a\u00020\u0007H\u0016J\u0013\u0010\u009b\u0001\u001a\u00020o2\b\u0010\u009c\u0001\u001a\u00030\u009d\u0001H\u0016J\u0015\u0010\u009e\u0001\u001a\u00020o2\n\u0010\u009f\u0001\u001a\u0005\u0018\u00010\u00a0\u0001H\u0014J\u0013\u0010\u00a1\u0001\u001a\u0002032\b\u0010\u00a2\u0001\u001a\u00030\u00a3\u0001H\u0016J\t\u0010\u00a4\u0001\u001a\u00020oH\u0014J\u0013\u0010\u00a5\u0001\u001a\u00020o2\b\u0010\u00a6\u0001\u001a\u00030\u00a7\u0001H\u0014J\u0013\u0010\u00a8\u0001\u001a\u0002032\b\u0010\u00a9\u0001\u001a\u00030\u00aa\u0001H\u0016J\t\u0010\u00ab\u0001\u001a\u00020oH\u0014J3\u0010\u00ac\u0001\u001a\u00020o2\u0007\u0010\u00ad\u0001\u001a\u00020\u00072\u0010\u0010\u00ae\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u00020!0\u00af\u00012\u0007\u0010\u00b0\u0001\u001a\u00020\u0013H\u0016\u00a2\u0006\u0003\u0010\u00b1\u0001J\t\u0010\u00b2\u0001\u001a\u00020oH\u0014J\u0015\u0010\u00b3\u0001\u001a\u00020o2\n\u0010\u00b4\u0001\u001a\u0005\u0018\u00010\u00b5\u0001H\u0016J\t\u0010\u00b6\u0001\u001a\u00020oH\u0002J\t\u0010\u00b7\u0001\u001a\u00020oH\u0014J\t\u0010\u00b8\u0001\u001a\u00020oH\u0014J\t\u0010\u00b9\u0001\u001a\u00020oH\u0002J\t\u0010\u00ba\u0001\u001a\u00020oH\u0002J\t\u0010\u00bb\u0001\u001a\u00020oH\u0002J\u0012\u0010\u00bb\u0001\u001a\u00020o2\u0007\u0010\u00bc\u0001\u001a\u00020\u0007H\u0002J\u0012\u0010\u00bd\u0001\u001a\u00020o2\u0007\u0010\u0088\u0001\u001a\u00020FH\u0002J\u001b\u0010\u00be\u0001\u001a\u00020o2\u0010\u0010\u00bf\u0001\u001a\u000b\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00c0\u0001H\u0002J\u0019\u0010\u00c1\u0001\u001a\u00020o2\u000e\u0010\u00c2\u0001\u001a\t\u0012\u0004\u0012\u00020F0\u008c\u0001H\u0002J\t\u0010\u00c3\u0001\u001a\u00020oH\u0002J\u0012\u0010\u00c4\u0001\u001a\u00020o2\u0007\u0010\u00c5\u0001\u001a\u00020\u0007H\u0002J\t\u0010\u00c6\u0001\u001a\u00020oH\u0002J\u0012\u0010\u00c7\u0001\u001a\u00020o2\u0007\u0010\u00c5\u0001\u001a\u00020\u0007H\u0002J\t\u0010\u00c8\u0001\u001a\u00020oH\u0002J\t\u0010\u00c9\u0001\u001a\u00020oH\u0002J\t\u0010\u00ca\u0001\u001a\u00020oH\u0002J\u0012\u0010\u00cb\u0001\u001a\u00020o2\u0007\u0010\u00cc\u0001\u001a\u00020FH\u0002J\u0012\u0010\u00cd\u0001\u001a\u00020o2\u0007\u0010\u00ce\u0001\u001a\u000203H\u0002J\u0013\u0010\u00cf\u0001\u001a\u00020o2\b\u0010\u00d0\u0001\u001a\u00030\u00d1\u0001H\u0002JN\u0010\u00d2\u0001\u001a\u00020o2\u0007\u0010\u00d3\u0001\u001a\u00020!2\u0006\u0010}\u001a\u00020!2\t\u0010\u00d4\u0001\u001a\u0004\u0018\u00010\u00072\t\u0010\u00d5\u0001\u001a\u0004\u0018\u00010!2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010\u00072\u000b\b\u0002\u0010\u00d7\u0001\u001a\u0004\u0018\u00010\u0007H\u0002\u00a2\u0006\u0003\u0010\u00d8\u0001J\u001b\u0010\u00d9\u0001\u001a\u00020o2\u0007\u0010\u0088\u0001\u001a\u00020F2\u0007\u0010\u00da\u0001\u001a\u00020!H\u0002J\t\u0010\u00db\u0001\u001a\u00020oH\u0002J\t\u0010\u00dc\u0001\u001a\u00020oH\u0002J\t\u0010\u00dd\u0001\u001a\u00020oH\u0002J\u0012\u0010\u00de\u0001\u001a\u00020o2\u0007\u0010\u0088\u0001\u001a\u00020FH\u0002J\t\u0010\u00df\u0001\u001a\u00020oH\u0002J\t\u0010\u00e0\u0001\u001a\u00020oH\u0002J\t\u0010\u00e1\u0001\u001a\u00020oH\u0002J\t\u0010\u00e2\u0001\u001a\u00020oH\u0002J\t\u0010\u00e3\u0001\u001a\u00020oH\u0002J\t\u0010\u00e4\u0001\u001a\u00020oH\u0002J\t\u0010\u00e5\u0001\u001a\u00020oH\u0002J\t\u0010\u00e6\u0001\u001a\u00020oH\u0002J\u0012\u0010\u00e7\u0001\u001a\u00020o2\u0007\u0010\u0088\u0001\u001a\u00020!H\u0002J\t\u0010\u00e8\u0001\u001a\u00020oH\u0002J\t\u0010\u00e9\u0001\u001a\u00020oH\u0002J\t\u0010\u00ea\u0001\u001a\u00020oH\u0002J\t\u0010\u00eb\u0001\u001a\u00020oH\u0002J\t\u0010\u00ec\u0001\u001a\u00020oH\u0002J\t\u0010\u00ed\u0001\u001a\u00020oH\u0002J\t\u0010\u00ee\u0001\u001a\u00020oH\u0002J\t\u0010\u00ef\u0001\u001a\u00020oH\u0002J\t\u0010\u00f0\u0001\u001a\u00020oH\u0002J\t\u0010\u00f1\u0001\u001a\u00020oH\u0002J\t\u0010\u00f2\u0001\u001a\u00020oH\u0002J\t\u0010\u00f3\u0001\u001a\u00020oH\u0002J\t\u0010\u00f4\u0001\u001a\u00020oH\u0002J\t\u0010\u00f5\u0001\u001a\u00020oH\u0002J\t\u0010\u00f6\u0001\u001a\u00020oH\u0002J\t\u0010\u00f7\u0001\u001a\u00020oH\u0002J#\u0010\u00f8\u0001\u001a\u00020o2\u0007\u0010\u00c5\u0001\u001a\u00020\u00072\u000f\u0010\u008b\u0001\u001a\n\u0012\u0005\u0012\u00030\u008d\u00010\u008c\u0001H\u0002J\u001c\u0010\u00f9\u0001\u001a\u00020o2\b\u0010\u00fa\u0001\u001a\u00030\u008d\u00012\u0007\u0010\u00fb\u0001\u001a\u000203H\u0002J\t\u0010\u00fc\u0001\u001a\u00020oH\u0002J\u001d\u0010\u00fd\u0001\u001a\u00020o2\u0007\u0010\u00fe\u0001\u001a\u0002032\t\u0010\u00ff\u0001\u001a\u0004\u0018\u00010!H\u0002J\t\u0010\u0080\u0002\u001a\u00020oH\u0002J\u0012\u0010\u0081\u0002\u001a\u00020o2\u0007\u0010\u0082\u0002\u001a\u00020]H\u0002JJ\u0010\u0083\u0002\u001a\u00020o2\u0007\u0010\u0084\u0002\u001a\u00020!2\u0006\u0010}\u001a\u00020!2\u0007\u0010\u0085\u0002\u001a\u00020\u00072\t\u0010\u00d4\u0001\u001a\u0004\u0018\u00010\u00072\t\u0010\u00d5\u0001\u001a\u0004\u0018\u00010!2\t\u0010\u00d6\u0001\u001a\u0004\u0018\u00010\u0007H\u0002\u00a2\u0006\u0003\u0010\u0086\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u00020\u0007X\u0082D\u00a2\u0006\u0004\n\u0002\b\bR\u000e\u0010\t\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0004\n\u0002\b\fR\u000e\u0010\r\u001a\u00020\u0005X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0014\u001a\u0004\u0018\u00010\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010(\u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020,X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u000200X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u000200X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u000203X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u000203X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u000203X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00107\u001a\u0004\u0018\u000108X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010<\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020>X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010?\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010@\u001a\u00020AX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010B\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010C\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010D\u001a\b\u0012\u0004\u0012\u00020F0EX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010G\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010H\u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010I\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010J\u001a\u000203X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010K\u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010L\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020F0MX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010N\u001a\b\u0012\u0004\u0012\u00020\u00070OX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010P\u001a\u00020QX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010R\u001a\u00020SX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010T\u001a\b\u0012\u0004\u0012\u00020!0UX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010V\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010W\u001a\u00020XX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010Y\u001a\u0004\u0018\u00010FX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010Z\u001a\b\u0012\u0004\u0012\u00020!0[X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\\\u001a\u0004\u0018\u00010]X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010^\u001a\u00020_X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010`\u001a\b\u0012\u0004\u0012\u00020\u00070OX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010a\u001a\u00020\u0005X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010b\u001a\u00020%X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010c\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010d\u001a\b\u0012\u0004\u0012\u00020]0[X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010e\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010f\u001a\u00020gX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010h\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010i\u001a\u0004\u0018\u00010jX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010k\u001a\u0004\u0018\u00010lX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010m\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0089\u0002"}, d2 = {"Lcom/spyro/vmeet/activity/ChatActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "Landroid/hardware/SensorEventListener;", "()V", "CONNECTION_CHECK_INTERVAL", "", "MAX_RECONNECT_ATTEMPTS", "", "MAX_RECONNECT_ATTEMPTS$1", "POLLING_DISABLE_DURATION", "POLLING_INTERVAL", "RECONNECT_DELAY_MS", "RECONNECT_DELAY_MS$1", "SHAKE_COOLDOWN_MS", "SHAKE_THRESHOLD", "", "accelerometer", "Landroid/hardware/Sensor;", "allMatchIds", "", "audioPlayer", "Lcom/spyro/vmeet/audio/AudioPlayer;", "audioRecorder", "Lcom/spyro/vmeet/audio/AudioRecorder;", "buttonCancelReply", "Landroid/widget/ImageButton;", "buttonEmoji", "buttonGif", "Landroid/widget/TextView;", "buttonImage", "buttonMic", "buttonSend", "chatTopic", "", "client", "Lokhttp3/OkHttpClient;", "connectionCheckHandler", "Landroid/os/Handler;", "connectionCheckRunnable", "Ljava/lang/Runnable;", "currentUserAvatarUrl", "currentUserId", "disablePollingUntil", "editTextMessage", "Landroid/widget/EditText;", "gson", "Lcom/google/gson/Gson;", "imageViewMenuOptions", "Landroid/widget/ImageView;", "imageViewOtherUserAvatar", "isChatActive", "", "isPollingActive", "isRecording", "lastBuzzTime", "lastCreatedImageFile", "Ljava/io/File;", "lastMessageId", "lastMessagePoll", "lastShakeTime", "lastStatusCheck", "layoutReplyPreview", "Landroid/widget/LinearLayout;", "matchId", "messageAdapter", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "messagePollingHandler", "messagePollingRunnable", "messagesList", "Ljava/util/ArrayList;", "Lcom/spyro/vmeet/data/Message;", "originalMatchId", "otherUserAvatar", "otherUserId", "otherUserIsViewing", "otherUserName", "pendingMessages", "Ljava/util/HashMap;", "processedMessageIds", "Ljava/util/HashSet;", "progressBar", "Landroid/widget/ProgressBar;", "reactionHandler", "Lcom/spyro/vmeet/util/ReactionHandler;", "recentlySentMessageTexts", "Ljava/util/LinkedHashSet;", "reconnectAttempts", "recyclerViewMessages", "Landroidx/recyclerview/widget/RecyclerView;", "replyingToMessage", "selectImageLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "selectedImageUri", "Landroid/net/Uri;", "sensorManager", "Landroid/hardware/SensorManager;", "sentMessageHashes", "statusCheckInterval", "statusHandler", "statusRunnable", "takePictureLauncher", "textViewOtherUserName", "textViewReplyPreview", "Landroidx/emoji2/widget/EmojiTextView;", "textViewStatus", "webSocket", "Lokhttp3/WebSocket;", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "webSocketRetryHandler", "attemptReconnection", "", "blockUser", "cancelVoiceRecording", "checkAndFixMessageReadStatus", "checkCameraPermissionsAndOpenCamera", "checkGalleryPermissionsAndOpenGallery", "checkIfUserIsAdmin", "userId", "checkIfUserIsVerified", "checkUserStatus", "clearAppBadge", "clearNotificationsForChat", "clearReplyState", "confirmMessageDelivery", "tempId", "createGson", "createImageFile", "createIsoTimestamp", "createMatchIfNeeded", "deleteChat", "ensureWebSocketConnection", "establishWebSocketConnection", "fetchCurrentUserAvatar", "fixKeyboardIssues", "generateMessageHash", "message", "getCurrentISOTimestamp", "handleReactionResponse", "reactions", "", "Lcom/spyro/vmeet/data/MessageReaction;", "wasRemoved", "handleReceivedBuzz", "initEmojiCompat", "initializeViews", "loadChatHistory", "markBuzzMessageAsError", "markMessageAsError", "markMessagesAsRead", "messageIds", "markUnreadMessagesAsRead", "onAccuracyChanged", "sensor", "accuracy", "onConfigurationChanged", "newConfig", "Landroid/content/res/Configuration;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateOptionsMenu", "menu", "Landroid/view/Menu;", "onDestroy", "onNewIntent", "intent", "Landroid/content/Intent;", "onOptionsItemSelected", "item", "Landroid/view/MenuItem;", "onPause", "onRequestPermissionsResult", "requestCode", "permissions", "", "grantResults", "(I[Ljava/lang/String;[I)V", "onResume", "onSensorChanged", "event", "Landroid/hardware/SensorEvent;", "onShakeDetected", "onStart", "onStop", "openCamera", "openOtherUserProfile", "pollForNewMessages", "sinceId", "processIncomingMessage", "processIncomingMessageFallback", "payload", "", "processNewMessages", "newMessages", "rebuildWebSocketConnection", "refreshMessageReactions", "messageId", "registerShakeDetection", "removeReaction", "scrollToBottom", "scrollToNewestMessage", "sendBuzz", "sendBuzzToServer", "buzzMessage", "sendChatVisibilityStatus", "isVisible", "sendGifMessage", "gif", "Lcom/spyro/vmeet/data/TenorGif;", "sendMessageViaHttp", "messageText", "replyId", "replyText", "replySenderId", "voiceDuration", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)V", "sendReaction", "reactionType", "sendWelcomeMessage", "setupKeyboardInsets", "setupRecyclerView", "setupReplyToMessage", "setupSendButton", "setupShakeDetection", "setupUserProfile", "setupVoiceMessageRecording", "showBlockUserConfirmation", "showChatOptionsMenu", "showDeleteChatConfirmation", "showEmojiPicker", "showErrorOnMainThread", "showGifPicker", "showImagePickerOptions", "startConnectionChecks", "startMessagePolling", "startReactionPolling", "startRealTimePolling", "startStatusUpdates", "startVoiceRecording", "stopConnectionChecks", "stopMessagePolling", "stopRealTimePolling", "stopStatusUpdates", "stopVoiceRecordingAndSend", "triggerBuzzEffects", "unregisterShakeDetection", "updateActiveChatId", "updateMessageReactionsInUI", "updateMessageWithReaction", "reaction", "isRemoval", "updatePrimaryMatchId", "updateStatusDisplay", "online", "lastSeen", "updateUI", "uploadImage", "imageUri", "uploadVoiceMessageAndSend", "filePath", "duration", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;)V", "Companion", "IntToBooleanAdapter", "app_release"})
public final class ChatActivity extends com.spyro.vmeet.activity.BaseActivity implements android.hardware.SensorEventListener {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TAG = "ChatActivity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_URL = "77.110.116.89";
    private static final int PORT = 3000;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String WS_URL = "ws://77.110.116.89:3000";
    private static final int NORMAL_CLOSURE_STATUS = 1000;
    private static final int MAX_RECONNECT_ATTEMPTS = 5;
    private static final long RECONNECT_DELAY_MS = 2000L;
    private static final long MESSAGE_POLL_INTERVAL = 1000L;
    private static final long BUZZ_COOLDOWN_MS = 10000L;
    private static final int REQUEST_IMAGE_PERMISSIONS = 101;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TENOR_API_KEY = "AIzaSyBlyFNlZao4Stf3p9n3gkv31G-ti-rhcRQ";
    private static final int TENOR_SEARCH_LIMIT = 20;
    private androidx.recyclerview.widget.RecyclerView recyclerViewMessages;
    private android.widget.EditText editTextMessage;
    private android.widget.ImageButton buttonSend;
    private android.widget.ImageButton buttonEmoji;
    private android.widget.ImageButton buttonImage;
    private android.widget.TextView buttonGif;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewOtherUserName;
    private android.widget.ImageView imageViewOtherUserAvatar;
    private android.widget.TextView textViewStatus;
    private android.widget.ImageView imageViewMenuOptions;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri selectedImageUri;
    @org.jetbrains.annotations.Nullable()
    private java.io.File lastCreatedImageFile;
    private long lastBuzzTime = 0L;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String> selectImageLauncher = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<android.net.Uri> takePictureLauncher = null;
    private com.spyro.vmeet.adapter.MessageAdapter messageAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.spyro.vmeet.data.Message> messagesList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.HashMap<java.lang.String, com.spyro.vmeet.data.Message> pendingMessages = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.LinkedHashSet<java.lang.String> recentlySentMessageTexts = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.HashSet<java.lang.Integer> processedMessageIds = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.HashSet<java.lang.Integer> sentMessageHashes = null;
    private long disablePollingUntil = 0L;
    private final long POLLING_DISABLE_DURATION = 2000L;
    private int matchId = -1;
    private int originalMatchId = -1;
    @org.jetbrains.annotations.Nullable()
    private int[] allMatchIds;
    private int currentUserId = -1;
    private int otherUserId = -1;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String otherUserName;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String otherUserAvatar;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String currentUserAvatarUrl;
    private boolean otherUserIsViewing = false;
    private boolean isChatActive = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String chatTopic;
    private okhttp3.OkHttpClient client;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.Nullable()
    private okhttp3.WebSocket webSocket;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.WebSocketClient webSocketClient;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler statusHandler = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable statusRunnable;
    private long lastStatusCheck = 0L;
    private long statusCheckInterval = 30000L;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler messagePollingHandler = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable messagePollingRunnable;
    private long lastMessagePoll = 0L;
    private int lastMessageId = -1;
    private boolean isPollingActive = false;
    private final long POLLING_INTERVAL = 1000L;
    private android.widget.ImageButton buttonMic;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.audio.AudioRecorder audioRecorder;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.audio.AudioPlayer audioPlayer;
    private boolean isRecording = false;
    private android.widget.LinearLayout layoutReplyPreview;
    private androidx.emoji2.widget.EmojiTextView textViewReplyPreview;
    private android.widget.ImageButton buttonCancelReply;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.Message replyingToMessage;
    private android.os.Handler webSocketRetryHandler;
    private int reconnectAttempts = 0;
    private final int MAX_RECONNECT_ATTEMPTS$1 = 5;
    private final long RECONNECT_DELAY_MS$1 = 3000L;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler connectionCheckHandler = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable connectionCheckRunnable;
    private final long CONNECTION_CHECK_INTERVAL = 10000L;
    private android.hardware.SensorManager sensorManager;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.Sensor accelerometer;
    private long lastShakeTime = 0L;
    private final float SHAKE_THRESHOLD = 12.0F;
    private final long SHAKE_COOLDOWN_MS = 2000L;
    private com.spyro.vmeet.util.ReactionHandler reactionHandler;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.ChatActivity.Companion Companion = null;
    
    public ChatActivity() {
        super();
    }
    
    private final com.google.gson.Gson createGson() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    public boolean onCreateOptionsMenu(@org.jetbrains.annotations.NotNull()
    android.view.Menu menu) {
        return false;
    }
    
    @java.lang.Override()
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull()
    android.view.MenuItem item) {
        return false;
    }
    
    private final void showBlockUserConfirmation() {
    }
    
    private final void blockUser() {
    }
    
    private final void showDeleteChatConfirmation() {
    }
    
    private final void deleteChat() {
    }
    
    @java.lang.Override()
    protected void onNewIntent(@org.jetbrains.annotations.NotNull()
    android.content.Intent intent) {
    }
    
    private final void initEmojiCompat() {
    }
    
    /**
     * Set up proper window insets handling for keyboard
     * This method handles the keyboard insets properly to avoid the black margin issue
     */
    private final void setupKeyboardInsets() {
    }
    
    private final void initializeViews() {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupUserProfile() {
    }
    
    private final void checkIfUserIsAdmin(int userId) {
    }
    
    private final void checkIfUserIsVerified(int userId) {
    }
    
    private final void sendWelcomeMessage() {
    }
    
    private final void setupSendButton() {
    }
    
    private final java.lang.String createIsoTimestamp() {
        return null;
    }
    
    private final void sendMessageViaHttp(java.lang.String messageText, java.lang.String tempId, java.lang.Integer replyId, java.lang.String replyText, java.lang.Integer replySenderId, java.lang.Integer voiceDuration) {
    }
    
    private final void confirmMessageDelivery(java.lang.String tempId) {
    }
    
    private final void markMessageAsError(java.lang.String tempId) {
    }
    
    /**
     * Create a match if it doesn't exist
     * This is used when we get a 403 error from the server, which means the user is not part of the match
     */
    private final void createMatchIfNeeded(int matchId, int userId, int otherUserId, java.lang.String tempId) {
    }
    
    private final void loadChatHistory() {
    }
    
    private final void markMessagesAsRead(java.util.List<java.lang.Integer> messageIds) {
    }
    
    private final void showErrorOnMainThread(java.lang.String message) {
    }
    
    /**
     * Update the primary match ID on the server
     * This ensures that the match ID used in this chat becomes the primary one
     * for future interactions between these users
     */
    private final void updatePrimaryMatchId() {
    }
    
    @java.lang.Override()
    protected void onStart() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    /**
     * Fix keyboard issues by clearing and requesting focus
     * This helps prevent the black margin issue when the keyboard is hidden
     */
    private final void fixKeyboardIssues() {
    }
    
    /**
     * Handle configuration changes like keyboard visibility or orientation changes
     */
    @java.lang.Override()
    public void onConfigurationChanged(@org.jetbrains.annotations.NotNull()
    android.content.res.Configuration newConfig) {
    }
    
    private final void startConnectionChecks() {
    }
    
    private final void stopConnectionChecks() {
    }
    
    private final void rebuildWebSocketConnection() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    @java.lang.Override()
    protected void onStop() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    /**
     * Start polling for new messages as a fallback mechanism for real-time updates
     * This ensures messages appear even if WebSockets aren't working properly
     */
    private final void startMessagePolling() {
    }
    
    /**
     * Stop polling for new messages
     */
    private final void stopMessagePolling() {
    }
    
    /**
     * Poll the server for new messages since our last known message ID
     */
    private final void pollForNewMessages() {
    }
    
    private final void markUnreadMessagesAsRead() {
    }
    
    private final void ensureWebSocketConnection() {
    }
    
    private final void establishWebSocketConnection() {
    }
    
    private final void processIncomingMessageFallback(java.util.Map<?, ?> payload) {
    }
    
    private final void attemptReconnection() {
    }
    
    private final void setupVoiceMessageRecording() {
    }
    
    private final void cancelVoiceRecording() {
    }
    
    private final void startVoiceRecording() {
    }
    
    private final void stopVoiceRecordingAndSend() {
    }
    
    private final void uploadVoiceMessageAndSend(java.lang.String filePath, java.lang.String tempId, int duration, java.lang.Integer replyId, java.lang.String replyText, java.lang.Integer replySenderId) {
    }
    
    @java.lang.Override()
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull()
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull()
    int[] grantResults) {
    }
    
    private final void setupReplyToMessage(com.spyro.vmeet.data.Message message) {
    }
    
    private final void clearReplyState() {
    }
    
    private final void checkAndFixMessageReadStatus() {
    }
    
    private final void showEmojiPicker() {
    }
    
    private final void showGifPicker() {
    }
    
    private final void sendGifMessage(com.spyro.vmeet.data.TenorGif gif) {
    }
    
    private final void processIncomingMessage(com.spyro.vmeet.data.Message message) {
    }
    
    private final int generateMessageHash(com.spyro.vmeet.data.Message message) {
        return 0;
    }
    
    private final void updateActiveChatId() {
    }
    
    private final void clearNotificationsForChat() {
    }
    
    private final void clearAppBadge() {
    }
    
    private final void sendChatVisibilityStatus(boolean isVisible) {
    }
    
    private final void processNewMessages(java.util.List<com.spyro.vmeet.data.Message> newMessages) {
    }
    
    private final void scrollToNewestMessage() {
    }
    
    private final void openOtherUserProfile() {
    }
    
    private final void startStatusUpdates() {
    }
    
    private final void stopStatusUpdates() {
    }
    
    private final void checkUserStatus() {
    }
    
    private final void updateStatusDisplay(boolean online, java.lang.String lastSeen) {
    }
    
    private final void sendReaction(com.spyro.vmeet.data.Message message, java.lang.String reactionType) {
    }
    
    private final void removeReaction(int messageId) {
    }
    
    private final void startRealTimePolling() {
    }
    
    private final void stopRealTimePolling() {
    }
    
    private final void pollForNewMessages(int sinceId) {
    }
    
    private final void handleReactionResponse(java.util.List<com.spyro.vmeet.data.MessageReaction> reactions, boolean wasRemoved) {
    }
    
    private final void updateMessageWithReaction(com.spyro.vmeet.data.MessageReaction reaction, boolean isRemoval) {
    }
    
    private final void refreshMessageReactions(int messageId) {
    }
    
    private final void updateMessageReactionsInUI(int messageId, java.util.List<com.spyro.vmeet.data.MessageReaction> reactions) {
    }
    
    private final java.lang.String getCurrentISOTimestamp() {
        return null;
    }
    
    private final void startReactionPolling() {
    }
    
    private final void showImagePickerOptions() {
    }
    
    private final void checkCameraPermissionsAndOpenCamera() {
    }
    
    private final void checkGalleryPermissionsAndOpenGallery() {
    }
    
    private final void openCamera() {
    }
    
    private final java.io.File createImageFile() {
        return null;
    }
    
    private final void updateUI() {
    }
    
    private final void scrollToBottom() {
    }
    
    private final void uploadImage(android.net.Uri imageUri) {
    }
    
    private final void fetchCurrentUserAvatar() {
    }
    
    private final void showChatOptionsMenu() {
    }
    
    private final void sendBuzz() {
    }
    
    private final void triggerBuzzEffects() {
    }
    
    private final void sendBuzzToServer(com.spyro.vmeet.data.Message buzzMessage) {
    }
    
    private final void markBuzzMessageAsError() {
    }
    
    private final void handleReceivedBuzz(com.spyro.vmeet.data.Message message) {
    }
    
    private final void setupShakeDetection() {
    }
    
    private final void registerShakeDetection() {
    }
    
    private final void unregisterShakeDetection() {
    }
    
    @java.lang.Override()
    public void onSensorChanged(@org.jetbrains.annotations.Nullable()
    android.hardware.SensorEvent event) {
    }
    
    @java.lang.Override()
    public void onAccuracyChanged(@org.jetbrains.annotations.Nullable()
    android.hardware.Sensor sensor, int accuracy) {
    }
    
    private final void onShakeDetected() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/spyro/vmeet/activity/ChatActivity$Companion;", "", "()V", "API_URL", "", "BASE_URL", "BUZZ_COOLDOWN_MS", "", "MAX_RECONNECT_ATTEMPTS", "", "MESSAGE_POLL_INTERVAL", "NORMAL_CLOSURE_STATUS", "PORT", "RECONNECT_DELAY_MS", "REQUEST_IMAGE_PERMISSIONS", "TAG", "TENOR_API_KEY", "TENOR_SEARCH_LIMIT", "WS_URL", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0002\u0018\u0000 \u00112\b\u0012\u0004\u0012\u00020\u00020\u00012\b\u0012\u0004\u0012\u00020\u00020\u0003:\u0001\u0011B\u0005\u00a2\u0006\u0002\u0010\u0004J%\u0010\u0005\u001a\u00020\u00022\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0016\u00a2\u0006\u0002\u0010\fJ \u0010\r\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u00022\u0006\u0010\u000f\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u0010H\u0016\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/activity/ChatActivity$IntToBooleanAdapter;", "Lcom/google/gson/JsonSerializer;", "", "Lcom/google/gson/JsonDeserializer;", "()V", "deserialize", "json", "Lcom/google/gson/JsonElement;", "typeOfT", "Ljava/lang/reflect/Type;", "context", "Lcom/google/gson/JsonDeserializationContext;", "(Lcom/google/gson/JsonElement;Ljava/lang/reflect/Type;Lcom/google/gson/JsonDeserializationContext;)Ljava/lang/Boolean;", "serialize", "src", "typeOfSrc", "Lcom/google/gson/JsonSerializationContext;", "Companion", "app_release"})
    static final class IntToBooleanAdapter implements com.google.gson.JsonSerializer<java.lang.Boolean>, com.google.gson.JsonDeserializer<java.lang.Boolean> {
        @org.jetbrains.annotations.NotNull()
        private static final java.lang.String TAG = "IntToBooleanAdapter";
        @org.jetbrains.annotations.NotNull()
        public static final com.spyro.vmeet.activity.ChatActivity.IntToBooleanAdapter.Companion Companion = null;
        
        public IntToBooleanAdapter() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.google.gson.JsonElement serialize(boolean src, @org.jetbrains.annotations.NotNull()
        java.lang.reflect.Type typeOfSrc, @org.jetbrains.annotations.NotNull()
        com.google.gson.JsonSerializationContext context) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.Boolean deserialize(@org.jetbrains.annotations.NotNull()
        com.google.gson.JsonElement json, @org.jetbrains.annotations.NotNull()
        java.lang.reflect.Type typeOfT, @org.jetbrains.annotations.NotNull()
        com.google.gson.JsonDeserializationContext context) {
            return null;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/activity/ChatActivity$IntToBooleanAdapter$Companion;", "", "()V", "TAG", "", "app_release"})
        public static final class Companion {
            
            private Companion() {
                super();
            }
        }
    }
}