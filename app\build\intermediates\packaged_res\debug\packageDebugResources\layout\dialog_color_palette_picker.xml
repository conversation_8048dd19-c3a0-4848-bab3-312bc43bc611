<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/rounded_dialog_background">
    
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Selecciona un color"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:layout_marginBottom="16dp" />
    
    <View
        android:id="@+id/colorPreviewInDialog"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/preview_background" />
    
    <!-- Color Palette Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/section_background"
        android:padding="16dp">
        
        <!-- Row 1: Primary Colors -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <View
                android:id="@+id/colorWhite"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorBlack"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#000000"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorRed"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#FF0000"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorGreen"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#00FF00"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />
        </LinearLayout>

        <!-- Row 2: More Colors -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <View
                android:id="@+id/colorBlue"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#0000FF"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorYellow"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#FFFF00"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorPink"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#FF00FF"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorOrange"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#FFA500"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />
        </LinearLayout>

        <!-- Row 3: More Colors -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <View
                android:id="@+id/colorPurple"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#800080"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorCyan"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#00FFFF"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorBrown"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#A52A2A"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorGray"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#808080"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />
        </LinearLayout>

        <!-- Row 4: Neon/Vibrant Colors -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <View
                android:id="@+id/colorNeonPink"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@color/neon_pink"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorNeonBlue"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@color/neon_blue"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorCyberpunkYellow"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="@color/cyberpunk_yellow"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />

            <View
                android:id="@+id/colorNeonGreen"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:background="#39FF14"
                android:layout_margin="4dp"
                android:clickable="true"
                android:focusable="true"
                android:elevation="2dp" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout> 