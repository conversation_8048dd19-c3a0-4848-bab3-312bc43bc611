package com.spyro.vmeet.data

data class PersonalityTrait(
    val id: String,
    val name: String,
    val category: String,
    val iconRes: Int,
    var isSelected: Boolean = false
)

object PersonalityTraits {

    // Categorías de rasgos
    const val CATEGORY_PRONOUNS = "pronouns"
    const val CATEGORY_SEXUALITY = "sexuality"
    const val CATEGORY_RELATIONSHIP = "relationship"
    const val CATEGORY_FAMILY = "family"
    const val CATEGORY_DATING = "dating"
    const val CATEGORY_MATCHING = "matching"
    const val CATEGORY_DATE_TYPE = "date_type"
    const val CATEGORY_FACE_REVEALS = "face_reveals"
    const val CATEGORY_EDUCATION = "education"
    const val CATEGORY_SCHOOL = "school"
    const val CATEGORY_WORK = "work"
    const val CATEGORY_RELIGION = "religion"
    const val CATEGORY_PETS = "pets"
    const val CATEGORY_LIFESTYLE = "lifestyle"

    fun getAllTraits(): List<PersonalityTrait> {
        return listOf(
            // Pronombres
            PersonalityTrait("he_him", "Él/Él", CATEGORY_PRONOUNS, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("she_her", "Ella/Ella", CATEGORY_PRONOUNS, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("they_them", "Elle/Elle", CATEGORY_PRONOUNS, com.spyro.vmeet.R.drawable.ic_profile),

            // Sexualidad
            PersonalityTrait("straight", "Heterosexual", CATEGORY_SEXUALITY, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("gay", "Gay", CATEGORY_SEXUALITY, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("lesbian", "Lesbiana", CATEGORY_SEXUALITY, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("bisexual", "Bisexual", CATEGORY_SEXUALITY, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("pansexual", "Pansexual", CATEGORY_SEXUALITY, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("asexual", "Asexual", CATEGORY_SEXUALITY, com.spyro.vmeet.R.drawable.ic_heart),

            // Tipo de relación
            PersonalityTrait("monogamy", "Monogamia", CATEGORY_RELATIONSHIP, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("polyamory", "Poliamor", CATEGORY_RELATIONSHIP, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("open_relationship", "Relación abierta", CATEGORY_RELATIONSHIP, com.spyro.vmeet.R.drawable.ic_heart),

            // Planes familiares
            PersonalityTrait("want_children", "Quiero hijos", CATEGORY_FAMILY, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("have_children", "Tengo hijos", CATEGORY_FAMILY, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("no_children", "No quiero hijos", CATEGORY_FAMILY, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("open_to_children", "Abierto a hijos", CATEGORY_FAMILY, com.spyro.vmeet.R.drawable.ic_profile),

            // Intenciones de citas
            PersonalityTrait("life_partner", "Pareja de vida", CATEGORY_DATING, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("long_term", "Relación seria", CATEGORY_DATING, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("short_term", "Algo casual", CATEGORY_DATING, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("friendship", "Amistad", CATEGORY_DATING, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("figuring_out", "Viendo qué pasa", CATEGORY_DATING, com.spyro.vmeet.R.drawable.ic_heart),

            // Abierto a emparejar personas trans
            PersonalityTrait("trans_inclusive", "Inclusivo con personas trans", CATEGORY_MATCHING, com.spyro.vmeet.R.drawable.ic_check),

            // Preferencias de tipo de cita
            PersonalityTrait("voice_calls", "Llamadas de voz", CATEGORY_DATE_TYPE, com.spyro.vmeet.R.drawable.ic_audiotrack),
            PersonalityTrait("video_calls", "Videollamadas", CATEGORY_DATE_TYPE, com.spyro.vmeet.R.drawable.ic_play),
            PersonalityTrait("in_vr", "En VR", CATEGORY_DATE_TYPE, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("in_real_life", "En la vida real", CATEGORY_DATE_TYPE, com.spyro.vmeet.R.drawable.ic_community),
            PersonalityTrait("text_chat", "Chat de texto", CATEGORY_DATE_TYPE, com.spyro.vmeet.R.drawable.ic_chat),

            // Abierto a revelar rostro
            PersonalityTrait("face_reveals_always", "Siempre", CATEGORY_FACE_REVEALS, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("face_reveals_sometimes", "A veces", CATEGORY_FACE_REVEALS, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("face_reveals_never", "Nunca", CATEGORY_FACE_REVEALS, com.spyro.vmeet.R.drawable.ic_close_circle),

            // Nivel educativo
            PersonalityTrait("elementary", "Educación Primaria", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("middle_school", "Educación Secundaria (ESO)", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("high_school", "Bachillerato", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("grado_medio", "Grado Medio (FP)", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("grado_superior", "Grado Superior (FP)", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("some_college", "Universidad parcial", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("undergraduate", "Licenciatura", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("graduate", "Posgrado", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("master", "Máster", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("phd", "Doctorado", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("professional_training", "Formación Profesional", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("continuing_education", "Formación Continua", CATEGORY_EDUCATION, com.spyro.vmeet.R.drawable.ic_eye),

            // Estado escolar
            PersonalityTrait("in_school", "Estudiando", CATEGORY_SCHOOL, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("not_in_school", "No estudiando", CATEGORY_SCHOOL, com.spyro.vmeet.R.drawable.ic_close_circle),

            // Trabajo
            PersonalityTrait("developer", "Desarrollador", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("designer", "Diseñador", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_add_image),
            PersonalityTrait("student", "Estudiante", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("teacher", "Profesor", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("engineer", "Ingeniero", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("artist", "Artista", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_add_image),
            PersonalityTrait("entrepreneur", "Emprendedor", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),

            // Sanidad
            PersonalityTrait("doctor", "Médico", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("nurse", "Enfermero/a", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("dentist", "Dentista", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("pharmacist", "Farmacéutico", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("psychologist", "Psicólogo", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("physiotherapist", "Fisioterapeuta", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("veterinarian", "Veterinario", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),

            // Servicios y Administración
            PersonalityTrait("lawyer", "Abogado", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("accountant", "Contable", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("banker", "Banquero", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("consultant", "Consultor", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("manager", "Gerente", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),
            PersonalityTrait("hr_specialist", "Recursos Humanos", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("marketing", "Marketing", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),
            PersonalityTrait("sales", "Ventas", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),

            // Educación y Cultura
            PersonalityTrait("researcher", "Investigador", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_search),
            PersonalityTrait("librarian", "Bibliotecario", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_eye),
            PersonalityTrait("journalist", "Periodista", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("translator", "Traductor", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("social_worker", "Trabajador Social", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),

            // Tecnología y Comunicaciones
            PersonalityTrait("data_scientist", "Científico de Datos", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("cybersecurity", "Ciberseguridad", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("it_support", "Soporte IT", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("photographer", "Fotógrafo", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_add_image),
            PersonalityTrait("videographer", "Videomaker", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_play),

            // Servicios Públicos y Seguridad
            PersonalityTrait("police_officer", "Policía", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("firefighter", "Bombero", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("military", "Militar", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("civil_servant", "Funcionario", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),

            // Industria y Construcción
            PersonalityTrait("architect", "Arquitecto", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("construction", "Construcción", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("electrician", "Electricista", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("plumber", "Fontanero", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),
            PersonalityTrait("mechanic", "Mecánico", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),

            // Hostelería y Turismo
            PersonalityTrait("chef", "Chef", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("waiter", "Camarero", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("hotel_manager", "Hotelero", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),
            PersonalityTrait("tour_guide", "Guía Turístico", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_search),

            // Transporte y Logística
            PersonalityTrait("pilot", "Piloto", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_search),
            PersonalityTrait("driver", "Conductor", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_search),
            PersonalityTrait("logistics", "Logística", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_settings),

            // Otros sectores
            PersonalityTrait("real_estate", "Inmobiliario", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),
            PersonalityTrait("insurance", "Seguros", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("fitness_trainer", "Entrenador Personal", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("beauty_specialist", "Estética", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("freelancer", "Freelancer", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_community),
            PersonalityTrait("retired", "Jubilado", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("unemployed", "Desempleado", CATEGORY_WORK, com.spyro.vmeet.R.drawable.ic_search),

            // Religión
            PersonalityTrait("christian", "Cristiano", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("muslim", "Musulmán", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("jewish", "Judío", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("buddhist", "Budista", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("hindu", "Hindú", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("atheist", "Ateo", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_close_circle),
            PersonalityTrait("agnostic", "Agnóstico", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_search),
            PersonalityTrait("spiritual", "Espiritual", CATEGORY_RELIGION, com.spyro.vmeet.R.drawable.ic_heart),

            // Mascotas
            PersonalityTrait("dog_lover", "Amante de perros", CATEGORY_PETS, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("cat_lover", "Amante de gatos", CATEGORY_PETS, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("no_pets", "Sin mascotas", CATEGORY_PETS, com.spyro.vmeet.R.drawable.ic_close_circle),
            PersonalityTrait("exotic_pets", "Mascotas exóticas", CATEGORY_PETS, com.spyro.vmeet.R.drawable.ic_eye),

            // Estilo de vida
            PersonalityTrait("smoker", "Fumador", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_close_circle),
            PersonalityTrait("non_smoker", "No fumador", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("social_drinker", "Bebedor social", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("non_drinker", "No bebe", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("cannabis_friendly", "Cannabis amigable", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_heart),
            PersonalityTrait("fitness_enthusiast", "Entusiasta del fitness", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_check),
            PersonalityTrait("gamer", "Gamer", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_play),
            PersonalityTrait("traveler", "Viajero", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_search),
            PersonalityTrait("homebody", "Hogareño", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_profile),
            PersonalityTrait("party_person", "Fiestero", CATEGORY_LIFESTYLE, com.spyro.vmeet.R.drawable.ic_community)
        )
    }

    fun getTraitsByCategory(category: String): List<PersonalityTrait> {
        return getAllTraits().filter { it.category == category }
    }

    fun getTraitById(id: String): PersonalityTrait? {
        return getAllTraits().find { it.id == id }
    }

    fun getTraitsByCategory(): List<com.spyro.vmeet.adapter.TraitCategory> {
        val categoryMap = mapOf(
            CATEGORY_PRONOUNS to "Pronombres",
            CATEGORY_SEXUALITY to "Sexualidad",
            CATEGORY_RELATIONSHIP to "Relaciones",
            CATEGORY_FAMILY to "Familia",
            CATEGORY_DATING to "Citas",
            CATEGORY_MATCHING to "Compatibilidad",
            CATEGORY_DATE_TYPE to "Tipo de cita",
            CATEGORY_FACE_REVEALS to "Revelar rostro",
            CATEGORY_EDUCATION to "Educación",
            CATEGORY_SCHOOL to "Estado escolar",
            CATEGORY_WORK to "Trabajo",
            CATEGORY_RELIGION to "Religión",
            CATEGORY_PETS to "Mascotas",
            CATEGORY_LIFESTYLE to "Estilo de vida"
        )

        return getAllTraits()
            .groupBy { it.category }
            .map { (categoryId, traits) ->
                com.spyro.vmeet.adapter.TraitCategory(
                    name = categoryMap[categoryId] ?: categoryId,
                    traits = traits.map { it.copy() }.toMutableList()
                )
            }
            .sortedBy { category ->
                // Ordenar categorías en un orden específico
                when (category.name) {
                    "Pronombres" -> 1
                    "Sexualidad" -> 2
                    "Relaciones" -> 3
                    "Familia" -> 4
                    "Citas" -> 5
                    "Compatibilidad" -> 6
                    "Tipo de cita" -> 7
                    "Revelar rostro" -> 8
                    "Educación" -> 9
                    "Estado escolar" -> 10
                    "Trabajo" -> 11
                    "Religión" -> 12
                    "Mascotas" -> 13
                    "Estilo de vida" -> 14
                    else -> 15
                }
            }
    }
}
