package com.spyro.vmeet.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\b\u0010\t\u001a\u00020\nH\u0002J\b\u0010\u000b\u001a\u00020\nH\u0002J\b\u0010\f\u001a\u00020\nH\u0002J\u0010\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u000e\u0010\u0010\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/ui/CommunityDropdownMenu;", "", "context", "Landroid/content/Context;", "userId", "", "(Landroid/content/Context;I)V", "popupWindow", "Landroid/widget/PopupWindow;", "dismiss", "", "navigateToCommunity", "navigateToVideos", "setupMenuItems", "popupView", "Landroid/view/View;", "show", "anchorView", "app_release"})
public final class CommunityDropdownMenu {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final int userId = 0;
    @org.jetbrains.annotations.Nullable()
    private android.widget.PopupWindow popupWindow;
    
    public CommunityDropdownMenu(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int userId) {
        super();
    }
    
    public final void show(@org.jetbrains.annotations.NotNull()
    android.view.View anchorView) {
    }
    
    private final void setupMenuItems(android.view.View popupView) {
    }
    
    private final void navigateToCommunity() {
    }
    
    private final void navigateToVideos() {
    }
    
    private final void dismiss() {
    }
}