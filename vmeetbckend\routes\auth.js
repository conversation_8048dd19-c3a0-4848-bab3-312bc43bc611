const express = require('express');
const bcrypt = require('bcryptjs');
const { getConnection } = require('../db');
const router = express.Router();
const emailService = require('../utils/emailService');
const verificationCodes = require('../utils/verificationCodes');
const {
    generateAccessToken,
    generateRefreshToken,
    storeRefreshToken,
    verifyAccessToken,
    verifyRefreshToken,
    removeRefreshToken,
    securityLogger
} = require('../middleware/jwtAuth');
const { authLimiter, registerLimiter, passwordResetLimiter } = require('../middleware/rateLimiting');

// Registro
router.post('/register', registerLimiter, async (req, res) => {
    const { username, email, password, deviceSerial, lastIp, birthdate, gender, sexuality } = req.body;
    if (!username || !email || !password) {
        return res.status(400).json({ error: 'Faltan datos obligatorios' });
    }

    if (!birthdate) {
        return res.status(400).json({ error: 'La fecha de nacimiento es obligatoria' });
    }

    // Username validation - only allow alphanumeric characters and underscores
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(username)) {
        return res.status(400).json({
            error: 'Nombre de usuario inválido',
            message: 'El nombre de usuario solo puede contener letras, números y guiones bajos'
        });
    }
    let conn;
    try {
        conn = await getConnection();
        const [rows] = await conn.execute(
            'SELECT id FROM users WHERE username = ? OR email = ?', [username, email]
        );
        if (rows.length > 0) {
            conn.release();
            return res.status(409).json({ error: 'Usuario o email ya existe' });
        }
        // Verificar si existe una columna 'email_verified' en la tabla users
        const [columns] = await conn.execute(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'users'
            AND COLUMN_NAME = 'email_verified'
        `);

        // Si no existe la columna, crearla
        if (columns.length === 0) {
            await conn.execute(`
                ALTER TABLE users
                ADD COLUMN email_verified BOOLEAN DEFAULT FALSE
            `);
        }

        const hash = await bcrypt.hash(password, 10);
        const [result] = await conn.execute(
            'INSERT INTO users (username, email, password, role, device_serial, last_ip, birthdate, gender, sexuality, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            [username, email, hash, 'usuario', deviceSerial || null, lastIp || null, birthdate, gender || null, sexuality || null, false]
        );

        // Generar código de verificación
        const verificationCode = verificationCodes.generateVerificationCode();

        // Guardar código en la base de datos
        await verificationCodes.saveVerificationCode(email, verificationCode, 'verification');

        // Enviar correo de verificación
        const emailSent = await emailService.sendVerificationEmail(email, username, verificationCode);

        conn.release();

        res.json({
            success: true,
            message: 'Usuario registrado correctamente. Por favor verifica tu correo electrónico.',
            userId: result.insertId,
            verification_email_sent: emailSent
        });
    } catch (err) {
        if (conn) conn.release().catch(() => {});
        res.status(500).json({ error: 'Error en el servidor', details: err.message });
    }
});

// Login
router.post('/login', authLimiter, async (req, res) => {
    const { username, password } = req.body;
    if (!username || !password) {
        return res.status(400).json({ error: 'Faltan datos obligatorios' });
    }
    let conn;
    try {
        conn = await getConnection();
        // Get user info
        const [rows] = await conn.execute(
            'SELECT * FROM users WHERE username = ? OR email = ?', [username, username]
        );

        // Update device info if provided
        if (req.body.deviceSerial || req.body.lastIp) {
            await conn.execute(
                'UPDATE users SET device_serial = ?, last_ip = ? WHERE id = ?',
                [req.body.deviceSerial || null, req.body.lastIp || null, rows[0]?.id]
            );
        }
        if (rows.length === 0) {
            conn.release();
            return res.status(401).json({ error: 'Usuario no encontrado' });
        }
        const user = rows[0];
        const match = await bcrypt.compare(password, user.password);

        // Check if user is banned
        const [bans] = await conn.execute(
            'SELECT * FROM user_bans WHERE user_id = ? AND (banned_until IS NULL OR banned_until > NOW()) ORDER BY id DESC LIMIT 1',
            [user.id]
        );

        conn.release();

        if (!match) return res.status(401).json({ error: 'Contraseña incorrecta' });

        // If user is banned, prevent login
        if (bans.length > 0) {
            const ban = bans[0];
            return res.status(403).json({
                error: 'Cuenta suspendida',
                message: 'Tu cuenta ha sido suspendida',
                reason: ban.reason,
                bannedUntil: ban.banned_until // Could be null for permanent bans
            });
        }

        // Verificar si el correo electrónico está verificado
        // Solo si la columna email_verified existe
        let emailVerified = true; // Por defecto asumimos que está verificado (para compatibilidad)

        try {
            const [columns] = await conn.execute(`
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'users'
                AND COLUMN_NAME = 'email_verified'
            `);

            if (columns.length > 0) {
                const [verificationStatus] = await conn.execute(
                    'SELECT email_verified FROM users WHERE id = ?',
                    [user.id]
                );

                if (verificationStatus.length > 0) {
                    emailVerified = verificationStatus[0].email_verified === 1;
                }
            }
        } catch (error) {
            console.error('Error al verificar estado de email:', error);
        }

        // Generate JWT tokens
        const userTokenInfo = {
            username: user.username,
            email: user.email,
            role: user.role
        };

        const accessToken = generateAccessToken(user.id, userTokenInfo);
        const refreshToken = generateRefreshToken(user.id);

        // Store refresh token
        await storeRefreshToken(user.id, refreshToken);

        // Log successful login
        securityLogger.info('Successful login', {
            userId: user.id,
            username: user.username,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });

        const { password: _, ...userInfo } = user;
        res.json({
            success: true,
            userId: userInfo.id,
            accessToken,
            refreshToken,
            user: {
                ...userInfo,
                isAdmin: userInfo.role === 'admin',
                email_verified: emailVerified
            }
        });
    } catch (err) {
        if (conn) conn.release().catch(() => {});
        res.status(500).json({ error: 'Error en el servidor', details: err.message });
    }
});

// Update device info
router.post('/update-device-info', async (req, res) => {
    const { userId, deviceSerial, lastIp } = req.body;
    if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    let conn;
    try {
        conn = await getConnection();
        await conn.execute(
            'UPDATE users SET device_serial = ?, last_ip = ? WHERE id = ?',
            [deviceSerial || null, lastIp || null, userId]
        );
        conn.release();
        res.json({ success: true });
    } catch (err) {
        if (conn) conn.release().catch(() => {});
        res.status(500).json({ error: 'Error updating device info', details: err.message });
    }
});

// Endpoint para verificar un correo electrónico
router.post('/verify_email', async (req, res) => {
    const { email, code } = req.body;

    if (!email || !code) {
        return res.status(400).json({ error: 'Faltan datos obligatorios' });
    }

    try {
        // Verificar el código
        const isValid = await verificationCodes.verifyCode(email, code, 'verification');

        if (!isValid) {
            return res.status(400).json({
                success: false,
                error: 'Código inválido o expirado'
            });
        }

        // Activar la cuenta
        const activated = await verificationCodes.activateAccount(email);

        if (!activated) {
            return res.status(500).json({
                success: false,
                error: 'Error al activar la cuenta'
            });
        }

        res.json({
            success: true,
            message: 'Correo electrónico verificado correctamente'
        });
    } catch (error) {
        console.error('Error al verificar correo:', error);
        res.status(500).json({
            success: false,
            error: 'Error en el servidor',
            details: error.message
        });
    }
});

// Endpoint para reenviar el código de verificación
router.post('/resend_verification', async (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.status(400).json({ error: 'Falta el correo electrónico' });
    }

    try {
        // Verificar si el correo existe
        const { exists, username } = await verificationCodes.checkEmailExists(email);

        if (!exists) {
            return res.status(404).json({
                success: false,
                error: 'No existe una cuenta con este correo electrónico'
            });
        }

        // Generar nuevo código
        const verificationCode = verificationCodes.generateVerificationCode();

        // Guardar código
        await verificationCodes.saveVerificationCode(email, verificationCode, 'verification');

        // Enviar correo
        const emailSent = await emailService.sendVerificationEmail(email, username, verificationCode);

        if (!emailSent) {
            return res.status(500).json({
                success: false,
                error: 'Error al enviar el correo electrónico'
            });
        }

        res.json({
            success: true,
            message: 'Se ha enviado un nuevo código de verificación'
        });
    } catch (error) {
        console.error('Error al reenviar código:', error);
        res.status(500).json({
            success: false,
            error: 'Error en el servidor',
            details: error.message
        });
    }
});

// Endpoint para verificar si un correo existe
router.post('/check_email', async (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.status(400).json({ error: 'Falta el correo electrónico' });
    }

    try {
        const result = await verificationCodes.checkEmailExists(email);
        res.json(result);
    } catch (error) {
        console.error('Error al verificar correo:', error);
        res.status(500).json({
            success: false,
            error: 'Error en el servidor',
            details: error.message
        });
    }
});

// Endpoint para solicitar restablecimiento de contraseña
router.post('/reset_password_request', async (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.status(400).json({ error: 'Falta el correo electrónico' });
    }

    try {
        // Verificar si el correo existe
        const { exists, username } = await verificationCodes.checkEmailExists(email);

        if (!exists) {
            return res.status(404).json({
                success: false,
                error: 'No existe una cuenta con este correo electrónico'
            });
        }

        // Generar código de restablecimiento
        const resetCode = verificationCodes.generateVerificationCode();

        // Guardar código
        await verificationCodes.saveVerificationCode(email, resetCode, 'reset');

        // Enviar correo
        const emailSent = await emailService.sendPasswordResetEmail(email, username, resetCode);

        if (!emailSent) {
            return res.status(500).json({
                success: false,
                error: 'Error al enviar el correo electrónico'
            });
        }

        res.json({
            success: true,
            message: 'Se ha enviado un código de restablecimiento a tu correo'
        });
    } catch (error) {
        console.error('Error al solicitar restablecimiento:', error);
        res.status(500).json({
            success: false,
            error: 'Error en el servidor',
            details: error.message
        });
    }
});

// Endpoint para verificar código de restablecimiento
router.post('/verify_reset_code', async (req, res) => {
    const { email, code } = req.body;

    if (!email || !code) {
        return res.status(400).json({ error: 'Faltan datos obligatorios' });
    }

    try {
        // Verificar el código
        const isValid = await verificationCodes.verifyCode(email, code, 'reset');

        res.json({
            success: isValid,
            message: isValid ? 'Código válido' : 'Código inválido o expirado'
        });
    } catch (error) {
        console.error('Error al verificar código:', error);
        res.status(500).json({
            success: false,
            error: 'Error en el servidor',
            details: error.message
        });
    }
});

// Endpoint para restablecer contraseña
router.post('/reset_password', async (req, res) => {
    const { email, code, new_password } = req.body;

    if (!email || !code || !new_password) {
        return res.status(400).json({ error: 'Faltan datos obligatorios' });
    }

    try {
        console.log(`Intentando restablecer contraseña para ${email} con código ${code}`);

        // Verificar si el correo existe
        const { exists } = await verificationCodes.checkEmailExists(email);

        if (!exists) {
            console.log(`El correo ${email} no existe en la base de datos`);
            return res.status(404).json({
                success: false,
                error: 'No existe una cuenta con este correo electrónico'
            });
        }

        // Verificar si hay un código de restablecimiento válido para este email
        // (sin marcar como usado, solo para verificar que existe)
        let conn;
        try {
            conn = await getConnection();

            // Buscar código válido
            const [rows] = await conn.execute(
                'SELECT * FROM verification_codes WHERE email = ? AND type = ? AND expires_at > NOW()',
                [email, 'reset']
            );

            if (rows.length === 0) {
                console.log(`No hay códigos de restablecimiento válidos para ${email}`);
                conn.release();
                return res.status(400).json({
                    success: false,
                    error: 'No hay solicitudes de restablecimiento válidas para este correo'
                });
            }

            conn.release();
        } catch (error) {
            console.error('Error al verificar código de restablecimiento:', error);
            if (conn) conn.release().catch(() => {});
            throw error;
        }

        // Hashear la nueva contraseña
        const hash = await bcrypt.hash(new_password, 10);
        console.log(`Contraseña hasheada correctamente para ${email}`);

        // Actualizar la contraseña
        const updated = await verificationCodes.updatePassword(email, hash);

        if (!updated) {
            console.log(`Error al actualizar la contraseña para ${email}`);
            return res.status(500).json({
                success: false,
                error: 'Error al actualizar la contraseña'
            });
        }

        console.log(`Contraseña actualizada correctamente para ${email}`);
        res.json({
            success: true,
            message: 'Contraseña restablecida correctamente'
        });
    } catch (error) {
        console.error('Error al restablecer contraseña:', error);
        res.status(500).json({
            success: false,
            error: 'Error en el servidor',
            details: error.message
        });
    }
});

// Refresh Token
router.post('/refresh', async (req, res) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
        return res.status(401).json({
            error: 'Refresh token requerido',
            code: 'NO_REFRESH_TOKEN'
        });
    }

    try {
        // Verify refresh token
        const decoded = await verifyRefreshToken(refreshToken);

        // Get user info
        let conn;
        try {
            conn = await getConnection();

            const [userRows] = await conn.execute(
                'SELECT id, username, email, role FROM users WHERE id = ?',
                [decoded.userId]
            );

            if (userRows.length === 0) {
                await conn.end();
                return res.status(401).json({
                    error: 'Usuario no encontrado',
                    code: 'USER_NOT_FOUND'
                });
            }

            await conn.end();

            const user = userRows[0];

            // Generate new tokens
            const userTokenInfo = {
                username: user.username,
                email: user.email,
                role: user.role
            };

            const newAccessToken = generateAccessToken(user.id, userTokenInfo);
            const newRefreshToken = generateRefreshToken(user.id);

            // Store new refresh token and remove old one
            await removeRefreshToken(user.id, refreshToken);
            await storeRefreshToken(user.id, newRefreshToken);

            // Log token refresh
            securityLogger.info('Token refreshed', {
                userId: user.id,
                username: user.username,
                ip: req.ip
            });

            res.json({
                success: true,
                accessToken: newAccessToken,
                refreshToken: newRefreshToken
            });

        } catch (dbError) {
            if (conn) await conn.end().catch(() => {});
            throw dbError;
        }

    } catch (error) {
        securityLogger.warn('Invalid refresh token attempt', {
            error: error.message,
            ip: req.ip
        });

        res.status(401).json({
            error: 'Refresh token inválido o expirado',
            code: 'INVALID_REFRESH_TOKEN'
        });
    }
});

// Logout
router.post('/logout', verifyAccessToken, async (req, res) => {
    try {
        const { refreshToken } = req.body;

        // Remove refresh token if provided
        if (refreshToken) {
            await removeRefreshToken(req.user.id, refreshToken);
        } else {
            // Remove all refresh tokens for user
            await removeRefreshToken(req.user.id);
        }

        // Log logout
        securityLogger.info('User logout', {
            userId: req.user.id,
            username: req.user.username,
            ip: req.ip
        });

        res.json({
            success: true,
            message: 'Sesión cerrada exitosamente'
        });

    } catch (error) {
        console.error('Error during logout:', error);
        res.status(500).json({
            error: 'Error al cerrar sesión',
            code: 'LOGOUT_ERROR'
        });
    }
});

// Verify Token (for frontend to check if token is still valid)
router.get('/verify', verifyAccessToken, (req, res) => {
    res.json({
        success: true,
        user: {
            id: req.user.id,
            username: req.user.username,
            email: req.user.email,
            role: req.user.role,
            isAdmin: req.user.isAdmin,
            verifiedStatus: req.user.verifiedStatus
        }
    });
});

module.exports = router;