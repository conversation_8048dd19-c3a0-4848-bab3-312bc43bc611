package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0010 \n\u0002\b\u0003\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0002=>B\u0015\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010\u0013\u001a\u00020\u0014J\u0010\u0010\u0015\u001a\u00020\t2\u0006\u0010\u0016\u001a\u00020\u0006H\u0002J\b\u0010\u0017\u001a\u00020\u0006H\u0016J\u0006\u0010\u0018\u001a\u00020\u0006J\u0010\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u0006H\u0002J\u0010\u0010\u001b\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u0006H\u0002J\u0018\u0010\u001c\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\t2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J \u0010 \u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010!\u001a\u00020\"2\u0006\u0010#\u001a\u00020\u001fH\u0002J\u001c\u0010$\u001a\u00020\u00142\n\u0010%\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u001a\u001a\u00020\u0006H\u0016J*\u0010$\u001a\u00020\u00142\n\u0010%\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u001a\u001a\u00020\u00062\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\'0\u0011H\u0016J\u001c\u0010(\u001a\u00060\u0002R\u00020\u00002\u0006\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020\u0006H\u0016J\u0014\u0010,\u001a\u00020\u00142\n\u0010%\u001a\u00060\u0002R\u00020\u0000H\u0016J\u0010\u0010-\u001a\u00020\u00142\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u0006\u0010.\u001a\u00020\u0014J\u000e\u0010/\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u0006J\u0006\u00100\u001a\u00020\u0014J\u0010\u00101\u001a\u00020\u00142\u0006\u00102\u001a\u00020\u0006H\u0002J\u0010\u00103\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u0006H\u0002J\u0006\u00104\u001a\u00020\u0014J\u0006\u00105\u001a\u00020\u0014J\u0006\u00106\u001a\u00020\u0014J\u0006\u00107\u001a\u00020\u0014J\u0006\u00108\u001a\u00020\u0014J\u0006\u00109\u001a\u00020\u0014J\u0014\u0010:\u001a\u00020\u00142\f\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00120<R\u000e\u0010\b\u001a\u00020\tX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000f0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/spyro/vmeet/adapter/VideoFeedAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/VideoFeedAdapter$VideoViewHolder;", "context", "Landroid/content/Context;", "userId", "", "(Landroid/content/Context;I)V", "API_URL", "", "currentPlayingPosition", "players", "Ljava/util/concurrent/ConcurrentHashMap;", "Landroidx/media3/exoplayer/ExoPlayer;", "userInfoCache", "Lcom/spyro/vmeet/adapter/VideoFeedAdapter$UserInfo;", "videos", "", "Lcom/spyro/vmeet/model/VideoPost;", "clearUserInfoCache", "", "formatCount", "count", "getItemCount", "getUserInfoCacheSize", "handleCommentClick", "position", "handleLikeClick", "loadAvatarImage", "avatarUrl", "imageView", "Lde/hdodenhof/circleimageview/CircleImageView;", "loadUserInfo", "usernameTextView", "Landroid/widget/TextView;", "avatarImageView", "onBindViewHolder", "holder", "payloads", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "onViewRecycled", "openUserProfile", "pauseAllVideos", "pauseAllVideosExcept", "reconnectCurrentPlayerView", "recordVideoView", "videoId", "recreatePlayerForPosition", "recreatePlayersIfNeeded", "refreshFirstVideoUserInfo", "releaseAllPlayers", "resumeCurrentVideo", "startFirstVideo", "stopAllVideos", "updateVideos", "newVideos", "", "UserInfo", "VideoViewHolder", "app_debug"})
public final class VideoFeedAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.VideoFeedAdapter.VideoViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.model.VideoPost> videos = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.Integer, androidx.media3.exoplayer.ExoPlayer> players = null;
    private int currentPlayingPosition = -1;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.Integer, com.spyro.vmeet.adapter.VideoFeedAdapter.UserInfo> userInfoCache = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    
    public VideoFeedAdapter(@org.jetbrains.annotations.NotNull()
    android.content.Context context, int userId) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.VideoFeedAdapter.VideoViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.VideoFeedAdapter.VideoViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.VideoFeedAdapter.VideoViewHolder holder, int position, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.Object> payloads) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void updateVideos(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.model.VideoPost> newVideos) {
    }
    
    public final void pauseAllVideos() {
    }
    
    public final void pauseAllVideosExcept(int position) {
    }
    
    public final void resumeCurrentVideo() {
    }
    
    private final void handleLikeClick(int position) {
    }
    
    private final void handleCommentClick(int position) {
    }
    
    private final void recordVideoView(int videoId) {
    }
    
    private final void loadUserInfo(int userId, android.widget.TextView usernameTextView, de.hdodenhof.circleimageview.CircleImageView avatarImageView) {
    }
    
    private final void loadAvatarImage(java.lang.String avatarUrl, de.hdodenhof.circleimageview.CircleImageView imageView) {
    }
    
    private final java.lang.String formatCount(int count) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewRecycled(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.VideoFeedAdapter.VideoViewHolder holder) {
    }
    
    public final void stopAllVideos() {
    }
    
    public final void releaseAllPlayers() {
    }
    
    public final void clearUserInfoCache() {
    }
    
    public final int getUserInfoCacheSize() {
        return 0;
    }
    
    public final void startFirstVideo() {
    }
    
    public final void refreshFirstVideoUserInfo() {
    }
    
    public final void recreatePlayersIfNeeded() {
    }
    
    public final void reconnectCurrentPlayerView() {
    }
    
    private final void recreatePlayerForPosition(int position) {
    }
    
    private final void openUserProfile(int userId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0005J\t\u0010\t\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\n\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u001f\u0010\u000b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\f\u001a\u00020\r2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001J\t\u0010\u0011\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/spyro/vmeet/adapter/VideoFeedAdapter$UserInfo;", "", "username", "", "avatarUrl", "(Ljava/lang/String;Ljava/lang/String;)V", "getAvatarUrl", "()Ljava/lang/String;", "getUsername", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    public static final class UserInfo {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String username = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.String avatarUrl = null;
        
        public UserInfo(@org.jetbrains.annotations.NotNull()
        java.lang.String username, @org.jetbrains.annotations.Nullable()
        java.lang.String avatarUrl) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getUsername() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getAvatarUrl() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.spyro.vmeet.adapter.VideoFeedAdapter.UserInfo copy(@org.jetbrains.annotations.NotNull()
        java.lang.String username, @org.jetbrains.annotations.Nullable()
        java.lang.String avatarUrl) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\bR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0017\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u0011\u0010\u0019\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0016R\u0011\u0010\u001b\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0016R\u0011\u0010\u001d\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0016\u00a8\u0006\u001f"}, d2 = {"Lcom/spyro/vmeet/adapter/VideoFeedAdapter$VideoViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "view", "Landroid/view/View;", "(Lcom/spyro/vmeet/adapter/VideoFeedAdapter;Landroid/view/View;)V", "imageComment", "Landroid/widget/ImageView;", "getImageComment", "()Landroid/widget/ImageView;", "imageLike", "getImageLike", "imageUserAvatar", "Lde/hdodenhof/circleimageview/CircleImageView;", "getImageUserAvatar", "()Lde/hdodenhof/circleimageview/CircleImageView;", "playerView", "Landroidx/media3/ui/PlayerView;", "getPlayerView", "()Landroidx/media3/ui/PlayerView;", "textComments", "Landroid/widget/TextView;", "getTextComments", "()Landroid/widget/TextView;", "textDescription", "getTextDescription", "textLikes", "getTextLikes", "textUsername", "getTextUsername", "textViews", "getTextViews", "app_debug"})
    public final class VideoViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final androidx.media3.ui.PlayerView playerView = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textUsername = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textDescription = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textLikes = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textComments = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViews = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageLike = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageComment = null;
        @org.jetbrains.annotations.NotNull()
        private final de.hdodenhof.circleimageview.CircleImageView imageUserAvatar = null;
        
        public VideoViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View view) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.media3.ui.PlayerView getPlayerView() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextUsername() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextDescription() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextLikes() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextComments() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTextViews() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getImageLike() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getImageComment() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final de.hdodenhof.circleimageview.CircleImageView getImageUserAvatar() {
            return null;
        }
    }
}