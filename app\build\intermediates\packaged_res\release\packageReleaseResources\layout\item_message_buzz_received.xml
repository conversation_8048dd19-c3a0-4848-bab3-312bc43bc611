<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <ImageView
        android:id="@+id/imageViewAvatar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:background="@drawable/glowing_border"
        android:scaleType="centerCrop"
        android:padding="1dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/layoutBuzzMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/bg_buzz_message_received"
        android:padding="16dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="64dp"
        android:elevation="4dp"
        app:layout_constraintStart_toEndOf="@id/imageViewAvatar"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_vibration"
                android:tint="@color/buzz_text_color"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textViewBuzzMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="¡BuZzZzzZzZZ!"
                android:textColor="@color/buzz_text_color"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:shadowColor="@color/buzz_text_shadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2"
                android:letterSpacing="0.1" />

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_vibration"
                android:tint="@color/buzz_text_color"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/textViewBuzzTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12:34"
            android:textColor="#B0BEC5"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            android:gravity="start" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
