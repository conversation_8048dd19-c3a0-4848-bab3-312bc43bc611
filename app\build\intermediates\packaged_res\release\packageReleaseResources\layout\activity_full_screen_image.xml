<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/photoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter" />

    <ImageButton
        android:id="@+id/buttonClose"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_alignParentEnd="true"
        android:layout_margin="16dp"
        android:background="@android:color/transparent"
        android:contentDescription="Close"
        android:padding="8dp"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        app:tint="@android:color/white" />

</RelativeLayout> 