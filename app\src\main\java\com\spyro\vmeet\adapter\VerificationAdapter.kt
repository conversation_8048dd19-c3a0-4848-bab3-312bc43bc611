package com.spyro.vmeet.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.spyro.vmeet.FullScreenImageActivity
import com.spyro.vmeet.R
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

class VerificationAdapter(
    private var verifications: List<JSONObject>,
    private val listener: VerificationActionListener
) : RecyclerView.Adapter<VerificationAdapter.VerificationViewHolder>() {

    interface VerificationActionListener {
        fun onApproveVerification(verificationId: Int)
        fun onRejectVerification(verificationId: Int, reason: String)
    }

    class VerificationViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvUsername: TextView = itemView.findViewById(R.id.tvUsername)
        val tvEmail: TextView = itemView.findViewById(R.id.tvEmail)
        val tvDocumentType: TextView = itemView.findViewById(R.id.tvDocumentType)
        val tvSubmittedAt: TextView = itemView.findViewById(R.id.tvSubmittedAt)
        val ivDocumentPhoto: ImageView = itemView.findViewById(R.id.ivDocumentPhoto)
        val ivSelfiePhoto: ImageView = itemView.findViewById(R.id.ivSelfiePhoto)
        val btnApprove: Button = itemView.findViewById(R.id.btnApprove)
        val btnReject: Button = itemView.findViewById(R.id.btnReject)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): VerificationViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_verification, parent, false)
        return VerificationViewHolder(view)
    }

    override fun onBindViewHolder(holder: VerificationViewHolder, position: Int) {
        val verification = verifications[position]
        val context = holder.itemView.context

        try {
            // Basic info
            holder.tvUsername.text = verification.optString("username", "Usuario desconocido")
            holder.tvEmail.text = verification.optString("email", "Email no disponible")

            // Document type
            val documentType = verification.optString("document_type", "dni")
            holder.tvDocumentType.text = getDocumentTypeDisplayName(documentType)

            // Submitted date
            val submittedAt = verification.optString("submitted_at", "")
            if (submittedAt.isNotEmpty()) {
                try {
                    val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                    val outputFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                    val date = inputFormat.parse(submittedAt)
                    holder.tvSubmittedAt.text = "Enviado: ${outputFormat.format(date!!)}"
                } catch (e: Exception) {
                    holder.tvSubmittedAt.text = "Enviado: $submittedAt"
                }
            } else {
                holder.tvSubmittedAt.text = "Fecha no disponible"
            }

            // Load images
            val baseUrl = "http://77.110.116.89:3000"
            val documentPhotoUrl = verification.optString("dni_photo_url", "")
            val selfiePhotoUrl = verification.optString("selfie_photo_url", "")

            if (documentPhotoUrl.isNotEmpty()) {
                val fullDocumentUrl = if (documentPhotoUrl.startsWith("http")) {
                    documentPhotoUrl
                } else {
                    "$baseUrl$documentPhotoUrl"
                }

                Glide.with(context)
                    .load(fullDocumentUrl)
                    .placeholder(R.drawable.ic_document_placeholder)
                    .error(R.drawable.ic_document_placeholder)
                    .into(holder.ivDocumentPhoto)

                holder.ivDocumentPhoto.setOnClickListener {
                    openFullScreenImage(context, fullDocumentUrl, "Documento de Identidad")
                }
            }

            if (selfiePhotoUrl.isNotEmpty()) {
                val fullSelfieUrl = if (selfiePhotoUrl.startsWith("http")) {
                    selfiePhotoUrl
                } else {
                    "$baseUrl$selfiePhotoUrl"
                }

                Glide.with(context)
                    .load(fullSelfieUrl)
                    .placeholder(R.drawable.ic_person_placeholder)
                    .error(R.drawable.ic_person_placeholder)
                    .into(holder.ivSelfiePhoto)

                holder.ivSelfiePhoto.setOnClickListener {
                    openFullScreenImage(context, fullSelfieUrl, "Selfie con Documento")
                }
            }

            // Button actions
            val verificationId = verification.optInt("id", -1)

            holder.btnApprove.setOnClickListener {
                if (verificationId != -1) {
                    listener.onApproveVerification(verificationId)
                }
            }

            holder.btnReject.setOnClickListener {
                if (verificationId != -1) {
                    showRejectDialog(context, verificationId)
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
            holder.tvUsername.text = "Error al cargar datos"
            holder.tvEmail.text = ""
            holder.tvDocumentType.text = ""
            holder.tvSubmittedAt.text = ""
        }
    }

    override fun getItemCount(): Int = verifications.size

    fun updateVerifications(newVerifications: List<JSONObject>) {
        verifications = newVerifications
        notifyDataSetChanged()
    }

    private fun getDocumentTypeDisplayName(type: String): String {
        return when (type) {
            "dni" -> "DNI/Cédula de identidad"
            "cedula" -> "Cédula de identidad"
            "passport" -> "Pasaporte"
            "driving_license" -> "Carnet de conducir"
            "residence_card" -> "Tarjeta de residencia"
            "voter_id" -> "Credencial para votar"
            else -> "Documento de identidad"
        }
    }

    private fun openFullScreenImage(context: Context, imageUrl: String, title: String) {
        val intent = Intent(context, FullScreenImageActivity::class.java).apply {
            putExtra("IMAGE_URL", imageUrl)
            putExtra("TITLE", title)
        }
        context.startActivity(intent)
    }

    private fun showRejectDialog(context: Context, verificationId: Int) {
        val dialogBuilder = AlertDialog.Builder(context)
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_reject_verification, null)

        val editTextReason = dialogView.findViewById<android.widget.EditText>(R.id.editTextRejectReason)

        dialogBuilder.setView(dialogView)
        dialogBuilder.setTitle("Rechazar Verificación")
        dialogBuilder.setMessage("Por favor, proporciona una razón para el rechazo:")

        dialogBuilder.setPositiveButton("Rechazar") { _, _ ->
            val reason = editTextReason.text.toString().trim()
            if (reason.isNotEmpty()) {
                listener.onRejectVerification(verificationId, reason)
            } else {
                listener.onRejectVerification(verificationId, "Verificación rechazada por administrador")
            }
        }

        dialogBuilder.setNegativeButton("Cancelar") { dialog, _ ->
            dialog.dismiss()
        }

        dialogBuilder.show()
    }
}
