<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Eliminar sala"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="¿Estás seguro que deseas eliminar esta sala?"
        android:layout_marginBottom="8dp"/>

    <TextView
        android:id="@+id/textViewRoomName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp"/>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Todos los mensajes serán eliminados permanentemente y los miembros no podrán acceder a la sala."
        android:textColor="#FF0000"
        android:layout_marginBottom="16dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/buttonCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancelar"
            android:layout_marginEnd="8dp"
            style="@style/Widget.AppCompat.Button.Borderless"/>

        <Button
            android:id="@+id/buttonDelete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Eliminar"
            android:textColor="#FFFFFF"
            android:backgroundTint="#FF0000"
            style="@style/Widget.AppCompat.Button.Colored"/>
    </LinearLayout>

</LinearLayout> 