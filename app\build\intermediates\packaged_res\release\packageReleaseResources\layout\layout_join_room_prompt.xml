<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/frameLayoutJoinPrompt"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusable="true"
    android:background="#99000000">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewJoin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        app:cardBackgroundColor="#2A2A4A"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:id="@+id/textViewJoinTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Unirse a esta sala"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/imageViewJoinRoom"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="16dp"
                android:src="@drawable/default_room_icon" />

            <TextView
                android:id="@+id/textViewJoinDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="Debes unirte a esta sala para ver y enviar mensajes"
                android:textColor="#E0E0E0"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/textViewJoinNote"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="Podrás abandonar la sala en cualquier momento"
                android:textColor="#B0B0B0"
                android:textSize="14sp" />

            <Button
                android:id="@+id/buttonJoinRoom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:backgroundTint="@color/neon_blue"
                android:padding="12dp"
                android:text="UNIRSE A LA SALA"
                android:textColor="@android:color/white"
                android:textStyle="bold" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</FrameLayout> 