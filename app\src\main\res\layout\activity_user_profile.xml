<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/cyberpunk_background"
    tools:context=".activity.UserProfileActivity">

    <ProgressBar
        android:id="@+id/progressBarProfile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- User Header -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardViewUserProfile"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        app:cardBackgroundColor="@color/cyberpunk_card_background"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/cyberpunk_card_stroke"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Glowing top edge for neon effect -->
            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/cyberpunk_card_stroke" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/imageViewProfileAvatar"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:background="@drawable/avatar_circle_border"
                    android:padding="2dp"
                    android:src="@drawable/ic_profile_placeholder"
                    tools:src="@drawable/ic_profile_placeholder" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/textViewProfileUsername"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Username"
                        android:textColor="@color/cyberpunk_text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <!-- Nivel y barra de progreso -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginTop="6dp">

                        <TextView
                            android:id="@+id/textViewUserLevel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Nivel 1"
                            android:textColor="@color/cyberpunk_accent"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <ProgressBar
                            android:id="@+id/progressBarUserLevel"
                            style="@android:style/Widget.ProgressBar.Horizontal"
                            android:layout_width="180dp"
                            android:layout_height="16dp"
                            android:progress="0"
                            android:max="100"
                            android:progressDrawable="@drawable/progress_bar_cyberpunk" />

                        <TextView
                            android:id="@+id/textViewUserXP"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 XP / 300 XP"
                            android:textColor="@color/cyberpunk_text_secondary"
                            android:textSize="13sp"
                            android:layout_marginTop="2dp" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="4dp">

                        <TextView
                            android:id="@+id/textViewAdminBadge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/badge_admin"
                            android:drawableStart="@drawable/ic_admin_crown"
                            android:drawablePadding="4dp"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:text="Administrador"
                            android:textColor="@android:color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="gone" />

                        <TextView
                            android:id="@+id/textViewVerifiedBadge"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:background="@drawable/badge_verified"
                            android:drawableStart="@drawable/ic_verified_check"
                            android:drawablePadding="4dp"
                            android:paddingHorizontal="8dp"
                            android:paddingVertical="4dp"
                            android:text="Verificado"
                            android:textColor="@android:color/white"
                            android:textSize="12sp"
                            android:textStyle="bold"
                            android:visibility="gone" />
                    </LinearLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/buttonSendMessage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Enviar mensaje"
                        android:textColor="@android:color/white"
                        app:icon="@drawable/ic_chat_bubble"
                        app:iconTint="@android:color/white"
                        app:cornerRadius="20dp"
                        android:backgroundTint="@color/cyberpunk_accent"
                        style="@style/Widget.MaterialComponents.Button.Icon" />

                    <TextView
                        android:id="@+id/textViewProfileInfo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text=""
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- User Posts Header -->
    <TextView
        android:id="@+id/textViewUserPostsHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="16dp"
        android:text="Publicaciones"
        android:textColor="@color/cyberpunk_text_primary"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/cardViewUserProfile" />

    <!-- User Posts RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewUserPosts"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewUserPostsHeader"
        tools:listitem="@layout/item_post" />

</androidx.constraintlayout.widget.ConstraintLayout>