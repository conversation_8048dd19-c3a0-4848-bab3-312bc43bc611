package com.spyro.vmeet.ui.custom;

/**
 * Custom TextView specifically designed to render numbers without any antialiasing
 * This completely eliminates gradients by drawing text pixel by pixel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\r\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u0000 12\u00020\u0001:\u00011B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\u0012\u001a\u00020\u0013H\u0002J\b\u0010\u0014\u001a\u00020\u0013H\u0002J\b\u0010\u0015\u001a\u00020\u0013H\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0016J\b\u0010\u0018\u001a\u00020\u0013H\u0014J\u0010\u0010\u0019\u001a\u00020\u00132\u0006\u0010\u001a\u001a\u00020\u001bH\u0014J\u0018\u0010\u001c\u001a\u00020\u00132\u0006\u0010\u001d\u001a\u00020\u00072\u0006\u0010\u001e\u001a\u00020\u0007H\u0014J\u000e\u0010\u001f\u001a\u00020\u00132\u0006\u0010 \u001a\u00020\fJ(\u0010!\u001a\u00020\u00132\u0006\u0010\"\u001a\u00020\u00072\u0006\u0010#\u001a\u00020\u00072\u0006\u0010$\u001a\u00020\u00072\u0006\u0010%\u001a\u00020\u0007H\u0016J\u001c\u0010&\u001a\u00020\u00132\b\u0010 \u001a\u0004\u0018\u00010\u00172\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J\u0010\u0010)\u001a\u00020\u00132\u0006\u0010*\u001a\u00020\u0007H\u0016J\u0010\u0010+\u001a\u00020\u00132\u0006\u0010,\u001a\u00020\u0007H\u0016J\u0010\u0010-\u001a\u00020\u00132\u0006\u0010.\u001a\u00020\u0011H\u0016J\u0018\u0010-\u001a\u00020\u00132\u0006\u0010/\u001a\u00020\u00072\u0006\u0010.\u001a\u00020\u0011H\u0016J\b\u00100\u001a\u00020\u0013H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0007X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00062"}, d2 = {"Lcom/spyro/vmeet/ui/custom/NumberTextView;", "Landroidx/appcompat/widget/AppCompatTextView;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "bitmapPaint", "Landroid/graphics/Paint;", "displayText", "", "textBitmap", "Landroid/graphics/Bitmap;", "textColorValue", "textSizePx", "", "applyPixelPerfectFilter", "", "createTextBitmap", "createTextBitmapImmediate", "getText", "", "onDetachedFromWindow", "onDraw", "canvas", "Landroid/graphics/Canvas;", "onMeasure", "widthMeasureSpec", "heightMeasureSpec", "setNumberText", "text", "setPadding", "left", "top", "right", "bottom", "setText", "type", "Landroid/widget/TextView$BufferType;", "setTextAlignment", "alignment", "setTextColor", "color", "setTextSize", "size", "unit", "setupBitmapPaint", "Companion", "app_debug"})
public final class NumberTextView extends androidx.appcompat.widget.AppCompatTextView {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "NumberTextView";
    @org.jetbrains.annotations.NotNull()
    private java.lang.String displayText = "";
    private float textSizePx = 48.0F;
    private int textColorValue = android.graphics.Color.WHITE;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.Bitmap textBitmap;
    @org.jetbrains.annotations.NotNull()
    private final android.graphics.Paint bitmapPaint = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ui.custom.NumberTextView.Companion Companion = null;
    
    @kotlin.jvm.JvmOverloads()
    public NumberTextView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public NumberTextView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public NumberTextView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    private final void setupBitmapPaint() {
    }
    
    public final void setNumberText(@org.jetbrains.annotations.NotNull()
    java.lang.String text) {
    }
    
    @java.lang.Override()
    public void setText(@org.jetbrains.annotations.Nullable()
    java.lang.CharSequence text, @org.jetbrains.annotations.Nullable()
    android.widget.TextView.BufferType type) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.CharSequence getText() {
        return null;
    }
    
    @java.lang.Override()
    public void setTextColor(int color) {
    }
    
    @java.lang.Override()
    public void setTextSize(float size) {
    }
    
    @java.lang.Override()
    public void setTextSize(int unit, float size) {
    }
    
    private final void createTextBitmapImmediate() {
    }
    
    private final void createTextBitmap() {
    }
    
    private final void applyPixelPerfectFilter() {
    }
    
    @java.lang.Override()
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
    
    @java.lang.Override()
    public void setTextAlignment(int alignment) {
    }
    
    @java.lang.Override()
    public void setPadding(int left, int top, int right, int bottom) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/spyro/vmeet/ui/custom/NumberTextView$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}