package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010\u0012\u001a\u00020\u00042\u0006\u0010\u0013\u001a\u00020\u000fH\u0002J\u001c\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u00152\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\b\u0010\u0016\u001a\u00020\u0017H\u0002J\u0012\u0010\u0018\u001a\u00020\u00172\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0014J\b\u0010\u001b\u001a\u00020\u0017H\u0014J\b\u0010\u001c\u001a\u00020\u0017H\u0002J\u0010\u0010\u001d\u001a\u00020\u00172\u0006\u0010\u001e\u001a\u00020\u0004H\u0002J\u0010\u0010\u001f\u001a\u00020\u00172\u0006\u0010 \u001a\u00020!H\u0002J\u0010\u0010\"\u001a\u00020\u00172\u0006\u0010 \u001a\u00020!H\u0002J\u0016\u0010#\u001a\u00020\u00172\f\u0010$\u001a\b\u0012\u0004\u0012\u00020!0%H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/spyro/vmeet/MatchesActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "API_URL", "", "TAG", "bottomNavView", "Lcom/google/android/material/bottomnavigation/BottomNavigationView;", "emptyTextView", "Landroid/widget/TextView;", "progressBar", "Landroid/widget/ProgressBar;", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "userId", "", "createCompatibilityLayout", "Landroid/view/View;", "getLastMessage", "matchId", "getUserInfo", "Lkotlin/Pair;", "loadMatches", "", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onResume", "setupBottomNavigation", "showError", "message", "showUnmatchConfirmationDialog", "matchItem", "Lcom/spyro/vmeet/MatchItem;", "unmatchUser", "updateUI", "matches", "", "app_debug"})
public final class MatchesActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    private int userId = 0;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView emptyTextView;
    private com.google.android.material.bottomnavigation.BottomNavigationView bottomNavView;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "MatchesActivity";
    
    public MatchesActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final android.view.View createCompatibilityLayout() {
        return null;
    }
    
    private final void loadMatches() {
    }
    
    private final kotlin.Pair<java.lang.String, java.lang.String> getUserInfo(int userId) {
        return null;
    }
    
    private final java.lang.String getLastMessage(int matchId) {
        return null;
    }
    
    private final void updateUI(java.util.List<com.spyro.vmeet.MatchItem> matches) {
    }
    
    /**
     * Show confirmation dialog before unmatching
     */
    private final void showUnmatchConfirmationDialog(com.spyro.vmeet.MatchItem matchItem) {
    }
    
    /**
     * Send API request to unmatch
     */
    private final void unmatchUser(com.spyro.vmeet.MatchItem matchItem) {
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void setupBottomNavigation() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
}