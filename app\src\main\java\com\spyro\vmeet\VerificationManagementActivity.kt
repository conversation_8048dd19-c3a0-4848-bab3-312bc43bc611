package com.spyro.vmeet

import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.Toolbar
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.spyro.vmeet.activity.BaseActivity
import com.spyro.vmeet.adapter.VerificationAdapter
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import org.json.JSONObject
import java.io.IOException
import java.util.concurrent.TimeUnit

class VerificationManagementActivity : BaseActivity(), VerificationAdapter.VerificationActionListener {
    companion object {
        private const val TAG = "VerificationManagement"
    }

    private lateinit var recyclerView: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvEmptyList: TextView
    private var userId: Int = 0
    private lateinit var adapter: VerificationAdapter

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_verification_management)

        userId = intent.getIntExtra("USER_ID", -1)
        if (userId <= 0) {
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", -1)
        }

        if (userId <= 0) {
            Toast.makeText(this, "Error: Usuario no identificado", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        // Set up toolbar
        val toolbar = findViewById<Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Verificaciones de Usuario"

        // Initialize views
        recyclerView = findViewById(R.id.recyclerViewVerifications)
        progressBar = findViewById(R.id.progressBar)
        tvEmptyList = findViewById(R.id.tvEmptyList)

        // Set up RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(this)
        adapter = VerificationAdapter(emptyList(), this)
        recyclerView.adapter = adapter

        // Verify admin status and load verifications
        checkAdminStatus()
    }

    override fun onResume() {
        super.onResume()
        loadVerifications()
    }

    private fun checkAdminStatus() {
        val url = "http://77.110.116.89:3000/user/check-admin/$userId"

        val request = Request.Builder()
            .url(url)
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error checking admin status", e)
                    Toast.makeText(
                        this@VerificationManagementActivity,
                        "Error al verificar permisos de administrador",
                        Toast.LENGTH_SHORT
                    ).show()
                    finish()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()
                try {
                    val jsonObject = JSONObject(responseBody)
                    val success = jsonObject.getBoolean("success")
                    val isAdmin = jsonObject.getBoolean("isAdmin")

                    if (!success || !isAdmin) {
                        runOnUiThread {
                            progressBar.visibility = View.GONE
                            Toast.makeText(
                                this@VerificationManagementActivity,
                                "No tienes permisos de administrador",
                                Toast.LENGTH_SHORT
                            ).show()
                            finish()
                        }
                    } else {
                        loadVerifications()
                    }
                } catch (e: Exception) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing admin status response", e)
                        Toast.makeText(
                            this@VerificationManagementActivity,
                            "Error al verificar permisos de administrador",
                            Toast.LENGTH_SHORT
                        ).show()
                        finish()
                    }
                }
            }
        })
    }

    private fun loadVerifications() {
        val url = "http://77.110.116.89:3000/verification/admin/list"

        val request = Request.Builder()
            .url("$url?page=1&limit=50")
            .get()
            .build()

        progressBar.visibility = View.VISIBLE
        recyclerView.visibility = View.GONE
        tvEmptyList.visibility = View.GONE

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    progressBar.visibility = View.GONE
                    Log.e(TAG, "Error loading verifications", e)
                    Toast.makeText(
                        this@VerificationManagementActivity,
                        "Error al cargar verificaciones",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()
                try {
                    val jsonObject = JSONObject(responseBody ?: "{}")
                    Log.d(TAG, "Verifications response: $responseBody")

                    val verifications = mutableListOf<JSONObject>()
                    val verificationsArray = jsonObject.optJSONArray("verifications")

                    if (verificationsArray != null) {
                        Log.d(TAG, "Found ${verificationsArray.length()} verifications")
                        for (i in 0 until verificationsArray.length()) {
                            try {
                                val verification = verificationsArray.getJSONObject(i)
                                Log.d(TAG, "Processing verification: ${verification.toString()}")
                                verifications.add(verification)
                            } catch (e: Exception) {
                                Log.e(TAG, "Error processing verification at index $i", e)
                                continue
                            }
                        }
                    }

                    runOnUiThread {
                        progressBar.visibility = View.GONE

                        if (verifications.isEmpty()) {
                            tvEmptyList.visibility = View.VISIBLE
                            recyclerView.visibility = View.GONE
                            tvEmptyList.text = "No hay verificaciones registradas"
                        } else {
                            tvEmptyList.visibility = View.GONE
                            recyclerView.visibility = View.VISIBLE
                            adapter.updateVerifications(verifications)
                            Log.d(TAG, "Updated adapter with ${verifications.size} verifications")
                        }
                    }
                } catch (e: Exception) {
                    runOnUiThread {
                        progressBar.visibility = View.GONE
                        Log.e(TAG, "Error parsing verifications response", e)
                        Toast.makeText(
                            this@VerificationManagementActivity,
                            "Error al procesar verificaciones",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    override fun onApproveVerification(verificationId: Int) {
        val url = "http://77.110.116.89:3000/verification/admin/approve/$verificationId"

        val jsonBody = JSONObject().apply {
            put("adminId", userId)
            put("notes", "Verificación aprobada por administrador")
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .put(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Toast.makeText(
                        this@VerificationManagementActivity,
                        "Error al aprobar verificación",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()
                try {
                    val jsonObject = JSONObject(responseBody ?: "{}")
                    val success = jsonObject.optBoolean("success", false)
                    val message = jsonObject.optString("message", "")

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(
                                this@VerificationManagementActivity,
                                "Verificación aprobada",
                                Toast.LENGTH_SHORT
                            ).show()
                            loadVerifications() // Reload list
                        } else {
                            Toast.makeText(
                                this@VerificationManagementActivity,
                                if (message.isNotEmpty()) message else "Error al aprobar verificación",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: Exception) {
                    runOnUiThread {
                        Toast.makeText(
                            this@VerificationManagementActivity,
                            "Error al procesar respuesta",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    override fun onRejectVerification(verificationId: Int, reason: String) {
        val url = "http://77.110.116.89:3000/verification/admin/reject/$verificationId"

        val jsonBody = JSONObject().apply {
            put("adminId", userId)
            put("notes", reason)
        }

        val requestBody = jsonBody.toString().toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .put(requestBody)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Toast.makeText(
                        this@VerificationManagementActivity,
                        "Error al rechazar verificación",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                val responseBody = response.body?.string()
                try {
                    val jsonObject = JSONObject(responseBody ?: "{}")
                    val success = jsonObject.optBoolean("success", false)
                    val message = jsonObject.optString("message", "")

                    runOnUiThread {
                        if (success) {
                            Toast.makeText(
                                this@VerificationManagementActivity,
                                "Verificación rechazada",
                                Toast.LENGTH_SHORT
                            ).show()
                            loadVerifications() // Reload list
                        } else {
                            Toast.makeText(
                                this@VerificationManagementActivity,
                                if (message.isNotEmpty()) message else "Error al rechazar verificación",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                } catch (e: Exception) {
                    runOnUiThread {
                        Toast.makeText(
                            this@VerificationManagementActivity,
                            "Error al procesar respuesta",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }
}
