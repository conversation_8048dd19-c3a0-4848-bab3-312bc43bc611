package com.spyro.vmeet

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.appbar.MaterialToolbar
import com.spyro.vmeet.adapter.MyVideosAdapter
import com.spyro.vmeet.model.VideoPost
import okhttp3.*
import org.json.JSONArray
import java.io.IOException

class MyVideosActivity : AppCompatActivity() {
    private lateinit var toolbar: MaterialToolbar
    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: MyVideosAdapter
    private var userId: Int = 0
    private val API_URL = "http://77.110.116.89:3000"
    private val client = OkHttpClient()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_my_videos)

        // Get userId
        userId = intent.getIntExtra("USER_ID", 0)
        if (userId == 0) {
            val prefs = getSharedPreferences("VMeetPrefs", MODE_PRIVATE)
            userId = prefs.getInt("USER_ID", 0)
            if (userId == 0) {
                finish()
                return
            }
        }

        initializeViews()
        setupToolbar()
        loadVideos()
    }

    private fun initializeViews() {
        toolbar = findViewById(R.id.toolbar)
        recyclerView = findViewById(R.id.recyclerView)

        // Setup RecyclerView
        adapter = MyVideosAdapter(this) { videoId ->
            showDeleteConfirmation(videoId)
        }
        recyclerView.layoutManager = GridLayoutManager(this, 2)
        recyclerView.adapter = adapter
    }

    private fun setupToolbar() {
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Mis Videos"
        toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun loadVideos() {
        val request = Request.Builder()
            .url("$API_URL/videos/user/$userId")
            .get()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Toast.makeText(
                        this@MyVideosActivity,
                        "Error al cargar los videos",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                response.body?.string()?.let { jsonStr ->
                    try {
                        val jsonArray = JSONArray(jsonStr)
                        val videos = mutableListOf<VideoPost>()

                        for (i in 0 until jsonArray.length()) {
                            val obj = jsonArray.getJSONObject(i)
                            videos.add(
                                VideoPost(
                                    id = obj.getInt("id"),
                                    userId = obj.getInt("userId"),
                                    videoUrl = obj.getString("videoUrl"),
                                    description = obj.getString("description"),
                                    likes = obj.getInt("likes"),
                                    comments = obj.getInt("comments"),
                                    views = obj.getInt("views")
                                )
                            )
                        }

                        runOnUiThread {
                            adapter.updateVideos(videos)
                        }
                    } catch (e: Exception) {
                        runOnUiThread {
                            Toast.makeText(
                                this@MyVideosActivity,
                                "Error al procesar los datos",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                }
            }
        })
    }

    private fun showDeleteConfirmation(videoId: Int) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Eliminar Video")
            .setMessage("¿Estás seguro de que quieres eliminar este video?")
            .setPositiveButton("Eliminar") { _, _ ->
                deleteVideo(videoId)
            }
            .setNegativeButton("Cancelar", null)
            .show()
    }

    private fun deleteVideo(videoId: Int) {
        val request = Request.Builder()
            .url("$API_URL/videos/$videoId?userId=$userId")
            .delete()
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                runOnUiThread {
                    Toast.makeText(
                        this@MyVideosActivity,
                        "Error al eliminar el video",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }

            override fun onResponse(call: Call, response: Response) {
                runOnUiThread {
                    if (response.isSuccessful) {
                        Toast.makeText(
                            this@MyVideosActivity,
                            "Video eliminado correctamente",
                            Toast.LENGTH_SHORT
                        ).show()
                        loadVideos() // Reload videos
                    } else {
                        Toast.makeText(
                            this@MyVideosActivity,
                            "Error al eliminar el video",
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                }
            }
        })
    }
}
