<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <TextView
        android:id="@+id/textViewRulesTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Reglas de la sala"
        android:textAlignment="center"
        android:textColor="@color/neon_blue"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewRulesDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="Para unirte a esta sala, debes aceptar las siguientes reglas:"
        android:textColor="#D0D0D0"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewRulesTitle" />

    <ScrollView
        android:id="@+id/scrollViewRules"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_marginTop="16dp"
        android:background="#22FFFFFF"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewRulesDescription">

        <TextView
            android:id="@+id/textViewRules"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />

    </ScrollView>

    <CheckBox
        android:id="@+id/checkBoxAcceptRules"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="He leído y acepto las reglas de la sala"
        android:textColor="#FFFFFF"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/scrollViewRules" />

    <Button
        android:id="@+id/buttonDeclineRules"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="8dp"
        android:backgroundTint="#8888AA"
        android:text="RECHAZAR"
        app:layout_constraintEnd_toStartOf="@id/buttonAcceptRules"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/checkBoxAcceptRules" />

    <Button
        android:id="@+id/buttonAcceptRules"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="16dp"
        android:backgroundTint="@color/neon_blue"
        android:text="ACEPTAR Y UNIRSE"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/buttonDeclineRules"
        app:layout_constraintTop_toBottomOf="@id/checkBoxAcceptRules" />

</androidx.constraintlayout.widget.ConstraintLayout> 