<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="document_type_values">
        <item>dni</item>
        <item>passport</item>
        <item>driving_license</item>
        <item>residence_card</item>
        <item>voter_id</item>
    </string-array>
    <string-array name="document_types">
        <item>DNI/Cédula de identidad</item>
        <item>Pasaporte</item>
        <item>Carnet de conducir</item>
        <item>Tarjeta de residencia</item>
        <item>Credencial para votar</item>
    </string-array>
    <color name="accent_cyan">#4DB6E6</color>
    <color name="background">#0A0A25</color>
    <color name="background_dark">#0A0A25</color>
    <color name="black">#FF000000</color>
    <color name="button_background">#162447</color>
    <color name="buzz_received_primary">#9C27B0</color>
    <color name="buzz_received_secondary">#673AB7</color>
    <color name="buzz_sent_primary">#FF6B35</color>
    <color name="buzz_sent_secondary">#F7931E</color>
    <color name="buzz_text_color">#FFFFFF</color>
    <color name="buzz_text_shadow">#000000</color>
    <color name="card_background">#101033</color>
    <color name="colorAccent">#FF00E4</color>
    <color name="colorPrimary">#FF6200EE</color>
    <color name="colorPrimaryDark">#FF3700B3</color>
    <color name="comfy_blue">#5DADE2</color>
    <color name="comfy_red">#E74C3C</color>
    <color name="cyberpunk_accent">#FFEB3B</color>
    <color name="cyberpunk_accent_primary">#FF00E4</color>
    <color name="cyberpunk_accent_secondary">#00FF66</color>
    <color name="cyberpunk_accent_voice">#00FF66</color>
    <color name="cyberpunk_accent_voice_thumb">#A4FFC9</color>
    <color name="cyberpunk_background">#1A1A2E</color>
    <color name="cyberpunk_card_background">#162447</color>
    <color name="cyberpunk_card_stroke">#4DB6E6</color>
    <color name="cyberpunk_divider">#E0E0E0</color>
    <color name="cyberpunk_green">#00FFAB</color>
    <color name="cyberpunk_highlight">#3A3A5D</color>
    <color name="cyberpunk_orange">#FFAB40</color>
    <color name="cyberpunk_purple">#B39DDB</color>
    <color name="cyberpunk_shadow">#0A0A15</color>
    <color name="cyberpunk_text">#E0E0E0</color>
    <color name="cyberpunk_text_primary">#E0E0E0</color>
    <color name="cyberpunk_text_secondary">#A0A0A0</color>
    <color name="cyberpunk_yellow">#FFFF00</color>
    <color name="dark_background">#0A0A25</color>
    <color name="dark_blue">#0B0B2B</color>
    <color name="dark_purple">#2A2A60</color>
    <color name="dark_red">#C62828</color>
    <color name="darker_blue">#050518</color>
    <color name="emoji_picker_bg">#333333</color>
    <color name="emoji_picker_tab_indicator">#9C27B0</color>
    <color name="emoji_picker_tab_selected">#9C27B0</color>
    <color name="emoji_picker_tab_text">#CCCCCC</color>
    <color name="emoji_picker_title_text">#FFFFFF</color>
    <color name="error_red">#FF4444</color>
    <color name="gray_dark">#2A2A2A</color>
    <color name="gray_light">#A0A0A0</color>
    <color name="login_button_color">#BB86FC</color>
    <color name="mention_highlight_color">#3F51B5</color>
    <color name="neon_blue">#4DB6E6</color>
    <color name="neon_blue_dark">#2E86AB</color>
    <color name="neon_blue_transparent">#224DB6E6</color>
    <color name="neon_green">#00FF66</color>
    <color name="neon_pink">#FF00E4</color>
    <color name="neon_purple">#9D00FF</color>
    <color name="offline_gray">#888888</color>
    <color name="online_green">#00FF66</color>
    <color name="primary_dark">#162447</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="read_message_bg">#E0E0E0</color>
    <color name="register_button_color">#A066F0</color>
    <color name="status_delivered">#888888</color>
    <color name="success_green">#00FF66</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="text_primary">#E0E0E0</color>
    <color name="text_secondary">#A0A0A0</color>
    <color name="unread_message_bg">#C2C2FF</color>
    <color name="warning_orange">#FFA500</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="status_bar_height">24dp</dimen>
    <dimen name="story_item_spacing">8dp</dimen>
    <item name="buttonCancelReveal" type="id"/>
    <string name="about_me_label">Acerca de mí:</string>
    <string name="app_name">VMeet</string>
    <string name="avatar_update_error">Error al actualizar el avatar</string>
    <string name="avatar_update_success">Avatar actualizado con éxito</string>
    <string name="create_new_post">Crear nueva publicación</string>
    <string name="default_notification_channel_id">VMeetChannel</string>
    <string name="edit_personality_traits">Editar Rasgos</string>
    <string name="edit_profile_button">Editar Perfil</string>
    <string name="email_label">Email:</string>
    <string name="error_loading_gifs">Error cargando GIFs. Por favor, inténtalo de nuevo.</string>
    <string name="error_loading_traits">Error al cargar rasgos</string>
    <string name="error_saving_traits">Error al guardar rasgos</string>
    <string name="favorite_games_label">Juegos favoritos:</string>
    <string name="first_fragment_label">Primer Fragmento</string>
    <string name="gcm_defaultSenderId" translatable="false">988461270314</string>
    <string name="google_api_key" translatable="false">AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg</string>
    <string name="google_app_id" translatable="false">1:988461270314:android:d0242913a2e29332704a53</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyB6f26GRZRpM0cX02NKfTmpyJKXXeQAtpg</string>
    <string name="google_storage_bucket" translatable="false">vmeet-5c6a2.firebasestorage.app</string>
    <string name="hello_blank_fragment">Fragmento en blanco</string>
    <string name="interests_label">Intereses:</string>
    <string name="logged_out">Sesión cerrada</string>
    <string name="mark_as_read">MARCAR COMO LEÍDO</string>
    <string name="next">Siguiente</string>
    <string name="permission_denied_gallery">Permiso denegado para acceder a la galería</string>
    <string name="personality_traits_subtitle">Estos rasgos ayudan a otros usuarios a conocerte mejor</string>
    <string name="personality_traits_title">Rasgos de Personalidad</string>
    <string name="previous">Anterior</string>
    <string name="profile_button_edit">EDITAR</string>
    <string name="profile_button_save">GUARDAR</string>
    <string name="profile_save_success">Perfil guardado con éxito</string>
    <string name="project_id" translatable="false">vmeet-5c6a2</string>
    <string name="reply">RESPONDER</string>
    <string name="reply_label">Escribe tu respuesta</string>
    <string name="sample_admob_app_id">ca-app-pub-3940256099942544~3347511713</string>
    <string name="save_traits">Guardar Rasgos</string>
    <string name="second_fragment_label">Segundo Fragmento</string>
    <string name="title_activity_comments">CommentsActivity</string>
    <string name="title_activity_login">LoginActivity</string>
    <string name="title_activity_main_menu">MainMenuActivity</string>
    <string name="title_activity_main_menu_cyber_punk">MainMenuCyberPunk</string>
    <string name="title_activity_matches">MatchesActivity</string>
    <string name="title_activity_profile">ProfileActivity</string>
    <string name="title_activity_swipe">SwipeActivity</string>
    <string name="title_chats">Chats</string>
    <string name="title_community">Comunidad</string>
    <string name="title_matches">Matches</string>
    <string name="title_profile">Perfil</string>
    <string name="title_swipe">Descubrir</string>
    <string name="trait_icon_description">Icono del rasgo</string>
    <string name="traits_saved_successfully">Rasgos guardados exitosamente</string>
    <string name="username_label">Nombre de usuario:</string>
    <style name="AdminBadgeStyle">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">#FFD700</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">0</item>
        <item name="android:shadowRadius">10</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:letterSpacing">0.15</item>
        <item name="android:fontFamily">sans-serif-black</item>
    </style>
    <style name="AlertDialogButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/neon_pink</item>
    </style>
    <style name="CustomAltButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/modern_button_alt</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
        <item name="android:textAppearance">@style/TextAppearance.VMeet.Button</item>
    </style>
    <style name="CustomBottomNavigation" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">#1A1A3A</item>
        <item name="itemIconTint">@color/comfy_blue</item>
        <item name="itemTextColor">@color/comfy_blue</item>
        <item name="labelVisibilityMode">labeled</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
        <item name="itemTextAppearanceActive">@style/TextAppearance.VMeet.Caption</item>
        <item name="itemTextAppearanceInactive">@style/TextAppearance.VMeet.Caption</item>
    </style>
    <style name="CustomPurpleButton" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/modern_button</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
        <item name="android:textAppearance">@style/TextAppearance.VMeet.Button</item>
    </style>
    <style name="CustomSwitchStyle" parent="Theme.AppCompat">
        <item name="colorAccent">@color/neon_blue</item>
        <item name="colorControlActivated">@color/neon_blue</item>
        <item name="colorSwitchThumbNormal">@color/text_secondary</item>
        <item name="android:colorForeground">@color/text_secondary</item>
    </style>
    <style name="MenuTextStyle">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:fontFamily">@font/app_font</item>
    </style>
    <style name="PopupMenu" parent="@style/Widget.Material3.PopupMenu">
        <item name="android:popupBackground">#212121</item>
    </style>
    <style name="ShapeAppearance.App.RoundedButton" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>
    <style name="ShapeAppearance.App.RoundedGif" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="TextAppearance.VMeet" parent="TextAppearance.AppCompat">
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
    </style>
    <style name="TextAppearance.VMeet.Body1">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.VMeet.Body2">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="TextAppearance.VMeet.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="TextAppearance.VMeet.Caption">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.VMeet.Headline1">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.VMeet.Headline2">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.VMeet.Headline3">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="Theme.VMeet" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="android:itemTextAppearance">@style/MenuTextStyle</item>
        <item name="popupMenuStyle">@style/PopupMenu</item>

        
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>        
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:statusBarColor">@color/darker_blue</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <style name="Theme.VMeet.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:background">@color/darker_blue</item>
        <item name="android:textColorPrimary">@android:color/white</item>
        <item name="android:textColorSecondary">@color/neon_blue</item>
        <item name="colorAccent">@color/neon_pink</item>
        <item name="android:buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
    </style>
    <style name="Theme.VMeet.Dialog.FullWidth" parent="Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">90%</item>
        <item name="android:windowMinWidthMinor">90%</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Theme.VMeet.NoActionBar" parent="Theme.VMeet">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>
    <style name="VMeetTextStyle">
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
    </style>
    <style name="VMeetTextStyle.Body">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="VMeetTextStyle.Subtitle">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="VMeetTextStyle.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="VMeetToolbar" parent="Widget.MaterialComponents.Toolbar">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">#1A1A3A</item>
        <item name="android:elevation">4dp</item>
        <item name="android:paddingTop">0dp</item>
        
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleTextColor">@color/comfy_blue</item>
        <item name="titleTextAppearance">@style/TextAppearance.VMeet.Headline2</item>
        <item name="android:fontFamily">@font/app_font</item>
        <item name="fontFamily">@font/app_font</item>
    </style>
    <declare-styleable name="PixelPerfectTextView">
        <attr name="android:text"/>
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
    </declare-styleable>
    <declare-styleable name="PurpleButton">
        <attr format="boolean" name="isAlt"/>
    </declare-styleable>
</resources>