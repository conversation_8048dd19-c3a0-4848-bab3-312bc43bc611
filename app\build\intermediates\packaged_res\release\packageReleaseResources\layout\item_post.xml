<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/darker_blue"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeColor="@color/neon_blue"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">        <!-- Glowing top edge for neon effect -->
        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/neon_blue" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Post Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/imageViewPostUserAvatar"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/avatar_circle_border"
                    android:padding="2dp"
                    android:src="@drawable/ic_profile_placeholder"
                    tools:srcCompat="@drawable/ic_profile_placeholder" />

                                <LinearLayout                    android:layout_width="0dp"                    android:layout_height="wrap_content"                    android:layout_weight="1"                    android:orientation="vertical"                    android:layout_marginStart="12dp">                    <TextView                        android:id="@+id/textViewPostUsername"                        android:layout_width="wrap_content"                        android:layout_height="wrap_content"                        android:text="Username"                        android:textColor="@color/cyberpunk_text_primary"                        android:textSize="16sp"                        android:textStyle="bold" />                    <TextView                        android:id="@+id/textViewPostTimestamp"                        android:layout_width="wrap_content"                        android:layout_height="wrap_content"                        android:text="Timestamp"                        android:textColor="@color/cyberpunk_text_secondary"                        android:textSize="12sp" />                </LinearLayout>                                <ImageButton                    android:id="@+id/buttonPostOptions"                    android:layout_width="48dp"                    android:layout_height="48dp"                    android:src="@drawable/ic_more_vert"                    android:background="?attr/selectableItemBackgroundBorderless"                    android:contentDescription="Post Options"                    app:tint="@color/cyberpunk_text_secondary" />
            </LinearLayout>

            <!-- Post Content -->
            <TextView
                android:id="@+id/textViewPostTextContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="This is the post content..."
                android:textColor="@color/cyberpunk_text_primary"
                android:lineSpacingExtra="4dp"
                android:textSize="15sp" />

            <!-- Image with rounded corners -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewPostImageContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                android:visibility="gone"
                tools:visibility="visible">

                <ImageView
                    android:id="@+id/imageViewPostImage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="200dp"
                    android:adjustViewBounds="true"
                    android:scaleType="centerCrop"
                    tools:src="@drawable/ic_placeholder_image" />
            </androidx.cardview.widget.CardView>

            <!-- YouTube Video Preview -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardViewYoutubeContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="0dp"
                android:visibility="gone"
                tools:visibility="visible">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- YouTube Thumbnail (shown initially) -->
                    <LinearLayout
                        android:id="@+id/layoutYoutubeThumbnail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        
                        <ImageView
                            android:id="@+id/imageViewYoutubeThumbnail"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:adjustViewBounds="true"
                            android:scaleType="centerCrop"
                            tools:src="@drawable/ic_placeholder_image" />

                        <ImageView
                            android:id="@+id/imageViewYoutubePlayButton"
                            android:layout_width="72dp"
                            android:layout_height="72dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_play_arrow"
                            android:background="@drawable/youtube_play_button_background"
                            android:padding="16dp"
                            app:tint="@color/white" />

                        <TextView
                            android:id="@+id/textViewYoutubeTitle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="8dp"
                            android:layout_gravity="bottom"
                            android:background="#80000000"
                            android:textColor="@color/white"
                            android:maxLines="2"
                            android:ellipsize="end"
                            android:textSize="14sp"
                            tools:text="YouTube Video Title" />
                    </LinearLayout>
                    
                    <!-- YouTube Player (hidden initially) -->
                    <com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
                        android:id="@+id/youtubePlayerView"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:visibility="gone" />
                        
                </FrameLayout>
            </androidx.cardview.widget.CardView>

            <!-- Voice Note Player -->
            <LinearLayout
                android:id="@+id/layoutVoiceNotePlayer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:gravity="center_vertical"
                android:background="@drawable/voice_note_player_background" 
                android:visibility="gone"
                tools:visibility="visible">

                <ImageButton
                    android:id="@+id/buttonPlayPauseVoiceNote"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_play_arrow"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:tint="@color/cyberpunk_accent_voice"
                    android:contentDescription="Play/Pause Voice Note" />

                <TextView
                    android:id="@+id/textViewVoiceNoteCurrentTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0:00"
                    android:textColor="@color/cyberpunk_text_secondary"
                    android:textSize="14sp"
                    android:layout_marginStart="8dp" />

                <SeekBar
                    android:id="@+id/seekBarVoiceNote"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:layout_marginEnd="8dp"
                    android:progressTint="@color/cyberpunk_accent_voice"
                    android:thumbTint="@color/cyberpunk_accent_voice_thumb" />

                <TextView
                    android:id="@+id/textViewVoiceNoteDuration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0:00"
                    android:textColor="@color/cyberpunk_text_secondary"
                    android:textSize="14sp" />
                
                <ImageView
                    android:id="@+id/imageViewVoiceNoteIcon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_audiotrack"
                    app:tint="@color/cyberpunk_text_secondary"
                    android:layout_marginStart="8dp"
                    android:visibility="gone" 
                    tools:visibility="visible"/>

            </LinearLayout>

            <!-- Divider -->
            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="@color/cyberpunk_divider" />

            <!-- Post Actions & Stats -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="12dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="4dp">

                    <ImageButton
                        android:id="@+id/buttonPostReact"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_like_outline"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        app:tint="@color/neon_pink" />
                        
                    <TextView
                        android:id="@+id/textViewPostReactionCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 Likes"
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:layout_marginStart="4dp"
                        android:layout_gravity="center_vertical"/>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:padding="4dp"
                    android:layout_marginStart="16dp">

                    <ImageButton
                        android:id="@+id/buttonPostComment"
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:src="@drawable/ic_comment_outline"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        app:tint="@color/neon_blue" />
                        
                    <TextView
                        android:id="@+id/textViewPostCommentCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 Comments"
                        android:textColor="@color/cyberpunk_text_secondary"
                        android:layout_marginStart="4dp"
                        android:layout_gravity="center_vertical"/>
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView> 