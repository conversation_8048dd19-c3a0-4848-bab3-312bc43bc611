package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0094\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010,\u001a\u00020-H\u0002J\b\u0010.\u001a\u00020-H\u0002J\u0006\u0010/\u001a\u00020-J\u0006\u00100\u001a\u00020-J_\u00101\u001a\u00020-2\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u00020\u00062\u0006\u00105\u001a\u00020\u00062\n\b\u0002\u00106\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u00107\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u00108\u001a\u0004\u0018\u0001032\n\b\u0002\u00109\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010:\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\u0002\u0010;J6\u0010<\u001a\u00020-2\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u00020\u00062\u0006\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020\u00062\u000e\u0010@\u001a\n\u0012\u0004\u0012\u00020A\u0018\u00010\u0012J\u0016\u0010B\u001a\u00020-2\u0006\u0010C\u001a\u00020\u00062\u0006\u00102\u001a\u000203J\u0016\u0010D\u001a\u00020-2\u0006\u0010E\u001a\u00020\u00062\u0006\u00102\u001a\u000203J\u0014\u0010F\u001a\u0004\u0018\u00010\u00062\b\u0010G\u001a\u0004\u0018\u00010\u0006H\u0002J\u0012\u0010H\u001a\u0004\u0018\u00010\u00062\b\u0010I\u001a\u0004\u0018\u00010\u0006J(\u0010J\u001a\u00020-2\u0006\u0010E\u001a\u00020\u00062\u0018\u0010K\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020M0\u0012\u0012\u0004\u0012\u00020-0LJ\u0016\u0010N\u001a\u00020-2\u0006\u0010C\u001a\u00020\u00062\u0006\u00102\u001a\u000203J\u0006\u0010O\u001a\u00020-J\u000e\u0010P\u001a\u00020-2\u0006\u0010Q\u001a\u000203J0\u0010R\u001a\u00020-2\u0006\u0010S\u001a\u0002032\u0006\u0010Q\u001a\u0002032\u0018\u0010K\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u0012\u0012\u0004\u0012\u00020-0LJ\u0016\u0010T\u001a\u00020-2\u0006\u0010E\u001a\u00020\u00062\u0006\u00102\u001a\u000203J\b\u0010U\u001a\u00020-H\u0014J\u0010\u0010V\u001a\u00020\b2\u0006\u0010W\u001a\u00020\u0006H\u0002J\u0006\u0010X\u001a\u00020-J\u0006\u0010Y\u001a\u00020-J6\u0010Z\u001a\u00020-2\u0006\u0010[\u001a\u00020>2\u0012\u0010\\\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020-0L2\u0012\u0010]\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020-0LJ>\u0010^\u001a\u00020-2\u0006\u0010=\u001a\u00020>2\u0006\u0010?\u001a\u00020\u00062\u0012\u0010\\\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020-0L2\u0012\u0010]\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020-0LJb\u0010_\u001a\u00020-2\u0006\u0010`\u001a\u00020>2\u0006\u0010a\u001a\u00020326\u0010\\\u001a2\u0012\u0013\u0012\u00110\u0006\u00a2\u0006\f\bc\u0012\b\bd\u0012\u0004\b\b(e\u0012\u0013\u0012\u001103\u00a2\u0006\f\bc\u0012\b\bd\u0012\u0004\b\b(a\u0012\u0004\u0012\u00020-0b2\u0012\u0010]\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020-0LR\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u001c\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u000bX\u0080\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001a\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000bX\u0080\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR \u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u000bX\u0080\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\rR \u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00120\u000bX\u0080\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\rR\u001a\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000bX\u0080\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\rR\u0019\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u000e\u0010\u001e\u001a\u00020\u001fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u000f0\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001dR\u000e\u0010!\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010$\u001a\u0004\u0018\u00010%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001d\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001dR\u001d\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00160\u00120\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001dR\u0017\u0010*\u001a\b\u0012\u0004\u0012\u00020\u000f0\u001b\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001d\u00a8\u0006f"}, d2 = {"Lcom/spyro/vmeet/ui/community/CommunityViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "API_URL", "", "POLLING_INTERVAL", "", "TAG", "_errorMessage", "Landroidx/lifecycle/MutableLiveData;", "get_errorMessage$app_debug", "()Landroidx/lifecycle/MutableLiveData;", "_isLoading", "", "get_isLoading$app_debug", "_posts", "", "Lcom/spyro/vmeet/data/community/Post;", "get_posts$app_debug", "_stories", "Lcom/spyro/vmeet/data/community/Story;", "get_stories$app_debug", "_storyUploadSuccess", "get_storyUploadSuccess$app_debug", "errorMessage", "Landroidx/lifecycle/LiveData;", "getErrorMessage", "()Landroidx/lifecycle/LiveData;", "handler", "Landroid/os/Handler;", "isLoading", "isPollingActive", "latestPostId", "latestPostTimestamp", "pollingRunnable", "Ljava/lang/Runnable;", "posts", "getPosts", "stories", "getStories", "storyUploadSuccess", "getStoryUploadSuccess", "checkForNewPosts", "", "checkForReactionUpdates", "clearErrorMessage", "clearStoryUploadSuccess", "createPost", "userId", "", "username", "content", "imageUrl", "voiceNoteUrl", "voiceNoteDuration", "gifUrl", "gifPreviewUrl", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)V", "createStory", "mediaUri", "Landroid/net/Uri;", "mediaType", "textOverlays", "Lcom/spyro/vmeet/ui/community/TextOverlayData;", "deletePost", "postId", "deleteStory", "storyId", "extractYoutubeId", "text", "formatUrl", "path", "getStoryViewers", "callback", "Lkotlin/Function1;", "Lcom/spyro/vmeet/data/community/StoryViewer;", "likePost", "loadPosts", "loadStories", "currentUserId", "loadStoriesForUser", "targetUserId", "markStoryAsSeen", "onCleared", "parseTimestamp", "dateString", "startPolling", "stopPolling", "uploadImage", "imageUri", "onSuccess", "onError", "uploadStoryMedia", "uploadVoiceNote", "audioFileUri", "duration", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "voiceUrl", "app_debug"})
public final class CommunityViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "CommunityViewModel";
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.spyro.vmeet.data.community.Post>> _posts = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.spyro.vmeet.data.community.Post>> posts = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.spyro.vmeet.data.community.Story>> _stories = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.spyro.vmeet.data.community.Story>> stories = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _storyUploadSuccess = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> storyUploadSuccess = null;
    private final long POLLING_INTERVAL = 1000L;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler handler = null;
    private boolean isPollingActive = false;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String latestPostId = "";
    private long latestPostTimestamp = 0L;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable pollingRunnable;
    
    public CommunityViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.util.List<com.spyro.vmeet.data.community.Post>> get_posts$app_debug() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.spyro.vmeet.data.community.Post>> getPosts() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.util.List<com.spyro.vmeet.data.community.Story>> get_stories$app_debug() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.spyro.vmeet.data.community.Story>> getStories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.Boolean> get_isLoading$app_debug() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.String> get_errorMessage$app_debug() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.MutableLiveData<java.lang.Boolean> get_storyUploadSuccess$app_debug() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getStoryUploadSuccess() {
        return null;
    }
    
    public final void startPolling() {
    }
    
    public final void stopPolling() {
    }
    
    private final void checkForNewPosts() {
    }
    
    private final void checkForReactionUpdates() {
    }
    
    private final java.lang.String extractYoutubeId(java.lang.String text) {
        return null;
    }
    
    public final void loadPosts() {
    }
    
    public final void createPost(int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    java.lang.String content, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String voiceNoteUrl, @org.jetbrains.annotations.Nullable()
    java.lang.Integer voiceNoteDuration, @org.jetbrains.annotations.Nullable()
    java.lang.String gifUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String gifPreviewUrl) {
    }
    
    public final void likePost(@org.jetbrains.annotations.NotNull()
    java.lang.String postId, int userId) {
    }
    
    public final void uploadImage(@org.jetbrains.annotations.NotNull()
    android.net.Uri imageUri, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError) {
    }
    
    public final void uploadVoiceNote(@org.jetbrains.annotations.NotNull()
    android.net.Uri audioFileUri, int duration, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Integer, kotlin.Unit> onSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError) {
    }
    
    public final void deletePost(@org.jetbrains.annotations.NotNull()
    java.lang.String postId, int userId) {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    public final void loadStories(int currentUserId) {
    }
    
    public final void uploadStoryMedia(@org.jetbrains.annotations.NotNull()
    android.net.Uri mediaUri, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSuccess, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onError) {
    }
    
    public final void createStory(int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    android.net.Uri mediaUri, @org.jetbrains.annotations.NotNull()
    java.lang.String mediaType, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.ui.community.TextOverlayData> textOverlays) {
    }
    
    public final void markStoryAsSeen(@org.jetbrains.annotations.NotNull()
    java.lang.String storyId, int userId) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String formatUrl(@org.jetbrains.annotations.Nullable()
    java.lang.String path) {
        return null;
    }
    
    public final void loadStoriesForUser(int targetUserId, int currentUserId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<com.spyro.vmeet.data.community.Story>, kotlin.Unit> callback) {
    }
    
    public final void getStoryViewers(@org.jetbrains.annotations.NotNull()
    java.lang.String storyId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<com.spyro.vmeet.data.community.StoryViewer>, kotlin.Unit> callback) {
    }
    
    public final void clearErrorMessage() {
    }
    
    public final void clearStoryUploadSuccess() {
    }
    
    public final void deleteStory(@org.jetbrains.annotations.NotNull()
    java.lang.String storyId, int userId) {
    }
    
    private final long parseTimestamp(java.lang.String dateString) {
        return 0L;
    }
}