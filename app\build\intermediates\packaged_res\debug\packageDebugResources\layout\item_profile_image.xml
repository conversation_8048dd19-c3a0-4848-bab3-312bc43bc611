<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/imageViewProfile"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:contentDescription="Imagen de perfil"/>

    <View
        android:id="@+id/viewMainIndicator"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:background="@color/comfy_blue"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- Botón de eliminar imagen (visible solo en modo edición) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabDeleteImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="Eliminar imagen"
        android:visibility="gone"
        app:fabSize="mini"
        app:tint="@android:color/white"
        app:backgroundTint="@android:color/holo_red_dark"
        app:srcCompat="@android:drawable/ic_menu_delete"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible"/>

</androidx.constraintlayout.widget.ConstraintLayout>