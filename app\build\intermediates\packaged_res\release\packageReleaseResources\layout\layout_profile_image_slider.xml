<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPagerImages"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- Remove the background panel, just keep the TabLayout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayoutDots"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        android:layout_marginBottom="12dp"
        android:background="@android:color/transparent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tabBackground="@drawable/tab_selector"
        app:tabGravity="center"
        app:tabPadding="3dp"
        app:tabIndicatorHeight="0dp"
        app:tabMinWidth="8dp"/>

    <Button
        android:id="@+id/buttonSetAsMain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Establecer como principal"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:alpha="0.8"
        android:visibility="gone"
        android:backgroundTint="@color/comfy_blue"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tabLayoutDots"/>
        
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginBottom="16dp"
        android:src="@android:drawable/ic_input_add"
        android:contentDescription="Add image"
        android:visibility="gone"
        app:tint="@color/white"
        app:backgroundTint="@color/comfy_blue"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/tabLayoutDots" />

</androidx.constraintlayout.widget.ConstraintLayout> 