<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimaryDark"
    android:padding="16dp">

    <TextView
        android:id="@+id/textViewResultTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="¡Resultado!"
        android:textAlignment="center"
        android:textColor="@color/comfy_blue"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewResultMessage"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewResultTitle"
        tools:text="¡Ambos habéis decidido revelar vuestras identidades!" />

    <!-- Match result - only shown when both users reveal -->
    <LinearLayout
        android:id="@+id/layoutMatchResult"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewResultMessage">

        <ImageView
            android:id="@+id/imageViewUserAvatar"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/circle_background"
            android:padding="2dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_profile_placeholder" />

        <ImageView
            android:id="@+id/imageViewHeartIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_heart"
            android:tint="@color/neon_pink" />

        <ImageView
            android:id="@+id/imageViewOtherUserAvatar"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/circle_background"
            android:padding="2dp"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_profile_placeholder" />
    </LinearLayout>

    <TextView
        android:id="@+id/textViewUserNames"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutMatchResult"
        tools:text="Juan y María" />

    <!-- Buttons -->
    <Button
        android:id="@+id/buttonContinueToChat"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="48dp"
        android:layout_marginEnd="32dp"
        android:background="@drawable/neon_button_blue"
        android:padding="12dp"
        android:text="Continuar al chat"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewUserNames" />

    <Button
        android:id="@+id/buttonNewMatch"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="32dp"
        android:background="@drawable/neon_button_green"
        android:padding="12dp"
        android:text="Buscar nueva cita a ciegas"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/buttonContinueToChat" />

</androidx.constraintlayout.widget.ConstraintLayout>
