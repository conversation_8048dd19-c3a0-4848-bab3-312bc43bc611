package com.spyro.vmeet.ui.community;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a4\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u00012\u00020\u0002:\u0001FB\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010+\u001a\u00020,H\u0002J\u0010\u0010-\u001a\u00020,2\u0006\u0010.\u001a\u00020/H\u0016J\u0012\u00100\u001a\u00020,2\b\u00101\u001a\u0004\u0018\u000102H\u0016J\u0012\u00103\u001a\u0002042\b\u00101\u001a\u0004\u0018\u000102H\u0016J&\u00105\u001a\u0004\u0018\u0001062\u0006\u00107\u001a\u0002082\b\u00109\u001a\u0004\u0018\u00010:2\b\u00101\u001a\u0004\u0018\u000102H\u0016J\u0012\u0010;\u001a\u00020,2\b\u0010<\u001a\u0004\u0018\u00010$H\u0016J\b\u0010=\u001a\u00020,H\u0016J\u001a\u0010>\u001a\u00020,2\u0006\u0010?\u001a\u0002062\b\u00101\u001a\u0004\u0018\u000102H\u0016J\b\u0010@\u001a\u00020,H\u0002J\b\u0010A\u001a\u00020,H\u0002J\b\u0010B\u001a\u00020,H\u0002J\b\u0010C\u001a\u00020,H\u0002J\b\u0010D\u001a\u00020,H\u0002J\b\u0010E\u001a\u00020,H\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001c\u0010\u001d\u001a\u0010\u0012\f\u0012\n  *\u0004\u0018\u00010\u001f0\u001f0\u001eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\"\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010#\u001a\u0004\u0018\u00010$X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010%\u001a\u0004\u0018\u00010$X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010*\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006G"}, d2 = {"Lcom/spyro/vmeet/ui/community/CreatePostDialog;", "Landroidx/fragment/app/DialogFragment;", "Lcom/spyro/vmeet/ui/community/ImageUploadHelper$ImageSelectedListener;", "()V", "buttonAddGif", "Landroid/widget/ImageButton;", "buttonAddImage", "buttonAddVoice", "buttonPostSubmit", "Landroid/widget/Button;", "buttonRemoveImage", "buttonRemoveVoiceNote", "cardViewImagePreview", "Landroidx/cardview/widget/CardView;", "editTextPostContent", "Landroid/widget/EditText;", "imageUploadHelper", "Lcom/spyro/vmeet/ui/community/ImageUploadHelper;", "imageViewPreview", "Landroid/widget/ImageView;", "isGifSelected", "", "isRecording", "listener", "Lcom/spyro/vmeet/ui/community/CreatePostDialog$CreatePostListener;", "mediaRecorder", "Landroid/media/MediaRecorder;", "recordingStartTime", "", "requestAudioPermissionLauncher", "Landroidx/activity/result/ActivityResultLauncher;", "", "kotlin.jvm.PlatformType", "selectedGifPreviewUrl", "selectedGifUrl", "selectedImageUri", "Landroid/net/Uri;", "selectedVoiceNoteUri", "textViewVoiceNoteInfo", "Landroid/widget/TextView;", "voiceNoteDuration", "", "voiceNoteFilePath", "clearGifSelection", "", "onAttach", "context", "Landroid/content/Context;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateDialog", "Landroid/app/Dialog;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onImageSelected", "imageUri", "onStop", "onViewCreated", "view", "removeVoiceNote", "setupListeners", "showGifPicker", "showImagePickerOptions", "startRecording", "stopRecording", "CreatePostListener", "app_debug"})
public final class CreatePostDialog extends androidx.fragment.app.DialogFragment implements com.spyro.vmeet.ui.community.ImageUploadHelper.ImageSelectedListener {
    private android.widget.EditText editTextPostContent;
    private android.widget.Button buttonPostSubmit;
    private android.widget.ImageButton buttonAddImage;
    private android.widget.ImageButton buttonAddGif;
    private android.widget.ImageButton buttonAddVoice;
    private androidx.cardview.widget.CardView cardViewImagePreview;
    private android.widget.ImageView imageViewPreview;
    private android.widget.ImageButton buttonRemoveImage;
    private android.widget.TextView textViewVoiceNoteInfo;
    private android.widget.ImageButton buttonRemoveVoiceNote;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri selectedVoiceNoteUri;
    private int voiceNoteDuration = 0;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String voiceNoteFilePath;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedGifUrl;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String selectedGifPreviewUrl;
    private boolean isGifSelected = false;
    @org.jetbrains.annotations.Nullable()
    private android.media.MediaRecorder mediaRecorder;
    private boolean isRecording = false;
    private long recordingStartTime = 0L;
    private com.spyro.vmeet.ui.community.ImageUploadHelper imageUploadHelper;
    @org.jetbrains.annotations.Nullable()
    private android.net.Uri selectedImageUri;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.ui.community.CreatePostDialog.CreatePostListener listener;
    @org.jetbrains.annotations.NotNull()
    private final androidx.activity.result.ActivityResultLauncher<java.lang.String> requestAudioPermissionLauncher = null;
    
    public CreatePostDialog() {
        super();
    }
    
    @java.lang.Override()
    public void onAttach(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    @java.lang.Override()
    public void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.app.Dialog onCreateDialog(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupListeners() {
    }
    
    private final void showImagePickerOptions() {
    }
    
    private final void showGifPicker() {
    }
    
    private final void clearGifSelection() {
    }
    
    @java.lang.Override()
    public void onImageSelected(@org.jetbrains.annotations.Nullable()
    android.net.Uri imageUri) {
    }
    
    private final void removeVoiceNote() {
    }
    
    private final void startRecording() {
    }
    
    private final void stopRecording() {
    }
    
    @java.lang.Override()
    public void onStop() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\bf\u0018\u00002\u00020\u0001JQ\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005H&\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/spyro/vmeet/ui/community/CreatePostDialog$CreatePostListener;", "", "onPostCreated", "", "postContent", "", "imageUri", "Landroid/net/Uri;", "voiceNoteUri", "voiceNoteDuration", "", "gifUrl", "gifPreviewUrl", "(Ljava/lang/String;Landroid/net/Uri;Landroid/net/Uri;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;)V", "app_debug"})
    public static abstract interface CreatePostListener {
        
        public abstract void onPostCreated(@org.jetbrains.annotations.NotNull()
        java.lang.String postContent, @org.jetbrains.annotations.Nullable()
        android.net.Uri imageUri, @org.jetbrains.annotations.Nullable()
        android.net.Uri voiceNoteUri, @org.jetbrains.annotations.Nullable()
        java.lang.Integer voiceNoteDuration, @org.jetbrains.annotations.Nullable()
        java.lang.String gifUrl, @org.jetbrains.annotations.Nullable()
        java.lang.String gifPreviewUrl);
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
        public static final class DefaultImpls {
        }
    }
}