package com.spyro.vmeet.util;

/**
 * Utility class for URL-related operations
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0005\u001a\u0004\u0018\u00010\u00042\b\u0010\u0006\u001a\u0004\u0018\u00010\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/spyro/vmeet/util/UrlUtils;", "", "()V", "API_URL", "", "formatUrl", "path", "app_debug"})
public final class UrlUtils {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://77.110.116.89:3000";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.util.UrlUtils INSTANCE = null;
    
    private UrlUtils() {
        super();
    }
    
    /**
     * Formats a URL by determining if it's already a complete URL or a relative path
     * If it's a relative path, the API_URL is prepended.
     * If it's already a complete URL, it's returned as is.
     *
     * @param path The URL or path to format
     * @return A properly formatted complete URL
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String formatUrl(@org.jetbrains.annotations.Nullable()
    java.lang.String path) {
        return null;
    }
}