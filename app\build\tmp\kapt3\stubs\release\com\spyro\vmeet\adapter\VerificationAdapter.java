package com.spyro.vmeet.adapter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0002\"#B\u001b\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\nH\u0002J\b\u0010\f\u001a\u00020\rH\u0016J\u0018\u0010\u000e\u001a\u00020\r2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\nH\u0002J\u0010\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0011\u001a\u00020\nH\u0002J\u0018\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00022\u0006\u0010\u0016\u001a\u00020\rH\u0016J\u0018\u0010\u0017\u001a\u00020\u00022\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\rH\u0016J \u0010\u001b\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u001c\u001a\u00020\n2\u0006\u0010\u001d\u001a\u00020\nH\u0002J\u0018\u0010\u001e\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u001f\u001a\u00020\rH\u0002J\u0014\u0010 \u001a\u00020\u00142\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006$"}, d2 = {"Lcom/spyro/vmeet/adapter/VerificationAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/adapter/VerificationAdapter$VerificationViewHolder;", "verifications", "", "Lorg/json/JSONObject;", "listener", "Lcom/spyro/vmeet/adapter/VerificationAdapter$VerificationActionListener;", "(Ljava/util/List;Lcom/spyro/vmeet/adapter/VerificationAdapter$VerificationActionListener;)V", "getDocumentTypeDisplayName", "", "type", "getItemCount", "", "getStatusColor", "context", "Landroid/content/Context;", "status", "getStatusDisplayName", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "openFullScreenImage", "imageUrl", "title", "showRejectDialog", "verificationId", "updateVerifications", "newVerifications", "VerificationActionListener", "VerificationViewHolder", "app_release"})
public final class VerificationAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.adapter.VerificationAdapter.VerificationViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<? extends org.json.JSONObject> verifications;
    @org.jetbrains.annotations.NotNull()
    private final com.spyro.vmeet.adapter.VerificationAdapter.VerificationActionListener listener = null;
    
    public VerificationAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends org.json.JSONObject> verifications, @org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.VerificationAdapter.VerificationActionListener listener) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.adapter.VerificationAdapter.VerificationViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.adapter.VerificationAdapter.VerificationViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    public final void updateVerifications(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends org.json.JSONObject> newVerifications) {
    }
    
    private final java.lang.String getDocumentTypeDisplayName(java.lang.String type) {
        return null;
    }
    
    private final java.lang.String getStatusDisplayName(java.lang.String status) {
        return null;
    }
    
    private final int getStatusColor(android.content.Context context, java.lang.String status) {
        return 0;
    }
    
    private final void openFullScreenImage(android.content.Context context, java.lang.String imageUrl, java.lang.String title) {
    }
    
    private final void showRejectDialog(android.content.Context context, int verificationId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0018\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\bH&\u00a8\u0006\t"}, d2 = {"Lcom/spyro/vmeet/adapter/VerificationAdapter$VerificationActionListener;", "", "onApproveVerification", "", "verificationId", "", "onRejectVerification", "reason", "", "app_release"})
    public static abstract interface VerificationActionListener {
        
        public abstract void onApproveVerification(int verificationId);
        
        public abstract void onRejectVerification(int verificationId, @org.jetbrains.annotations.NotNull()
        java.lang.String reason);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\bR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u000f\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0015\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0014R\u0011\u0010\u0017\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0014R\u0011\u0010\u0019\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0011\u0010\u001b\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0014\u00a8\u0006\u001d"}, d2 = {"Lcom/spyro/vmeet/adapter/VerificationAdapter$VerificationViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Landroid/view/View;)V", "btnApprove", "Landroid/widget/Button;", "getBtnApprove", "()Landroid/widget/Button;", "btnReject", "getBtnReject", "ivDocumentPhoto", "Landroid/widget/ImageView;", "getIvDocumentPhoto", "()Landroid/widget/ImageView;", "ivSelfiePhoto", "getIvSelfiePhoto", "tvDocumentType", "Landroid/widget/TextView;", "getTvDocumentType", "()Landroid/widget/TextView;", "tvEmail", "getTvEmail", "tvStatus", "getTvStatus", "tvSubmittedAt", "getTvSubmittedAt", "tvUsername", "getTvUsername", "app_release"})
    public static final class VerificationViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvUsername = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvEmail = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvDocumentType = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvSubmittedAt = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView ivDocumentPhoto = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView ivSelfiePhoto = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnApprove = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnReject = null;
        
        public VerificationViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvUsername() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvEmail() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvDocumentType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvSubmittedAt() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getIvDocumentPhoto() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getIvSelfiePhoto() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getBtnApprove() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getBtnReject() {
            return null;
        }
    }
}