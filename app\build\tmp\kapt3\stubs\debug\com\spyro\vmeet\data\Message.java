package com.spyro.vmeet.data;

/**
 * Represents a single chat message.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\bM\b\u0087\b\u0018\u00002\u00020\u0001B\u00a5\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000b\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\b\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\b\u0012\u0010\b\u0002\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u001c\u0018\u00010\u001b\u0012\u0014\b\u0002\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u001e\u0012\u0010\b\u0002\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u001b\u00a2\u0006\u0002\u0010 J\t\u0010A\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010B\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010C\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010D\u001a\u00020\u0003H\u00c6\u0003J\t\u0010E\u001a\u00020\bH\u00c6\u0003J\u000b\u0010F\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010G\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010H\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010I\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0010\u0010J\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u00108J\u000b\u0010K\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010M\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u00108J\u000b\u0010N\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0011\u0010O\u001a\n\u0012\u0004\u0012\u00020\u001c\u0018\u00010\u001bH\u00c6\u0003J\u0015\u0010P\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u001eH\u00c6\u0003J\u0011\u0010Q\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u001bH\u00c6\u0003J\t\u0010R\u001a\u00020\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\u0003H\u00c6\u0003J\t\u0010T\u001a\u00020\bH\u00c6\u0003J\t\u0010U\u001a\u00020\bH\u00c6\u0003J\t\u0010V\u001a\u00020\u000bH\u00c6\u0003J\t\u0010W\u001a\u00020\u000bH\u00c6\u0003J\t\u0010X\u001a\u00020\u000bH\u00c6\u0003J\u00ae\u0002\u0010Y\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\b2\u0010\b\u0002\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u001c\u0018\u00010\u001b2\u0014\b\u0002\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u001e2\u0010\b\u0002\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u001bH\u00c6\u0001\u00a2\u0006\u0002\u0010ZJ\u0013\u0010[\u001a\u00020\u000b2\b\u0010\\\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010]\u001a\u00020\bJ\u0010\u0010^\u001a\u0004\u0018\u00010\b2\u0006\u0010_\u001a\u00020\u0003J\u000e\u0010`\u001a\u00020\u000b2\u0006\u0010_\u001a\u00020\u0003J\u0016\u0010a\u001a\u00020\u000b2\u0006\u0010_\u001a\u00020\u00032\u0006\u0010b\u001a\u00020\bJ\t\u0010c\u001a\u00020\u0003H\u00d6\u0001J\u0006\u0010d\u001a\u00020\u000bJ\u0006\u0010e\u001a\u00020\u000bJ\u0006\u0010f\u001a\u00020\u000bJ\u0006\u0010g\u001a\u00020\u000bJ\u0006\u0010h\u001a\u00020\u000bJ\u0006\u0010i\u001a\u00020\u000bJ\t\u0010j\u001a\u00020\bH\u00d6\u0001R\u0018\u0010\u000e\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0016\u0010\t\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\"R\u0018\u0010\u0015\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\"R\u0018\u0010\u0014\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\"R\u0018\u0010\u0013\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\"R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0018\u0010\u0012\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\"R\u0011\u0010\r\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010*R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010*R\u0016\u0010\n\u001a\u00020\u000b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010*R\u0016\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010(R\u001e\u0010\u001f\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u001b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0016\u0010\u0011\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\"R&\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u001eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u00100\"\u0004\b1\u00102R\"\u0010\u001a\u001a\n\u0012\u0004\u0012\u00020\u001c\u0018\u00010\u001bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010-\"\u0004\b4\u00105R\u0016\u0010\u0006\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010(R\u001a\u0010\u0016\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u00109\u001a\u0004\b7\u00108R\u001a\u0010\u0018\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u00109\u001a\u0004\b:\u00108R\u0018\u0010\u0017\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010\"R\u0018\u0010\u0019\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010\"R\u0016\u0010\u0005\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010(R\u0016\u0010\u0007\u001a\u00020\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010\"R\u0016\u0010\u0010\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010(R\u0018\u0010\u000f\u001a\u0004\u0018\u00010\b8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010\"\u00a8\u0006k"}, d2 = {"Lcom/spyro/vmeet/data/Message;", "", "id", "", "matchId", "senderId", "receiverId", "text", "", "createdAt", "isRead", "", "isPending", "isError", "clientMessageId", "voiceFile", "voiceDuration", "messageType", "imageFile", "gifUrl", "gifPreviewUrl", "gifId", "replyToId", "replyToText", "replyToSenderId", "senderAvatarUrl", "reactions", "", "Lcom/spyro/vmeet/data/MessageReaction;", "metadata", "", "mentionedUserIds", "(IIIILjava/lang/String;Ljava/lang/String;ZZZLjava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;Ljava/util/List;)V", "getClientMessageId", "()Ljava/lang/String;", "getCreatedAt", "getGifId", "getGifPreviewUrl", "getGifUrl", "getId", "()I", "getImageFile", "()Z", "getMatchId", "getMentionedUserIds", "()Ljava/util/List;", "getMessageType", "getMetadata", "()Ljava/util/Map;", "setMetadata", "(Ljava/util/Map;)V", "getReactions", "setReactions", "(Ljava/util/List;)V", "getReceiverId", "getReplyToId", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getReplyToSenderId", "getReplyToText", "getSenderAvatarUrl", "getSenderId", "getText", "getVoiceDuration", "getVoiceFile", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(IIIILjava/lang/String;Ljava/lang/String;ZZZLjava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;Ljava/util/List;)Lcom/spyro/vmeet/data/Message;", "equals", "other", "getSenderName", "getUserReaction", "userId", "hasUserReacted", "hasUserReactedWith", "reactionType", "hashCode", "isAdmin", "isBuzzMessage", "isGifMessage", "isImageMessage", "isReplyMessage", "isVoiceMessage", "toString", "app_debug"})
public final class Message {
    private final int id = 0;
    @com.google.gson.annotations.SerializedName(value = "match_id")
    private final int matchId = 0;
    @com.google.gson.annotations.SerializedName(value = "sender_id")
    private final int senderId = 0;
    @com.google.gson.annotations.SerializedName(value = "receiver_id")
    private final int receiverId = 0;
    @com.google.gson.annotations.SerializedName(value = "message_text")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String text = null;
    @com.google.gson.annotations.SerializedName(value = "created_at")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String createdAt = null;
    @com.google.gson.annotations.SerializedName(value = "isRead", alternate = {"read"})
    private final boolean isRead = false;
    private final boolean isPending = false;
    private final boolean isError = false;
    @com.google.gson.annotations.SerializedName(value = "clientMessageId")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String clientMessageId = null;
    @com.google.gson.annotations.SerializedName(value = "voice_file")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String voiceFile = null;
    @com.google.gson.annotations.SerializedName(value = "voice_duration")
    private final int voiceDuration = 0;
    @com.google.gson.annotations.SerializedName(value = "message_type")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String messageType = null;
    @com.google.gson.annotations.SerializedName(value = "image_file")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String imageFile = null;
    @com.google.gson.annotations.SerializedName(value = "gif_url")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String gifUrl = null;
    @com.google.gson.annotations.SerializedName(value = "gif_preview_url")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String gifPreviewUrl = null;
    @com.google.gson.annotations.SerializedName(value = "gif_id")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String gifId = null;
    @com.google.gson.annotations.SerializedName(value = "reply_to_id")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer replyToId = null;
    @com.google.gson.annotations.SerializedName(value = "reply_to_text")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String replyToText = null;
    @com.google.gson.annotations.SerializedName(value = "reply_to_sender_id")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer replyToSenderId = null;
    @com.google.gson.annotations.SerializedName(value = "sender_avatar_url")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String senderAvatarUrl = null;
    @org.jetbrains.annotations.Nullable()
    private java.util.List<com.spyro.vmeet.data.MessageReaction> reactions;
    @org.jetbrains.annotations.NotNull()
    private java.util.Map<java.lang.String, java.lang.String> metadata;
    @com.google.gson.annotations.SerializedName(value = "mentioned_user_ids")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<java.lang.Integer> mentionedUserIds = null;
    
    public Message(int id, int matchId, int senderId, int receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    java.lang.String createdAt, boolean isRead, boolean isPending, boolean isError, @org.jetbrains.annotations.Nullable()
    java.lang.String clientMessageId, @org.jetbrains.annotations.Nullable()
    java.lang.String voiceFile, int voiceDuration, @org.jetbrains.annotations.NotNull()
    java.lang.String messageType, @org.jetbrains.annotations.Nullable()
    java.lang.String imageFile, @org.jetbrains.annotations.Nullable()
    java.lang.String gifUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String gifPreviewUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String gifId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer replyToId, @org.jetbrains.annotations.Nullable()
    java.lang.String replyToText, @org.jetbrains.annotations.Nullable()
    java.lang.Integer replyToSenderId, @org.jetbrains.annotations.Nullable()
    java.lang.String senderAvatarUrl, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.data.MessageReaction> reactions, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> metadata, @org.jetbrains.annotations.Nullable()
    java.util.List<java.lang.Integer> mentionedUserIds) {
        super();
    }
    
    public final int getId() {
        return 0;
    }
    
    public final int getMatchId() {
        return 0;
    }
    
    public final int getSenderId() {
        return 0;
    }
    
    public final int getReceiverId() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCreatedAt() {
        return null;
    }
    
    public final boolean isRead() {
        return false;
    }
    
    public final boolean isPending() {
        return false;
    }
    
    public final boolean isError() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getClientMessageId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getVoiceFile() {
        return null;
    }
    
    public final int getVoiceDuration() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMessageType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getImageFile() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGifUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGifPreviewUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getGifId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getReplyToId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getReplyToText() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getReplyToSenderId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSenderAvatarUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.spyro.vmeet.data.MessageReaction> getReactions() {
        return null;
    }
    
    public final void setReactions(@org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.data.MessageReaction> p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> getMetadata() {
        return null;
    }
    
    public final void setMetadata(@org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<java.lang.Integer> getMentionedUserIds() {
        return null;
    }
    
    public final boolean isVoiceMessage() {
        return false;
    }
    
    public final boolean isImageMessage() {
        return false;
    }
    
    public final boolean isGifMessage() {
        return false;
    }
    
    public final boolean isBuzzMessage() {
        return false;
    }
    
    public final boolean isReplyMessage() {
        return false;
    }
    
    /**
     * Gets the sender name if available in metadata
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSenderName() {
        return null;
    }
    
    /**
     * Checks if the sender is an admin based on metadata
     */
    public final boolean isAdmin() {
        return false;
    }
    
    /**
     * Checks if a specific user has reacted to this message
     */
    public final boolean hasUserReacted(int userId) {
        return false;
    }
    
    /**
     * Gets the specific reaction a user has added to this message, if any
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUserReaction(int userId) {
        return null;
    }
    
    /**
     * Checks if this user has already used a specific reaction on this message
     */
    public final boolean hasUserReactedWith(int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String reactionType) {
        return false;
    }
    
    public Message() {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    public final int component12() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component19() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.spyro.vmeet.data.MessageReaction> component22() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> component23() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<java.lang.Integer> component24() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.spyro.vmeet.data.Message copy(int id, int matchId, int senderId, int receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    java.lang.String createdAt, boolean isRead, boolean isPending, boolean isError, @org.jetbrains.annotations.Nullable()
    java.lang.String clientMessageId, @org.jetbrains.annotations.Nullable()
    java.lang.String voiceFile, int voiceDuration, @org.jetbrains.annotations.NotNull()
    java.lang.String messageType, @org.jetbrains.annotations.Nullable()
    java.lang.String imageFile, @org.jetbrains.annotations.Nullable()
    java.lang.String gifUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String gifPreviewUrl, @org.jetbrains.annotations.Nullable()
    java.lang.String gifId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer replyToId, @org.jetbrains.annotations.Nullable()
    java.lang.String replyToText, @org.jetbrains.annotations.Nullable()
    java.lang.Integer replyToSenderId, @org.jetbrains.annotations.Nullable()
    java.lang.String senderAvatarUrl, @org.jetbrains.annotations.Nullable()
    java.util.List<com.spyro.vmeet.data.MessageReaction> reactions, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> metadata, @org.jetbrains.annotations.Nullable()
    java.util.List<java.lang.Integer> mentionedUserIds) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}