const express = require('express');
const cors = require('cors');
const path = require('path');
const mysql = require('mysql2/promise'); // Import mysql2
const multer = require('multer'); // Import multer
const crypto = require('crypto'); // For generating random names
const WebSocket = require('ws'); // Import WebSocket
const http = require('http'); // Import http module

// --- Multer Configuration --- START ---
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/') // Save files to the 'uploads' directory
    },
    filename: function (req, file, cb) {
        // Generate a random filename + keep original extension
        const randomName = crypto.randomBytes(16).toString('hex');
        const extension = path.extname(file.originalname);
        cb(null, randomName + extension)
    }
});

// Image upload configuration
const imageUpload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB file size limit
    fileFilter: function (req, file, cb) {
        // Accept only image files
        if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/i)) {
            return cb(new Error('Only image files are allowed!'), false);
        }
        cb(null, true);
    }
});

// Voice message upload configuration
const voiceUpload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB file size limit
    fileFilter: function (req, file, cb) {
        // Accept audio files
        if (!file.originalname.match(/\.(mp3|m4a|aac|wav|ogg)$/i)) {
            return cb(new Error('Only audio files are allowed!'), false);
        }
        cb(null, true);
    }
});

// Story media (image/video) upload configuration
const storyMediaUpload = multer({
    storage: storage, // Use the same diskStorage
    limits: { fileSize: 25 * 1024 * 1024 }, // 25MB limit for stories (adjust as needed)
    fileFilter: function (req, file, cb) {
        // Accept image and common video files
        if (file.fieldname === "media") { // Ensure the field name is 'media'
            if (file.mimetype.startsWith('image/') || file.mimetype.startsWith('video/')) {
                cb(null, true);
            } else {
                console.error('Invalid story media type:', file.mimetype);
                cb(new Error('Only image or video files are allowed for stories!'), false);
            }
        } else {
            cb(null, false); // Only accept 'media' fieldname for files
        }
    }
});
// --- Database Configuration --- START ---
const dbConfig = {
    host: '*************', // Your VPS database host
    user: 'vmeet',
    password: 'Dickies30@@@', // Ensure this is secure
    database: 'vmeet',
    port: 3306,
    connectTimeout: 60000
};

// Reemplazamos conexiones directas por un pool
const pool = mysql.createPool(dbConfig);

async function getConnection() {
    const connection = await pool.getConnection();
    // Alias end() to release() so pooled connections are released safely
    const origRelease = connection.release.bind(connection);
    connection.end = () => Promise.resolve(origRelease());
    return connection;
}

// Track user presence - add near the top of the file
const userPresence = new Map(); // Map of userId -> {online: boolean, lastSeen: timestamp}

// Global user-to-last-uploaded-file mapping
const lastUploadedVoiceFiles = new Map(); // Map of userId -> {filePath, timestamp}

// Make it globally accessible
global.lastUploadedVoiceFiles = lastUploadedVoiceFiles;

// Map to store pending messages for offline users
const pendingMessages = new Map(); // Map of userId -> Array of messages

// Create Express app
const app = express();

// Mock data and counters
let userIdCounter = 11; // Start from 11 (existing users are 1-11)

// Security middleware
const helmet = require('helmet');
const { verifyAccessToken, verifyAdmin, permissiveAuth } = require('./middleware/jwtAuth');
const { generalLimiter, adminLimiter, messageLimiter, swipeLimiter } = require('./middleware/rateLimiting');

// Apply security headers
app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false // Disable for file uploads
}));

// Apply general rate limiting
app.use(generalLimiter);

// IMPORTANT: Parse JSON body BEFORE any routes
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cors({
    origin: process.env.NODE_ENV === 'production'
        ? ['https://vmeet.app', 'https://www.vmeet.app']
        : true,
    credentials: true
}));

// Serve static files from public_images_root for /images path
app.use('/images', express.static(path.join(__dirname, 'public_images_root/images')));

// Import notifications module for real-time notifications
const notifications = require('./notifications');

// Import the blind date WebSocket handler
const blindDateHandler = require('./websocket_blind_date_handler');

// Import Firebase module (already initialized) and make it globally accessible
try {
    const firebase = require('./firebase');
    const admin = require('firebase-admin');

    // Make Firebase Admin globally accessible
    global.firebaseAdmin = admin;
    console.log('Firebase Admin SDK set globally successfully');
} catch (error) {
    console.error('Error setting Firebase Admin SDK globally:', error);
    global.firebaseAdmin = null;
}

// Debug middleware for logging request bodies
app.use((req, res, next) => {
  const requestPath = `${req.method} ${req.url}`;
  console.log(`[${new Date().toISOString()}] ${requestPath}`);

  // Add specific logging for uploads
  if (req.url.includes('/uploads') && req.method === 'POST') {
    console.log('[UPLOADS ENDPOINT] Headers:', req.headers);
    console.log('[UPLOADS ENDPOINT] Content-Type:', req.headers['content-type']);
    console.log('[UPLOADS ENDPOINT] Body type:', typeof req.body);
    console.log('[UPLOADS ENDPOINT] Files:', req.files);
  }

  // Add specific logging for /match endpoint
  if (req.url.includes('/match') && req.method === 'POST') {
    console.log('[MATCH ENDPOINT] Raw Body:', req.body);
    console.log('[MATCH ENDPOINT] Content-Type:', req.headers['content-type']);
  }

  // Log request body for debugging POST/PUT requests
  if ((req.method === 'POST' || req.method === 'PUT') && req.body) {
    console.log('[REQUEST BODY]', JSON.stringify(req.body));
  }

  next();
});

// Serve static files for user uploads
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Import routes
const authRoutes = require('./routes/auth');
const profileRoutes = require('./routes/profile');
const matchesRoutes = require('./routes/matches');
const chatRoutes = require('./routes/chat');
const swipeRoutes = require('./routes/swipe');
const uploadRoutes = require('./routes/upload');
const notificationRoutes = require('./routes/notifications'); // Add notifications routes
const communityRoutes = require('./routes/community'); // Add community routes
const userRouter = require('./routes/user'); // Add user routes
const communityStoriesRoutes = require('./routes/community_stories'); // Add community stories routes
const adminRoutes = require('./routes/admin'); // Add admin routes
const chatRoomsRoutes = require('./routes/chat-rooms'); // Add chat rooms routes
const blindDateRoutes = require('./routes/blindDate'); // Add blind date routes
const settingsRoutes = require('./routes/settings'); // Add settings routes
const directMessagesRoutes = require('./routes/direct-messages'); // Add direct messages routes
const radarRoutes = require('./routes/radar'); // Add radar routes
const experienceRoutes = require('./routes/experience'); // Add experience routes
const videosRoutes = require('./routes/videos'); // Add videos routes
const verificationRoutes = require('./routes/verification'); // Add verification routes

// Helper function to ensure messages have sender avatar URLs
async function enrichMessageWithSenderAvatar(message, connection) {
    // If message already has sender_avatar_url, no need to enrich
    if (message.sender_avatar_url) {
        return message;
    }

    try {
        const [users] = await connection.execute(
            'SELECT avatar_url FROM users WHERE id = ?',
            [message.sender_id]
        );

        if (users.length > 0) {
            return {
                ...message,
                sender_avatar_url: users[0].avatar_url
            };
        }
    } catch (error) {
        console.error('Error enriching message with sender avatar:', error);
    }

    // Return original message if enrichment fails
    return message;
}

// Mount API routes with correct paths
// Public routes (no authentication required)
app.use('/auth', authRoutes);

// Temporary bypass middleware for transition - NO AUTHENTICATION REQUIRED
const bypassAuth = (req, res, next) => {
    // Set a default user if none exists
    if (!req.user) {
        // Extract userId from various sources
        const userId = req.query.userId || req.body.userId || req.params.userId || req.params.id;
        const pathUserId = req.path.match(/\/(\d+)$/)?.[1];
        const finalUserId = userId || pathUserId;

        if (finalUserId) {
            const parsedUserId = parseInt(finalUserId);
            if (!isNaN(parsedUserId) && parsedUserId > 0) {
                req.user = {
                    id: parsedUserId,
                    userId: parsedUserId,
                    isLegacy: true
                };
            }
        }
    }
    next();
};

// Protected routes with bypass authentication (temporary for transition)
app.use('/profile', bypassAuth, profileRoutes);
app.use('/matches', bypassAuth, matchesRoutes);
app.use('/chat', bypassAuth, messageLimiter, chatRoutes);
app.use('/swipe', bypassAuth, swipeLimiter, swipeRoutes);
app.use('/uploads', bypassAuth, uploadRoutes);
app.use('/notifications', bypassAuth, notificationRoutes);
app.use('/community', bypassAuth, communityRoutes);
app.use('/user', bypassAuth, userRouter);
app.use('/community/stories', bypassAuth, communityStoriesRoutes);
app.use('/chat-rooms', bypassAuth, messageLimiter, chatRoomsRoutes);
app.use('/blind-date', bypassAuth, blindDateRoutes);
app.use('/settings', bypassAuth, settingsRoutes);
app.use('/direct-messages', bypassAuth, messageLimiter, directMessagesRoutes);
app.use('/radar', bypassAuth, radarRoutes);
app.use('/xp', bypassAuth, experienceRoutes);
app.use('/videos', bypassAuth, videosRoutes);
app.use('/verification', bypassAuth, verificationRoutes);

// Admin routes (require admin privileges with backward compatibility)
app.use('/admin', verifyAdmin, adminLimiter, adminRoutes);

// New endpoint for uploading story media
app.post('/uploads/community-story-media', storyMediaUpload.single('media'), (req, res) => {
    console.log("Uploads: /uploads/community-story-media hit"); // Simplified logging for backend
    if (!req.file) {
        console.error("Uploads: No media file uploaded or invalid file type for story.");
        return res.status(400).json({ success: false, message: 'No media file uploaded or invalid file type.' });
    }
    const mediaType = req.body.mediaType; // Sent from client as form data
    if (!mediaType || (mediaType !== 'image' && mediaType !== 'video')) {
        console.error(`Uploads: Invalid or missing mediaType for story: ${mediaType}. Deleting file: ${req.file.path}`);
        // fs.unlinkSync(req.file.path); // Consider enabling this to clean up orphaned files
        return res.status(400).json({ success: false, message: 'Invalid or missing mediaType. Must be image or video.' });
    }

    console.log(`Uploads: Uploaded story media: ${req.file.filename}, Type: ${mediaType}`);
    const fileUrl = `/uploads/${req.file.filename}`; // Relative URL
    res.json({ success: true, message: 'Story media uploaded successfully', url: fileUrl, mediaTypeFromBackend: mediaType });
}, (error, req, res, next) => { // Error handling middleware for multer
    if (error instanceof multer.MulterError) {
        console.error("Uploads: Multer error in story media upload: ", error);
        return res.status(400).json({ success: false, message: `Upload error: ${error.message}` });
    } else if (error) {
        console.error("Uploads: File filter or other error in story media upload: ", error);
        return res.status(400).json({ success: false, message: error.message });
    }
    next(); // Should not be reached if error occurred, but good practice
});

// Root endpoint
app.get('/', (req, res) => {
  console.log("Root endpoint hit");
  res.json({ success: true, message: 'VMeet API is running' });
});

// Direct voice upload endpoint for debugging
app.post('/direct/voice', voiceUpload.single('audio'), async (req, res) => {
  console.log('Direct voice upload endpoint hit');
  console.log('Request body:', req.body);  // Log the entire request body
  console.log('Request file:', req.file ? {
      filename: req.file.filename,
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
  } : 'No file');  // Log file details
  try {
      if (!req.file) {
          console.log('No audio file found in request');
          return res.status(400).json({ success: false, message: 'No audio file uploaded' });
      }

      // Extract metadata from the request body
      const matchId = req.body.matchId ? parseInt(req.body.matchId) : null;
      const senderId = req.body.senderId ? parseInt(req.body.senderId) : null;
      const receiverId = req.body.receiverId ? parseInt(req.body.receiverId) : null;

      // Handle duration with different possible client-side field names
      const duration = parseInt(req.body.duration || req.body.voiceDuration || req.body.voice_duration || 0) || 0;

      console.log('Voice message metadata:', { matchId, senderId, receiverId, duration });

      // Return the file path to the client
      const filePath = `/uploads/${req.file.filename}`;

      // Add this for clarity - the client can use this path in the voiceFile field
      console.log(`[VOICE_UPLOAD] IMPORTANT: Client should send this path in voiceFile field: ${filePath}`);

      // IMPORTANT: Store the last uploaded file for this sender, even if metadata is incomplete
      if (senderId) {
          console.log(`[VOICE_UPLOAD] Storing latest voice file path for user ${senderId}: ${filePath}`);

          // First, clear any existing entries for this user to prevent confusion
          if (lastUploadedVoiceFiles.has(senderId)) {
              console.log(`[VOICE_UPLOAD] Clearing previous voice file record for user ${senderId}`);
          }

          // Store the new file path with timestamp
          lastUploadedVoiceFiles.set(senderId, {
              filePath: filePath,
              timestamp: new Date(),
              duration: duration || 0
          });

          // Log the full map contents for debugging
          console.log(`[VOICE_UPLOAD] Current voice file map entries: ${Array.from(lastUploadedVoiceFiles.keys()).join(', ')}`);

          // CRITICAL NEW FEATURE: Track uploads in database for reliability
          try {
              const conn = await getConnection();

              // First, create the tracking table if it doesn't exist
              await conn.execute(`
                  CREATE TABLE IF NOT EXISTS file_uploads (
                      upload_id INT AUTO_INCREMENT PRIMARY KEY,
                      uploader_id INT NOT NULL,
                      file_path VARCHAR(255) NOT NULL,
                      original_filename VARCHAR(255),
                      file_type VARCHAR(50),
                      file_size INT,
                      duration INT DEFAULT 0,
                      used BOOLEAN DEFAULT FALSE,
                      upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                  )
              `);

              // Then insert the upload record
              await conn.execute(`
                  INSERT INTO file_uploads
                  (uploader_id, file_path, original_filename, file_type, file_size, duration)
                  VALUES (?, ?, ?, ?, ?, ?)
              `, [
                  senderId,
                  filePath,
                  req.file.originalname,
                  req.file.mimetype.startsWith('audio/') ? 'audio' : 'other',
                  req.file.size,
                  duration || 0
              ]);

              console.log(`[VOICE_UPLOAD] Tracked upload in database: ${filePath}`);
              await conn.release();
          } catch (dbError) {
              console.error(`[VOICE_UPLOAD] Failed to track upload in database: ${dbError.message}`);
              // Non-fatal error, continue processing
          }
      } else {
          console.log(`[VOICE_UPLOAD] Warning: Cannot store voice file path without sender ID`);
      }

      // Store in database if we have the required metadata
      if (matchId && senderId && receiverId) {
          let conn;
          try {
              conn = await getConnection();

              // Insert voice message into database
              const [result] = await conn.execute(`
                  INSERT INTO messages (match_id, sender_id, receiver_id, message_text, voice_file, voice_duration, message_type, \`read\`)
                  VALUES (?, ?, ?, ?, ?, ?, 'voice', 0)
              `, [matchId, senderId, receiverId, 'Mensaje de voz', filePath, duration]);

              const messageId = result.insertId;

              console.log(`Voice message saved to DB with ID: ${messageId}`);

              // Get sender information for notification
              const [senders] = await conn.execute(
                  'SELECT username, avatar_url FROM users WHERE id = ?',
                  [senderId]
              );

              await conn.release();

              // Check if receiver is online and send notification if offline
              const receiverWs = clients.get(receiverId);
              const isReceiverOnline = receiverWs && receiverWs.readyState === WebSocket.OPEN;

              // Log online status
              console.log(`Receiver ${receiverId} is ${isReceiverOnline ? 'online' : 'offline'}`);

              // If user is offline, send a notification
              if (!isReceiverOnline && senders.length > 0) {
                  const senderName = senders[0].username;

                  // Get avatar URL
                  let senderAvatarUrl = null;
                  if (senders[0].avatar_url) {
                      if (senders[0].avatar_url.startsWith('http')) {
                          senderAvatarUrl = senders[0].avatar_url;
                      } else {
                          senderAvatarUrl = `http://*************:3000${senders[0].avatar_url}`;
                      }
                  }

                  console.log(`Sending notification for voice message to offline user ${receiverId}`);

                  // Send notification for voice message
                  notifications.notifyNewMessage(
                      receiverId,
                      senderId,
                      matchId,
                      'Mensaje de voz',
                      senderAvatarUrl
                  ).catch(error => {
                      console.error(`Failed to send voice message notification:`, error);
                  });
              } else if (isReceiverOnline) {
                  console.log(`Forwarding voice message to online user ${receiverId} via WebSocket`);

                  // Create message payload
                  const voicePayload = {
                      id: messageId,
                      matchId: matchId,
                      senderId: senderId,
                      receiverId: receiverId,
                      text: 'Mensaje de voz',
                      voiceFile: filePath,
                      voiceDuration: duration,
                      messageType: 'voice',
                      created_at: new Date().toISOString()
                  };

                  // Try to forward over WebSocket
                  try {
                      receiverWs.send(JSON.stringify({
                          type: 'voice_message',
                          payload: voicePayload
                      }));
                      console.log(`Successfully forwarded voice message via WebSocket`);
                  } catch (wsError) {
                      console.error(`Failed to forward voice message over WebSocket:`, wsError);

                      // Fallback to notification
                      notifications.notifyNewMessage(
                          receiverId,
                          senderId,
                          matchId,
                          'Mensaje de voz'
                      ).catch(error => {
                          console.error(`Failed to send fallback voice notification:`, error);
                      });
                  }
              }

              // Include message ID in response
              res.json({
                  success: true,
                  filePath: filePath,
                  fileName: req.file.filename,
                  messageId: messageId
              });
          } catch (dbError) {
              console.error('DB error saving voice message:', dbError);
              if (conn) await conn.release().catch(e => console.error('Failed to close connection:', e));

              // Still return success for the file upload
              res.json({
                  success: true,
                  filePath: filePath,
                  fileName: req.file.filename,
                  error: 'Failed to save to database: ' + dbError.message
              });
          }
      } else {
          // Just return the file info if we don't have metadata
          console.log('Missing metadata, just returning file path');
          res.json({
              success: true,
              filePath: filePath,
              fileName: req.file.filename
          });
      }
  } catch (error) {
      console.error('Error in direct voice upload:', error);
      res.status(500).json({
          success: false,
          message: 'Error uploading voice message',
          error: error.message
      });
  }
});

// Add messages/upload-image endpoint
app.post('/messages/upload-image', imageUpload.single('image'), async (req, res) => {
  console.log('[IMAGE_MESSAGE] Image message upload endpoint hit');
  console.log('[IMAGE_MESSAGE] Request body:', req.body);
  console.log('[IMAGE_MESSAGE] Request file:', req.file ? {
      filename: req.file.filename,
      originalname: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
  } : 'No file');

  try {
      if (!req.file) {
          console.log('No image file found in request');
          return res.status(400).json({ success: false, message: 'No image file uploaded' });
      }

      // Extract message data from request body
      const senderId = req.body.sender_id ? parseInt(req.body.sender_id) : null;
      const receiverId = req.body.receiver_id ? parseInt(req.body.receiver_id) : null;
      const matchId = req.body.match_id ? parseInt(req.body.match_id) : null;
      const clientMessageId = req.body.clientMessageId || null;

      // Validate required fields
      if (!senderId || !receiverId || !matchId) {
          return res.status(400).json({
              success: false,
              message: 'Missing required fields (sender_id, receiver_id, match_id)'
          });
      }

      // Generate file path for database
      const imageFilePath = `/uploads/${req.file.filename}`;

      // Connect to database
      let conn;
      try {
          conn = await getConnection();

          // Insert the message into the database
          const [result] = await conn.execute(
              'INSERT INTO messages (match_id, sender_id, receiver_id, message_text, message_type, image_file, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
              [matchId, senderId, receiverId, 'Image message', 'image', imageFilePath]
          );

          const messageId = result.insertId;

          // Fetch the saved message to return with complete data
          const [messages] = await conn.execute(
              'SELECT * FROM messages WHERE id = ?',
              [messageId]
          );

          if (messages.length === 0) {
              throw new Error('Failed to retrieve the saved message');
          }

          const savedMessage = messages[0];

          // Close the database connection
          await conn.release();
          console.log(`Successfully processed image message upload. ID: ${messageId}, Image: ${imageFilePath}`);

          // Forward message to the receiver and sender via WebSocket for real-time updates
          if (clients) {
              // Try to get WebSocket connections for sender and receiver
              const receiverWs = clients.get(Number(receiverId));
              const senderWs = clients.get(Number(senderId));

              // Prepare message payload for WebSocket
              const messagePayload = {
                  ...savedMessage,
                  messageType: 'image'
              };

              // Forward to receiver if online
              if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
                  try {
                      receiverWs.send(JSON.stringify({
                          type: 'new_message',
                          payload: messagePayload
                      }));
                      console.log(`Successfully forwarded image message to receiver ${receiverId}`);
                  } catch (wsError) {
                      console.error(`Failed to forward image message to receiver ${receiverId}:`, wsError);
                  }
              } else {
                  console.log(`Receiver ${receiverId} is offline or has no active WebSocket connection`);
              }

              // Confirm to sender if different from receiver
              if (senderWs && senderWs.readyState === WebSocket.OPEN && senderId !== receiverId) {
                  try {
                      senderWs.send(JSON.stringify({
                          type: 'message_sent',
                          payload: messagePayload
                      }));
                      console.log(`Successfully confirmed image message to sender ${senderId}`);
                  } catch (wsError) {
                      console.error(`Failed to confirm image message to sender ${senderId}:`, wsError);
                  }
              }
          }

          // Also send push notification for the message
          try {
              // Only send notification if receiver is offline or notification failed via WebSocket
              const receiverWs = clients.get(Number(receiverId));
              const receiverIsOffline = !receiverWs || receiverWs.readyState !== WebSocket.OPEN;

              if (receiverIsOffline) {
                  console.log(`Sending push notification for image message to offline user ${receiverId}`);

                  notifications.notifyNewMessage(
                      receiverId,
                      senderId,
                      matchId,
                      'Image message', // Text preview
                      'image' // Message type
                  ).catch(error => {
                      console.error('Error sending image message notification:', error);
                  });
              } else {
                  console.log(`Skipping push notification for image message as user ${receiverId} is online`);
              }
          } catch (notifError) {
              console.error('Error in notification process for image message:', notifError);
          }

          // Return successful response with the new message
          res.json({
              success: true,
              id: messageId,
              image_file: imageFilePath,
              message: savedMessage
          });
      } catch (dbError) {
          console.error('Database error when saving image message:', dbError);
          if (conn) {
    if (typeof conn.release === 'function') {
        await conn.release().catch(() => {});
    } else if (typeof conn.end === 'function') {
        await conn.end().catch(() => {});
    }
}

          return res.status(500).json({
              success: false,
              message: 'Error saving image message to database',
              error: dbError.message
          });
      }
  } catch (error) {
      console.error('General error handling image message upload:', error);
      return res.status(500).json({
          success: false,
          message: 'Error processing image message',
          error: error.message
      });
  }
});

// Comment out the mock authentication routes since we're using the real ones now
/*
app.post('/auth/login', (req, res) => {
  res.json({
    success: true,
    userId: 1, // Add explicit userId property at top level
    user: {
      id: 1,
      username: "TestUser",
      email: req.body.email || "<EMAIL>",
      avatar_url: null
    }
  });
});

app.post('/auth/register', (req, res) => {
  // Increment the counter for new registrations
  userIdCounter++;
  const newUserId = userIdCounter;

  console.log(`Registered new user with ID: ${newUserId}, username: ${req.body.username}`);

  res.json({
    success: true,
    message: 'Usuario registrado correctamente',
    userId: newUserId,
    user: {
      id: newUserId,
      username: req.body.username || "NewUser",
      email: req.body.email || "<EMAIL>",
      avatar_url: null
    }
  });
});
*/

// User discovery
app.get('/users/discover/:userId', async (req, res) => { // Make async
    const userId = parseInt(req.params.userId);
    if (isNaN(userId)) {
        return res.status(400).json({ success: false, message: 'Invalid User ID' });
    }

    let conn;
    try {
        conn = await getConnection();
        console.log(`Discover request for user ID: ${userId}`);

        const [rows] = await conn.execute(`
            SELECT u.id, u.username, u.avatar_url, u.bio, u.favorite_game, u.birthdate, u.last_seen
            FROM users u
            WHERE u.id != ?
            AND u.id NOT IN (
                SELECT m.user_id_2
                FROM matches m
                WHERE m.user_id_1 = ?
            )
            ORDER BY RAND()
            LIMIT 20
        `, [userId, userId]);

        const usersWithFullAvatarUrl = rows.map(user => {
            // Check if user is online
            let isOnline = false;
            const presence = userPresence.get(user.id.toString());
            if (presence && presence.online) {
                isOnline = true;
            }

            return {
                ...user,
                avatar_url: user.avatar_url ? `/uploads/${path.basename(user.avatar_url)}` : null,
                is_online: isOnline,
                last_seen: user.last_seen ? user.last_seen.toISOString() : null
            };
        });

        await conn.release();
        res.json({ success: true, users: usersWithFullAvatarUrl });

    } catch (error) {
        console.error("Error in /users/discover/:userId:", error);
        if (conn) await conn.release().catch(e => console.error('Failed to close connection:', e));
        // Send empty array to prevent client crash, but indicate error
        res.status(500).json({ success: false, message: 'Error fetching users from database', users: [] });
    }
});

// Match routes
app.get('/matches/:userId', async (req, res) => { // Make async
    const userId = parseInt(req.params.userId);
    if (isNaN(userId) || userId <= 0) {
        return res.status(400).json({ success: false, message: 'Invalid User ID' });
    }

    let conn;
    try {
        conn = await getConnection();
        console.log(`Getting matches for user ID: ${userId}`);

        // This query retrieves pairs where the current user is either user_id_1 or user_id_2
        // and the match is confirmed (matched = 1).
        // It then joins with the users table to get the other user's details.
        const [rows] = await conn.execute(`
            SELECT
                m.id AS match_id,
                m.created_at AS match_created_at,
                u.id AS other_user_id,
                u.username AS other_username,
                u.avatar_url AS other_avatar_url
            FROM matches m
            JOIN users u ON (m.user_id_1 = u.id AND m.user_id_2 = ?) OR (m.user_id_2 = u.id AND m.user_id_1 = ?)
            WHERE m.matched = 1
            AND u.id != ? -- Ensure we select the OTHER user in the pair
            ORDER BY m.created_at DESC
        `, [userId, userId, userId]);

        const matches = rows.map(row => ({
            id: row.match_id, // This is the match ID
            user_id_1: (row.other_user_id === userId) ? row.other_user_id : userId, // Correctly assign user_id_1
            user_id_2: (row.other_user_id !== userId) ? row.other_user_id : userId, // Correctly assign user_id_2
            created_at: row.match_created_at,
            // The user object in the MatchItem is the *other* user
            otherUser: {
                id: row.other_user_id,
                username: row.other_username,
                avatar_url: row.other_avatar_url ? `/uploads/${path.basename(row.other_avatar_url)}` : null
            }
        }));

        // Deduplicate matches if the query structure might return two rows per match (A-B and B-A)
        // The current query should ideally return one row per match where u.id is the other user.
        // However, a simple deduplication can be added if needed based on other_user_id.
        const uniqueMatches = [];
        const seenOtherUserIds = new Set();
        for (const match of matches) {
            if (!seenOtherUserIds.has(match.otherUser.id)) {
                uniqueMatches.push(match);
                seenOtherUserIds.add(match.otherUser.id);
            }
        }

        await conn.release();
        res.json({ success: true, matches: uniqueMatches });

    } catch (error) {
        console.error("Error in GET /matches/:userId:", error);
        if (conn) await conn.release().catch(e => console.error('Connection close failed:', e));
        res.status(500).json({ success: false, message: 'Error fetching matches' });
    }
});

// Chat routes
app.get('/chat/:matchId/messages', async (req, res) => { // Make async
    const matchId = parseInt(req.params.matchId);

    if (isNaN(matchId) || matchId <= 0) {
        return res.status(400).json({ success: false, message: 'Invalid Match ID' });
    }

    let conn;
    try {
        conn = await getConnection();
        console.log(`Fetching messages for match ID: ${matchId}`);

        const [rows] = await conn.execute(`
            SELECT id, match_id, sender_id, receiver_id, message_text, created_at, \`read\`
            FROM messages
            WHERE match_id = ?
            ORDER BY created_at ASC
        `, [matchId]);

        await conn.release();

        // Convert timestamps if necessary, although MySQL often returns them in a usable format
        const messages = rows.map(row => {
            // Log raw read value for debugging
            console.log(`Message ${row.id} raw read value:`, row.read, 'type:', typeof row.read);

            return {
                ...row,
                // Ensure timestamp format is consistent if needed, e.g., ISO string
                created_at: row.created_at instanceof Date ? row.created_at.toISOString() : row.created_at,
                // Convert read status to boolean for client - handle different possible formats
                isRead: row.read === 1 || row.read === true || row.read === '1'
            };
        });

        res.json({ success: true, messages: messages });

    } catch (error) {
        console.error(`Error fetching messages for match ${matchId}:`, error);
        if (conn) await conn.release().catch(e => console.error('Connection close failed:', e));
        res.status(500).json({ success: false, message: 'Error fetching chat history' });
    }
});

app.get('/chat/:matchId/last-message', (req, res) => {
  const message = { id: 302, match_id: parseInt(req.params.matchId), sender_id: 101, message_text: "¿Qué juegos te gustan?", created_at: new Date().toISOString() };
  res.json({ success: true, message: message });
});

app.post('/chat/:matchId/message', (req, res) => {
  res.status(201).json({
    success: true,
    message: { id: 999, match_id: parseInt(req.params.matchId), sender_id: req.body.sender_id || 1, message_text: req.body.message_text || "Mensaje de ejemplo", created_at: new Date().toISOString() }
  });
});

// Profile routes
/*
app.get('/profile/:id', async (req, res) => { // Make async
    const userId = parseInt(req.params.id);
    if (isNaN(userId) || userId <= 0) {
        return res.status(400).json({ success: false, message: 'Invalid User ID' });
    }

    let conn;
    try {
        conn = await getConnection();
        console.log(`Getting profile for user ID: ${userId}`);

        const [rows] = await conn.execute(`
            SELECT id, username, email, avatar_url, bio, favorite_game,
                   favorite_platform, location, gender, created_at,
                   pronouns, job_title, school, looking_for, birthdate
            FROM users
            WHERE id = ?
        `, [userId]);

        await conn.release();

        if (rows.length > 0) {
            const user = rows[0];
            // Ensure avatar URL is correctly prefixed
            user.avatar_url = user.avatar_url ? `/uploads/${path.basename(user.avatar_url)}` : null;
            res.json({ success: true, user: user });
        } else {
            res.status(404).json({ success: false, message: 'User not found' });
        }

    } catch (error) {
        console.error("Error in GET /profile/:id:", error);
        if (conn) await conn.release().catch(e => console.error('Connection close failed:', e));
        res.status(500).json({ success: false, message: 'Error fetching profile' });
    }
});
*/

// --- Profile Update Route --- START ---
/*
app.put('/profile/:id', async (req, res) => {
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
        return res.status(400).json({ success: false, message: 'Invalid User ID' });
    }

    // Destructure all possible fields from the request body
    const {
        avatar_url,
        bio,
        favorite_game,
        favorite_platform,
        location,
        birthdate,
        gender,
        // New fields
        pronouns,
        job_title,
        school,
        looking_for
    } = req.body;

    console.log(`PUT /profile/${userId} received:`, req.body);

    // Prepare data for SQL update
    const fieldsToUpdate = {};
    const values = [];

    // Add fields to update only if they are provided in the request
    // Handle potential null/empty values appropriately for the database
    if (avatar_url !== undefined) { fieldsToUpdate.avatar_url = avatar_url; }
    if (bio !== undefined) { fieldsToUpdate.bio = bio; }
    if (favorite_game !== undefined) { fieldsToUpdate.favorite_game = favorite_game; }
    if (favorite_platform !== undefined) { fieldsToUpdate.favorite_platform = favorite_platform; }
    if (location !== undefined) { fieldsToUpdate.location = location; }
    if (gender !== undefined) { fieldsToUpdate.gender = gender; }
    if (pronouns !== undefined) { fieldsToUpdate.pronouns = pronouns; }
    if (job_title !== undefined) { fieldsToUpdate.job_title = job_title; }
    if (school !== undefined) { fieldsToUpdate.school = school; }
    if (looking_for !== undefined) { fieldsToUpdate.looking_for = looking_for; }

    // Handle birthdate explicitly: empty string from client should become NULL in DB
    if (birthdate !== undefined) {
        fieldsToUpdate.birthdate = (birthdate === '' || birthdate === null) ? null : birthdate;
    }

    const fieldEntries = Object.entries(fieldsToUpdate);

    if (fieldEntries.length === 0) {
        console.log('No fields provided to update.');
        // Maybe return the current profile data?
        // For now, just indicate success but nothing changed.
        return res.json({ success: true, message: 'No update data provided.' });
    }

    // Construct dynamic SET clause
    const setClause = fieldEntries.map(([key]) => `${key} = ?`).join(', ');
    const queryValues = fieldEntries.map(([, value]) => value);
    queryValues.push(userId); // Add userId for the WHERE clause

    const sql = `UPDATE users SET ${setClause} WHERE id = ?`;

    console.log('Executing SQL:', sql);
    console.log('With values:', queryValues);

    let conn;
    try {
        conn = await getConnection();
        const [result] = await conn.execute(sql, queryValues);
        await conn.release();

        if (result.affectedRows > 0) {
            console.log(`Profile for user ${userId} updated successfully.`);
            // Optionally, fetch and return the updated profile data
            res.json({ success: true, message: 'Perfil actualizado correctamente.' });
        } else {
            console.log(`Profile update failed for user ${userId} (User not found?).`);
            res.status(404).json({ success: false, message: 'Usuario no encontrado para actualizar.' });
        }

    } catch (error) {
        console.error("Error updating profile:", error);
        if (conn) await conn.release().catch(e => console.error('Failed to close connection:', e));

        // Check for specific SQL errors
        if (error.code === 'ER_PARSE_ERROR') {
             res.status(400).json({ success: false, message: `Error de sintaxis SQL: ${error.message}` });
        } else if (error.code === 'ER_TRUNCATED_WRONG_VALUE_FOR_FIELD' || error.message.includes('Incorrect date value')) {
             res.status(400).json({ success: false, message: `Valor inválido para campo (posiblemente fecha): ${error.message}` });
        } else {
             res.status(500).json({ success: false, message: 'Error al actualizar el perfil en la base de datos.' });
        }
    }
});
*/
// --- Profile Update Route --- END ---

// --- Avatar Upload Route --- START ---
// Uses the 'upload' middleware configured above to handle a single file upload
// expected in a form field named 'avatar'
app.post('/uploads/avatar', imageUpload.single('avatar'), (req, res) => {
    if (!req.file) {
        // No file was uploaded or file was rejected by filter
        return res.status(400).json({ success: false, message: 'No file uploaded or invalid file type.' });
    }

    // File was successfully uploaded
    console.log('Uploaded file:', req.file);

    // Construct the relative URL path for the client
    const fileUrl = `/uploads/${req.file.filename}`;

    // Respond with the URL of the uploaded file
    // The client will need to save this URL in the user's profile (e.g., via a subsequent PUT /profile/:id request)
    res.json({ success: true, message: 'Avatar uploaded successfully', url: fileUrl });
}, (error, req, res, next) => {
    // Catch errors from multer (e.g., file size limit)
    if (error instanceof multer.MulterError) {
        console.error("Multer error:", error);
        return res.status(400).json({ success: false, message: `Multer error: ${error.message}` });
    } else if (error) {
        // Catch errors from fileFilter
        console.error("File filter error:", error);
        return res.status(400).json({ success: false, message: error.message });
    }
    // If no error, continue
    next();
});
// --- Avatar Upload Route --- END ---

// Add endpoint to get user presence status - add near user routes
app.get('/user/status/:userId', async (req, res) => {
    const userId = parseInt(req.params.userId);

    if (isNaN(userId)) {
        return res.status(400).json({ success: false, message: 'Invalid User ID' });
    }

    try {
        // First check in-memory presence map
        const presence = userPresence.get(userId);

        // If user is online according to in-memory map
        if (presence && presence.online) {
            console.log(`User ${userId} is currently online`);
            return res.json({
                success: true,
                online: true,
                lastSeen: presence.lastSeen
            });
        }

        // If not online, check the database for last seen time
        let conn;
        try {
            conn = await getConnection();
            const [rows] = await conn.execute(
                'SELECT last_seen FROM users WHERE id = ?',
                [userId]
            );

            let lastSeen = null;
            if (rows.length > 0 && rows[0].last_seen) {
                lastSeen = rows[0].last_seen;
                console.log(`User ${userId} last seen at ${lastSeen}`);
            } else {
                console.log(`No last_seen record for user ${userId}`);
            }

            await conn.release();

            return res.json({
                success: true,
                online: false,
                lastSeen: lastSeen ? new Date(lastSeen).toISOString() : null
            });
        } catch (error) {
            console.error(`Failed to get last_seen for user ${userId}:`, error);
            if (conn) await conn.release().catch(e => console.error('Connection close failed:', e));

            // If we can't get the database record, return whatever we have in memory
            // or just offline with null lastSeen
            return res.json({
                success: true,
                online: false,
                lastSeen: (presence && presence.lastSeen) || null
            });
        }
    } catch (error) {
        console.error(`Error in /user/status/${userId}:`, error);
        return res.status(500).json({ success: false, message: 'Internal server error' });
    }
});

// Catch-all route (Keep commented out for now)
/*
app.use('*', (req, res) => {
  res.json({ success: true, message: 'Endpoint not implemented yet' });
});
*/

// --- Message Reactions Endpoints --- START ---
// Add reaction to a message
app.post('/message/:messageId/reaction', async (req, res) => {
    const messageId = parseInt(req.params.messageId);
    const { userId, reactionType } = req.body;

    if (!messageId || isNaN(messageId) || !userId || !reactionType) {
        return res.status(400).json({
            success: false,
            message: 'Missing required fields: messageId, userId, reactionType'
        });
    }

    console.log(`Adding reaction ${reactionType} from user ${userId} to message ${messageId}`);

    let conn;
    try {
        conn = await getConnection();

        // First, ensure the message exists
        const [messages] = await conn.execute(
            'SELECT * FROM messages WHERE id = ?',
            [messageId]
        );

        if (messages.length === 0) {
            await conn.release();
            return res.status(404).json({
                success: false,
                message: 'Message not found'
            });
        }

        const message = messages[0];

        // Check if this user already has a reaction of this type on this message
        const [existingReactions] = await conn.execute(
            'SELECT * FROM message_reactions WHERE message_id = ? AND user_id = ?',
            [messageId, userId]
        );

        if (existingReactions.length > 0) {
            await conn.release();
            return res.status(200).json({
                success: true,
                message: 'Reaction already exists',
                reaction: existingReactions[0]
            });
        }

        // Add the reaction
        const [result] = await conn.execute(
            'INSERT INTO message_reactions (message_id, user_id, reaction, created_at) VALUES (?, ?, ?, NOW())',
            [messageId, userId, reactionType]
        );

        const reactionId = result.insertId;

        // Get the complete reaction data
        const [insertedReactions] = await conn.execute(
            'SELECT * FROM message_reactions WHERE id = ?',
            [reactionId]
        );

        const reaction = insertedReactions[0];

        // Get user info for the reaction
        const [users] = await conn.execute(
            'SELECT id, username, avatar_url FROM users WHERE id = ?',
            [userId]
        );

        const userInfo = users.length > 0 ? users[0] : null;

        // Broadcast the reaction via WebSocket
        const reactionPayload = {
            id: reaction.id,
            messageId: messageId,
            userId: userId,
            reactionType: reactionType,
            created_at: reaction.created_at,
            user: userInfo
        };

        // Send to message sender and receiver
        const senderWs = clients.get(message.sender_id);
        const receiverWs = clients.get(message.receiver_id);

        if (senderWs && senderWs.readyState === WebSocket.OPEN) {
            senderWs.send(JSON.stringify({
                type: 'message_reaction_added',
                payload: reactionPayload
            }));
        }

        if (receiverWs && receiverWs.readyState === WebSocket.OPEN && message.receiver_id !== message.sender_id) {
            receiverWs.send(JSON.stringify({
                type: 'message_reaction_added',
                payload: reactionPayload
            }));
        }

        await conn.release();

        res.status(201).json({
            success: true,
            message: 'Reaction added successfully',
            reaction: {
                ...reaction,
                user: userInfo
            }
        });
    } catch (error) {
        console.error('Error adding reaction:', error);
        if (conn) await conn.release().catch(e => console.error('Failed to close connection:', e));
        res.status(500).json({
            success: false,
            message: 'Error adding reaction'
        });
    }
});

// Get reactions for a message
app.get('/message/:messageId/reactions', async (req, res) => {
    const messageId = parseInt(req.params.messageId);

    if (!messageId || isNaN(messageId)) {
        return res.status(400).json({
            success: false,
            message: 'Invalid message ID'
        });
    }

    let conn;
    try {
        conn = await getConnection();

        // Get all reactions for this message with user info
        const [rows] = await conn.execute(`
            SELECT r.*, u.username, u.avatar_url
            FROM message_reactions r
            JOIN users u ON r.user_id = u.id
            WHERE r.message_id = ?
            ORDER BY r.created_at ASC
        `, [messageId]);

        await conn.release();

        // Format the reactions
        const reactions = rows.map(row => ({
            id: row.id,
            messageId: row.message_id,
            userId: row.user_id,
            reactionType: row.reaction_type,
            created_at: row.created_at,
            user: {
                id: row.user_id,
                username: row.username,
                avatar_url: row.avatar_url
            }
        }));

        res.json({
            success: true,
            reactions: reactions
        });
    } catch (error) {
        console.error('Error getting reactions:', error);
        if (conn) await conn.release().catch(e => console.error('Failed to close connection:', e));
        res.status(500).json({
            success: false,
            message: 'Error retrieving reactions'
        });
    }
});

// Remove a reaction from a message
app.delete('/message/:messageId/reaction', async (req, res) => {
    const messageId = parseInt(req.params.messageId);
    const { userId, reactionType } = req.body;

    if (!messageId || isNaN(messageId) || !userId || !reactionType) {
        return res.status(400).json({
            success: false,
            message: 'Missing required fields: messageId, userId, reactionType'
        });
    }

    let conn;
    try {
        conn = await getConnection();

        // First, ensure the message exists
        const [messages] = await conn.execute(
            'SELECT * FROM messages WHERE id = ?',
            [messageId]
        );

        if (messages.length === 0) {
            await conn.release();
            return res.status(404).json({
                success: false,
                message: 'Message not found'
            });
        }

        const message = messages[0];

        // Find the reaction
        const [reactions] = await conn.execute(
            'SELECT * FROM message_reactions WHERE message_id = ? AND user_id = ? AND reaction_type = ?',
            [messageId, userId, reactionType]
        );

        if (reactions.length === 0) {
            await conn.release();
            return res.status(404).json({
                success: false,
                message: 'Reaction not found'
            });
        }

        const reaction = reactions[0];

        // Delete the reaction
        await conn.execute(
            'DELETE FROM message_reactions WHERE message_id = ? AND user_id = ? AND reaction_type = ?',
            [messageId, userId, reactionType]
        );

        // Broadcast the reaction removal via WebSocket
        const removalPayload = {
            messageId: messageId,
            userId: userId,
            reactionType: reactionType
        };

        // Send to message sender and receiver
        const senderWs = clients.get(message.sender_id);
        const receiverWs = clients.get(message.receiver_id);

        if (senderWs && senderWs.readyState === WebSocket.OPEN) {
            senderWs.send(JSON.stringify({
                type: 'message_reaction_removed',
                payload: removalPayload
            }));
        }

        if (receiverWs && receiverWs.readyState === WebSocket.OPEN && message.receiver_id !== message.sender_id) {
            receiverWs.send(JSON.stringify({
                type: 'message_reaction_removed',
                payload: removalPayload
            }));
        }

        await conn.release();

        res.json({
            success: true,
            message: 'Reaction removed successfully'
        });
    } catch (error) {
        console.error('Error removing reaction:', error);
        if (conn) await conn.release().catch(e => console.error('Failed to close connection:', e));
        res.status(500).json({
            success: false,
            message: 'Error removing reaction'
        });
    }
});
// --- Message Reactions Endpoints --- END ---

// Start server
const PORT = process.env.PORT || 3000;

// Create HTTP server from Express app
const server = http.createServer(app);

// Create WebSocket server attached to the HTTP server
const wss = new WebSocket.Server({ server });

// Store connected clients (mapping userId to WebSocket object)
const clients = new Map();

// Export clients map so other modules can access it
module.exports.clients = clients;

// Add a function to get the clients map
module.exports.getClients = function() {
    return clients;
};

// Make WebSocket server globally accessible for the notifications module
global.websocketServer = { wss, clients };

// Set the clients map in the blind date handler
blindDateHandler.setClients(clients);

// Add a heartbeat interval to keep connections alive
const HEARTBEAT_INTERVAL = 30000; // 30 seconds

wss.on('connection', (ws) => {
    console.log('Client connected via WebSocket');
    let userId = null; // Store the user ID for this connection

    // Setup ping for this connection
    let pingTimeout = null;

    function heartbeat() {
        clearTimeout(pingTimeout);

        // Terminate connection if no pong response in 35 seconds
        pingTimeout = setTimeout(() => {
            console.log(`Terminating stale connection for user ${userId || 'unknown'}`);
            if (userId) {
                clients.delete(userId);

                // Mark as offline
                if (userPresence.has(userId)) {
                    const currentTime = new Date().toISOString();
                    userPresence.set(userId, {
                        online: false,
                        lastSeen: currentTime
                    });
                    updateUserLastSeen(userId);
                }
            }
            ws.terminate();
        }, 35000);
    }

    // Set initial heartbeat
    heartbeat();

    // Handle pong responses
    ws.on('pong', () => {
        console.log(`Received pong from user ${ws.userId || 'unknown'}`);
        heartbeat();
    });

    // Handle ping messages - IMPORTANT: Respond with pong
    ws.on('ping', (data) => {
        const pingSource = ws.userId ? `user ${ws.userId}` : 'unknown client';
        console.log(`[PING RECEIVED] from ${pingSource}. Data length: ${data ? data.length : 'N/A'}`);
        try {
            ws.pong(data);
            console.log(`[PONG SENT] to ${pingSource}`);
        } catch (e) {
            console.error(`[PONG ERROR] for ${pingSource}:`, e);
        }
    });

    ws.on('message', async (message) => {
        // Reset heartbeat on any message
        heartbeat();

        let data;
        try {
            // Attempt to parse the message as a string first
            const messageString = message.toString();
            data = JSON.parse(messageString);
            console.log('Received WS message:', data);
        } catch (e) {
            console.error('Failed to parse WS message or invalid format:', message);
            // Check if ws is still open before sending
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'error', message: 'Invalid JSON format.' }));
            }
            return;
        }

        switch (data.type) {
            case 'identify':
                userId = parseInt(data.userId);
                if (!isNaN(userId) && userId > 0) {
                    // Store userId directly on the WebSocket object for later access
                    ws.userId = userId;
                    ws.lastActivity = Date.now();

                    // If the user already has a connection, close it
                    const existingConnection = clients.get(userId);
                    if (existingConnection && existingConnection !== ws) {
                        console.log(`User ${userId} has existing connection, closing it`);
                        try {
                            // Attempt to gracefully close the existing connection
                            existingConnection.close();
                        } catch (e) {
                            console.error(`Error closing existing connection for user ${userId}:`, e.message);
                            // Try to forcefully terminate if close fails
                            try {
                                existingConnection.terminate();
                            } catch (termError) {
                                console.error(`Error terminating existing connection for user ${userId}:`, termError.message);
                            }
                        }
                    }

                    // Store any active chat ID if provided
                    const activeMatchId = data.activeMatchId ? parseInt(data.activeMatchId) : null;
                    if (activeMatchId && !isNaN(activeMatchId)) {
                        ws.currentMatchId = activeMatchId;
                        console.log(`User ${userId} is active in chat ${activeMatchId}`);
                    }

                    // Store any active room ID if provided
                    const activeRoomId = data.activeRoomId ? parseInt(data.activeRoomId) : null;
                    if (activeRoomId && !isNaN(activeRoomId)) {
                        ws.currentRoomId = activeRoomId;
                        console.log(`User ${userId} is active in room ${activeRoomId}`);
                    }

                    // IMPORTANT - Set this connection in the clients map
                    clients.set(userId, ws);
                    console.log(`WebSocket client identified as user ${userId}`);
                    console.log(`Total active connections: ${clients.size}, Users: ${Array.from(clients.keys()).join(', ')}`);

                    // Set user as online and update lastSeen
                    const currentTime = new Date().toISOString();
                    console.log(`Marking user ${userId} as online at ${currentTime}`);

                    userPresence.set(userId, {
                        online: true,
                        lastSeen: currentTime
                    });

                    // Update database with last seen timestamp
                    updateUserLastSeen(userId);

                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'identified', message: `Identified as user ${userId}` }));
                    }
                } else {
                    console.error('Invalid user ID for identification:', data.userId);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid user ID for identification.' }));
                        ws.close(); // Close connection if identification fails
                    }
                }
                break;

            case 'chat_message':
                if (!userId) {
                     if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                     }
                    return;
                }

                const { matchId, senderId, receiverId, text } = data.payload;

                // --- BEGIN DIAGNOSTIC LOGGING FOR 'Mensaje de voz' IN CHAT_MESSAGE HANDLER ---
                if (text === "Mensaje de voz") {
                    console.log(`[CHAT_MESSAGE_HANDLER] Received message with text: "Mensaje de voz"`);
                    console.log(`[CHAT_MESSAGE_HANDLER] Full payload for this message:`, JSON.stringify(data.payload, null, 2));
                }
                // --- END DIAGNOSTIC LOGGING ---

                const parsedMatchId = parseInt(matchId);
                const parsedSenderId = parseInt(senderId);
                const parsedReceiverId = parseInt(receiverId);

                // Add detailed logging for reply data
                console.log('Chat message complete payload:', JSON.stringify(data.payload));
                if (data.payload.reply_to_id || data.payload.reply_to_text || data.payload.reply_to_sender_id) {
                    console.log('REPLY DATA DETECTED:');
                    console.log('- reply_to_id:', data.payload.reply_to_id);
                    console.log('- reply_to_text:', data.payload.reply_to_text);
                    console.log('- reply_to_sender_id:', data.payload.reply_to_sender_id);
                }

                // Basic validation
                if (!parsedMatchId || isNaN(parsedMatchId) ||
                    !parsedSenderId || isNaN(parsedSenderId) ||
                    !parsedReceiverId || isNaN(parsedReceiverId) ||
                    !text || parsedSenderId !== userId) {
                     console.error('Invalid chat message payload or sender mismatch:', data.payload);
                     if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid chat message payload or sender mismatch.' }));
                     }
                     return;
                }

                console.log(`Chat message from ${parsedSenderId} to ${parsedReceiverId} in match ${parsedMatchId}: ${text}`);

                // Store message in the database
                let conn;
                try {
                    conn = await getConnection();

                    // Check if this is a reply message
                    if (data.payload.reply_to_id || data.payload.reply_to_text || data.payload.reply_to_sender_id) {
                        // Convert reply_to_id to integer if it's a string
                        let replyToId = data.payload.reply_to_id;
                        if (typeof replyToId === 'string') {
                            replyToId = parseInt(replyToId);
                            console.log(`Converted reply_to_id from string to integer: ${replyToId}`);
                        }

                        const replyToText = data.payload.reply_to_text || null;

                        // Convert reply_to_sender_id to integer if it's a string
                        let replyToSenderId = data.payload.reply_to_sender_id;
                        if (typeof replyToSenderId === 'string') {
                            replyToSenderId = parseInt(replyToSenderId);
                            console.log(`Converted reply_to_sender_id from string to integer: ${replyToSenderId}`);
                        }

                        console.log(`Message is a reply to message ${replyToId}`);

                        const [result] = await conn.execute(`
                            INSERT INTO messages (match_id, sender_id, receiver_id, message_text, created_at,
                                                reply_to_id, reply_to_text, reply_to_sender_id, \`read\`)
                            VALUES (?, ?, ?, ?, NOW(), ?, ?, ?, 0)
                        `, [parsedMatchId, parsedSenderId, parsedReceiverId, text,
                            replyToId, replyToText, replyToSenderId]);

                        const insertedMessageId = result.insertId;
                        console.log(`Reply message saved to DB with ID: ${insertedMessageId}`);
                    } else {
                        // Regular message without reply data
                        const [result] = await conn.execute(`
                            INSERT INTO messages (match_id, sender_id, receiver_id, message_text, created_at, \`read\`)
                            VALUES (?, ?, ?, ?, NOW(), 0)
                        `, [parsedMatchId, parsedSenderId, parsedReceiverId, text]);

                        const insertedMessageId = result.insertId;
                        console.log(`Message saved to DB with ID: ${insertedMessageId}`);
                    }

                    // Get the inserted message with all its data
                    const [messages] = await conn.execute(`
                        SELECT * FROM messages WHERE id = LAST_INSERT_ID()
                    `);

                    await conn.release();

                    if (messages.length > 0) {
                        const savedMessage = messages[0];

                        // Log the saved message to see if reply data was stored
                        console.log('Saved message from DB:', {
                            id: savedMessage.id,
                            reply_to_id: savedMessage.reply_to_id,
                            reply_to_text: savedMessage.reply_to_text,
                            reply_to_sender_id: savedMessage.reply_to_sender_id
                        });

                        // Send confirmation with full message data back to sender
                        const confirmationPayload = {
                            ...data.payload,
                            id: savedMessage.id,
                            created_at: savedMessage.created_at,
                            reply_to_id: savedMessage.reply_to_id,
                            reply_to_text: savedMessage.reply_to_text,
                            reply_to_sender_id: savedMessage.reply_to_sender_id,
                            read: 0,  // IMPORTANT: Always set read=0 for sent message confirmations
                            isRead: false  // Also set isRead=false for clients expecting this property
                        };

                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'chat_message', // Use chat_message instead of message_saved for better compatibility
                                payload: confirmationPayload
                            }));
                        }

                        // *** CRITICAL SECTION FOR REAL-TIME MESSAGING ***
                        // Forward to receiver with enhanced reliability
                        let receiverWs = null;

                        // Try to get receiver's websocket connection - if not found, check a second time after a forced reconnect attempt
                        for (let attempt = 1; attempt <= 2; attempt++) {
                            receiverWs = clients.get(parsedReceiverId);
                            const receiverPresence = userPresence.get(parsedReceiverId);
                            const receiverIsMarkedOnline = receiverPresence && receiverPresence.online;

                            console.log(`[Attempt ${attempt}] Receiver ${parsedReceiverId}: ` +
                                `has connection? ${!!receiverWs}, ` +
                                `state: ${receiverWs ? receiverWs.readyState : 'N/A'}, ` +
                                `marked online? ${receiverIsMarkedOnline}`);

                            // If we found a good connection on first attempt, stop trying
                            if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
                                break;
                            }

                            // If this is our first attempt and the user appears online but connection isn't valid,
                            // log the issue but continue to attempt delivery
                            if (attempt === 1 && receiverIsMarkedOnline &&
                                (!receiverWs || receiverWs.readyState !== WebSocket.OPEN)) {

                                console.log(`Critical: User ${parsedReceiverId} marked online but has no valid WebSocket!`);
                                console.log(`Debug - All connected users: ${Array.from(clients.keys()).join(', ')}`);

                                // Don't try again, delivery will fail and we'll use the notification fallback
                                break;
                            }
                        }

                        // At this point we've either found a valid connection or confirmed there isn't one
                        if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
                            console.log(`Forwarding chat message to online user ${parsedReceiverId}`);

                            // Create a modified payload for the receiver with correct read status
                            const receiverPayload = {
                                ...confirmationPayload,
                                read: 0,
                                isRead: false
                            };

                            try {
                                // Update receiver's last activity timestamp before sending
                                receiverWs.lastActivity = Date.now();

                                // Send the message
                                receiverWs.send(JSON.stringify({
                                    type: 'new_message',
                                    payload: receiverPayload
                                }));

                                console.log(`Successfully forwarded message to receiver ${parsedReceiverId}`);

                                // Update database with last seen timestamp
                                updateUserLastSeen(parsedReceiverId);

                                // Message successfully sent, remove from pending if it exists
                                removeFromPendingMessages(parsedReceiverId, receiverPayload.id);
                            } catch (wsError) {
                                console.error(`Failed to forward message to receiver ${parsedReceiverId}:`, wsError);

                                // Only delete from clients if the error suggests a truly broken connection
                                if (wsError.code === 'EPIPE' || wsError.code === 'ECONNRESET' ||
                                    wsError.message.includes('not open') || wsError.message.includes('closed')) {
                                    console.log(`WebSocket appears completely broken, removing from clients map.`);
                                    clients.delete(parsedReceiverId);

                                    // Update presence for truly broken connections
                                    const currentTime = new Date().toISOString();
                                    userPresence.set(parsedReceiverId, {
                                        online: false,
                                        lastSeen: currentTime
                                    });
                                }

                                // Add to pending messages as fallback for polling
                                console.log(`Adding message to pending list for user ${parsedReceiverId} and sending FCM notification`);

                                // Add this message to the pending messages list for the receiver
                                addToPendingMessages(parsedReceiverId, {
                                    id: confirmationPayload.id,
                                    match_id: parsedMatchId,
                                    sender_id: parsedSenderId,
                                    receiver_id: parsedReceiverId,
                                    message: text,
                                    created_at: new Date().toISOString(),
                                    read: false,
                                    voice_url: null,
                                    duration: 0
                                });

                                // Also send notification
                                notifications.notifyNewMessage(
                                    parsedReceiverId,
                                    parsedSenderId,
                                    parsedMatchId,
                                    text
                                ).catch(error => {
                                    console.error("Error sending push notification:", error);
                                });
                            }
                        } else {
                            // Store message for delivery via polling and send push notification
                            console.log(`Receiver ${parsedReceiverId} is offline or has no WebSocket connection. Adding to pending messages.`);

                            // Add this message to the pending messages list for the receiver
                            addToPendingMessages(parsedReceiverId, {
                                id: confirmationPayload.id,
                                match_id: parsedMatchId,
                                sender_id: parsedSenderId,
                                receiver_id: parsedReceiverId,
                                message: text,
                                created_at: new Date().toISOString(),
                                read: false,
                                voice_url: null,
                                duration: 0
                            });

                            // Also send push notification
                            notifications.notifyNewMessage(
                                parsedReceiverId,
                                parsedSenderId,
                                parsedMatchId,
                                text
                            ).catch(error => {
                                console.error("Error sending push notification:", error);
                            });
                        }
                    } else {
                        console.error("Failed to retrieve saved message");
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Failed to retrieve saved message',
                                original_payload: data.payload
                            }));
                        }
                    }

                } catch (error) {
                    console.error("Error saving chat message to DB:", error);
                    if (conn) await conn.release().catch(e => console.error('DB connection close failed after error:', e));
                    // Notify sender about the failure
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Failed to save message to database.',
                            original_payload: data.payload
                        }));
                    }
                    return; // Stop further processing for this message
                }
                break;

            case 'update_active_chat':
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                const newMatchId = parseInt(data.activeMatchId);
                if (!isNaN(newMatchId) && newMatchId > 0) {
                    // Update which chat the user is actively viewing
                    ws.currentMatchId = newMatchId;
                    // Also mark chat as visible by default when setting active
                    ws.isChatVisible = true;
                    console.log(`User ${userId} is now active in chat ${newMatchId}`);

                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'active_chat_updated',
                            activeMatchId: newMatchId
                        }));
                    }
                } else {
                    console.error(`Invalid matchId in update_active_chat: ${data.activeMatchId}`);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid matchId.' }));
                    }
                }
                break;

            case 'update_chat_visibility':
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                const chatId = parseInt(data.matchId);
                const isVisible = !!data.isVisible; // Convert to boolean

                if (!isNaN(chatId) && chatId > 0) {
                    // Update whether the user is actually viewing this chat
                    // This is used to determine whether to send notifications
                    ws.isChatVisible = isVisible;
                    console.log(`User ${userId} chat ${chatId} visibility changed to: ${isVisible ? 'visible' : 'not visible'}`);

                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'chat_visibility_updated',
                            matchId: chatId,
                            isVisible: isVisible
                        }));
                    }

                    // Find the other user in this chat and send them the visibility update
                    (async () => {
                        try {
                            const conn = await getConnection();
                            // Find the other user in this match
                            const [matchRows] = await conn.execute(
                                'SELECT user_id_1, user_id_2 FROM matches WHERE id = ?',
                                [chatId]
                            );
                            await conn.release();

                            if (matchRows && matchRows.length > 0) {
                                const match = matchRows[0];
                                // Determine which user is the other party in this chat
                                const otherUserId = match.user_id_1 == userId ? match.user_id_2 : match.user_id_1;

                                console.log(`Notifying user ${otherUserId} that user ${userId} ${isVisible ? 'is viewing' : 'stopped viewing'} chat ${chatId}`);

                                // Find all WebSocket connections for the other user
                                wss.clients.forEach(client => {
                                    if (client.userId === otherUserId && client.readyState === WebSocket.OPEN) {
                                        // Send visibility update to the other user
                                        client.send(JSON.stringify({
                                            type: 'chat_visibility_update',  // Note the different type name
                                            userId: userId,               // Who changed visibility
                                            matchId: chatId,
                                            isVisible: isVisible
                                        }));
                                    }
                                });
                            }
                        } catch (error) {
                            console.error('Error notifying other user of chat visibility change:', error);
                        }
                    })();
                } else {
                    console.error(`Invalid matchId in update_chat_visibility: ${data.matchId}`);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid matchId.' }));
                    }
                }
                break;

            case 'mark_messages_read':
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                // Extract data from payload
                const { messageIds, matchId: readMatchId } = data;

                if (!messageIds || !Array.isArray(messageIds) || messageIds.length === 0) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid or missing messageIds array' }));
                    }
                    return;
                }

                // Validate matchId
                if (!readMatchId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Missing matchId' }));
                    }
                    return;
                }

                try {
                    console.log(`User ${userId} marking messages as read: ${messageIds} in match ${readMatchId}`);

                    // Update messages in the database
                    const conn = await getConnection();
                    try {
                        // Generate placeholders for the IN clause
                        const placeholders = messageIds.map(() => '?').join(',');

                        // Mark messages as read in database
                        await conn.execute(
                            `UPDATE messages SET \`read\` = 1 WHERE id IN (${placeholders}) AND receiver_id = ?`,
                            [...messageIds, userId]
                        );
                        console.log(`Updated read status for messages: ${messageIds}, reader: ${userId}`);

                        // Get senders of these messages to notify them
                        const [senders] = await conn.execute(
                            `SELECT DISTINCT sender_id FROM messages WHERE id IN (${placeholders})`,
                            [...messageIds]
                        );

                        // Notify senders that their messages were read
                        for (const sender of senders) {
                            const senderId = sender.sender_id;
                            console.log(`Notifying sender ${senderId} that messages were read by ${userId}`);

                            // Find sender's WebSocket connection
                            const senderConn = clients.get(senderId);
                            if (senderConn && senderConn.readyState === WebSocket.OPEN) {
                                senderConn.send(JSON.stringify({
                                    type: 'messages_read',
                                    reader: userId,
                                    matchId: readMatchId,
                                    messageIds: messageIds
                                }));
                            }
                        }
                    } finally {
                        await conn.release();
                    }

                } catch (err) {
                    console.error('Error marking messages as read:', err);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Error marking messages as read' }));
                    }
                }
                break;

            case 'message_reaction':
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                // Extract reaction data
                const { messageId, reactionType } = data;

                if (!messageId || !reactionType) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Missing required fields: messageId, reactionType' }));
                    }
                    return;
                }

                console.log(`WS: Adding reaction ${reactionType} from user ${userId} to message ${messageId}`);

                let reactionConn;
                try {
                    reactionConn = await getConnection();

                    // First, ensure the message exists
                    const [messages] = await reactionConn.execute(
                        'SELECT * FROM messages WHERE id = ?',
                        [messageId]
                    );

                    if (messages.length === 0) {
                        await reactionConn.end();
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({ type: 'error', message: 'Message not found' }));
                        }
                        return;
                    }

                    const message = messages[0];

                    // Check if this user already has a reaction on this message
                    const [existingReactions] = await reactionConn.execute(
                        'SELECT * FROM message_reactions WHERE message_id = ? AND user_id = ?',
                        [messageId, userId]
                    );

                    let reaction;
                    let isRemoval = false;

                    if (existingReactions.length > 0) {
                        // Reaction already exists
                        reaction = existingReactions[0];

                        if (reaction.reaction === reactionType) {
                            // Same reaction type - remove it (toggle behavior)
                            isRemoval = true;
                            try {
                                await reactionConn.execute(
                                    'DELETE FROM message_reactions WHERE message_id = ? AND user_id = ?',
                                    [messageId, userId]
                                );
                                console.log(`Removed reaction ${reactionType} from message ${messageId}`);
                            } catch (deleteError) {
                                console.error(`Error removing reaction: ${deleteError.message}`);
                                isRemoval = false;
                            }
                        } else {
                            // Different reaction type - update it
                            await reactionConn.execute(
                                'UPDATE message_reactions SET reaction = ?, created_at = NOW() WHERE id = ?',
                                [reactionType, reaction.id]
                            );

                            // Get the updated reaction
                            const [updatedReactions] = await reactionConn.execute(
                                'SELECT * FROM message_reactions WHERE id = ?',
                                [reaction.id]
                            );

                            if (updatedReactions.length > 0) {
                                reaction = updatedReactions[0];
                            }
                            console.log(`Updated reaction to ${reactionType} for message ${messageId}`);
                        }
                    } else {
                        // No existing reaction - add a new one
                        try {
                            const [result] = await reactionConn.execute(
                                'INSERT INTO message_reactions (message_id, user_id, reaction, created_at) VALUES (?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE reaction = VALUES(reaction), created_at = NOW()',
                                [messageId, userId, reactionType]
                            );

                            const reactionId = result.insertId;

                            // Get the complete reaction data
                            const [insertedReactions] = await reactionConn.execute(
                                'SELECT * FROM message_reactions WHERE message_id = ? AND user_id = ?',
                                [messageId, userId]
                            );

                            if (insertedReactions.length > 0) {
                                reaction = insertedReactions[0];
                            }
                            console.log(`Added new reaction ${reactionType} to message ${messageId}`);
                        } catch (insertError) {
                            console.error(`Error inserting reaction: ${insertError.message}`);
                            // Even if insert fails, try to get any existing reaction
                            const [existingReactions] = await reactionConn.execute(
                                'SELECT * FROM message_reactions WHERE message_id = ? AND user_id = ?',
                                [messageId, userId]
                            );

                            if (existingReactions.length > 0) {
                                reaction = existingReactions[0];
                                console.log(`Found existing reaction despite insert error`);
                            }
                        }
                    }

                    // Get user info for the reaction
                    const [users] = await reactionConn.execute(
                        'SELECT id, username, avatar_url FROM users WHERE id = ?',
                        [userId]
                    );

                    const userInfo = users.length > 0 ? users[0] : null;

                    // Broadcast the reaction via WebSocket
                    const reactionPayload = {
                        id: reaction ? reaction.id : null,
                        messageId: parseInt(messageId),
                        userId: parseInt(userId),
                        reactionType: reactionType,
                        created_at: reaction ? reaction.created_at : new Date().toISOString(),
                        user: userInfo,
                        removed: isRemoval
                    };

                    // Determine event type
                    const eventType = isRemoval ? 'message_reaction_removed' : 'message_reaction_added';

                    // Send to client that made the request
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: eventType,
                            payload: reactionPayload
                        }));
                    }

                    // Send to message sender and receiver if different from current user
                    if (message.sender_id !== userId && clients.has(message.sender_id)) {
                        const senderWs = clients.get(message.sender_id);
                        if (senderWs && senderWs.readyState === WebSocket.OPEN) {
                            senderWs.send(JSON.stringify({
                                type: eventType,
                                payload: reactionPayload
                            }));
                        }
                    }

                    if (message.receiver_id !== userId && clients.has(message.receiver_id)) {
                        const receiverWs = clients.get(message.receiver_id);
                        if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
                            receiverWs.send(JSON.stringify({
                                type: eventType,
                                payload: reactionPayload
                            }));
                        }
                    }

                    await reactionConn.end();

                } catch (error) {
                    console.error('Error handling message reaction:', error);
                    if (reactionConn) await reactionConn.end().catch(e => console.error('Failed to close connection:', e));
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Error handling reaction'
                        }));
                    }
                }
                break;

            case 'voice_message':
                if (!userId) {
                     if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                     }
                    return;
                }

                // Extract voice message data
                const voiceData = data;
                const voiceMatchId = parseInt(voiceData.matchId);
                const voiceSenderId = parseInt(voiceData.senderId);
                const voiceReceiverId = parseInt(voiceData.receiverId);
                const voiceFile = voiceData.voiceFile;
                const voiceDuration = parseInt(voiceData.voiceDuration || 0);
                const clientMessageId = voiceData.clientMessageId;

                // Detailed logging of voice file data
                console.log(`Voice message details - File: ${voiceFile}, Duration: ${voiceDuration}s, ClientMsgId: ${clientMessageId}`);

                // Reply data
                const replyToId = voiceData.reply_to_id || null;
                const replyToText = voiceData.reply_to_text || null;
                const replyToSenderId = voiceData.reply_to_sender_id || null;

                // Validation
                if (!voiceMatchId || isNaN(voiceMatchId) ||
                    !voiceSenderId || isNaN(voiceSenderId) ||
                    !voiceReceiverId || isNaN(voiceReceiverId) ||
                    !voiceFile || voiceSenderId !== userId) {
                    console.error('Invalid voice message payload or sender mismatch:', voiceData);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid voice message payload or sender mismatch.' }));
                    }
                    return;
                }

                // Validate the voice file path further
                if (!voiceFile.startsWith('/uploads/') && !voiceFile.startsWith('data:')) {
                    console.error('Invalid voice file path format:', voiceFile);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Invalid voice file format.' }));
                    }
                    return;
                }

                console.log(`Voice message from ${voiceSenderId} to ${voiceReceiverId} in match ${voiceMatchId}, duration: ${voiceDuration}s`);

                // Store in database
                let voiceConn;
                try {
                    voiceConn = await getConnection();

                    // CRITICAL: Ensure we're explicitly setting the message_type to 'voice'
                    const messageType = 'voice';

                    // Debug the data we're about to insert
                    console.log(`[VOICE_MESSAGE_HANDLER] Preparing for DB INSERT.`);
                    console.log(`- Target Match ID: ${voiceMatchId}`);
                    console.log(`- Target Sender ID: ${voiceSenderId}`);
                    console.log(`- Target Receiver ID: ${voiceReceiverId}`);
                    console.log(`- Voice File Path (variable voiceFile): '${voiceFile}'`);
                    console.log(`- Voice Duration (variable voiceDuration): ${voiceDuration}`);
                    console.log(`- Message Type (variable messageType): '${messageType}'`);
                    console.log(`- Client Message ID: ${clientMessageId}`);
                    console.log(`- Is Reply (replyToId exists?): ${!!replyToId}`);
                    if (replyToId) {
                        console.log(`  - Reply To ID: ${replyToId}`);
                        console.log(`  - Reply To Text: ${replyToText}`);
                        console.log(`  - Reply To Sender ID: ${replyToSenderId}`);
                    }

                    let result;
                    let insertedColumns;

                    // Check if this is a reply message
                    if (replyToId || replyToText || replyToSenderId) {
                        console.log(`Voice message is a reply to message ${replyToId}`);

                        insertedColumns = 'match_id, sender_id, receiver_id, message_text, created_at, ' +
                                        'voice_file, voice_duration, message_type, ' +
                                        'reply_to_id, reply_to_text, reply_to_sender_id, `read`';

                        const insertValues = '?, ?, ?, ?, NOW(), ?, ?, ?, ?, ?, ?, 0';

                        const insertSQL = `INSERT INTO messages (${insertedColumns}) VALUES (${insertValues})`;
                        console.log(`[VOICE_MESSAGE_HANDLER] Executing SQL (Reply): ${insertSQL}`);
                        console.log(`[VOICE_MESSAGE_HANDLER] With Values (Reply):`, [voiceMatchId, voiceSenderId, voiceReceiverId, "Mensaje de voz", voiceFile, voiceDuration, messageType, replyToId, replyToText, replyToSenderId]);

                        [result] = await voiceConn.execute(insertSQL,
                           [voiceMatchId, voiceSenderId, voiceReceiverId, "Mensaje de voz",
                            voiceFile, voiceDuration, messageType, replyToId, replyToText, replyToSenderId]);
                    } else {
                        // Regular voice message without reply
                        insertedColumns = 'match_id, sender_id, receiver_id, message_text, created_at, ' +
                                        'voice_file, voice_duration, message_type, `read`';

                        const insertValues = '?, ?, ?, ?, NOW(), ?, ?, ?, 0';

                        const insertSQL = `INSERT INTO messages (${insertedColumns}) VALUES (${insertValues})`;
                        console.log(`[VOICE_MESSAGE_HANDLER] Executing SQL (Regular): ${insertSQL}`);
                        console.log(`[VOICE_MESSAGE_HANDLER] With Values (Regular):`, [voiceMatchId, voiceSenderId, voiceReceiverId, "Mensaje de voz", voiceFile, voiceDuration, messageType]);

                        [result] = await voiceConn.execute(insertSQL,
                           [voiceMatchId, voiceSenderId, voiceReceiverId, "Mensaje de voz",
                            voiceFile, voiceDuration, messageType]);
                    }

                    const insertedVoiceId = result.insertId;
                    console.log(`Voice message saved to DB with ID: ${insertedVoiceId}`);

                    if (insertedVoiceId) {
                        // Verify the message was saved correctly (primarily for ID and created_at)
                        const [voiceMessages] = await voiceConn.execute(`
                            SELECT * FROM messages WHERE id = ?`, [insertedVoiceId]
                        );

                        if (voiceMessages.length === 0) {
                            console.error(`ERROR: Voice message with ID ${insertedVoiceId} not found after insertion!`);
                            if (ws.readyState === WebSocket.OPEN) {
                                ws.send(JSON.stringify({ type: 'error', message: 'Failed to verify voice message was saved' }));
                            }
                            if (voiceConn) await voiceConn.end().catch(e => console.error('DB connection close failed after verification error:', e));
                            return; // Return early as we can't proceed
                        }

                        const savedVoiceMessage = voiceMessages[0];

                        // The INSERT statement is now solely responsible for voice_file and message_type.
                        // Removed the previous fix-up logic for savedVoiceMessage.message_type and savedVoiceMessage.voice_file.

                        console.log(`Retrieved voice message details post-insert - ID: ${savedVoiceMessage.id}, Type (from DB): ${savedVoiceMessage.message_type}, File (from DB): ${savedVoiceMessage.voice_file}`);

                        const voicePayload = {
                            id: savedVoiceMessage.id, // Use ID from DB
                            matchId: voiceMatchId,
                            senderId: voiceSenderId,
                            receiverId: voiceReceiverId,
                            text: "Mensaje de voz", // This is standard for voice messages
                            voiceFile: voiceFile, // Use original voiceFile path variable
                            voiceDuration: voiceDuration, // Use original voiceDuration variable
                            messageType: messageType, // Use original messageType variable ('voice')
                            clientMessageId: clientMessageId,
                            created_at: savedVoiceMessage.created_at, // Use created_at from DB
                            reply_to_id: replyToId, // Use original replyToId
                            reply_to_text: replyToText, // Use original replyToText
                            reply_to_sender_id: replyToSenderId, // Use original replyToSenderId
                            read: 0,
                            isRead: false
                        };

                        // Close database connection on success path before sending confirmations
                        // This was originally here and seems correct to free up connection.
                        if (voiceConn) {
                             await voiceConn.end();
                             voiceConn = null; // Mark as closed
                        }

                        // Confirm to sender
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'voice_message_saved',
                                payload: voicePayload
                            }));
                            console.log(`Confirmed voice message save to sender ${voiceSenderId}`);
                        }

                        // Forward to receiver
                        const receiverVoiceWs = clients.get(voiceReceiverId);
                        if (receiverVoiceWs && receiverVoiceWs.readyState === WebSocket.OPEN) {
                            try {
                                const receiverVoicePayload = {
                                    ...voicePayload,
                                    messageType: 'voice'
                                };
                                receiverVoiceWs.send(JSON.stringify({
                                    type: 'voice_message',
                                    payload: receiverVoicePayload
                                }));
                                console.log(`Forwarded voice message from ${voiceSenderId} to ${voiceReceiverId}`);
                            } catch (e) {
                                console.error(`Error sending voice message via WebSocket, falling back to notification: ${e.message}`);
                                clients.delete(voiceReceiverId);
                                console.log(`Receiver ${voiceReceiverId} has broken connection, sending voice message notification instead`);
                                notifications.notifyNewMessage(
                                    voiceReceiverId,
                                    voiceSenderId,
                                    voiceMatchId,
                                    "Mensaje de voz"
                                ).catch(error => {
                                    console.error("Error sending voice message notification:", error);
                                });
                            }
                        } else {
                            if (!receiverVoiceWs) {
                                console.log(`Receiver ${voiceReceiverId} not found in clients map - definitely offline`);
                            } else {
                                console.log(`Receiver ${voiceReceiverId} has WebSocket in state: ${receiverVoiceWs.readyState} (1=OPEN) - treating as offline`);
                            }
                            const presenceInfo = userPresence.get(voiceReceiverId);
                            if (presenceInfo && presenceInfo.online) {
                                console.log(`WARNING: User ${voiceReceiverId} marked online but has no valid WebSocket!`);
                                const currentTime = new Date().toISOString();
                                userPresence.set(voiceReceiverId, {
                                    online: false,
                                    lastSeen: currentTime
                                });
                                updateUserLastSeen(voiceReceiverId);
                            }
                            console.log(`Sending FCM notification for voice message to user ${voiceReceiverId}`);
                            notifications.notifyNewMessage(
                                voiceReceiverId,
                                voiceSenderId,
                                voiceMatchId,
                                "Mensaje de voz"
                            ).catch(error => {
                                console.error("Error sending voice message notification:", error);
                            });
                        }
                    } else {
                        // This 'else' corresponds to 'if (insertedVoiceId)' being false
                        console.error("Failed to save voice message to database (no insertId received).");
                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'error',
                                message: 'Failed to save voice message to database.'
                            }));
                        }
                        // voiceConn is open here if insert failed; it will be closed by the outer catch block.
                    }
                } catch (error) {
                    console.error("Error saving voice message to DB:", error);
                    if (voiceConn) await voiceConn.end().catch(e => console.error('DB connection close failed after voice error:', e));

                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Failed to save voice message to database.'
                        }));
                    }
                }
                break;

            case 'new_post':
                // Handle new post creation via WebSocket
                console.log(`[WS] New post from user ${userId}`);

                try {
                    // Broadcast the new post to all connected clients
                    wss.clients.forEach(function each(client) {
                        if (client.readyState === WebSocket.OPEN) {
                            client.send(JSON.stringify({
                                type: 'new_post',
                                post: data.post
                            }));
                        }
                    });
                } catch (error) {
                    console.error(`[WS] Error broadcasting new post: ${error.message}`);
                }
                break;

            case 'post_reaction':
                // Handle post reaction via WebSocket
                console.log(`[WS] Post reaction from user ${userId} on post ${data.postId}`);

                try {
                    // Broadcast the reaction to all connected clients
                    wss.clients.forEach(function each(client) {
                        if (client.readyState === WebSocket.OPEN) {
                            client.send(JSON.stringify({
                                type: 'post_reaction',
                                postId: data.postId,
                                userId: userId,
                                reaction: data.reaction,
                                username: data.username
                            }));
                        }
                    });
                } catch (error) {
                    console.error(`[WS] Error broadcasting post reaction: ${error.message}`);
                }
                break;

            case 'new_comment':
                // Handle new comment via WebSocket
                console.log(`[WS] New comment from user ${userId} on post ${data.postId}`);

                try {
                    // Broadcast the new comment to all connected clients
                    wss.clients.forEach(function each(client) {
                        if (client.readyState === WebSocket.OPEN) {
                            client.send(JSON.stringify({
                                type: 'new_comment',
                                postId: data.postId,
                                comment: data.comment
                            }));
                        }
                    });
                } catch (error) {
                    console.error(`[WS] Error broadcasting new comment: ${error.message}`);
                }
                break;

                         case 'chat_deleted':
                 console.log(`Received chat_deleted event: ${message}`);

                 // Forward chat deletion message to the relevant users
                 if (data.payload && data.payload.matchId && data.payload.deletedBy) {
                     const chatMatchId = data.payload.matchId;
                     const chatDeletedBy = data.payload.deletedBy;

                     // Forward to all users except the sender
                     for (const [clientUserId, clientWs] of clients.entries()) {
                         if (clientUserId !== chatDeletedBy && clientWs.readyState === WebSocket.OPEN) {
                             clientWs.send(message);
                         }
                     }
                 }
                 break;

            case 'active_room':
                // Record what room the user is currently viewing
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                const roomId = parseInt(data.roomId);
                if (!isNaN(roomId) && roomId > 0) {
                    ws.currentRoomId = roomId;
                    console.log(`User ${userId} is now active in room ${roomId}`);

                    // Mark messages as read for this user in this room
                    try {
                        const conn = await getConnection();
                        await conn.execute(
                            'UPDATE chat_room_members SET last_seen = NOW(), has_unread_messages = false WHERE room_id = ? AND user_id = ?',
                            [roomId, userId]
                        );
                        await conn.release();

                        if (ws.readyState === WebSocket.OPEN) {
                            ws.send(JSON.stringify({
                                type: 'active_room_acknowledged',
                                roomId: roomId
                            }));
                        }
                    } catch (error) {
                        console.error(`Error updating last_seen for user ${userId} in room ${roomId}:`, error);
                    }
                } else {
                    // If invalid room ID sent, clear the current room
                    delete ws.currentRoomId;
                    console.log(`User ${userId} is no longer active in any room`);

                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'active_room_cleared'
                        }));
                    }
                }
                break;

            // Handle blind date related messages
            case 'blind_date_join_queue':
            case 'blind_date_leave_queue':
            case 'blind_date_message':
            case 'blind_date_typing':
            case 'blind_date_reveal_decision':
            case 'blind_date_subscribe_session':
            case 'blind_date_check_session':
            case 'blind_date_poll_messages':
            case 'blind_date_poll_session_status':
            case 'blind_date_poll_reveal_result':
            case 'blind_date_leave_chat':
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                // Forward to blind date handler
                try {
                    await blindDateHandler.handleBlindDateMessage(data, ws, userId);
                } catch (error) {
                    console.error('Error handling blind date message:', error);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Error processing blind date message',
                            details: error.message
                        }));
                    }
                }
                break;

            case 'typing_start':
                // Handle typing start event
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                const typingMatchId = parseInt(data.matchId);
                const typingReceiverId = parseInt(data.receiverId);

                if (!typingMatchId || !typingReceiverId || isNaN(typingMatchId) || isNaN(typingReceiverId)) {
                    console.error('Invalid typing_start payload:', data);
                    return;
                }

                console.log(`User ${userId} started typing to ${typingReceiverId} in match ${typingMatchId}`);

                // Forward typing indicator to receiver
                const typingReceiverWs = clients.get(typingReceiverId);
                if (typingReceiverWs && typingReceiverWs.readyState === WebSocket.OPEN) {
                    typingReceiverWs.send(JSON.stringify({
                        type: 'typing_start',
                        payload: {
                            matchId: typingMatchId,
                            senderId: userId,
                            receiverId: typingReceiverId
                        }
                    }));
                    console.log(`Forwarded typing_start from ${userId} to ${typingReceiverId}`);
                }
                break;

            case 'typing_stop':
                // Handle typing stop event
                if (!userId) {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: 'Please identify first.' }));
                    }
                    return;
                }

                const stopTypingMatchId = parseInt(data.matchId);
                const stopTypingReceiverId = parseInt(data.receiverId);

                if (!stopTypingMatchId || !stopTypingReceiverId || isNaN(stopTypingMatchId) || isNaN(stopTypingReceiverId)) {
                    console.error('Invalid typing_stop payload:', data);
                    return;
                }

                console.log(`User ${userId} stopped typing to ${stopTypingReceiverId} in match ${stopTypingMatchId}`);

                // Forward typing stop to receiver
                const stopTypingReceiverWs = clients.get(stopTypingReceiverId);
                if (stopTypingReceiverWs && stopTypingReceiverWs.readyState === WebSocket.OPEN) {
                    stopTypingReceiverWs.send(JSON.stringify({
                        type: 'typing_stop',
                        payload: {
                            matchId: stopTypingMatchId,
                            senderId: userId,
                            receiverId: stopTypingReceiverId
                        }
                    }));
                    console.log(`Forwarded typing_stop from ${userId} to ${stopTypingReceiverId}`);
                }
                break;

            case 'ping':
                // Handle ping messages from client
                console.log(`Received ping from user ${userId || 'unknown'}`);
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'pong',
                        timestamp: Date.now()
                    }));
                }
                break;

            case 'pong':
                // Just log pong messages from client
                console.log(`Received pong from user ${userId || 'unknown'}`);
                break;

            default:
                // Only log and send error for truly unknown message types (not ping/pong)
                if (data.type !== 'ping' && data.type !== 'pong') {
                    console.log('Received unknown WS message type:', data.type);
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({ type: 'error', message: `Unknown message type: ${data.type}` }));
                    }
                }
        }
    });

    ws.on('close', () => {
        if (userId) {
            console.log(`WebSocket client disconnected (User ID: ${userId})`);

            // Get the current entry from the clients map for this user
            const currentConnection = clients.get(userId);

            // Only remove this connection from the map if it's the same object
            // as what's currently in the map (to avoid race conditions with reconnects)
            if (currentConnection === ws) {
                console.log(`Removing user ${userId} from active clients map`);
                clients.delete(userId);

                // Update user presence to offline
                const currentTime = new Date().toISOString();
                console.log(`Marking user ${userId} as offline at ${currentTime}`);

                userPresence.set(userId, {
                    online: false,
                    lastSeen: currentTime
                });
            } else {
                console.log(`Not removing user ${userId} from clients map as a newer connection exists`);
            }

            // Always update the last seen timestamp in the database
            updateUserLastSeen(userId);
        } else {
            console.log('Unidentified WebSocket client disconnected');
        }
    });

    ws.on('error', (error) => {
        console.error(`WebSocket error for User ID ${userId}:`, error);
        if (userId) {
            clients.delete(userId); // Clean up on error as well
        }
        // Ensure the connection is closed on error
        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
             ws.terminate();
        }
    });
});

// Helper function to update user's last_seen in the database
async function updateUserLastSeen(userId) {
    if (!userId) return;

    let conn;
    try {
        conn = await getConnection();
        await conn.execute(
            'UPDATE users SET last_seen = NOW() WHERE id = ?',
            [userId]
        );
        await conn.release();
        console.log(`Updated last_seen for user ${userId}`);
    } catch (error) {
        console.error(`Failed to update last_seen for user ${userId}:`, error);
        if (conn) await conn.release().catch(e => console.error('Connection close failed:', e));
    }
}

// Add a method to clean up stale WebSocket connections
// Function to add a message to the pending messages list for a receiver
function addToPendingMessages(receiverId, message) {
    receiverId = parseInt(receiverId);
    if (!receiverId || isNaN(receiverId)) return;

    if (!pendingMessages.has(receiverId)) {
        pendingMessages.set(receiverId, []);
    }

    // Check if we already have this message to avoid duplicates
    const existing = pendingMessages.get(receiverId).find(m => m.id === message.id);
    if (!existing) {
        pendingMessages.get(receiverId).push(message);
        console.log(`Added message ${message.id} to pending list for user ${receiverId}. Total pending: ${pendingMessages.get(receiverId).length}`);
    }
}

// Function to remove a message from the pending list once delivered
function removeFromPendingMessages(receiverId, messageId) {
    receiverId = parseInt(receiverId);
    if (!receiverId || isNaN(receiverId) || !pendingMessages.has(receiverId)) return;

    const userPendingMessages = pendingMessages.get(receiverId);
    const initialCount = userPendingMessages.length;

    // Filter out the delivered message
    const updatedMessages = userPendingMessages.filter(msg => msg.id !== messageId);

    if (updatedMessages.length < initialCount) {
        console.log(`Removed message ${messageId} from pending list for user ${receiverId}`);

        if (updatedMessages.length === 0) {
            pendingMessages.delete(receiverId);
            console.log(`No more pending messages for user ${receiverId}, removed from pending map`);
        } else {
            pendingMessages.set(receiverId, updatedMessages);
        }
    }
}

function cleanupStaleConnections() {
    console.log(`Checking for stale WebSocket connections...`);
    let cleanedCount = 0;
    let erroredCount = 0;
    let idleCount = 0;
    const now = Date.now();
    const IDLE_TIMEOUT = 5 * 60 * 1000; // 5 minutes in milliseconds

    // Check all clients for stale connections
    for (const [userId, ws] of clients.entries()) {
        // Check if WebSocket is not in OPEN state
        if (ws.readyState !== WebSocket.OPEN) {
            console.log(`Found stale connection for user ${userId} in state ${ws.readyState}`);
            clients.delete(userId);
            cleanedCount++;

            // Update user presence
            const currentTime = new Date().toISOString();
            userPresence.set(userId, {
                online: false,
                lastSeen: currentTime
            });

            // Update database with last seen timestamp
            updateUserLastSeen(userId);
        } else {
            // Check for idle connections that haven't had activity in a long time
            if (ws.lastActivity && (now - ws.lastActivity) > IDLE_TIMEOUT) {
                console.log(`Closing idle connection for user ${userId} (inactive for ${Math.round((now - ws.lastActivity)/1000)} seconds)`);
                try {
                    ws.close(1000, "Connection idle timeout");
                } catch (err) {
                    console.error(`Error closing idle connection for user ${userId}:`, err.message);
                    try {
                        ws.terminate();
                    } catch (e) {
                        console.error(`Error terminating idle connection for user ${userId}:`, e.message);
                    }
                }
                clients.delete(userId);
                cleanedCount++;
                idleCount++;

                // Update user presence
                const currentTime = new Date().toISOString();
                userPresence.set(userId, {
                    online: false,
                    lastSeen: currentTime
                });

                // Update database with last seen timestamp
                updateUserLastSeen(userId);
                continue;
            }

            // For open connections, ping to ensure they're responsive
            try {
                // Record this activity
                ws.lastActivity = now;
                ws.ping();
                console.log(`Sent ping to verify connection for user ${userId}`);
            } catch (e) {
                console.log(`Failed to ping user ${userId}, connection may be broken:`, e.message);
                clients.delete(userId);
                cleanedCount++;
                erroredCount++;

                // Update user presence
                const currentTime = new Date().toISOString();
                userPresence.set(userId, {
                    online: false,
                    lastSeen: currentTime
                });

                // Update database with last seen timestamp
                updateUserLastSeen(userId);
            }
        }
    }

    // Also check for consistency between clients map and userPresence
    for (const [userId, presence] of userPresence.entries()) {
        if (presence.online && !clients.has(userId)) {
            console.log(`Inconsistency detected: user ${userId} marked as online but has no WebSocket connection`);

            // Fix the inconsistency
            const currentTime = new Date().toISOString();
            userPresence.set(userId, {
                online: false,
                lastSeen: currentTime
            });

            // Update database with last seen timestamp
            updateUserLastSeen(userId);
            cleanedCount++;
        }
    }

    if (idleCount > 0) {
        console.log(`Closed ${idleCount} idle connections that exceeded timeout period.`);
    }

    if (erroredCount > 0) {
        console.log(`Warning: ${erroredCount} connections failed ping test. This may indicate client connectivity issues.`);
    }

    if (cleanedCount > 0) {
        console.log(`Cleaned up ${cleanedCount} stale WebSocket connections or fixed inconsistencies`);
    } else {
        console.log(`No stale WebSocket connections found`);
    }

    return cleanedCount;
}

// Create necessary tables for admin functionality
async function createAdminTables() {
    let conn;
    try {
        conn = await getConnection();

        // Ensure users table has role column
        await conn.execute(`
            ALTER TABLE users
            ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'usuario'
        `).catch(err => console.error('Error adding role column to users table:', err));

        // Create global_notifications table if it doesn't exist
        await conn.execute(`
            CREATE TABLE IF NOT EXISTS global_notifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                image_url VARCHAR(255),
                button_url VARCHAR(255),
                button_text VARCHAR(50),
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `);

        console.log('Admin tables created or verified');
    } catch (error) {
        console.error('Error creating admin tables:', error);
    } finally {
        if (conn) {
            try {
                if (typeof conn.release === 'function') {
                    conn.release();
                } else if (typeof conn.end === 'function') {
                    await conn.end();
                } else {
                    console.warn('Conexión no tiene release ni end:', conn.constructor && conn.constructor.name);
                }
            } catch (err) {
                console.error('Error liberando/cerrando conexión en createAdminTables:', err, conn.constructor && conn.constructor.name);
            }
        }
    }
}

// Create admin tables on server start
createAdminTables();

// Start the combined HTTP and WebSocket server
server.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error("Error starting server:", err);
    return;
  }
  console.log(`VMeet API and WebSocket Server running on http://0.0.0.0:${PORT}`);

  // Clean up test notifications immediately on startup
  try {
    notifications.clearTestNotifications().catch(error => {
        console.error("Error clearing test notifications:", error);
    });
} catch (error) {
    console.error("Error running test notification cleanup:", error);
}

// Set up a heartbeat interval for all connections
setInterval(() => {
    // Send ping to all clients
    wss.clients.forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
            try {
                ws.ping();
                console.log(`Sent ping to verify connection for user ${ws.userId || 'unknown'}`);
            } catch (e) {
                console.error(`Error during global ping for client (userId: ${ws.userId || 'unknown'}, state: ${ws.readyState}):`, e.message);
                // If ws.ping() throws, the ws object is likely broken.
                // Terminate it; its 'close' event will handle cleanup from 'clients' map if identified.
                try {
                    ws.terminate();
                    console.log(`Terminated connection for client (userId: ${ws.userId || 'unknown'}) due to ping error in global loop.`);
                } catch (termError) {
                    console.error(`Error terminating client (userId: ${ws.userId || 'unknown'}):`, termError.message);
                }
            }
        }
    });

    // Run cleanup to detect and remove stale connections
    const cleanedCount = cleanupStaleConnections();

    // If we cleaned up any connections, log some debug info about current state
    if (cleanedCount > 0) {
        console.log(`Current active users: ${Array.from(clients.keys()).join(', ')}`);
        console.log(`Online according to userPresence: ${Array.from(userPresence.entries())
            .filter(([_, presence]) => presence.online)
            .map(([userId, _]) => userId)
            .join(', ')}`);
    }

    // Run pending notifications check periodically
    try {
        notifications.checkPendingNotifications().catch(error => {
            console.error("Error checking pending notifications:", error);
        });
    } catch (error) {
      console.error("Error running pending notifications check:", error);
    }

    // Clear test notifications every interval to prevent spam
    try {
      notifications.clearTestNotifications().catch(error => {
        console.error("Error clearing test notifications:", error);
      });
    } catch (error) {
      console.error("Error running test notification cleanup:", error);
    }
  }, HEARTBEAT_INTERVAL);
});