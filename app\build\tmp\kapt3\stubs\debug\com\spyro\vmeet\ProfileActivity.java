package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b8\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b)\b\u0007\u0018\u0000 \u00e9\u00012\u00020\u0001:\u0004\u00e9\u0001\u00ea\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010s\u001a\u00020tH\u0002J\u0019\u0010u\u001a\u0004\u0018\u00010\u00062\b\u0010v\u001a\u0004\u0018\u00010\u0004H\u0002\u00a2\u0006\u0002\u0010wJ\u0010\u0010x\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J\b\u0010y\u001a\u00020tH\u0002J$\u0010z\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\u0012\u0010{\u001a\u000e\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020t0|H\u0002J\b\u0010}\u001a\u00020tH\u0002J\b\u0010~\u001a\u00020?H\u0002J\b\u0010\u007f\u001a\u00020?H\u0002J\u001b\u0010\u0080\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\b\u0010\u0081\u0001\u001a\u00030\u0082\u0001H\u0002J\u0011\u0010\u0083\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J\u0011\u0010\u0084\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J\n\u0010\u0085\u0001\u001a\u00030\u0086\u0001H\u0002J\'\u0010\u0087\u0001\u001a\u00020t2\u0007\u0010\u0088\u0001\u001a\u00020\u00062\u0007\u0010\u0089\u0001\u001a\u00020\u00062\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0004H\u0002J,\u0010\u008a\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\u0007\u0010\u008b\u0001\u001a\u00020\u00062\u0007\u0010\u008c\u0001\u001a\u00020\u00062\u0007\u0010\u008d\u0001\u001a\u00020MH\u0002J,\u0010\u008e\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\u0007\u0010\u008f\u0001\u001a\u00020\u00042\u0007\u0010\u008c\u0001\u001a\u00020\u00062\u0007\u0010\u008d\u0001\u001a\u00020MH\u0002J\u0012\u0010\u0090\u0001\u001a\u00020t2\u0007\u0010\u008c\u0001\u001a\u00020\u0006H\u0002J\u0019\u0010\u0091\u0001\u001a\u0004\u0018\u00010\u00062\u0007\u0010\u0092\u0001\u001a\u00020\u0004H\u0002\u00a2\u0006\u0002\u0010wJ\u0011\u0010\u0093\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J\u0011\u0010\u0094\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J,\u0010\u0095\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\u0007\u0010\u008f\u0001\u001a\u00020\u00042\u0007\u0010\u008c\u0001\u001a\u00020\u00062\u0007\u0010\u008d\u0001\u001a\u00020MH\u0002J\u0013\u0010\u0096\u0001\u001a\u00020\u00042\b\u0010v\u001a\u0004\u0018\u00010\u0004H\u0002J\u0013\u0010\u0097\u0001\u001a\u00020\u00042\b\u0010v\u001a\u0004\u0018\u00010\u0004H\u0002J$\u0010\u0098\u0001\u001a\u00020\u00042\u0007\u0010\u0099\u0001\u001a\u00020\u00042\u0007\u0010\u009a\u0001\u001a\u00020\u00042\u0007\u0010\u009b\u0001\u001a\u00020\u0004H\u0002J\u0015\u0010\u009c\u0001\u001a\u0004\u0018\u00010\u00042\b\u0010\u009d\u0001\u001a\u00030\u009e\u0001H\u0002J\t\u0010\u009f\u0001\u001a\u00020tH\u0002J\t\u0010\u00a0\u0001\u001a\u00020tH\u0002J\u0011\u0010\u00a1\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J$\u0010\u00a2\u0001\u001a\u0015\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0012\u0006\u0012\u0004\u0018\u00010\u0004\u0018\u00010\u00a3\u00012\u0006\u0010o\u001a\u00020\u0006H\u0002J\t\u0010\u00a4\u0001\u001a\u00020tH\u0002J\'\u0010\u00a5\u0001\u001a\u00020t2\u0007\u0010\u00a6\u0001\u001a\u00020\u00062\u0007\u0010\u00a7\u0001\u001a\u00020\u00062\n\u0010\u00a8\u0001\u001a\u0005\u0018\u00010\u00a9\u0001H\u0014J\u0015\u0010\u00aa\u0001\u001a\u00020t2\n\u0010\u00ab\u0001\u001a\u0005\u0018\u00010\u00ac\u0001H\u0014J\u0013\u0010\u00ad\u0001\u001a\u00020?2\b\u0010\u00ae\u0001\u001a\u00030\u00af\u0001H\u0016J\t\u0010\u00b0\u0001\u001a\u00020tH\u0014J\u0013\u0010\u00b1\u0001\u001a\u00020?2\b\u0010\u00b2\u0001\u001a\u00030\u00b3\u0001H\u0016J\t\u0010\u00b4\u0001\u001a\u00020tH\u0014J\u0013\u0010\u00b5\u0001\u001a\u00020?2\b\u0010\u00ae\u0001\u001a\u00030\u00af\u0001H\u0016J4\u0010\u00b6\u0001\u001a\u00020t2\u0007\u0010\u00a6\u0001\u001a\u00020\u00062\u0010\u0010\u00b7\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u00020\u00040\u00b8\u00012\b\u0010\u00b9\u0001\u001a\u00030\u00ba\u0001H\u0016\u00a2\u0006\u0003\u0010\u00bb\u0001J\t\u0010\u00bc\u0001\u001a\u00020tH\u0014J\t\u0010\u00bd\u0001\u001a\u00020tH\u0002J\t\u0010\u00be\u0001\u001a\u00020tH\u0002J\u0012\u0010\u00bf\u0001\u001a\u00020t2\u0007\u0010\u008c\u0001\u001a\u00020\u0006H\u0002J\t\u0010\u00c0\u0001\u001a\u00020tH\u0002J\u001f\u0010\u00c1\u0001\u001a\t\u0012\u0005\u0012\u00030\u00c2\u0001012\r\u0010\u00c3\u0001\u001a\b\u0012\u0004\u0012\u00020H01H\u0002J\u0013\u0010\u00c4\u0001\u001a\u00020t2\b\u0010\u00c5\u0001\u001a\u00030\u0082\u0001H\u0002J\t\u0010\u00c6\u0001\u001a\u00020tH\u0002J\t\u0010\u00c7\u0001\u001a\u00020tH\u0002J\t\u0010\u00c8\u0001\u001a\u00020tH\u0002J\'\u0010\u00c9\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\t\u0010\u009a\u0001\u001a\u0004\u0018\u00010\u00042\t\u0010\u009b\u0001\u001a\u0004\u0018\u00010\u0004H\u0002J\u0012\u0010\u00ca\u0001\u001a\u00020t2\u0007\u0010\u008c\u0001\u001a\u00020\u0006H\u0002J\u0012\u0010\u00cb\u0001\u001a\u00020t2\u0007\u0010\u00cc\u0001\u001a\u00020\u0006H\u0002J\t\u0010\u00cd\u0001\u001a\u00020tH\u0002J\t\u0010\u00ce\u0001\u001a\u00020tH\u0002J\t\u0010\u00cf\u0001\u001a\u00020tH\u0002J\t\u0010\u00d0\u0001\u001a\u00020tH\u0002J\u0012\u0010\u00d1\u0001\u001a\u00020t2\u0007\u0010\u008c\u0001\u001a\u00020\u0006H\u0002J\t\u0010\u00d2\u0001\u001a\u00020tH\u0002J\t\u0010\u00d3\u0001\u001a\u00020tH\u0002J\t\u0010\u00d4\u0001\u001a\u00020tH\u0002J\u0011\u0010\u00d5\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u0006H\u0002J\t\u0010\u00d6\u0001\u001a\u00020tH\u0002J\u001b\u0010\u00d7\u0001\u001a\u00020t2\u0007\u0010\u00d8\u0001\u001a\u00020\u00042\u0007\u0010\u00d9\u0001\u001a\u00020\u0004H\u0002J\t\u0010\u00da\u0001\u001a\u00020tH\u0002J\t\u0010\u00db\u0001\u001a\u00020tH\u0002J\t\u0010\u00dc\u0001\u001a\u00020tH\u0002J\t\u0010\u00dd\u0001\u001a\u00020tH\u0002J\u001d\u0010\u00de\u0001\u001a\u00020t2\t\u0010\u009a\u0001\u001a\u0004\u0018\u00010\u00042\t\u0010\u009b\u0001\u001a\u0004\u0018\u00010\u0004J\u0018\u0010\u00df\u0001\u001a\u00020t2\r\u0010\u00e0\u0001\u001a\b\u0012\u0004\u0012\u00020\u000401H\u0002J,\u0010\u00e1\u0001\u001a\u00020t2\u0006\u0010o\u001a\u00020\u00062\u0007\u0010\u00e2\u0001\u001a\u00020\u00042\u0007\u0010\u008c\u0001\u001a\u00020\u00062\u0007\u0010\u008d\u0001\u001a\u00020MH\u0002J\u001d\u0010\u00e3\u0001\u001a\u00020t2\u0007\u0010\u00e4\u0001\u001a\u00020?2\t\u0010\u00e5\u0001\u001a\u0004\u0018\u00010\u0004H\u0002J\t\u0010\u00e6\u0001\u001a\u00020tH\u0002J\u0013\u0010\u00e7\u0001\u001a\u00020t2\b\u0010\u00e8\u0001\u001a\u00030\u009e\u0001H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020 X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020-X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020/X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u00100\u001a\b\u0012\u0004\u0012\u00020\u000401X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010<\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u000203X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020?X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010@\u001a\u00020?X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010A\u001a\u00020BX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010C\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010D\u001a\u00020EX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010F\u001a\b\u0012\u0004\u0012\u00020H0GX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010I\u001a\u00020JX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010K\u001a\b\u0012\u0004\u0012\u00020\u00040GX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010L\u001a\u00020MX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010N\u001a\u00020OX\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010P\u001a\b\u0012\u0004\u0012\u00020\u000401X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010Q\u001a\u00020BX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010R\u001a\u00020SX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010T\u001a\u0004\u0018\u00010UX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010V\u001a\u00020WX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010X\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010Z\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010[\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\\\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010]\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010^\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010_\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010`\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010a\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010b\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010c\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010d\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010e\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010f\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010g\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010h\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010i\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010j\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010k\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010l\u001a\u00020YX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010m\u001a\u00020nX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010o\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010p\u001a\u00020\u001eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010q\u001a\u00020rX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u00eb\u0001"}, d2 = {"Lcom/spyro/vmeet/ProfileActivity;", "Lcom/spyro/vmeet/activity/BaseActivity;", "()V", "API_URL", "", "MAX_PROFILE_IMAGES", "", "PERMISSION_REQUEST_CODE", "PICK_IMAGE_REQUEST", "appBarLayout", "Lcom/google/android/material/appbar/AppBarLayout;", "avatarUrl", "bottomNavView", "Lcom/google/android/material/bottomnavigation/BottomNavigationView;", "buttonAdminPanel", "Landroid/widget/Button;", "buttonBlockUser", "buttonBlockedUsers", "buttonEditSave", "buttonEditTraits", "Lcom/google/android/material/button/MaterialButton;", "buttonLogout", "buttonReportUser", "buttonSendMessage", "buttonSetAsMain", "client", "Lokhttp3/OkHttpClient;", "collapsingToolbarLayout", "Lcom/google/android/material/appbar/CollapsingToolbarLayout;", "editModeDetailsLayout", "Landroid/widget/LinearLayout;", "editTextBio", "Lcom/google/android/material/textfield/TextInputEditText;", "editTextBirthdate", "editTextFavoriteGame", "editTextFavoritePlatform", "editTextGender", "Landroid/widget/AutoCompleteTextView;", "editTextJob", "editTextLocation", "editTextLookingFor", "editTextPronouns", "editTextSchool", "editTextSexuality", "fabAddImage", "Lcom/google/android/material/floatingactionbutton/FloatingActionButton;", "fabChangeAvatar", "Lcom/google/android/material/floatingactionbutton/ExtendedFloatingActionButton;", "genderOptions", "", "inputLayoutBio", "Lcom/google/android/material/textfield/TextInputLayout;", "inputLayoutBirthdate", "inputLayoutFavoriteGame", "inputLayoutFavoritePlatform", "inputLayoutGender", "inputLayoutJob", "inputLayoutLocation", "inputLayoutLookingFor", "inputLayoutPronouns", "inputLayoutSchool", "inputLayoutSexuality", "isEditMode", "", "isUserBlocked", "lastStatusCheck", "", "mainImageUrl", "nestedScrollView", "Landroidx/core/widget/NestedScrollView;", "personalityTraits", "", "Lcom/spyro/vmeet/data/PersonalityTrait;", "profileImageAdapter", "Lcom/spyro/vmeet/ProfileImageAdapter;", "profileImages", "progressBarUserLevel", "Landroid/widget/ProgressBar;", "recyclerViewPersonalityTraits", "Landroidx/recyclerview/widget/RecyclerView;", "sexualityOptions", "statusCheckInterval", "statusHandler", "Landroid/os/Handler;", "statusRunnable", "Ljava/lang/Runnable;", "tabLayoutDots", "Lcom/google/android/material/tabs/TabLayout;", "textViewAdminBadge", "Landroid/widget/TextView;", "textViewAge", "textViewBioLabel", "textViewBioValue", "textViewBirthdateValue", "textViewCityCountryValue", "textViewFavGameValue", "textViewFavPlatformValue", "textViewGenderValue", "textViewJobValue", "textViewLocationValue", "textViewLookingForValue", "textViewOnlineStatus", "textViewPronounsValue", "textViewSchoolValue", "textViewSexualityValue", "textViewUserLevel", "textViewUserXP", "textViewUsername", "textViewVerifiedBadge", "toolbar", "Landroidx/appcompat/widget/Toolbar;", "userId", "viewModeDetailsLayout", "viewPagerImages", "Landroidx/viewpager2/widget/ViewPager2;", "blockUser", "", "calculateAge", "birthdateString", "(Ljava/lang/String;)Ljava/lang/Integer;", "checkAdminStatus", "checkAndShowProfileTips", "checkIfUserAcceptsMessages", "callback", "Lkotlin/Function1;", "checkIfUserIsBlocked", "checkIfViewerIsAdmin", "checkStoragePermission", "checkUserRole", "userObject", "Lorg/json/JSONObject;", "checkUserStatus", "checkVerificationStatus", "createCompatibilityLayout", "Landroid/view/View;", "createDirectChat", "currentUserId", "otherUserId", "deleteImageById", "imageId", "position", "progressBar", "deleteImageByUrl", "imageUrlToDelete", "deleteProfileImage", "extractImageIdFromUrl", "imageUrl", "fetchLocationFromAlternativeSource", "fetchLocationFromRadar", "findAndDeleteImageByUrl", "formatBirthdateForDisplay", "formatBirthdateForInput", "formatLocation", "location", "city", "country", "getPathFromUri", "uri", "Landroid/net/Uri;", "initializeViews", "loadPersonalityTraits", "loadProfile", "loadUserLocation", "Lkotlin/Pair;", "logoutUser", "onActivityResult", "requestCode", "resultCode", "data", "Landroid/content/Intent;", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateOptionsMenu", "menu", "Landroid/view/Menu;", "onDestroy", "onOptionsItemSelected", "item", "Landroid/view/MenuItem;", "onPause", "onPrepareOptionsMenu", "onRequestPermissionsResult", "permissions", "", "grantResults", "", "(I[Ljava/lang/String;[I)V", "onResume", "openChatWithUser", "openEditPersonalityTraits", "openFullScreenImage", "openImagePicker", "organizeCategoriesForDisplay", "Lcom/spyro/vmeet/adapter/TraitCategory;", "selectedTraits", "populateUI", "user", "requestStoragePermission", "revertAvatarImage", "saveProfileData", "saveUserLocation", "setMainProfileImage", "setupBottomNavigation", "loggedInUserId", "setupListeners", "setupPersonalityTraitsRecyclerView", "showBlockUserConfirmation", "showDatePickerDialog", "showImageOptions", "showProfileCompletionTips", "showReportUserDialog", "showUnblockUserConfirmation", "startStatusUpdates", "stopStatusUpdates", "submitReport", "reason", "details", "switchToEditMode", "switchToViewMode", "unblockUser", "updateBlockButtonState", "updateLocationData", "updatePersonalityTraitsDisplay", "selectedTraitIds", "updateProfileToRemoveImage", "imageUrlToRemove", "updateStatusDisplay", "online", "lastSeen", "updateTextViewsFromEditTexts", "uploadAvatar", "imageUri", "Companion", "FileUtils", "app_debug"})
public final class ProfileActivity extends com.spyro.vmeet.activity.BaseActivity {
    private final int PICK_IMAGE_REQUEST = 1;
    private final int PERMISSION_REQUEST_CODE = 100;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String avatarUrl;
    private int userId = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String API_URL = "http://*************:3000";
    private boolean isEditMode = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> genderOptions = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> sexualityOptions = null;
    private android.widget.Button buttonAdminPanel;
    private com.google.android.material.appbar.AppBarLayout appBarLayout;
    private com.google.android.material.appbar.CollapsingToolbarLayout collapsingToolbarLayout;
    private com.google.android.material.floatingactionbutton.ExtendedFloatingActionButton fabChangeAvatar;
    private androidx.core.widget.NestedScrollView nestedScrollView;
    private android.widget.TextView textViewUsername;
    private android.widget.TextView textViewAge;
    private android.widget.TextView textViewPronounsValue;
    private android.widget.TextView textViewAdminBadge;
    private android.widget.TextView textViewVerifiedBadge;
    private android.widget.TextView textViewUserLevel;
    private android.widget.ProgressBar progressBarUserLevel;
    private android.widget.TextView textViewUserXP;
    private com.google.android.material.textfield.TextInputLayout inputLayoutPronouns;
    private com.google.android.material.textfield.TextInputEditText editTextPronouns;
    private android.widget.TextView textViewBioLabel;
    private android.widget.TextView textViewBioValue;
    private com.google.android.material.textfield.TextInputLayout inputLayoutBio;
    private com.google.android.material.textfield.TextInputEditText editTextBio;
    private android.widget.LinearLayout viewModeDetailsLayout;
    private android.widget.TextView textViewJobValue;
    private android.widget.TextView textViewSchoolValue;
    private android.widget.TextView textViewLocationValue;
    private android.widget.TextView textViewFavGameValue;
    private android.widget.TextView textViewFavPlatformValue;
    private android.widget.TextView textViewLookingForValue;
    private android.widget.TextView textViewBirthdateValue;
    private android.widget.TextView textViewGenderValue;
    private android.widget.TextView textViewSexualityValue;
    private android.widget.TextView textViewCityCountryValue;
    private android.widget.LinearLayout editModeDetailsLayout;
    private com.google.android.material.textfield.TextInputLayout inputLayoutJob;
    private com.google.android.material.textfield.TextInputEditText editTextJob;
    private com.google.android.material.textfield.TextInputLayout inputLayoutSchool;
    private com.google.android.material.textfield.TextInputEditText editTextSchool;
    private com.google.android.material.textfield.TextInputLayout inputLayoutLocation;
    private com.google.android.material.textfield.TextInputEditText editTextLocation;
    private com.google.android.material.textfield.TextInputLayout inputLayoutFavoriteGame;
    private com.google.android.material.textfield.TextInputEditText editTextFavoriteGame;
    private com.google.android.material.textfield.TextInputLayout inputLayoutFavoritePlatform;
    private com.google.android.material.textfield.TextInputEditText editTextFavoritePlatform;
    private com.google.android.material.textfield.TextInputLayout inputLayoutLookingFor;
    private com.google.android.material.textfield.TextInputEditText editTextLookingFor;
    private com.google.android.material.textfield.TextInputLayout inputLayoutBirthdate;
    private com.google.android.material.textfield.TextInputEditText editTextBirthdate;
    private com.google.android.material.textfield.TextInputLayout inputLayoutGender;
    private android.widget.AutoCompleteTextView editTextGender;
    private com.google.android.material.textfield.TextInputLayout inputLayoutSexuality;
    private android.widget.AutoCompleteTextView editTextSexuality;
    private android.widget.Button buttonEditSave;
    private android.widget.Button buttonLogout;
    private android.widget.Button buttonBlockedUsers;
    private android.widget.Button buttonBlockUser;
    private android.widget.Button buttonReportUser;
    private com.google.android.material.bottomnavigation.BottomNavigationView bottomNavView;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient client = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler statusHandler = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.Runnable statusRunnable;
    private long lastStatusCheck = 0L;
    private long statusCheckInterval = 30000L;
    private android.widget.TextView textViewOnlineStatus;
    private androidx.appcompat.widget.Toolbar toolbar;
    private androidx.viewpager2.widget.ViewPager2 viewPagerImages;
    private com.google.android.material.tabs.TabLayout tabLayoutDots;
    private android.widget.Button buttonSetAsMain;
    private com.google.android.material.floatingactionbutton.FloatingActionButton fabAddImage;
    private com.spyro.vmeet.ProfileImageAdapter profileImageAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> profileImages = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String mainImageUrl;
    private final int MAX_PROFILE_IMAGES = 5;
    private boolean isUserBlocked = false;
    private com.google.android.material.button.MaterialButton buttonSendMessage;
    private androidx.recyclerview.widget.RecyclerView recyclerViewPersonalityTraits;
    private com.google.android.material.button.MaterialButton buttonEditTraits;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.PersonalityTrait> personalityTraits = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ProfileActivity";
    private static final int EDIT_TRAITS_REQUEST_CODE = 1001;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.concurrent.ConcurrentHashMap<java.lang.Integer, kotlin.Pair<java.lang.String, java.lang.String>> locationCache = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.ProfileActivity.Companion Companion = null;
    
    public ProfileActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final android.view.View createCompatibilityLayout() {
        return null;
    }
    
    private final void initializeViews() {
    }
    
    private final void switchToViewMode() {
    }
    
    private final void switchToEditMode() {
    }
    
    private final void saveProfileData() {
    }
    
    private final void updateTextViewsFromEditTexts() {
    }
    
    private final boolean checkStoragePermission() {
        return false;
    }
    
    private final void requestStoragePermission() {
    }
    
    @java.lang.Override()
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull()
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull()
    int[] grantResults) {
    }
    
    private final void openImagePicker() {
    }
    
    private final void uploadAvatar(android.net.Uri imageUri) {
    }
    
    private final void revertAvatarImage() {
    }
    
    private final java.lang.String getPathFromUri(android.net.Uri uri) {
        return null;
    }
    
    private final void setupBottomNavigation(int loggedInUserId) {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    private final void loadProfile(int userId) {
    }
    
    private final void populateUI(org.json.JSONObject user) {
    }
    
    private final java.lang.Integer calculateAge(java.lang.String birthdateString) {
        return null;
    }
    
    private final java.lang.String formatBirthdateForInput(java.lang.String birthdateString) {
        return null;
    }
    
    private final java.lang.String formatBirthdateForDisplay(java.lang.String birthdateString) {
        return null;
    }
    
    private final void logoutUser() {
    }
    
    private final void showDatePickerDialog() {
    }
    
    private final void startStatusUpdates(int userId) {
    }
    
    private final void stopStatusUpdates() {
    }
    
    private final void checkUserStatus(int userId) {
    }
    
    private final void updateStatusDisplay(boolean online, java.lang.String lastSeen) {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    @java.lang.Override()
    public boolean onCreateOptionsMenu(@org.jetbrains.annotations.NotNull()
    android.view.Menu menu) {
        return false;
    }
    
    @java.lang.Override()
    public boolean onPrepareOptionsMenu(@org.jetbrains.annotations.NotNull()
    android.view.Menu menu) {
        return false;
    }
    
    @java.lang.Override()
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull()
    android.view.MenuItem item) {
        return false;
    }
    
    private final void showBlockUserConfirmation() {
    }
    
    private final void blockUser() {
    }
    
    private final void setMainProfileImage(int position) {
    }
    
    private final void showImageOptions(int position) {
    }
    
    private final void deleteProfileImage(int position) {
    }
    
    private final void deleteImageById(int userId, int imageId, int position, android.widget.ProgressBar progressBar) {
    }
    
    private final void findAndDeleteImageByUrl(int userId, java.lang.String imageUrlToDelete, int position, android.widget.ProgressBar progressBar) {
    }
    
    private final void deleteImageByUrl(int userId, java.lang.String imageUrlToDelete, int position, android.widget.ProgressBar progressBar) {
    }
    
    private final void updateProfileToRemoveImage(int userId, java.lang.String imageUrlToRemove, int position, android.widget.ProgressBar progressBar) {
    }
    
    private final java.lang.Integer extractImageIdFromUrl(java.lang.String imageUrl) {
        return null;
    }
    
    private final void openFullScreenImage(int position) {
    }
    
    private final void showProfileCompletionTips() {
    }
    
    private final void checkAdminStatus(int userId) {
    }
    
    private final void checkUserRole(int userId, org.json.JSONObject userObject) {
    }
    
    private final void checkIfUserIsBlocked() {
    }
    
    private final void updateBlockButtonState() {
    }
    
    private final void showUnblockUserConfirmation() {
    }
    
    private final void unblockUser() {
    }
    
    private final void showReportUserDialog() {
    }
    
    private final void submitReport(java.lang.String reason, java.lang.String details) {
    }
    
    private final void openChatWithUser() {
    }
    
    private final boolean checkIfViewerIsAdmin() {
        return false;
    }
    
    private final void createDirectChat(int currentUserId, int otherUserId, java.lang.String avatarUrl) {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    private final void checkIfUserAcceptsMessages(int userId, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    private final void setupListeners() {
    }
    
    private final void checkAndShowProfileTips() {
    }
    
    private final void setupPersonalityTraitsRecyclerView() {
    }
    
    private final void openEditPersonalityTraits() {
    }
    
    private final void loadPersonalityTraits() {
    }
    
    private final void updatePersonalityTraitsDisplay(java.util.List<java.lang.String> selectedTraitIds) {
    }
    
    private final java.util.List<com.spyro.vmeet.adapter.TraitCategory> organizeCategoriesForDisplay(java.util.List<com.spyro.vmeet.data.PersonalityTrait> selectedTraits) {
        return null;
    }
    
    @java.lang.Override()
    protected void onActivityResult(int requestCode, int resultCode, @org.jetbrains.annotations.Nullable()
    android.content.Intent data) {
    }
    
    /**
     * Format location string with city and country information
     *
     * @param location The user's original location string (if provided)
     * @param city The user's city
     * @param country The user's country
     * @return Formatted location string
     */
    private final java.lang.String formatLocation(java.lang.String location, java.lang.String city, java.lang.String country) {
        return null;
    }
    
    /**
     * Actualiza los datos de ubicación del usuario desde el radar
     * Esta función debería ser llamada cuando recibimos datos actualizados del radar
     */
    public final void updateLocationData(@org.jetbrains.annotations.Nullable()
    java.lang.String city, @org.jetbrains.annotations.Nullable()
    java.lang.String country) {
    }
    
    /**
     * Obtiene la información de ubicación desde el radar para un usuario específico
     */
    private final void fetchLocationFromRadar(int userId) {
    }
    
    /**
     * Intenta obtener la ubicación desde una fuente alternativa si el radar falla
     */
    private final void fetchLocationFromAlternativeSource(int userId) {
    }
    
    /**
     * Guarda la información de ubicación de un usuario en SharedPreferences
     */
    private final void saveUserLocation(int userId, java.lang.String city, java.lang.String country) {
    }
    
    /**
     * Recupera la información de ubicación de un usuario desde SharedPreferences
     * @return Pair<String?, String?> con (city, country) o null si no hay datos
     */
    private final kotlin.Pair<java.lang.String, java.lang.String> loadUserLocation(int userId) {
        return null;
    }
    
    private final void checkVerificationStatus(int userId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R*\u0010\u0007\u001a\u001e\u0012\u0004\u0012\u00020\u0004\u0012\u0014\u0012\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0006\u0012\u0006\u0012\u0004\u0018\u00010\u00060\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/spyro/vmeet/ProfileActivity$Companion;", "", "()V", "EDIT_TRAITS_REQUEST_CODE", "", "TAG", "", "locationCache", "Ljava/util/concurrent/ConcurrentHashMap;", "Lkotlin/Pair;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J;\u0010\u0003\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\u0010\t\u001a\u0004\u0018\u00010\u00042\u000e\u0010\n\u001a\n\u0012\u0004\u0012\u00020\u0004\u0018\u00010\u000bH\u0002\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\r\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0007\u001a\u00020\bH\u0002J\u0010\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0007\u001a\u00020\bH\u0002\u00a8\u0006\u0013"}, d2 = {"Lcom/spyro/vmeet/ProfileActivity$FileUtils;", "", "()V", "getDataColumn", "", "context", "Landroid/content/Context;", "uri", "Landroid/net/Uri;", "selection", "selectionArgs", "", "(Landroid/content/Context;Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)Ljava/lang/String;", "getPath", "isDownloadsDocument", "", "isExternalStorageDocument", "isGooglePhotosUri", "isMediaDocument", "app_debug"})
    public static final class FileUtils {
        @org.jetbrains.annotations.NotNull()
        public static final com.spyro.vmeet.ProfileActivity.FileUtils INSTANCE = null;
        
        private FileUtils() {
            super();
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.String getPath(@org.jetbrains.annotations.NotNull()
        android.content.Context context, @org.jetbrains.annotations.NotNull()
        android.net.Uri uri) {
            return null;
        }
        
        private final java.lang.String getDataColumn(android.content.Context context, android.net.Uri uri, java.lang.String selection, java.lang.String[] selectionArgs) {
            return null;
        }
        
        private final boolean isExternalStorageDocument(android.net.Uri uri) {
            return false;
        }
        
        private final boolean isDownloadsDocument(android.net.Uri uri) {
            return false;
        }
        
        private final boolean isMediaDocument(android.net.Uri uri) {
            return false;
        }
        
        private final boolean isGooglePhotosUri(android.net.Uri uri) {
            return false;
        }
    }
}