<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Announcement banner with marquee text -->
    <TextView
        android:id="@+id/textViewAnnouncement"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="#1A4266"
        android:padding="8dp"
        android:text="Mantén un comportamiento respetuoso en todas las salas de chat y diviértete. VMeet es un espacio para conectar con otros y hacer nuevos amigos."
        android:textColor="@color/white"
        android:textSize="14sp"
        android:singleLine="true"
        android:ellipsize="marquee"
        android:marqueeRepeatLimit="marquee_forever"
        android:scrollHorizontally="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:freezesText="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewChatRooms"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/textViewAnnouncement"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:listitem="@layout/item_chat_room" />

    <TextView
        android:id="@+id/textViewEmptyRooms"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="No hay salas disponibles.\nLos administradores pueden crear salas de chat grupales."
        android:textColor="@color/white"
        android:visibility="gone"
        android:gravity="center"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="@id/recyclerViewChatRooms"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/progressBarRooms"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/recyclerViewChatRooms"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabCreateRoom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        app:tint="@android:color/white"
        app:backgroundTint="@color/neon_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 