1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.spyro.vmeet"
4    android:versionCode="31"
5    android:versionName="0.92" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:5-66
11-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:5-106
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:6:78-104
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:7:22-73
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:5-70
16-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:8:22-68
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:5-10:50
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:9:22-78
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:10:22-48
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:5-74
20-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:11:22-72
21    <uses-permission android:name="android.permission.VIBRATE" />
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:5-65
21-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:12:22-63
22    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:5-76
22-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:13:22-74
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:5-64
23-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:14:22-62
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:5-74
24-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:15:22-72
25
26    <!-- Location permissions for Radar feature - ONLY foreground access -->
27    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:5-78
27-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:18:22-76
28    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:5-80
28-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:19:22-78
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:5-79
29-->[androidx.media3:media3-exoplayer:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d54c6bc7ec49a885f32785c74a047713\transformed\media3-exoplayer-1.3.0\AndroidManifest.xml:22:22-76
30
31    <uses-feature
31-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
32        android:glEsVersion="0x00020000"
32-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
33        android:required="true" />
33-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
34
35    <queries>
35-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
36
37        <!-- Needs to be explicitly declared on Android R+ -->
38        <package android:name="com.google.android.apps.maps" />
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
39    </queries>
40
41    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:5-68
41-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:24:22-65
42    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:5-82
42-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:26:22-79
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:25:22-76
44    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:5-88
44-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:26:22-85
45    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:5-82
45-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:27:22-79
46    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:5-110
46-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:26:22-107
47
48    <permission
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
49        android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.spyro.vmeet.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
53
54    <application
54-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:21:5-184:19
55        android:name="com.spyro.vmeet.VMeetApplication"
55-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:22:9-41
56        android:allowBackup="true"
56-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:23:9-35
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\40bc0ded6ebc3196905947594b68c381\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:dataExtractionRules="@xml/data_extraction_rules"
58-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:24:9-65
59        android:extractNativeLibs="false"
60        android:fullBackupContent="@xml/backup_rules"
60-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:25:9-54
61        android:icon="@mipmap/ic_launcher"
61-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:26:9-43
62        android:label="@string/app_name"
62-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:27:9-41
63        android:networkSecurityConfig="@xml/network_security_config"
63-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:31:9-69
64        android:roundIcon="@mipmap/ic_launcher_round"
64-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:28:9-54
65        android:supportsRtl="true"
65-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:29:9-35
66        android:theme="@style/Theme.VMeet" >
66-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:30:9-43
67        <activity
67-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:33:9-42:20
68            android:name="com.spyro.vmeet.MainActivity"
68-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:34:13-41
69            android:exported="true"
69-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:35:13-36
70            android:label="@string/app_name"
70-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:36:13-45
71            android:theme="@style/Theme.VMeet" >
71-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:37:13-47
72            <intent-filter>
72-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:38:13-41:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:17-69
73-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:39:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:17-77
75-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:40:27-74
76            </intent-filter>
77        </activity>
78        <activity android:name="com.spyro.vmeet.LoginActivity" />
78-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:9-51
78-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:43:19-48
79        <activity android:name="com.spyro.vmeet.TutorialActivity" />
79-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:9-54
79-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:44:19-51
80        <activity
80-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:45:9-47:40
81            android:name="com.spyro.vmeet.ProfileActivity"
81-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:46:13-44
82            android:exported="false" />
82-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:47:13-37
83        <activity
83-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:48:9-50:40
84            android:name="com.spyro.vmeet.BlockedUsersActivity"
84-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:49:13-49
85            android:exported="false" />
85-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:50:13-37
86        <activity android:name="com.spyro.vmeet.SwipeActivity" />
86-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:9-51
86-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:51:19-48
87        <activity android:name="com.spyro.vmeet.MatchesActivity" />
87-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:9-53
87-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:52:19-50
88        <activity android:name="com.spyro.vmeet.CommunityHostActivity" />
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:9-59
88-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:53:19-56
89        <activity android:name="com.spyro.vmeet.ui.community.CommentsActivity" />
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:9-67
89-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:54:19-64
90        <activity android:name="com.spyro.vmeet.ui.community.StoryEditorActivity" />
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:55:9-56:64
90-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:56:13-61
91        <activity
91-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:57:9-60:76
92            android:name="com.spyro.vmeet.activity.BlindDateActivity"
92-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:58:13-55
93            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
93-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:59:13-83
94            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
94-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:60:13-73
95        <activity
95-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:61:9-64:76
96            android:name="com.spyro.vmeet.activity.ChatActivity"
96-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:62:13-50
97            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
97-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:63:13-83
98            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
98-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:64:13-73
99        <activity android:name="com.spyro.vmeet.activity.ChatListActivity" />
99-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:9-63
99-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:65:19-60
100        <activity android:name="com.spyro.vmeet.activity.SettingsActivity" />
100-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:9-63
100-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:66:19-60
101
102        <!-- Email verification and password reset activities -->
103        <activity android:name="com.spyro.vmeet.activity.EmailVerificationActivity" />
103-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:9-72
103-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:69:19-69
104        <activity android:name="com.spyro.vmeet.activity.PasswordResetActivity" />
104-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:9-68
104-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:70:19-65
105        <activity android:name="com.spyro.vmeet.activity.ForgotPasswordActivity" />
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:9-69
105-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:71:19-66
106
107        <!-- Chat Room Loader - handles safe transition to chat room -->
108        <activity
108-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:74:9-78:58
109            android:name="com.spyro.vmeet.activity.RoomLoaderActivity"
109-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:75:13-56
110            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
110-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:77:13-83
111            android:theme="@style/Theme.VMeet.NoActionBar"
111-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:76:13-59
112            android:windowSoftInputMode="adjustResize" />
112-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:78:13-55
113
114        <!-- Chat Room Activity for group chat functionality -->
115        <activity
115-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:81:9-87:50
116            android:name="com.spyro.vmeet.activity.ChatRoomActivity"
116-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:82:13-54
117            android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
117-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:83:13-83
118            android:excludeFromRecents="false"
118-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:87:13-47
119            android:launchMode="singleTop"
119-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:85:13-43
120            android:taskAffinity=""
120-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:86:13-36
121            android:windowSoftInputMode="adjustResize|stateAlwaysHidden" />
121-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:84:13-73
122
123        <!-- Video feature activities -->
124        <activity
124-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:90:9-94:61
125            android:name="com.spyro.vmeet.VideoFeedActivity"
125-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:91:13-46
126            android:configChanges="orientation|screenSize|keyboardHidden"
126-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:92:13-74
127            android:screenOrientation="portrait"
127-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:93:13-49
128            android:theme="@style/Theme.VMeet.NoActionBar" />
128-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:94:13-59
129        <activity
129-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:95:9-97:61
130            android:name="com.spyro.vmeet.UploadVideoActivity"
130-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:96:13-48
131            android:theme="@style/Theme.VMeet.NoActionBar" />
131-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:97:13-59
132        <activity
132-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:98:9-100:61
133            android:name="com.spyro.vmeet.MyVideosActivity"
133-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:99:13-45
134            android:theme="@style/Theme.VMeet.NoActionBar" />
134-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:100:13-59
135
136        <!-- Admin panel activities -->
137        <activity
137-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:103:9-105:55
138            android:name="com.spyro.vmeet.AdminPanelActivity"
138-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:104:13-47
139            android:label="Panel de Administración" />
139-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:105:13-52
140        <activity
140-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:106:9-108:51
141            android:name="com.spyro.vmeet.UserManagementActivity"
141-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:107:13-51
142            android:label="Gestión de Usuarios" />
142-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:108:13-48
143        <activity
143-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:109:9-111:52
144            android:name="com.spyro.vmeet.ReportsActivity"
144-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:110:13-44
145            android:label="Reportes de Usuarios" />
145-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:111:13-49
146
147        <!-- Full-screen image viewer activity with a special theme for immersive viewing -->
148        <activity
148-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:114:9-116:66
149            android:name="com.spyro.vmeet.FullScreenImageActivity"
149-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:115:13-52
150            android:theme="@style/Theme.AppCompat.NoActionBar" />
150-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:116:13-63
151
152        <!-- Story Viewer Activity -->
153        <activity
153-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:119:9-121:66
154            android:name="com.spyro.vmeet.ui.community.StoryViewerActivity"
154-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:120:13-61
155            android:theme="@style/Theme.AppCompat.NoActionBar" />
155-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:121:13-63
156
157        <!-- User Profile Activity -->
158        <activity
158-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:124:9-126:49
159            android:name="com.spyro.vmeet.activity.UserProfileActivity"
159-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:125:13-57
160            android:label="Perfil de Usuario" />
160-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:126:13-46
161
162        <!-- Edit Personality Traits Activity -->
163        <activity
163-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:129:9-131:61
164            android:name="com.spyro.vmeet.activity.EditPersonalityTraitsActivity"
164-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:130:13-67
165            android:label="Editar Rasgos de Personalidad" />
165-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:131:13-58
166
167        <!-- Community Guidelines Activity -->
168        <activity
168-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:134:9-136:54
169            android:name="com.spyro.vmeet.activity.CommunityGuidelinesActivity"
169-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:135:13-65
170            android:label="Normas de la comunidad" />
170-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:136:13-51
171
172        <!-- User Verification Activity -->
173        <activity
173-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:139:9-142:62
174            android:name="com.spyro.vmeet.VerificationActivity"
174-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:140:13-49
175            android:label="Verificación de Usuario"
175-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:141:13-52
176            android:theme="@style/Theme.VMeet.NoActionBar" />
176-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:142:13-59
177        <activity
177-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:144:9-147:62
178            android:name="com.spyro.vmeet.VerificationManagementActivity"
178-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:145:13-59
179            android:label="Gestión de Verificaciones"
179-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:146:13-54
180            android:theme="@style/Theme.VMeet.NoActionBar" />
180-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:147:13-59
181
182        <!-- Firebase Cloud Messaging Service -->
183        <service
183-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:150:9-156:19
184            android:name="com.spyro.vmeet.notifications.VMeetFirebaseMessagingService"
184-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:151:13-72
185            android:exported="false" >
185-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:152:13-37
186            <intent-filter>
186-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:153:13-155:29
187                <action android:name="com.google.firebase.MESSAGING_EVENT" />
187-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:17-78
187-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:25-75
188            </intent-filter>
189        </service>
190
191        <!-- BroadcastReceiver for notification actions -->
192        <receiver
192-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:159:9-166:20
193            android:name="com.spyro.vmeet.notifications.MessageActionReceiver"
193-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:160:13-64
194            android:exported="false" >
194-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:161:13-37
195            <intent-filter>
195-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:162:13-165:29
196                <action android:name="com.spyro.vmeet.ACTION_MARK_AS_READ" />
196-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:163:17-78
196-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:163:25-75
197                <action android:name="com.spyro.vmeet.ACTION_REPLY" />
197-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:17-71
197-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:164:25-68
198            </intent-filter>
199        </receiver>
200
201        <!-- Sample AdMob App ID. Replace with your real App ID before production. -->
202        <meta-data
202-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:169:9-171:69
203            android:name="com.google.android.gms.ads.APPLICATION_ID"
203-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:170:13-69
204            android:value="ca-app-pub-3940256099942544~**********" />
204-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:171:13-67
205
206        <!-- FileProvider for sharing images from camera -->
207        <provider
208            android:name="androidx.core.content.FileProvider"
208-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:175:13-62
209            android:authorities="com.spyro.vmeet.provider"
209-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:176:13-60
210            android:exported="false"
210-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:177:13-37
211            android:grantUriPermissions="true" >
211-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:178:13-47
212            <meta-data
212-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:179:13-181:54
213                android:name="android.support.FILE_PROVIDER_PATHS"
213-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:180:17-67
214                android:resource="@xml/file_paths" />
214-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:181:17-51
215        </provider>
216        <provider
216-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:24:9-32:20
217            android:name="androidx.startup.InitializationProvider"
217-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:25:13-67
218            android:authorities="com.spyro.vmeet.androidx-startup"
218-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:26:13-68
219            android:exported="false" >
219-->[androidx.emoji2:emoji2-bundled:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cbc53e89e7d7086e3c97d44234b6c3a\transformed\emoji2-bundled-1.4.0\AndroidManifest.xml:27:13-37
220            <meta-data
220-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
221                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
221-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
222                android:value="androidx.startup" />
222-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c9d1e906c1d5a3613b361301621b0e1\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
223            <meta-data
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
224                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
225                android:value="androidx.startup" />
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
226        </provider> <!-- Needs to be explicitly declared on P+ -->
227        <uses-library
227-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
228            android:name="org.apache.http.legacy"
228-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
229            android:required="false" />
229-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f154c736d32c29ef5ac34148b1bc1839\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
230
231        <service
231-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:23:9-29:19
232            android:name="com.google.firebase.components.ComponentDiscoveryService"
232-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:24:13-84
233            android:directBootAware="true"
233-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
234            android:exported="false" >
234-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:25:13-37
235            <meta-data
235-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:26:13-28:85
236                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
236-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:27:17-129
237                android:value="com.google.firebase.components.ComponentRegistrar" />
237-->[com.google.firebase:firebase-messaging-ktx:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9731524e1da4e59f621ad584aab45cc8\transformed\firebase-messaging-ktx-23.4.1\AndroidManifest.xml:28:17-82
238            <meta-data
238-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
239                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
239-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
240                android:value="com.google.firebase.components.ComponentRegistrar" />
240-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
241            <meta-data
241-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
242                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
242-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
243                android:value="com.google.firebase.components.ComponentRegistrar" />
243-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
244            <meta-data
244-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:11:13-13:85
245                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
245-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:12:17-129
246                android:value="com.google.firebase.components.ComponentRegistrar" />
246-->[com.google.firebase:firebase-analytics-ktx:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6020e15454eb3023abfd43119355834\transformed\firebase-analytics-ktx-21.5.1\AndroidManifest.xml:13:17-82
247            <meta-data
247-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:37:13-39:85
248                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
248-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:38:17-139
249                android:value="com.google.firebase.components.ComponentRegistrar" />
249-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:39:17-82
250            <meta-data
250-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
251                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
251-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
252                android:value="com.google.firebase.components.ComponentRegistrar" />
252-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
253            <meta-data
253-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
254                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
254-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
255                android:value="com.google.firebase.components.ComponentRegistrar" />
255-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d61a38323c5a9566e7139267b102e96b\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
256            <meta-data
256-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
257                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
257-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
258                android:value="com.google.firebase.components.ComponentRegistrar" />
258-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd45ed32078b57511206f1e17b1efdaf\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
259            <meta-data
259-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
260                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
260-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
261                android:value="com.google.firebase.components.ComponentRegistrar" />
261-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
262            <meta-data
262-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
263                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
263-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
264                android:value="com.google.firebase.components.ComponentRegistrar" />
264-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3bd91aa156a248205bf161d511a23daf\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
265        </service>
266
267        <receiver
267-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
268            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
268-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
269            android:exported="true"
269-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
270            android:permission="com.google.android.c2dm.permission.SEND" >
270-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
271            <intent-filter>
271-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:33:13-35:29
272                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
272-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:17-81
272-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:34:25-78
273            </intent-filter>
274
275            <meta-data
275-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
276                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
276-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
277                android:value="true" />
277-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
278        </receiver>
279        <!--
280             FirebaseMessagingService performs security checks at runtime,
281             but set to not exported to explicitly avoid allowing another app to call it.
282        -->
283        <service
283-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
284            android:name="com.google.firebase.messaging.FirebaseMessagingService"
284-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
285            android:directBootAware="true"
285-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
286            android:exported="false" >
286-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43c42daca05d9341a9841d39d7242133\transformed\firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
287            <intent-filter android:priority="-500" >
287-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:153:13-155:29
288                <action android:name="com.google.firebase.MESSAGING_EVENT" />
288-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:17-78
288-->C:\Users\<USER>\AndroidStudioProjects\VMeet\VMeet\app\src\main\AndroidManifest.xml:154:25-75
289            </intent-filter>
290        </service>
291
292        <activity
292-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
293            android:name="com.google.android.gms.common.api.GoogleApiActivity"
293-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
294            android:exported="false"
294-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
295            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
295-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\330d2aa732692cb12a9344e8c098a4ae\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
296
297        <property
297-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:30:9-32:61
298            android:name="android.adservices.AD_SERVICES_CONFIG"
298-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:31:13-65
299            android:resource="@xml/ga_ad_services_config" />
299-->[com.google.android.gms:play-services-measurement-api:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4044055c75f69b61473048462fb40967\transformed\play-services-measurement-api-21.5.1\AndroidManifest.xml:32:13-58
300
301        <provider
301-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
302            android:name="com.google.firebase.provider.FirebaseInitProvider"
302-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
303            android:authorities="com.spyro.vmeet.firebaseinitprovider"
303-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
304            android:directBootAware="true"
304-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
305            android:exported="false"
305-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
306            android:initOrder="100" />
306-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\184c15c4bf33c5ed9613750443476ffa\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
307
308        <receiver
308-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:29:9-33:20
309            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
309-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:30:13-85
310            android:enabled="true"
310-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:31:13-35
311            android:exported="false" >
311-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:32:13-37
312        </receiver>
313
314        <service
314-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:35:9-38:40
315            android:name="com.google.android.gms.measurement.AppMeasurementService"
315-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:36:13-84
316            android:enabled="true"
316-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:37:13-35
317            android:exported="false" />
317-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:38:13-37
318        <service
318-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:39:9-43:72
319            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
319-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:40:13-87
320            android:enabled="true"
320-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:41:13-35
321            android:exported="false"
321-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:42:13-37
322            android:permission="android.permission.BIND_JOB_SERVICE" />
322-->[com.google.android.gms:play-services-measurement:21.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47dda889edf76cfbc336fa5a65994766\transformed\play-services-measurement-21.5.1\AndroidManifest.xml:43:13-69
323
324        <uses-library
324-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
325            android:name="android.ext.adservices"
325-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
326            android:required="false" />
326-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00702924910c308c285b87ba7ed324df\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
327
328        <meta-data
328-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
329            android:name="com.google.android.gms.version"
329-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
330            android:value="@integer/google_play_services_version" />
330-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\802769e58155747426a628dc0cc15a1d\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
331
332        <receiver
332-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
333            android:name="androidx.profileinstaller.ProfileInstallReceiver"
333-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
334            android:directBootAware="false"
334-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
335            android:enabled="true"
335-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
336            android:exported="true"
336-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
337            android:permission="android.permission.DUMP" >
337-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
339                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
340            </intent-filter>
341            <intent-filter>
341-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
342                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
342-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
343            </intent-filter>
344            <intent-filter>
344-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
345                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
345-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
346            </intent-filter>
347            <intent-filter>
347-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
348                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
348-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5511e075652f41a40f02f3d7c21e64b3\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
349            </intent-filter>
350        </receiver>
351
352        <service
352-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
353            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
353-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
354            android:exported="false" >
354-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
355            <meta-data
355-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
356                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
356-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
357                android:value="cct" />
357-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\094f59c6c409f0abc53a8edbcbae85c5\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
358        </service>
359        <service
359-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
360            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
360-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
361            android:exported="false"
361-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
362            android:permission="android.permission.BIND_JOB_SERVICE" >
362-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
363        </service>
364
365        <receiver
365-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
366            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
366-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
367            android:exported="false" />
367-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6bda6848383e297a3c55071bdb9b480e\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
368    </application>
369
370</manifest>
