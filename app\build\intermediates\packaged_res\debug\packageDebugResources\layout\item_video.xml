<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.media3.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:use_controller="false" />

    <!-- Action Buttons (Right Side) -->
    <LinearLayout
        android:id="@+id/layoutActions"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="180dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Like Button -->
        <LinearLayout
            android:id="@+id/layoutLikes"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageLike"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/circular_button_background"
                android:padding="12dp"
                android:src="@drawable/ic_heart"
                app:tint="@color/neon_pink" />

            <TextView
                android:id="@+id/textLikes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="1.2K" />
        </LinearLayout>

        <!-- Comments Button -->
        <LinearLayout
            android:id="@+id/layoutComments"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageComment"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/circular_button_background"
                android:padding="12dp"
                android:src="@drawable/ic_comment_outline"
                app:tint="@color/neon_blue" />

            <TextView
                android:id="@+id/textComments"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="234" />
        </LinearLayout>

        <!-- Views Counter -->
        <LinearLayout
            android:id="@+id/layoutViews"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageViews"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/circular_button_background"
                android:padding="12dp"
                android:src="@drawable/ic_eye"
                app:tint="@color/neon_green" />

            <TextView
                android:id="@+id/textViews"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="10.5K" />
        </LinearLayout>
    </LinearLayout>

    <!-- Video Description -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="24dp"
        android:orientation="vertical"
        android:background="@drawable/gradient_overlay"
        android:padding="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/layoutActions"
        app:layout_constraintStart_toStartOf="parent">

        <!-- User Info with Avatar -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/imageUserAvatar"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/default_avatar"
                app:civ_border_width="1dp"
                app:civ_border_color="@color/neon_blue" />

            <TextView
                android:id="@+id/textUsername"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:shadowColor="@color/black"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="2"
                tools:text="\@username" />

        </LinearLayout>

        <TextView
            android:id="@+id/textDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:shadowColor="@color/black"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2"
            android:maxLines="3"
            android:ellipsize="end"
            tools:text="Video description goes here..." />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
