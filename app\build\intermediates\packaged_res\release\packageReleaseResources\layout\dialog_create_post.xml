<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="@color/darker_blue"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    app:strokeWidth="2dp"
    app:strokeColor="@color/neon_blue">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Crear publicación"
            android:textColor="@color/neon_blue"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/neon_blue"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="16dp" />

        <!-- Post Content -->
        <EditText
            android:id="@+id/editTextPostContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="120dp"
            android:gravity="top|start"
            android:hint="¡Comparte algo que mole!"
            android:textColorHint="@color/cyberpunk_text_secondary"
            android:textColor="@color/cyberpunk_text_primary"
            android:background="@drawable/post_edit_text_background"
            android:padding="12dp"
            android:inputType="textMultiLine|textCapSentences" />

        <!-- Image Preview -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewImagePreview"
            android:layout_width="match_parent"
            android:layout_height="180dp"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/imageViewPreview"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:scaleType="centerCrop"
                    android:contentDescription="Preview of selected image" />

                <ImageButton
                    android:id="@+id/buttonRemoveImage"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:layout_gravity="end|top"
                    android:layout_margin="8dp"
                    android:src="@android:drawable/ic_menu_close_clear_cancel"
                    android:background="@drawable/cyberpunk_circle_button"
                    app:tint="@color/white" />
            </FrameLayout>
        </androidx.cardview.widget.CardView>

        <!-- Voice Note Info and Remove Button -->
        <LinearLayout
            android:id="@+id/layout_voice_note_controls_create_post"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="12dp"
            android:visibility="gone" 
            tools:visibility="visible">

            <TextView
                android:id="@+id/textViewVoiceNoteInfo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Nota de voz no adjunta"
                android:textColor="@color/cyberpunk_text_secondary"
                android:textSize="14sp"
                tools:text="Nota de voz adjunta: 35s"/>

            <ImageButton
                android:id="@+id/buttonRemoveVoiceNote"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_close_circle" 
                android:background="?attr/selectableItemBackgroundBorderless"
                app:tintMode="src_in"
                app:tint="@color/cyberpunk_text_secondary"
                android:contentDescription="Eliminar nota de voz"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <!-- Media Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp">

            <ImageButton
                android:id="@+id/buttonAddImage"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_add_image"
                android:background="?attr/selectableItemBackgroundBorderless"
                app:tint="@color/neon_green" />

            <ImageButton
                android:id="@+id/buttonAddGif"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_add_gif"
                android:background="?attr/selectableItemBackgroundBorderless"
                app:tint="@color/neon_purple"
                android:layout_marginStart="16dp" />

            <ImageButton
                android:id="@+id/buttonAddVoice"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_add_mic"
                android:background="?attr/selectableItemBackgroundBorderless"
                app:tint="@color/neon_pink"
                android:layout_marginStart="16dp" />

            <Space
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <Button
                android:id="@+id/buttonPostSubmit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Publicar"
                android:textColor="@color/white"
                android:backgroundTint="@color/neon_blue" />
        </LinearLayout>

    </LinearLayout>
</androidx.cardview.widget.CardView> 