package com.spyro.vmeet.utils

import android.content.Context
import android.util.Log
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.spyro.vmeet.R
import com.spyro.vmeet.ui.VerificationBadge
import okhttp3.*
import org.json.JSONObject
import java.io.IOException

object VerificationHelper {
    private const val TAG = "VerificationHelper"
    private const val API_URL = "http://77.110.116.89:3000"
    private val client = OkHttpClient()

    interface VerificationStatusCallback {
        fun onStatusReceived(status: String, isVerified: Boolean)
        fun onError(error: String)
    }

    /**
     * Check verification status for a user and update UI accordingly
     */
    fun checkVerificationStatus(
        userId: Int,
        callback: VerificationStatusCallback? = null
    ) {
        val request = Request.Builder()
            .url("$API_URL/verification/status/$userId")
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                Log.e(TAG, "Error checking verification status for user $userId", e)
                callback?.onError("Error de conexión")
            }

            override fun onResponse(call: Call, response: Response) {
                response.body?.let { responseBody ->
                    val responseText = responseBody.string()
                    try {
                        val jsonResponse = JSONObject(responseText)
                        val status = jsonResponse.optString("status", "not_verified")
                        val verifiedStatus = jsonResponse.optString("verified_status", "not_verified")
                        val isVerified = status == "approved"

                        Log.d(TAG, "User $userId verification status: $status, verified: $isVerified")
                        callback?.onStatusReceived(status, isVerified)

                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing verification status response", e)
                        callback?.onError("Error al procesar respuesta")
                    }
                }
            }
        })
    }

    /**
     * Update a VerificationBadge with user's verification status
     */
    fun updateVerificationBadge(
        userId: Int,
        badge: VerificationBadge,
        context: Context
    ) {
        checkVerificationStatus(userId, object : VerificationStatusCallback {
            override fun onStatusReceived(status: String, isVerified: Boolean) {
                // Update UI on main thread
                if (context is android.app.Activity) {
                    context.runOnUiThread {
                        badge.setVerificationStatus(status)
                    }
                }
            }

            override fun onError(error: String) {
                // Hide badge on error
                if (context is android.app.Activity) {
                    context.runOnUiThread {
                        badge.hide()
                    }
                }
            }
        })
    }

    /**
     * Update a regular ImageView with verification badge
     */
    fun updateVerificationImageView(
        userId: Int,
        imageView: ImageView,
        context: Context
    ) {
        checkVerificationStatus(userId, object : VerificationStatusCallback {
            override fun onStatusReceived(status: String, isVerified: Boolean) {
                // Update UI on main thread
                if (context is android.app.Activity) {
                    context.runOnUiThread {
                        when (status) {
                            "approved" -> {
                                imageView.visibility = android.view.View.VISIBLE
                                imageView.setImageResource(R.drawable.ic_check)
                                imageView.setColorFilter(
                                    ContextCompat.getColor(context, R.color.neon_green)
                                )
                                imageView.contentDescription = "Usuario verificado"
                            }
                            "pending" -> {
                                imageView.visibility = android.view.View.VISIBLE
                                imageView.setImageResource(R.drawable.ic_check)
                                imageView.setColorFilter(
                                    ContextCompat.getColor(context, R.color.cyberpunk_accent_secondary)
                                )
                                imageView.contentDescription = "Verificación pendiente"
                            }
                            else -> {
                                imageView.visibility = android.view.View.GONE
                                imageView.contentDescription = null
                            }
                        }
                    }
                }
            }

            override fun onError(error: String) {
                // Hide badge on error
                if (context is android.app.Activity) {
                    context.runOnUiThread {
                        imageView.visibility = android.view.View.GONE
                    }
                }
            }
        })
    }

    /**
     * Get verification status text for display
     */
    fun getVerificationStatusText(status: String): String {
        return when (status) {
            "approved" -> "Verificado"
            "pending" -> "Verificación pendiente"
            "rejected" -> "Verificación rechazada"
            else -> "No verificado"
        }
    }

    /**
     * Get verification status color resource
     */
    fun getVerificationStatusColor(status: String): Int {
        return when (status) {
            "approved" -> R.color.neon_green
            "pending" -> R.color.cyberpunk_accent_secondary
            "rejected" -> R.color.comfy_red
            else -> R.color.cyberpunk_text_secondary
        }
    }

    /**
     * Check if user is verified (approved status)
     */
    fun isUserVerified(status: String): Boolean {
        return status == "approved"
    }
}
