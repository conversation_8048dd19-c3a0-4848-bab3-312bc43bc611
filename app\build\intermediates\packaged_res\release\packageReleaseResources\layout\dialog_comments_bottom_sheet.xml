<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bottom_sheet_background"
    android:orientation="vertical"
    android:padding="16dp"
    android:fitsSystemWindows="true">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <TextView
            android:id="@+id/textCommentsCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="0 comentarios"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold" />

        <View
            android:layout_width="32dp"
            android:layout_height="4dp"
            android:layout_gravity="center"
            android:background="@color/gray_light"
            android:layout_marginEnd="16dp" />

    </LinearLayout>

    <!-- Comments List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewComments"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp"
        android:scrollbars="vertical" />

    <!-- Comment Input -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/comment_input_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp">

        <EditText
            android:id="@+id/editTextComment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="Añadir comentario..."
            android:maxLines="3"
            android:padding="8dp"
            android:textColor="@color/white"
            android:textColorHint="@color/gray_light"
            android:textSize="14sp" />

        <ImageButton
            android:id="@+id/buttonSend"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/circular_button_background"
            android:src="@drawable/ic_send"
            android:layout_marginStart="8dp"
            app:tint="@color/neon_blue" />

    </LinearLayout>

</LinearLayout>
