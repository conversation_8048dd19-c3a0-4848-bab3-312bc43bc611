package com.spyro.vmeet;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0002\u0016\u0017B\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0002\u0010\u0006J\b\u0010\t\u001a\u00020\nH\u0016J\u001c\u0010\u000b\u001a\u00020\f2\n\u0010\r\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u000e\u001a\u00020\nH\u0016J\u001c\u0010\u000f\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\nH\u0016J\u000e\u0010\u0013\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\bJ\u0014\u0010\u0014\u001a\u00020\f2\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/spyro/vmeet/UserCardAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/spyro/vmeet/UserCardAdapter$UserCardViewHolder;", "users", "", "Lcom/spyro/vmeet/User;", "(Ljava/util/List;)V", "listener", "Lcom/spyro/vmeet/UserCardAdapter$OnItemClickListener;", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setOnItemClickListener", "updateUsers", "newUsers", "OnItemClickListener", "UserCardViewHolder", "app_release"})
public final class UserCardAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.spyro.vmeet.UserCardAdapter.UserCardViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.spyro.vmeet.User> users;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.UserCardAdapter.OnItemClickListener listener;
    
    public UserCardAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.User> users) {
        super();
    }
    
    public final void setOnItemClickListener(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.UserCardAdapter.OnItemClickListener listener) {
    }
    
    public final void updateUsers(@org.jetbrains.annotations.NotNull()
    java.util.List<com.spyro.vmeet.User> newUsers) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.spyro.vmeet.UserCardAdapter.UserCardViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.spyro.vmeet.UserCardAdapter.UserCardViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/spyro/vmeet/UserCardAdapter$OnItemClickListener;", "", "onItemClick", "", "user", "Lcom/spyro/vmeet/User;", "app_release"})
    public static abstract interface OnItemClickListener {
        
        public abstract void onItemClick(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.User user);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\u0004\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u0019\u0010\u0012\u001a\u0004\u0018\u00010\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0006H\u0002\u00a2\u0006\u0002\u0010\u0015R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/spyro/vmeet/UserCardAdapter$UserCardViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "(Lcom/spyro/vmeet/UserCardAdapter;Landroid/view/View;)V", "API_URL", "", "imageViewAvatar", "Landroid/widget/ImageView;", "onlineStatusIndicator", "textViewBio", "Landroid/widget/TextView;", "textViewNameAge", "textViewOnlineStatus", "bind", "", "user", "Lcom/spyro/vmeet/User;", "calculateAge", "", "birthdateString", "(Ljava/lang/String;)Ljava/lang/Integer;", "app_release"})
    public final class UserCardViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewAvatar = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewNameAge = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewBio = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View onlineStatusIndicator = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewOnlineStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String API_URL = "http://77.110.116.89:3000";
        
        public UserCardViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.spyro.vmeet.User user) {
        }
        
        private final java.lang.Integer calculateAge(java.lang.String birthdateString) {
            return null;
        }
    }
}