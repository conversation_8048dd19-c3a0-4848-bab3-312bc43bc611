package com.spyro.vmeet.activity;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0088\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b%\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u001f\b\u0007\u0018\u0000 \u00b2\u00012\u00020\u0001:\u0002\u00b2\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010S\u001a\u00020TH\u0002J\b\u0010U\u001a\u00020TH\u0002J\b\u0010V\u001a\u00020\u001dH\u0002J\b\u0010W\u001a\u00020TH\u0002J\b\u0010X\u001a\u00020TH\u0002J\b\u0010Y\u001a\u00020TH\u0002J\b\u0010Z\u001a\u00020TH\u0002J\u0010\u0010[\u001a\u00020T2\u0006\u0010\\\u001a\u000201H\u0002J\u0018\u0010]\u001a\u00020T2\u0006\u0010^\u001a\u00020\u00042\u0006\u0010_\u001a\u00020\u0004H\u0002J\b\u0010`\u001a\u00020TH\u0002J\b\u0010a\u001a\u00020TH\u0002J\u0010\u0010b\u001a\u00020T2\u0006\u0010c\u001a\u00020\u0016H\u0002J\b\u0010d\u001a\u00020\u0016H\u0002J\u0018\u0010e\u001a\u00020T2\u0006\u0010f\u001a\u00020\u00162\u0006\u0010g\u001a\u00020\u0016H\u0002J\u0018\u0010h\u001a\u00020T2\u0006\u0010\\\u001a\u0002012\u0006\u0010i\u001a\u00020\u0016H\u0002J\b\u0010j\u001a\u00020TH\u0002J\b\u0010k\u001a\u00020TH\u0002J\b\u0010l\u001a\u00020TH\u0002J\b\u0010m\u001a\u00020TH\u0002J\u0012\u0010n\u001a\u00020T2\b\b\u0002\u0010o\u001a\u00020\u001dH\u0002J\b\u0010p\u001a\u00020TH\u0002J\b\u0010q\u001a\u00020TH\u0002J\u0010\u0010r\u001a\u00020T2\u0006\u0010s\u001a\u00020\u0016H\u0002J\b\u0010t\u001a\u00020TH\u0002J\u0010\u0010u\u001a\u00020T2\u0006\u0010\\\u001a\u000201H\u0002J\u0010\u0010v\u001a\u00020T2\u0006\u0010w\u001a\u00020\u0004H\u0002J\u0012\u0010x\u001a\u00020T2\b\u0010y\u001a\u0004\u0018\u00010zH\u0014J\u0010\u0010{\u001a\u00020\u001d2\u0006\u0010|\u001a\u00020}H\u0016J\b\u0010~\u001a\u00020TH\u0014J\u0012\u0010\u007f\u001a\u00020\u001d2\b\u0010\u0080\u0001\u001a\u00030\u0081\u0001H\u0016J\t\u0010\u0082\u0001\u001a\u00020TH\u0014J4\u0010\u0083\u0001\u001a\u00020T2\u0007\u0010\u0084\u0001\u001a\u00020\u00042\u0010\u0010\u0085\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u00020\u00160\u0086\u00012\b\u0010\u0087\u0001\u001a\u00030\u0088\u0001H\u0016\u00a2\u0006\u0003\u0010\u0089\u0001J\t\u0010\u008a\u0001\u001a\u00020TH\u0014J\u0011\u0010\u008b\u0001\u001a\u00020T2\u0006\u0010\\\u001a\u00020\u0016H\u0002J\u0011\u0010\u008c\u0001\u001a\u00020T2\u0006\u0010g\u001a\u00020\u0016H\u0002J5\u0010\u008d\u0001\u001a\u00020T2\u0007\u0010\u008e\u0001\u001a\u00020\u00162\u0006\u0010f\u001a\u00020\u00162\u000e\u0010\u008f\u0001\u001a\t\u0012\u0004\u0012\u00020\u00040\u0090\u00012\t\u0010\u0091\u0001\u001a\u0004\u0018\u000101H\u0002J3\u0010\u0092\u0001\u001a\u00020T2\u0007\u0010\u008e\u0001\u001a\u00020\u00162\f\b\u0002\u0010\u0093\u0001\u001a\u0005\u0018\u00010\u0094\u00012\u000b\b\u0002\u0010\u0095\u0001\u001a\u0004\u0018\u00010\u0004H\u0002\u00a2\u0006\u0003\u0010\u0096\u0001J5\u0010\u0097\u0001\u001a\u00020T2\u0007\u0010\u008e\u0001\u001a\u00020\u00162\u0006\u0010f\u001a\u00020\u00162\u000e\u0010\u008f\u0001\u001a\t\u0012\u0004\u0012\u00020\u00040\u0090\u00012\t\u0010\u0091\u0001\u001a\u0004\u0018\u000101H\u0002J5\u0010\u0098\u0001\u001a\u00020\u001d2\u0007\u0010\u008e\u0001\u001a\u00020\u00162\u0006\u0010f\u001a\u00020\u00162\u000e\u0010\u008f\u0001\u001a\t\u0012\u0004\u0012\u00020\u00040\u0090\u00012\t\u0010\u0091\u0001\u001a\u0004\u0018\u000101H\u0002J\t\u0010\u0099\u0001\u001a\u00020TH\u0002J\t\u0010\u009a\u0001\u001a\u00020TH\u0002J\t\u0010\u009b\u0001\u001a\u00020TH\u0002J\u0011\u0010\u009c\u0001\u001a\u00020T2\u0006\u0010\\\u001a\u000201H\u0002J\t\u0010\u009d\u0001\u001a\u00020TH\u0002J\t\u0010\u009e\u0001\u001a\u00020TH\u0002J\t\u0010\u009f\u0001\u001a\u00020TH\u0002J\u001a\u0010\u00a0\u0001\u001a\u00020T2\u0006\u0010^\u001a\u00020\u00042\u0007\u0010\u00a1\u0001\u001a\u00020\u0016H\u0002J\t\u0010\u00a2\u0001\u001a\u00020TH\u0002J\t\u0010\u00a3\u0001\u001a\u00020TH\u0002J\t\u0010\u00a4\u0001\u001a\u00020TH\u0002J\t\u0010\u00a5\u0001\u001a\u00020TH\u0002J\t\u0010\u00a6\u0001\u001a\u00020TH\u0002J\t\u0010\u00a7\u0001\u001a\u00020TH\u0002J\t\u0010\u00a8\u0001\u001a\u00020TH\u0002J\t\u0010\u00a9\u0001\u001a\u00020TH\u0002J\t\u0010\u00aa\u0001\u001a\u00020TH\u0002J\t\u0010\u00ab\u0001\u001a\u00020TH\u0002J\t\u0010\u00ac\u0001\u001a\u00020TH\u0002J4\u0010\u00ad\u0001\u001a\u00020T2\u0007\u0010\u00ae\u0001\u001a\u00020\u00162\u0006\u0010s\u001a\u00020\u00162\u0007\u0010\u00af\u0001\u001a\u00020\u00042\t\u0010\u00b0\u0001\u001a\u0004\u0018\u00010\u0004H\u0002\u00a2\u0006\u0003\u0010\u00b1\u0001R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u001dX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020%X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020(X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010*\u001a\b\u0012\u0004\u0012\u00020,0+X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082.\u00a2\u0006\u0002\n\u0000R\u0014\u0010/\u001a\b\u0012\u0004\u0012\u00020100X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u000203X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u00104\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u00105\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020106X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u000203X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u000209X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010;\u001a\u00020<X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010=\u001a\u00020<X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010>\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010?\u001a\u0004\u0018\u000101X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010@\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010A\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010B\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010C\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010D\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010E\u001a\u00020FX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010G\u001a\u00020FX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010H\u001a\u00020IX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010J\u001a\u00020FX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010K\u001a\u00020FX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010L\u001a\u00020MX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010N\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010O\u001a\u0004\u0018\u00010PX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010Q\u001a\u0004\u0018\u00010RX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u00b3\u0001"}, d2 = {"Lcom/spyro/vmeet/activity/ChatRoomActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "()V", "MAX_RECONNECT_ATTEMPTS", "", "MUTE_CHECK_INTERVAL", "", "POLLING_INTERVAL", "RECONNECT_DELAY_MS", "audioPlayer", "Lcom/spyro/vmeet/audio/AudioPlayer;", "audioRecorder", "Lcom/spyro/vmeet/audio/AudioRecorder;", "buttonCancelReply", "Landroid/widget/ImageButton;", "buttonMic", "buttonSend", "cardViewMentionSuggestions", "Landroidx/cardview/widget/CardView;", "client", "Lokhttp3/OkHttpClient;", "currentMentionQuery", "", "currentUserAvatarUrl", "editTextMessage", "Landroid/widget/EditText;", "gson", "Lcom/google/gson/Gson;", "hasJoinedRoom", "", "imageViewRoomIcon", "Landroid/widget/ImageView;", "isPollingActive", "isRecording", "isUserAdmin", "isUserMuted", "layoutMuted", "Landroid/widget/LinearLayout;", "layoutReplyPreview", "mentionSuggestionsAdapter", "Lcom/spyro/vmeet/adapter/MentionSuggestionsAdapter;", "mentionTriggerPosition", "mentionUserList", "", "Lcom/spyro/vmeet/data/MentionUser;", "messageAdapter", "Lcom/spyro/vmeet/adapter/MessageAdapter;", "messagesList", "Ljava/util/ArrayList;", "Lcom/spyro/vmeet/data/Message;", "muteCheckHandler", "Landroid/os/Handler;", "muteEndTime", "pendingMessages", "Ljava/util/HashMap;", "pollingHandler", "progressBar", "Landroid/widget/ProgressBar;", "reconnectAttempts", "recyclerViewMentionSuggestions", "Landroidx/recyclerview/widget/RecyclerView;", "recyclerViewMessages", "remainingMuteMinutes", "replyingToMessage", "roomDescription", "roomIcon", "roomId", "roomName", "rules", "textViewMemberCount", "Landroid/widget/TextView;", "textViewMuteRemainingTime", "textViewReplyPreview", "Landroidx/emoji2/widget/EmojiTextView;", "textViewRoomDescription", "textViewRoomName", "toolbar", "Landroidx/appcompat/widget/Toolbar;", "userId", "webSocket", "Lokhttp3/WebSocket;", "webSocketClient", "Lcom/spyro/vmeet/data/WebSocketClient;", "attemptReconnection", "", "cancelVoiceRecording", "checkRecordingPermission", "checkRoomMembership", "checkUserAdminStatus", "checkUserMuteStatus", "clearReplyState", "deleteMessage", "message", "executeMuteUser", "targetUserId", "durationMinutes", "extractIntentData", "fetchCurrentUserAvatar", "fetchMentionSuggestions", "query", "getCurrentISOTimestamp", "handleMessageError", "clientTempId", "errorMessage", "handleMessageReaction", "reaction", "initializeViews", "initializeWebSocketClient", "joinRoom", "leaveRoom", "loadMessages", "isRetry", "loadMessagesWithRetry", "markAllMessagesAsRead", "markMessageAsError", "tempId", "markMessagesAsRead", "muteUser", "navigateToUserProfile", "profileUserId", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateOptionsMenu", "menu", "Landroid/view/Menu;", "onDestroy", "onOptionsItemSelected", "item", "Landroid/view/MenuItem;", "onPause", "onRequestPermissionsResult", "requestCode", "permissions", "", "grantResults", "", "(I[Ljava/lang/String;[I)V", "onResume", "processWebSocketMessage", "reportErrorToUser", "retryMessageSend", "messageText", "mentionedUserIds", "", "replyTo", "sendMessage", "voiceFile", "Ljava/io/File;", "voiceDuration", "(Ljava/lang/String;Ljava/io/File;Ljava/lang/Integer;)V", "sendMessageViaHttp", "sendMessageViaWebSocket", "setupMentionSuggestions", "setupMessagesList", "setupReplyFunctionality", "setupReplyToMessage", "setupVoiceMessageRecording", "showJoinRoomPrompt", "showLeaveRoomConfirmation", "showMuteDialog", "username", "showRoomInfoDialog", "startMuteStatusCheck", "startPolling", "startPulseAnimation", "startVoiceRecording", "stopMuteStatusCheck", "stopPolling", "stopVoiceRecordingAndSend", "updateAdapterWithAdminStatus", "updateMuteUI", "updateRoomInfo", "uploadVoiceMessageAndSend", "filePath", "duration", "replyToId", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/Integer;)V", "Companion", "app_release"})
public final class ChatRoomActivity extends androidx.appcompat.app.AppCompatActivity {
    private androidx.recyclerview.widget.RecyclerView recyclerViewMessages;
    private android.widget.EditText editTextMessage;
    private android.widget.ImageButton buttonSend;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewRoomName;
    private android.widget.TextView textViewRoomDescription;
    private android.widget.ImageView imageViewRoomIcon;
    private android.widget.TextView textViewMemberCount;
    private androidx.appcompat.widget.Toolbar toolbar;
    private android.widget.ImageButton buttonMic;
    private android.widget.LinearLayout layoutReplyPreview;
    private androidx.emoji2.widget.EmojiTextView textViewReplyPreview;
    private android.widget.ImageButton buttonCancelReply;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.Message replyingToMessage;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.audio.AudioRecorder audioRecorder;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.audio.AudioPlayer audioPlayer;
    private boolean isRecording = false;
    private androidx.cardview.widget.CardView cardViewMentionSuggestions;
    private androidx.recyclerview.widget.RecyclerView recyclerViewMentionSuggestions;
    private com.spyro.vmeet.adapter.MentionSuggestionsAdapter mentionSuggestionsAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.spyro.vmeet.data.MentionUser> mentionUserList = null;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String currentMentionQuery;
    private int mentionTriggerPosition = -1;
    private com.spyro.vmeet.adapter.MessageAdapter messageAdapter;
    @org.jetbrains.annotations.NotNull()
    private final java.util.ArrayList<com.spyro.vmeet.data.Message> messagesList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.HashMap<java.lang.String, com.spyro.vmeet.data.Message> pendingMessages = null;
    private int roomId = -1;
    private int userId = -1;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomName;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomDescription;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String roomIcon;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String rules;
    private boolean hasJoinedRoom = false;
    private boolean isUserAdmin = false;
    private boolean isPollingActive = false;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler pollingHandler = null;
    private final long POLLING_INTERVAL = 1000L;
    private okhttp3.OkHttpClient client;
    @org.jetbrains.annotations.Nullable()
    private okhttp3.WebSocket webSocket;
    @org.jetbrains.annotations.Nullable()
    private com.spyro.vmeet.data.WebSocketClient webSocketClient;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    private int reconnectAttempts = 0;
    private final int MAX_RECONNECT_ATTEMPTS = 5;
    private final long RECONNECT_DELAY_MS = 3000L;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String currentUserAvatarUrl;
    private android.widget.LinearLayout layoutMuted;
    private android.widget.TextView textViewMuteRemainingTime;
    private boolean isUserMuted = false;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String muteEndTime;
    private int remainingMuteMinutes = 0;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler muteCheckHandler = null;
    private final long MUTE_CHECK_INTERVAL = 60000L;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ChatRoomActivity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BASE_URL = "*************";
    private static final int PORT = 3000;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String API_URL = "http://*************:3000";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String WS_URL = "ws://*************:3000";
    private static final int NORMAL_CLOSURE_STATUS = 1000;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ROOM_ID = "ROOM_ID";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ROOM_NAME = "ROOM_NAME";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String USER_ID = "USER_ID";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ROOM_DESCRIPTION = "ROOM_DESCRIPTION";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ROOM_ICON = "ROOM_ICON";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String RULES = "RULES";
    @org.jetbrains.annotations.NotNull()
    public static final com.spyro.vmeet.activity.ChatRoomActivity.Companion Companion = null;
    
    public ChatRoomActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initializeViews() {
    }
    
    private final void extractIntentData() {
    }
    
    private final void updateRoomInfo() {
    }
    
    private final void setupMessagesList() {
    }
    
    /**
     * Navigate to the user profile when their avatar is clicked
     */
    private final void navigateToUserProfile(int profileUserId) {
    }
    
    private final void setupReplyFunctionality() {
    }
    
    private final void setupReplyToMessage(com.spyro.vmeet.data.Message message) {
    }
    
    private final void clearReplyState() {
    }
    
    private final void handleMessageReaction(com.spyro.vmeet.data.Message message, java.lang.String reaction) {
    }
    
    private final void setupVoiceMessageRecording() {
    }
    
    private final boolean checkRecordingPermission() {
        return false;
    }
    
    private final void startVoiceRecording() {
    }
    
    private final void cancelVoiceRecording() {
    }
    
    private final void stopVoiceRecordingAndSend() {
    }
    
    private final void uploadVoiceMessageAndSend(java.lang.String filePath, java.lang.String tempId, int duration, java.lang.Integer replyToId) {
    }
    
    private final void reportErrorToUser(java.lang.String errorMessage) {
    }
    
    private final void markMessageAsError(java.lang.String tempId) {
    }
    
    @java.lang.Override()
    public void onRequestPermissionsResult(int requestCode, @org.jetbrains.annotations.NotNull()
    java.lang.String[] permissions, @org.jetbrains.annotations.NotNull()
    int[] grantResults) {
    }
    
    private final void checkRoomMembership() {
    }
    
    private final void showJoinRoomPrompt() {
    }
    
    private final void joinRoom() {
    }
    
    /**
     * Load messages with retry functionality if first attempt fails
     */
    private final void loadMessagesWithRetry() {
    }
    
    private final void loadMessages(boolean isRetry) {
    }
    
    /**
     * Marks all messages in the current room as read.
     * This resets the unread counter when opening a chat room.
     */
    private final void markMessagesAsRead() {
    }
    
    private final void sendMessage(java.lang.String messageText, java.io.File voiceFile, java.lang.Integer voiceDuration) {
    }
    
    /**
     * Send a message via WebSocket for real-time delivery
     * @return true if the message was sent successfully, false otherwise
     */
    private final boolean sendMessageViaWebSocket(java.lang.String messageText, java.lang.String clientTempId, java.util.List<java.lang.Integer> mentionedUserIds, com.spyro.vmeet.data.Message replyTo) {
        return false;
    }
    
    /**
     * Send a message via HTTP as fallback method or for ensuring persistence
     */
    private final void sendMessageViaHttp(java.lang.String messageText, java.lang.String clientTempId, java.util.List<java.lang.Integer> mentionedUserIds, com.spyro.vmeet.data.Message replyTo) {
    }
    
    /**
     * Retry sending a failed message after a delay
     */
    private final void retryMessageSend(java.lang.String messageText, java.lang.String clientTempId, java.util.List<java.lang.Integer> mentionedUserIds, com.spyro.vmeet.data.Message replyTo) {
    }
    
    private final void handleMessageError(java.lang.String clientTempId, java.lang.String errorMessage) {
    }
    
    private final void startPolling() {
    }
    
    private final void stopPolling() {
    }
    
    private final void initializeWebSocketClient() {
    }
    
    private final void processWebSocketMessage(java.lang.String message) {
    }
    
    private final void attemptReconnection() {
    }
    
    @java.lang.Override()
    protected void onDestroy() {
    }
    
    @java.lang.Override()
    protected void onResume() {
    }
    
    @java.lang.Override()
    protected void onPause() {
    }
    
    @java.lang.Override()
    public boolean onOptionsItemSelected(@org.jetbrains.annotations.NotNull()
    android.view.MenuItem item) {
        return false;
    }
    
    private final void showRoomInfoDialog() {
    }
    
    private final void showLeaveRoomConfirmation() {
    }
    
    private final void leaveRoom() {
    }
    
    private final void startPulseAnimation() {
    }
    
    /**
     * Mark all messages in this room as read
     */
    private final void markAllMessagesAsRead() {
    }
    
    @java.lang.Override()
    public boolean onCreateOptionsMenu(@org.jetbrains.annotations.NotNull()
    android.view.Menu menu) {
        return false;
    }
    
    private final void setupMentionSuggestions() {
    }
    
    private final void fetchMentionSuggestions(java.lang.String query) {
    }
    
    private final java.lang.String getCurrentISOTimestamp() {
        return null;
    }
    
    private final void fetchCurrentUserAvatar() {
    }
    
    /**
     * Check if current user has admin privileges
     */
    private final void checkUserAdminStatus() {
    }
    
    /**
     * Update adapter with current admin status
     */
    private final void updateAdapterWithAdminStatus() {
    }
    
    /**
     * Delete a message (admin only)
     */
    private final void deleteMessage(com.spyro.vmeet.data.Message message) {
    }
    
    /**
     * Mute a user (admin only)
     */
    private final void muteUser(com.spyro.vmeet.data.Message message) {
    }
    
    /**
     * Show dialog to select mute duration
     */
    private final void showMuteDialog(int targetUserId, java.lang.String username) {
    }
    
    /**
     * Execute the API call to mute a user
     */
    private final void executeMuteUser(int targetUserId, int durationMinutes) {
    }
    
    /**
     * Check if the current user is muted
     */
    private final void checkUserMuteStatus() {
    }
    
    /**
     * Update UI based on user's mute status
     */
    private final void updateMuteUI() {
    }
    
    /**
     * Start periodic mute status check
     */
    private final void startMuteStatusCheck() {
    }
    
    /**
     * Stop periodic mute status check
     */
    private final void stopMuteStatusCheck() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/spyro/vmeet/activity/ChatRoomActivity$Companion;", "", "()V", "API_URL", "", "BASE_URL", "NORMAL_CLOSURE_STATUS", "", "PORT", "ROOM_DESCRIPTION", "ROOM_ICON", "ROOM_ID", "ROOM_NAME", "RULES", "TAG", "USER_ID", "WS_URL", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}